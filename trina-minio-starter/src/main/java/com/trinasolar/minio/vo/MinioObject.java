package com.trinasolar.minio.vo;

import io.minio.StatObjectResponse;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;


/**
 * 存储对象的元数据
 */
@Data
@AllArgsConstructor
public class MinioObject {
    private String bucketName;
    private String name;
    private ZonedDateTime createdTime;
    private Long length;
    private String etag;
    private String contentType;
    private Map<String, List<String>> httpHeaders;

    public MinioObject(StatObjectResponse os) {
        this.bucketName = os.bucket();
        this.name = os.object();
        this.createdTime = os.lastModified();
        this.length = os.size();
        this.etag = os.etag();
        this.contentType = os.contentType();
        this.httpHeaders = os.headers().toMultimap();
    }

}
