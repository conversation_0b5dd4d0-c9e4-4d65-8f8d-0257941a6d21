package com.trinasolar.i18n.file;

import java.util.Map;
import java.util.Optional;

/**
 * 翻译文件加载器接口
 *
 * <AUTHOR>
 */
public interface TranslationFileLoader {

    /**
     * 从文件加载单个翻译
     *
     * @param key 翻译键
     * @param language 语言
     * @return 翻译结果，未找到则返回空
     */
    Optional<String> loadTranslation(String key, String language);

    /**
     * 加载指定语言的所有翻译
     *
     * @param language 语言
     * @return 翻译映射，未找到则返回空映射
     */
    Map<String, String> loadAllTranslations(String language);

    /**
     * 重新加载所有翻译文件
     */
    void reloadAllFiles();

    /**
     * 重新加载指定语言的翻译文件
     *
     * @param language 语言
     */
    void reloadFiles(String language);

    /**
     * 获取文件路径
     *
     * @param language 语言
     * @param isNewTranslation 是否是新翻译文件
     * @return 文件路径
     */
    String getFilePath(String language, boolean isNewTranslation);
} 