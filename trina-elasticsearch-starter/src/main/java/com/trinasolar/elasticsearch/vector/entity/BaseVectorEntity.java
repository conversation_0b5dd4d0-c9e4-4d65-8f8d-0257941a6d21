package com.trinasolar.elasticsearch.vector.entity;

import com.trinasolar.elasticsearch.base.entity.BaseEsEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 向量实体基类
 * 扩展BaseEsEntity，提供向量搜索相关的基础功能
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public abstract class BaseVectorEntity extends BaseEsEntity {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 获取向量字段映射配置
     * 子类可以重写此方法来定义向量字段的映射信息
     * 
     * @return 向量字段映射配置
     */
    public VectorFieldMapping getVectorFieldMapping() {
        return null;
    }
    
    /**
     * 获取默认的相似性度量算法
     * 
     * @return 相似性度量算法
     */
    public String getDefaultSimilarity() {
        return "cosine";
    }
    
    /**
     * 获取默认的向量维度
     * 
     * @return 向量维度
     */
    public int getDefaultVectorDims() {
        return 768;
    }
    
    /**
     * 是否启用向量索引
     * 
     * @return 是否启用向量索引
     */
    public boolean isVectorIndexEnabled() {
        return true;
    }
    
    /**
     * 获取向量索引的候选数量
     * 用于HNSW算法的ef_construction参数
     * 
     * @return 候选数量
     */
    public int getVectorIndexCandidates() {
        return 100;
    }
    
    /**
     * 获取向量索引的连接数
     * 用于HNSW算法的m参数
     * 
     * @return 连接数
     */
    public int getVectorIndexConnections() {
        return 16;
    }
}