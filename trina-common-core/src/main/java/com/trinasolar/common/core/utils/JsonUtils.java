package com.trinasolar.common.core.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
public class JsonUtils {
    /**
     * 通过json路径找到json值,返回String，如果未查到返回""
     *
     * @param jsonObject
     * @param path
     * @return
     */
    public static String getJsonFieldString(JSONObject jsonObject, String path) {
        List<Object> list = getJsonFieldValue(jsonObject, path);
        if (list.isEmpty()) {
            return "";
        }
        if (!(list.get(0) instanceof String)) {
            log.debug("从Json中提取数据异常：目标提取String，实际提取：{}", list);
            log.debug("从Json中提取数据异常：目标提取String，原始数据：{}", jsonObject.toJSONString());
            return "";
        }
        return (String) list.get(0);
    }

    public static Integer getJsonFieldInt(JSONObject jsonObject, String path) {
        List<Object> list = getJsonFieldValue(jsonObject, path);
        if (list.isEmpty()) {
            return 0;
        }
        if (!(list.get(0) instanceof Integer)) {
            log.debug("从Json中提取数据异常：目标提取Integer，实际提取：{}", list);
            log.debug("从Json中提取数据异常：目标提取Integer，原始数据：{}", jsonObject.toJSONString());
            return 0;
        }
        return (Integer) list.get(0);
    }

    /**
     * 通过json路径找到json值
     *
     * @param jsonObject 要取值的json对象
     * @param path       对象路径
     * @return 对象值列表 由于可能存在A.B.C路径中B为列表的情况，所以结果可能有多个
     */
    public static List<Object> getJsonFieldValue(JSONObject jsonObject, String path) {
        List<String> keyWordList = new ArrayList(Arrays.asList(path.split("\\.")));
        List<Object> list = new ArrayList<>();
        String key = keyWordList.get(0);
        Object object = jsonObject.get(key);
        keyWordList.remove(0);
        if (keyWordList.isEmpty()) {
            if (null != object) {
                list.add(object);
            }
            return list;
        }

        String subPath = StringUtils.join(keyWordList, ".");
        if (object instanceof List) {
            JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(object));
            if (jsonArray.isEmpty()) {
                return new ArrayList<>();
            }
            jsonArray.forEach(e -> list.addAll(getJsonFieldValue(JSON.parseObject(JSON.toJSONString(e)), subPath)));
        } else if (object instanceof Map) {
            JSONObject subJsonObject = JSONObject.parseObject(JSON.toJSONString(object));
            list.addAll(getJsonFieldValue(JSON.parseObject(JSON.toJSONString(subJsonObject)), subPath));
        }
        return list;
    }

    /**
     * 通用的 通过json路径找到json值
     *
     * @param jsonObject 要取值的json对象
     * @param path       对象路径
     * @param value      要设置的值
     * @return 修改后的对象
     */
    public static Object setJsonFieldValue(JSONObject jsonObject, String path, Object value) {
        List<String> keyWordList = new ArrayList(Arrays.asList(path.split("\\.")));
        String key = keyWordList.get(0);
        keyWordList.remove(0);
        //如果关键词为空，说明此处为目标点，设置值
        if (keyWordList.isEmpty()) {
            jsonObject.fluentPut(key, value);
            return jsonObject;
        }
        //关键词不为空，取出子jsonObject进行递归
        Object object = jsonObject.get(key);

        String subPath = StringUtils.join(keyWordList, ".");
        if (object instanceof List) {
            JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(object));
            if (jsonArray.isEmpty()) {
                //说明不是目标点,原样返回
                return object;
            }
            JSONArray newArray = new JSONArray();
            jsonArray.forEach(e -> newArray.add(setJsonFieldValue(JSON.parseObject(JSON.toJSONString(e)), subPath, value)));
            jsonObject.fluentPut(key, newArray);
        } else if (object instanceof Map) {
            JSONObject subJsonObject = JSONObject.parseObject(JSON.toJSONString(object));
            jsonObject.fluentPut(key, setJsonFieldValue(JSON.parseObject(JSON.toJSONString(subJsonObject)), subPath, value));
        }
        return jsonObject;
    }
}
