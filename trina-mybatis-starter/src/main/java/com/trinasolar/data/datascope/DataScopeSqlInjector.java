package com.trinasolar.data.datascope;


import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.github.yulichang.injector.MPJSqlInjector;
import org.apache.ibatis.session.Configuration;

import java.util.List;

/**
 * 支持自定义数据权限方法注入
 *
 * <AUTHOR>
 * @since 2025-07-15 10:24
 **/
public class DataScopeSqlInjector extends MPJSqlInjector {

    @Override
    public List<AbstractMethod> getMethodList(Configuration configuration, Class<?> mapperClass, TableInfo tableInfo) {
        List<AbstractMethod> methodList = super.getMethodList(configuration, mapperClass, tableInfo);
        methodList.add(new SelectListByScope());
        methodList.add(new SelectPageByScope());
        methodList.add(new SelectCountByScope());
        return methodList;
    }

}
