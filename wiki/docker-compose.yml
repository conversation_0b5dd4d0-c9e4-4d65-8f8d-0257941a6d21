version: '3.8'

services:
  wiki:
    build:
      context: .
      dockerfile: Dockerfile.nginx
    container_name: trina-wiki
    ports:
      - "80:80"
    volumes:
      - nginx_logs:/var/log/nginx
    environment:
      - NGINX_HOST=localhost
      - NGINX_PORT=80
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/wiki/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - wiki-network

volumes:
  nginx_logs:
    driver: local

networks:
  wiki-network:
    driver: bridge