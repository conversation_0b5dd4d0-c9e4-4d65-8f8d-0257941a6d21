package com.trinasolar.gateway.security.exception;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trinasolar.common.core.base.R;
import com.trinasolar.common.core.exception.AuthenticationException;
import com.trinasolar.common.core.exception.AuthorizationException;
import com.trinasolar.common.core.exception.BusinessException;
import com.trinasolar.gateway.security.sureness.exception.SurenessLoadDataException;
import com.trinasolar.gateway.security.sureness.exception.SurenessNoInitException;
import com.trinasolar.gateway.security.util.ErrorResponseUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.reactive.function.client.WebClientRequestException;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.PrematureCloseException;

import java.util.concurrent.TimeoutException;

/**
 * 网关全局异常处理器
 * <p>
 * 统一处理网关中的所有异常，确保返回统一的 R 结构（code、msg、data）
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@RestControllerAdvice
@RequiredArgsConstructor
@Order(-1) // 确保优先级高于默认异常处理器
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.REACTIVE)
public class GatewayGlobalExceptionHandler {

    private final ObjectMapper objectMapper;

    /**
     * 处理认证异常
     */
    @ExceptionHandler(AuthenticationException.class)
    public Mono<Void> handleAuthenticationException(AuthenticationException ex, ServerWebExchange exchange) {
        log.warn("Authentication failed: {}", ex.getMessage());
        ServerHttpResponse response = exchange.getResponse();
        return ErrorResponseUtils.writeUnauthorizedResponse(response, objectMapper, ex.getMessage(), null);
    }

    /**
     * 处理授权异常
     */
    @ExceptionHandler(AuthorizationException.class)
    public Mono<Void> handleAuthorizationException(AuthorizationException ex, ServerWebExchange exchange) {
        log.warn("Authorization failed: {}", ex.getMessage());
        ServerHttpResponse response = exchange.getResponse();
        return ErrorResponseUtils.writeForbiddenResponse(response, objectMapper, ex.getMessage());
    }

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public Mono<Void> handleBusinessException(BusinessException ex, ServerWebExchange exchange) {
        log.warn("Business exception: {}", ex.getMessage());
        ServerHttpResponse response = exchange.getResponse();
        return ErrorResponseUtils.writeBadRequestResponse(response, objectMapper, ex.getMessage());
    }

    /**
     * 处理 Sureness 初始化异常
     */
    @ExceptionHandler(SurenessNoInitException.class)
    public Mono<Void> handleSurenessNoInitException(SurenessNoInitException ex, ServerWebExchange exchange) {
        log.error("Sureness not initialized: {}", ex.getMessage());
        ServerHttpResponse response = exchange.getResponse();
        return ErrorResponseUtils.writeInternalServerErrorResponse(response, objectMapper, 
                "安全组件未初始化", getRequestId(exchange));
    }

    /**
     * 处理 Sureness 数据加载异常
     */
    @ExceptionHandler(SurenessLoadDataException.class)
    public Mono<Void> handleSurenessLoadDataException(SurenessLoadDataException ex, ServerWebExchange exchange) {
        log.error("Sureness data load failed: {}", ex.getMessage());
        ServerHttpResponse response = exchange.getResponse();
        return ErrorResponseUtils.writeInternalServerErrorResponse(response, objectMapper, 
                "权限数据加载失败", getRequestId(exchange));
    }

    /**
     * 处理 WebClient 请求异常
     */
    @ExceptionHandler(WebClientRequestException.class)
    public Mono<Void> handleWebClientRequestException(WebClientRequestException ex, ServerWebExchange exchange) {
        log.error("WebClient request failed: {}", ex.getMessage());
        ServerHttpResponse response = exchange.getResponse();
        return ErrorResponseUtils.writeServiceUnavailableResponse(response, objectMapper, 
                "上游服务不可用，请稍后重试");
    }

    /**
     * 处理 WebClient 响应异常
     */
    @ExceptionHandler(WebClientResponseException.class)
    public Mono<Void> handleWebClientResponseException(WebClientResponseException ex, ServerWebExchange exchange) {
        log.error("WebClient response error: {} - {}", ex.getStatusCode(), ex.getResponseBodyAsString());
        ServerHttpResponse response = exchange.getResponse();
        
        String message = "上游服务返回错误";
        if (ex.getStatusCode().is4xxClientError()) {
            message = "请求参数错误";
            return ErrorResponseUtils.writeBadRequestResponse(response, objectMapper, message);
        } else if (ex.getStatusCode().is5xxServerError()) {
            message = "上游服务内部错误";
            return ErrorResponseUtils.writeServiceUnavailableResponse(response, objectMapper, message);
        }
        
        return ErrorResponseUtils.writeInternalServerErrorResponse(response, objectMapper, message, getRequestId(exchange));
    }

    /**
     * 处理连接过早关闭异常
     */
    @ExceptionHandler(PrematureCloseException.class)
    public Mono<Void> handlePrematureCloseException(PrematureCloseException ex, ServerWebExchange exchange) {
        log.error("Connection prematurely closed: {}", ex.getMessage());
        ServerHttpResponse response = exchange.getResponse();
        return ErrorResponseUtils.writeServiceUnavailableResponse(response, objectMapper, 
                "网络连接异常，请稍后重试");
    }

    /**
     * 处理超时异常
     */
    @ExceptionHandler(TimeoutException.class)
    public Mono<Void> handleTimeoutException(TimeoutException ex, ServerWebExchange exchange) {
        log.error("Request timeout: {}", ex.getMessage());
        ServerHttpResponse response = exchange.getResponse();
        return ErrorResponseUtils.writeGatewayTimeoutResponse(response, objectMapper, "请求超时，请稍后重试");
    }

    /**
     * 处理响应状态异常
     */
    @ExceptionHandler(ResponseStatusException.class)
    public Mono<Void> handleResponseStatusException(ResponseStatusException ex, ServerWebExchange exchange) {
        log.error("Response status exception: {} - {}", ex.getStatusCode(), ex.getReason());
        ServerHttpResponse response = exchange.getResponse();
        String requestPath = getRequestPath(exchange);
        
        HttpStatus status = HttpStatus.resolve(ex.getStatusCode().value());
        if (status == null) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
        }

        return switch (status) {
            case NOT_FOUND -> ErrorResponseUtils.writeNotFoundResponse(response, objectMapper, requestPath);
            case BAD_REQUEST -> ErrorResponseUtils.writeBadRequestResponse(response, objectMapper,
                    ex.getReason() != null ? ex.getReason() : "请求参数错误");
            case UNAUTHORIZED -> ErrorResponseUtils.writeUnauthorizedResponse(response, objectMapper,
                    ex.getReason() != null ? ex.getReason() : "未授权访问", null);
            case FORBIDDEN -> ErrorResponseUtils.writeForbiddenResponse(response, objectMapper,
                    ex.getReason() != null ? ex.getReason() : "权限不足");
            case SERVICE_UNAVAILABLE -> ErrorResponseUtils.writeServiceUnavailableResponse(response, objectMapper,
                    ex.getReason() != null ? ex.getReason() : "服务不可用");
            case GATEWAY_TIMEOUT -> ErrorResponseUtils.writeGatewayTimeoutResponse(response, objectMapper,
                    ex.getReason() != null ? ex.getReason() : "网关超时");
            default -> ErrorResponseUtils.writeInternalServerErrorResponse(response, objectMapper,
                    ex.getReason() != null ? ex.getReason() : "系统内部错误", getRequestId(exchange));
        };
    }

    /**
     * 处理参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public Mono<Void> handleIllegalArgumentException(IllegalArgumentException ex, ServerWebExchange exchange) {
        log.warn("Illegal argument: {}", ex.getMessage());
        ServerHttpResponse response = exchange.getResponse();
        return ErrorResponseUtils.writeBadRequestResponse(response, objectMapper, 
                "请求参数错误: " + ex.getMessage());
    }

    /**
     * 处理安全异常
     */
    @ExceptionHandler(SecurityException.class)
    public Mono<Void> handleSecurityException(SecurityException ex, ServerWebExchange exchange) {
        log.warn("Security exception: {}", ex.getMessage());
        ServerHttpResponse response = exchange.getResponse();
        return ErrorResponseUtils.writeForbiddenResponse(response, objectMapper, ex.getMessage());
    }

    /**
     * 处理所有其他异常
     */
    @ExceptionHandler(Exception.class)
    public Mono<Void> handleGenericException(Exception ex, ServerWebExchange exchange) {
        log.error("Unexpected exception occurred", ex);
        ServerHttpResponse response = exchange.getResponse();
        return ErrorResponseUtils.writeInternalServerErrorResponse(response, objectMapper, 
                "系统内部错误，请联系管理员", getRequestId(exchange));
    }

    /**
     * 获取请求路径
     */
    private String getRequestPath(ServerWebExchange exchange) {
        ServerHttpRequest request = exchange.getRequest();
        return request.getPath().value();
    }

    /**
     * 获取请求ID
     */
    private String getRequestId(ServerWebExchange exchange) {
        ServerHttpRequest request = exchange.getRequest();
        return request.getHeaders().getFirst("X-Request-ID");
    }
}
