---
title: <PERSON>na Email Starter 使用指南
description: <PERSON>na Email Starter 组件的快速开始、配置方法和使用说明
---

# Trina Email Starter 使用指南

## 快速集成

### 1. 添加依赖

```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-email-starter</artifactId>
    <version>4.0.0</version>
</dependency>
```

### 2.1 注入依赖

```java

@Autowired
private MailService mailService;

```

### 2.2 控制层接口调用
```java
@PostMapping("/sendMail")
public Boolean test(@ModelAttribute EmailRequest emailRequest){
    return mailService.sendMail(emailRequest);
}
```

### 3. 配置文件

```yaml
spring:
  mail:
    host: smtp.qq.com
    port: 587
    username: 邮箱地址(<EMAIL>)
    password: 授权码
    protocol: smtp
    from: 邮箱地址(<EMAIL>)
    auth: true
    ssl: false
    starttls: true
```

## 4.配置说明
- （以QQ邮箱SMTP配置为例）

| 配置项      | 说明               | 默认值                   |
|----------|------------------|-----------------------|
| host     | 邮件服务器的主机地址       | 例如smtp.qq.com         |
| port     | 邮件服务器的端口号        | 587 （SMTP 端口）         |
| username | 邮件服务器的用户名        | 通常是邮箱地址               |
| password | 邮件服务器的授权码        | 授权码                   |
| protocol | 邮件协议，通常是邮件协议，通常是 | smtp                  |
| from     | 默认发件人的邮箱地址       | 通常是username           |
| auth     | 启用SMTP认证         | 必须为true               |
| ssl      | 是否启用SSL加密        | 应设为false（与starttls互斥） |
| starttls | 启用STARTTLS加密（推荐） | 推荐配置（比SSL更安全）         |
