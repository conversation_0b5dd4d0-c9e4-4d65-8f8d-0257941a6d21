package com.trinasolar.common.core.context;


import com.alibaba.ttl.TransmittableThreadLocal;
import lombok.experimental.UtilityClass;

/**
 * 当前线程租户工具类
 *
 * <AUTHOR>
 * @since 2025-05-29 17:48
 **/
@UtilityClass
public class TenantContextHolder {

    private final ThreadLocal<Long> THREAD_LOCAL_TENANT = new TransmittableThreadLocal<>();

    private final ThreadLocal<Boolean> THREAD_LOCAL_TENANT_SKIP_FLAG = new TransmittableThreadLocal<>();

    /**
     * 设置是否过滤的标识
     */
    public void setTenantSkip() {
        THREAD_LOCAL_TENANT_SKIP_FLAG.set(Boolean.TRUE);
    }

    /**
     * 获取TTL中的租户ID
     *
     * @return {@link Long }
     */
    public Long getTenantId() {
        return THREAD_LOCAL_TENANT.get();
    }

    /**
     * TTL 设置租户ID<br/>
     * <b>谨慎使用此方法,避免嵌套调用。尽量使用 {@code TenantBroker} </b>
     *
     * @param tenantId 租户 ID
     */
    public void setTenantId(Long tenantId) {
        THREAD_LOCAL_TENANT.set(tenantId);
    }


    /**
     * 获取是否跳过租户过滤的标识
     *
     * @return {@link Boolean }
     */
    public Boolean getTenantSkip() {
        return THREAD_LOCAL_TENANT_SKIP_FLAG.get() != null && THREAD_LOCAL_TENANT_SKIP_FLAG.get();
    }

    /**
     * 清除
     */
    public void clear() {
        THREAD_LOCAL_TENANT.remove();
        THREAD_LOCAL_TENANT_SKIP_FLAG.remove();
    }
}
