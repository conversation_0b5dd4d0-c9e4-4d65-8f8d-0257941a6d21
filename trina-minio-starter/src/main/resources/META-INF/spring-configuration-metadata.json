{"properties": [{"name": "spring.s3.endpoint", "type": "java.lang.String", "description": "S3服务端点URL"}, {"name": "spring.s3.region", "type": "java.lang.String", "description": "S3区域"}, {"name": "spring.s3.accessKey", "type": "java.lang.String", "description": "S3访问密钥"}, {"name": "spring.s3.secret<PERSON>ey", "type": "java.lang.String", "description": "S3秘密密钥"}, {"name": "spring.s3.bucket<PERSON>ame", "type": "java.lang.String", "description": "S3默认存储桶名称"}, {"name": "spring.s3.pathStyleAccess", "type": "java.lang.Bo<PERSON>an", "description": "是否使用路径样式访问", "defaultValue": true}, {"name": "spring.s3.connectionTimeout", "type": "java.lang.Integer", "description": "连接超时时间(毫秒)", "defaultValue": 10000}], "hints": []}