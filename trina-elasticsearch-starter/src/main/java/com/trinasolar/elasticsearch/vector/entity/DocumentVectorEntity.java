package com.trinasolar.elasticsearch.vector.entity;

import com.trinasolar.elasticsearch.base.annotation.EsField;
import com.trinasolar.elasticsearch.base.annotation.EsIndex;
import com.trinasolar.elasticsearch.base.annotation.FieldType;
import com.trinasolar.elasticsearch.base.entity.BaseEsEntity;
import com.trinasolar.elasticsearch.vector.annotation.VectorField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文档向量实体类示例
 * 展示如何使用向量模块进行文档的向量化存储和搜索
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@EsIndex(name = "document_vector", shards = 3, replicas = 1)
public class DocumentVectorEntity extends BaseVectorEntity {
    
    /**
     * 文档标题
     */
    @EsField(type = FieldType.TEXT, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String title;
    
    /**
     * 文档内容
     */
    @EsField(type = FieldType.TEXT, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String content;
    
    /**
     * 文档摘要
     */
    @EsField(type = FieldType.TEXT)
    private String summary;
    
    /**
     * 文档类型
     */
    @EsField(type = FieldType.KEYWORD)
    private String documentType;
    
    /**
     * 文档来源
     */
    @EsField(type = FieldType.KEYWORD)
    private String source;
    
    /**
     * 文档标签
     */
    @EsField(type = FieldType.KEYWORD)
    private String[] tags;
    
    /**
     * 文档语言
     */
    @EsField(type = FieldType.KEYWORD)
    private String language;
    
    /**
     * 文档大小（字节）
     */
    @EsField(type = FieldType.LONG)
    private Long fileSize;
    
    /**
     * 文档URL
     */
    @EsField(type = FieldType.KEYWORD, index = false)
    private String url;
    
    /**
     * 标题向量（使用BERT等模型生成的768维向量）
     */
    @VectorField(
        name = "title_vector",
        dims = 768,
        similarity = "cosine",
        index = true,
        m = 16,
        efConstruction = 100
    )
    @EsField(type = FieldType.DENSE_VECTOR, dims = 768)
    private float[] titleVector;
    
    /**
     * 内容向量（使用BERT等模型生成的768维向量）
     */
    @VectorField(
        name = "content_vector",
        dims = 768,
        similarity = "cosine",
        index = true,
        m = 16,
        efConstruction = 100
    )
    @EsField(type = FieldType.DENSE_VECTOR, dims = 768)
    private float[] contentVector;
    
    /**
     * 摘要向量（使用较小的模型生成的384维向量）
     */
    @VectorField(
        name = "summary_vector",
        dims = 384,
        similarity = "cosine",
        index = true,
        m = 16,
        efConstruction = 100
    )
    @EsField(type = FieldType.DENSE_VECTOR, dims = 384)
    private float[] summaryVector;
    
    /**
     * 多模态向量（融合文本和其他特征的向量）
     */
    @VectorField(
        name = "multimodal_vector",
        dims = 512,
        similarity = "dot_product",
        index = true,
        m = 32,
        efConstruction = 200
    )
    @EsField(type = FieldType.DENSE_VECTOR, dims = 512)
    private float[] multimodalVector;
    
    /**
     * 文档质量评分
     */
    @EsField(type = FieldType.FLOAT)
    private Float qualityScore;
    
    /**
     * 文档重要性评分
     */
    @EsField(type = FieldType.FLOAT)
    private Float importanceScore;
    
    /**
     * 文档相关性评分
     */
    @EsField(type = FieldType.FLOAT)
    private Float relevanceScore;
    
    /**
     * 是否已向量化
     */
    @EsField(type = FieldType.BOOLEAN)
    private Boolean vectorized;
    
    /**
     * 向量化模型版本
     */
    @EsField(type = FieldType.KEYWORD)
    private String vectorModelVersion;
    
    /**
     * 向量化时间戳
     */
    @EsField(type = FieldType.DATE)
    private Long vectorizedAt;
    
    /**
     * 获取默认搜索向量字段
     */
    public String getDefaultVectorField() {
        return "content_vector";
    }
    
    /**
     * 获取默认向量维度
     */
    @Override
    public int getDefaultVectorDims() {
        return 768;
    }
    
    /**
     * 获取默认相似性度量
     */
    @Override
    public String getDefaultSimilarity() {
        return "cosine";
    }
    
    /**
     * 检查是否已完成向量化
     */
    public boolean isFullyVectorized() {
        return vectorized != null && vectorized &&
               titleVector != null && titleVector.length > 0 &&
               contentVector != null && contentVector.length > 0 &&
               summaryVector != null && summaryVector.length > 0;
    }
    
    /**
     * 获取可用的向量字段列表
     */
    public String[] getAvailableVectorFields() {
        return new String[]{
            "title_vector",
            "content_vector", 
            "summary_vector",
            "multimodal_vector"
        };
    }
    
    /**
     * 根据字段名获取向量
     */
    public float[] getVectorByFieldName(String fieldName) {
        switch (fieldName) {
            case "title_vector":
                return titleVector;
            case "content_vector":
                return contentVector;
            case "summary_vector":
                return summaryVector;
            case "multimodal_vector":
                return multimodalVector;
            default:
                return null;
        }
    }
    
    /**
     * 根据字段名设置向量
     */
    public void setVectorByFieldName(String fieldName, float[] vector) {
        switch (fieldName) {
            case "title_vector":
                this.titleVector = vector;
                break;
            case "content_vector":
                this.contentVector = vector;
                break;
            case "summary_vector":
                this.summaryVector = vector;
                break;
            case "multimodal_vector":
                this.multimodalVector = vector;
                break;
            default:
                throw new IllegalArgumentException("未知的向量字段: " + fieldName);
        }
    }
    
    /**
     * 获取向量字段的维度
     */
    public int getVectorDimension(String fieldName) {
        switch (fieldName) {
            case "title_vector":
            case "content_vector":
                return 768;
            case "summary_vector":
                return 384;
            case "multimodal_vector":
                return 512;
            default:
                return 0;
        }
    }
    
    /**
     * 标记为已向量化
     */
    public void markAsVectorized(String modelVersion) {
        this.vectorized = true;
        this.vectorModelVersion = modelVersion;
        this.vectorizedAt = System.currentTimeMillis();
    }
    
    /**
     * 清除所有向量
     */
    public void clearVectors() {
        this.titleVector = null;
        this.contentVector = null;
        this.summaryVector = null;
        this.multimodalVector = null;
        this.vectorized = false;
        this.vectorModelVersion = null;
        this.vectorizedAt = null;
    }
    
    /**
     * 获取文档的文本内容（用于向量化）
     */
    public String getFullText() {
        StringBuilder fullText = new StringBuilder();
        if (title != null && !title.trim().isEmpty()) {
            fullText.append(title).append(" ");
        }
        if (content != null && !content.trim().isEmpty()) {
            fullText.append(content).append(" ");
        }
        if (summary != null && !summary.trim().isEmpty()) {
            fullText.append(summary);
        }
        return fullText.toString().trim();
    }
    
    /**
     * 获取文档的元数据信息
     */
    public String getMetadataText() {
        StringBuilder metadata = new StringBuilder();
        if (documentType != null) {
            metadata.append("类型:").append(documentType).append(" ");
        }
        if (source != null) {
            metadata.append("来源:").append(source).append(" ");
        }
        if (tags != null && tags.length > 0) {
            metadata.append("标签:").append(String.join(",", tags)).append(" ");
        }
        if (language != null) {
            metadata.append("语言:").append(language);
        }
        return metadata.toString().trim();
    }
}