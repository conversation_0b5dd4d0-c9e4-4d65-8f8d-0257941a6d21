package com.trinasolar.minio.service;

import com.trinasolar.minio.entity.FileResult;
import com.trinasolar.minio.utils.FileUtils;
import com.trinasolar.minio.utils.MD5Utils;
import com.trinasolar.minio.vo.MinioItem;
import io.minio.GetObjectArgs;
import io.minio.ListObjectsArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.Result;
import io.minio.StatObjectArgs;
import io.minio.StatObjectResponse;
import io.minio.http.Method;
import io.minio.messages.Bucket;
import io.minio.messages.Item;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.util.Assert;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * minio
 * https://docs.minio.io/docs/java-client-api-reference.html
 */
@RequiredArgsConstructor
@Slf4j
public class MinioTemplate implements InitializingBean, FileTemplate {
    private final String endpoint;
    private final String accessKey;
    private final String secretKey;
    private final String bucketName;
    private MinioClient client;

    /**
     * 创建bucket
     *
     * @param bucketName bucket名称
     */
    @SneakyThrows
    public void createBucket(String bucketName) {
        boolean exists = client.bucketExists(io.minio.BucketExistsArgs.builder().bucket(bucketName).build());
        if (!exists) {
            client.makeBucket(io.minio.MakeBucketArgs.builder().bucket(bucketName).build());
            String policyText = "{\"Version\":\"2012-10-17\"," +
                    "\"Statement\":[{\"Effect\":\"Allow\"," +
                    "\"Principal\":{\"AWS\":[\"*\"]}," +
                    "\"Action\":[\"s3:ListBucketMultipartUploads\",\"s3:GetBucketLocation\",\"s3:ListBucket\"]," +
                    "\"Resource\":[\"arn:aws:s3:::" + bucketName + "\"]}," +
                    "{\"Effect\":\"Allow\"," +
                    "\"Principal\":{\"AWS\":[\"*\"]}," +
                    "\"Action\":[\"s3:ListMultipartUploadParts\",\"s3:PutObject\",\"s3:AbortMultipartUpload\",\"s3:DeleteObject\",\"s3:GetObject\"]," +
                    "\"Resource\":[\"arn:aws:s3:::" + bucketName + "/*\"]}]}";
            client.setBucketPolicy(io.minio.SetBucketPolicyArgs.builder().bucket(bucketName).config(policyText).build());
        }
    }

    /**
     * 获取全部bucket
     */
    @SneakyThrows
    public List<Bucket> getAllBuckets() {
        return client.listBuckets();
    }

    /**
     * @param bucketName bucket名称
     */
    @SneakyThrows
    public Optional<Bucket> getBucket(String bucketName) {
        return client.listBuckets().stream().filter(b -> b.name().equals(bucketName)).findFirst();
    }

    /**
     * @param bucketName bucket名称
     */
    @SneakyThrows
    public void removeBucket(String bucketName) {
        client.removeBucket(io.minio.RemoveBucketArgs.builder().bucket(bucketName).build());
    }

    /**
     * 根据文件前置查询文件
     *
     * @param bucketName bucket名称
     * @param prefix     前缀
     * @param recursive  是否递归查询
     * @return MinioItem 列表
     */
    @SneakyThrows
    public List<MinioItem> getAllObjectsByPrefix(String bucketName, String prefix, boolean recursive) {
        List<MinioItem> objectList = new ArrayList<>();
        Iterable<Result<Item>> objectsIterator = client.listObjects(
                ListObjectsArgs.builder().bucket(bucketName).prefix(prefix).recursive(recursive).build());

        for (Result<Item> itemResult : objectsIterator) {
            objectList.add(new MinioItem(itemResult.get()));
        }
        return objectList;
    }

    /**
     * 获取文件外链
     *
     * @param bucketName bucket名称
     * @param objectName 文件名称
     * @param expires    过期时间 <=7
     * @return url
     */
    @SneakyThrows
    public String getObjectURL(String bucketName, String objectName, Integer expires) {
        return client.getPresignedObjectUrl(
                io.minio.GetPresignedObjectUrlArgs.builder()
                        .method(Method.GET)
                        .bucket(bucketName)
                        .object(objectName)
                        .expiry(expires)
                        .build());
    }

    /**
     * 获取文件
     *
     * @param bucketName bucket名称
     * @param objectName 文件名称
     * @return 二进制流
     */
    @SneakyThrows
    public InputStream getObject(String bucketName, String objectName) {
        return client.getObject(
                GetObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .build());
    }

    /**
     * 上传文件
     *
     * @param bucketName bucket名称
     * @param objectName 文件名称
     * @param stream     文件流
     * @throws Exception
     */
    public FileResult putObject(String bucketName, String objectName, InputStream stream) throws Exception {
        long size = stream.available();
        ByteArrayOutputStream outputStream = FileUtils.readInputStream(stream);
        InputStream stream1 = new ByteArrayInputStream(outputStream.toByteArray());
        InputStream stream2 = new ByteArrayInputStream(outputStream.toByteArray());
        String md5Str = MD5Utils.getFileMD5String(stream1);
        Integer index = objectName.lastIndexOf(".");
        String md5FileName = md5Str + objectName.substring(index);

        if (this.getObjectInfo(bucketName, md5FileName) == null) {
            this.client.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(md5FileName)
                            .stream(stream2, size, -1)
                            .contentType("application/octet-stream")
                            .build());
        }

        return new FileResult(bucketName, objectName, md5FileName, "/minio/" + bucketName + "/" + md5FileName);
    }

    /**
     * 提交文件至minio,名称为 objectName，如果存在则覆盖
     * @param bucketName
     * @param objectName
     * @param stream
     * @return
     * @throws Exception
     */
    @Override
    public FileResult putObjectForce(String bucketName, String objectName, InputStream stream) throws Exception {
        long size = stream.available();
        this.client.putObject(
                PutObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .stream(stream, size, -1)
                        .contentType("application/octet-stream")
                        .build());
        return new FileResult(bucketName, objectName, objectName, "/minio/" + bucketName + "/" + objectName);
    }

    /**
     * 上传文件
     *
     * @param bucketName  bucket名称
     * @param objectName  文件名称
     * @param stream      文件流
     * @param size        大小
     * @param contextType 类型
     * @throws Exception
     */
    public FileResult putObject(String bucketName, String objectName, InputStream stream, long size, String contextType) throws Exception {
        client.putObject(
                PutObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .stream(stream, size, -1)
                        .contentType(contextType)
                        .build());

        return new FileResult(bucketName, objectName, objectName, "/minio/" + bucketName + "/" + objectName);
    }

    /**
     * 获取文件信息
     *
     * @param bucketName bucket名称
     * @param objectName 文件名称
     */
    public StatObjectResponse getObjectInfo(String bucketName, String objectName) {
        StatObjectResponse statObjectResponse = null;
        try {
            statObjectResponse = client.statObject(
                    StatObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build());
        } catch (Exception e) {
            log.error("获取文件信息异常:" + e.getMessage());
        }
        return statObjectResponse;
    }

    /**
     * 删除文件
     *
     * @param bucketName bucket名称
     * @param objectName 文件名称
     * @throws Exception
     */
    public void removeObject(String bucketName, String objectName) throws Exception {
        client.removeObject(
                io.minio.RemoveObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .build());
    }

    @Override
    public String getPresignedObjectUrl(String bucket, String object, Integer expireTime, TimeUnit timeUnit) throws Exception {
        int expireSecond = (int) timeUnit.toSeconds(expireTime);
        return client.getPresignedObjectUrl(
                io.minio.GetPresignedObjectUrlArgs.builder()
                        .method(Method.GET)
                        .bucket(bucket)
                        .object(object)
                        .expiry(expireSecond, TimeUnit.SECONDS)
                        .build());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Assert.hasText(endpoint, "minio.url 为空");
        Assert.hasText(accessKey, "minio.access-key为空");
        Assert.hasText(secretKey, "minio.secret-key为空");
        Assert.hasText(bucketName, "minio.bucket-name 为空，可以设置应用编码为桶名，不要设置中文");
        this.client = MinioClient.builder()
                .endpoint(endpoint)
                .credentials(accessKey, secretKey)
                .build();
        //自动创建bucket
        this.createBucket(bucketName);
    }
}