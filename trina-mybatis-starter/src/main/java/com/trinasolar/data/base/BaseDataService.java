package com.trinasolar.data.base;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 基础业务接口
 * 提供标准CRUD和高级查询功能
 */
public interface BaseDataService<T> extends IService<T> {

    /**
     * 根据示例实体查询单个记录
     *
     * @param entity 实体对象（非空属性为查询条件）
     * @return 匹配的单个实体
     */
    T getByExample(T entity);

    /**
     * 根据查询条件获取实体列表
     *
     * @param query 查询条件
     * @return 实体列表
     */
    List<T> getList(BaseQuery<T> query);

    /**
     * 分页查询
     *
     * @param query 包含分页和查询条件的请求
     * @return 分页查询结果
     */
    IPage<T> getPage(BaseQuery<T> query);

    /**
     * 高级条件查询列表
     *
     * @param queryWrapper 自定义查询条件
     * @return 实体列表
     */
    List<T> listByWrapper(Wrapper<T> queryWrapper);

    /**
     * 高级条件分页查询
     *
     * @param query        分页参数
     * @param queryWrapper 自定义查询条件
     * @return 分页查询结果
     */
    IPage<T> pageByWrapper(BaseQuery<T> query, Wrapper<T> queryWrapper);

    /**
     * 根据条件计数
     *
     * @param query 查询条件
     * @return 记录数
     */
    long count(BaseQuery<T> query);

    /**
     * 根据查询条件获取单个实体
     *
     * @param queryWrapper 查询条件
     * @return 单个实体
     */
    T getOne(Wrapper<T> queryWrapper);

    /**
     * 根据实体对象获取单个实体
     *
     * @param entity 实体对象（非空属性为查询条件）
     * @return 单个实体
     */
    T getOne(T entity);

    /**
     * 根据实体对象获取单个实体，指定是否抛出异常
     *
     * @param entity  实体对象（非空属性为查询条件）
     * @param throwEx 查询到多条记录时是否抛出异常
     * @return 单个实体
     */
    T getOne(T entity, boolean throwEx);

    /**
     * 根据查询条件获取单个实体，指定是否抛出异常
     *
     * @param queryWrapper 查询条件
     * @param throwEx      查询到多条记录时是否抛出异常
     * @return 单个实体
     */
    T getOne(Wrapper<T> queryWrapper, boolean throwEx);

    /**
     * 根据ID集合批量查询
     *
     * @param idList ID集合
     * @return 实体列表
     */
    List<T> listByIds(Collection<? extends Serializable> idList);

    /**
     * 根据字段名和字段值集合查询
     *
     * @param column 字段名
     * @param values 字段值集合
     * @return 匹配的实体列表
     */
    List<T> listByColumnValues(String column, Collection<?> values);

    /**
     * 根据Map条件查询
     *
     * @param columnMap 列名和值的映射
     * @return 实体列表
     */
    List<T> listByMap(Map<String, Object> columnMap);

    /**
     * 将查询结果转换为Map
     *
     * @param queryWrapper 查询条件
     * @param mapper       转换函数
     * @param <K>          Map的键类型
     * @param <V>          Map的值类型
     * @return 转换后的Map
     */
    <K, V> Map<K, V> listToMap(Wrapper<T> queryWrapper, Function<? super T, K> mapper, Function<? super T, V> valueMapper);

    /**
     * 根据过滤条件和高级查询条件获取实体列表
     *
     * @param filters 过滤条件
     * @param query   查询条件
     * @return 实体列表
     */
    List<T> getListWithFilters(Map<String, Object> filters, BaseQuery<T> query);

    /**
     * 根据过滤条件和高级查询条件分页查询
     *
     * @param filters 过滤条件
     * @param query   包含分页和查询条件的请求
     * @return 分页查询结果
     */
    IPage<T> getPageWithFilters(Map<String, Object> filters, BaseQuery<T> query);
}
