package com.trinasolar.elasticsearch.base.annotation;

import java.lang.annotation.*;

/**
 * Elasticsearch索引注解
 * 用于配置实体类对应的Elasticsearch索引信息
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface EsIndex {
    
    /**
     * 索引名称
     * @return 索引名称
     */
    String name();
    
    /**
     * 主分片数
     * @return 主分片数，默认1
     */
    int shards() default 1;
    
    /**
     * 副本数
     * @return 副本数，默认1
     */
    int replicas() default 1;
    
    /**
     * 刷新间隔
     * @return 刷新间隔，默认"1s"
     */
    String refreshInterval() default "1s";
    
    /**
     * 最大结果窗口
     * @return 最大结果窗口，默认10000
     */
    int maxResultWindow() default 10000;
    
    /**
     * 动态映射策略
     * @return 动态映射策略，默认false
     */
    DynamicMapping dynamicMapping() default DynamicMapping.FALSE;
    
    /**
     * 是否自动创建索引
     * @return 是否自动创建索引，默认true
     */
    boolean autoCreate() default true;
    
    /**
     * 索引别名
     * @return 索引别名数组
     */
    String[] aliases() default {};
}