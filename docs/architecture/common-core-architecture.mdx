---
title: Trina Common Core 架构文档
description: Trina Common Core 核心模块的架构设计、技术原理和组件说明
---

# Trina Common Core 架构文档

## 1. 概述

Trina Common Core 是 Trina Common 框架的核心基础模块，提供了企业级应用开发所需的基础工具类、通用实体、异常处理、上下文管理等核心功能。该模块采用分层架构设计，确保代码的可维护性、可扩展性和可重用性。

## 2. 设计原理

### 2.1 核心设计思想

**分层架构**：采用清晰的分层结构，将不同职责的功能分离到不同的包中
- `base`：基础实体类，提供数据模型的基础结构
- `constant`：常量定义，统一管理系统常量
- `context`：上下文管理，提供线程安全的数据共享机制
- `exception`：异常处理，统一的异常定义和处理机制
- `utils`：工具类，提供常用的工具方法
- `config`：配置类，提供自动配置和线程池配置
- `spring`：Spring集成，提供Spring上下文工具

**线程安全**：使用 ThreadLocal 和 TransmittableThreadLocal 确保多线程环境下的数据安全

**可扩展性**：通过接口和抽象类设计，支持功能的扩展和定制

### 2.2 技术选型

| 技术组件 | 版本 | 选择理由 |
|---------|------|----------|
| Hutool | 最新 | 提供丰富的工具类，减少重复代码 |
| Apache Commons Lang3 | 3.11 | 字符串和集合处理的标准化工具 |
| TransmittableThreadLocal | 最新 | 解决线程池环境下ThreadLocal传递问题 |
| FastJSON2 | 最新 | 高性能JSON处理，支持Java 17 |
| MyBatis-Plus | 最新 | 增强MyBatis功能，提供CRUD便利 |
| Swagger Core | 最新 | API文档生成，支持OpenAPI 3.0 |

## 3. 核心组件

### 3.1 基础实体层 (base)

```mermaid
classDiagram
    class BaseEntity {
        +Long id
        +Integer sort
        +String createBy
        +String updateBy
        +LocalDateTime createAt
        +LocalDateTime updateAt
    }
    
    class BaseSysEntity {
        +Integer version
        +Integer status
        +Boolean deleted
        +Boolean reserved
        +String description
    }
    
    class BaseTenantEntity {
        +Long tenantId
    }
    
    class R {
        +int code
        +String msg
        +T data
        +ok()
        +failed()
    }
    
    BaseSysEntity --|> BaseEntity
    BaseTenantEntity --|> BaseSysEntity
```

**BaseEntity**：基础实体类，包含通用字段如ID、排序、创建和更新信息
**BaseSysEntity**：系统实体类，扩展了版本控制、状态管理、逻辑删除等功能
**BaseTenantEntity**：租户实体类，支持多租户架构
**R**：统一响应格式，标准化API返回结构

### 3.2 上下文管理层 (context)

```mermaid
graph TD
    A[TenantContextHolder] --> B[TransmittableThreadLocal]
    C[MapContext] --> D[NamedThreadLocal]
    E[ListContext] --> D
    F[ObjectContext] --> D
    
    B --> G[租户ID管理]
    D --> H[线程安全数据存储]
    
    G --> I[多租户支持]
    H --> J[线程隔离数据]
```

**TenantContextHolder**：租户上下文管理，支持多租户架构
**MapContext**：Map类型线程安全上下文
**ListContext**：List类型线程安全上下文
**ObjectContext**：Object类型线程安全上下文

### 3.3 异常处理层 (exception)

```mermaid
graph TD
    A[BaseException] --> B[BusinessException]
    A --> C[自定义异常]
    
    B --> D[业务异常处理]
    C --> E[模块异常处理]
    
    D --> F[统一异常响应]
    E --> F
```

**BaseException**：基础异常类，支持模块化异常处理
**BusinessException**：业务异常类，处理业务逻辑异常

### 3.4 工具类层 (utils)

```mermaid
graph LR
    A[StringUtils] --> B[字符串处理]
    C[DateUtils] --> D[时间处理]
    E[ConvertUtils] --> F[类型转换]
    G[RandomUtil] --> H[随机数生成]
    I[ClassUtils] --> J[反射工具]
    K[ThreadUtils] --> L[线程工具]
    M[YamlUtil] --> N[配置处理]
```

**StringUtils**：字符串处理工具，扩展Apache Commons Lang3
**DateUtils**：时间处理工具，支持Java 8时间API
**ConvertUtils**：类型转换工具，提供安全的类型转换
**RandomUtil**：随机数生成工具
**ClassUtils**：类操作工具，提供反射相关功能
**ThreadUtils**：线程工具，提供线程池创建和管理
**YamlUtil**：YAML配置处理工具

### 3.5 配置管理层 (config)

```mermaid
graph TD
    A[AutoConfig] --> B[ExceptionRetryAspect]
    C[ThreadPoolConfig] --> D[threadPoolTaskExecutor]
    C --> E[scheduledExecutorService]
    
    B --> F[重试机制]
    D --> G[异步任务执行]
    E --> H[定时任务执行]
```

**AutoConfig**：自动配置类，注册核心组件
**ThreadPoolConfig**：线程池配置，提供异步任务和定时任务支持

## 4. 架构图

### 4.1 整体架构

```mermaid
graph TB
    subgraph "应用层"
        A[Controller]
        B[Service]
    end
    
    subgraph "Trina Common Core"
        C[BaseEntity]
        D[R响应格式]
        E[TenantContextHolder]
        F[BaseException]
        G[StringUtils]
        H[DateUtils]
        I[ThreadPoolConfig]
    end
    
    subgraph "基础设施层"
        J[Spring Boot]
        K[MyBatis-Plus]
        L[Hutool]
    end
    
    A --> C
    A --> D
    B --> E
    B --> F
    B --> G
    B --> H
    C --> J
    D --> J
    E --> J
    F --> J
    G --> L
    H --> L
    I --> J
```

### 4.2 数据流架构

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as 控制器
    participant Service as 服务层
    participant Core as Core模块
    participant DB as 数据库
    
    Client->>Controller: 请求数据
    Controller->>Core: 获取租户信息
    Core-->>Controller: 租户ID
    Controller->>Service: 执行业务逻辑
    Service->>Core: 使用工具类
    Core-->>Service: 处理结果
    Service->>DB: 数据操作
    DB-->>Service: 操作结果
    Service-->>Controller: 业务结果
    Controller->>Core: 构建响应
    Core-->>Controller: R对象
    Controller-->>Client: 统一响应
```

## 5. 核心特性

### 5.1 多租户支持

- **租户隔离**：通过TenantContextHolder实现租户数据隔离
- **动态切换**：支持运行时租户切换
- **线程安全**：使用TransmittableThreadLocal确保线程池环境下的租户传递

### 5.2 统一响应格式

- **标准化**：所有API统一使用R格式返回
- **链式调用**：支持链式方法调用，提高代码可读性
- **类型安全**：泛型支持，确保类型安全

### 5.3 异常处理机制

- **分层异常**：BaseException作为基础，支持业务异常扩展
- **国际化支持**：支持异常消息的国际化
- **模块化**：支持按模块定义异常

### 5.4 线程池管理

- **自动配置**：提供默认的线程池配置
- **可定制**：支持自定义线程池参数
- **监控支持**：集成异常监控和线程状态监控

## 6. 技术实现细节

### 6.1 ThreadLocal 优化

```java
// 使用 TransmittableThreadLocal 解决线程池传递问题
private final ThreadLocal<Long> THREAD_LOCAL_TENANT = new TransmittableThreadLocal<>();
```

### 6.2 实体类设计

```java
// 使用 MyBatis-Plus 注解简化数据库操作
@TableId(value = "id", type = IdType.ASSIGN_ID)
private Long id;

@TableField(value = "create_time", fill = FieldFill.INSERT)
private LocalDateTime createAt;
```

### 6.3 工具类扩展

```java
// 扩展 Apache Commons Lang3，提供更多便利方法
public class StringUtils extends org.apache.commons.lang3.StringUtils {
    // 自定义方法
}
```

## 7. 性能优化

### 7.1 对象池化

- 使用ThreadLocal缓存常用对象
- 避免频繁创建临时对象

### 7.2 懒加载

- 上下文对象采用懒加载策略
- 减少不必要的对象创建

### 7.3 线程池优化

- 合理设置核心线程数和最大线程数
- 使用CallerRunsPolicy避免任务丢失

## 8. 扩展性设计

### 8.1 接口设计

- 提供标准化的接口定义
- 支持自定义实现扩展

### 8.2 配置化

- 支持通过配置文件自定义行为
- 提供默认配置和自定义配置

### 8.3 插件化

- 支持通过SPI机制扩展功能
- 提供标准的扩展点

## 9. 最佳实践

### 9.1 使用建议

1. **优先使用工具类**：避免重复造轮子，优先使用提供的工具类
2. **正确使用上下文**：及时清理ThreadLocal，避免内存泄漏
3. **统一异常处理**：使用BaseException体系，保持异常处理的一致性
4. **合理使用线程池**：根据业务需求选择合适的线程池配置

### 9.2 注意事项

1. **内存管理**：及时清理ThreadLocal，避免内存泄漏
2. **线程安全**：在多线程环境下正确使用上下文对象
3. **性能考虑**：避免在循环中频繁创建工具类对象
4. **版本兼容**：注意依赖版本的兼容性问题

## 10. 总结

Trina Common Core 作为框架的核心基础模块，提供了企业级应用开发所需的基础设施。通过分层架构设计、线程安全保证、统一规范制定，为上层应用提供了稳定可靠的基础支撑。

该模块的设计充分考虑了现代Java应用的特点，结合了Spring Boot生态、MyBatis-Plus增强、Hutool工具库等成熟技术，为企业级应用开发提供了完整的解决方案。