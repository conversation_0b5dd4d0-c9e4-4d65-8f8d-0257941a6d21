1、项目文档要求：所有模块及关键功能代码必须提供完整的文档，文档放在docs目录，文档名称为英文，文档格式为markdown，后缀统一mdx，应当是wiki友好的格式，文档包括atchitecture架构设计文档，将框架如何设计，guides文档讲用户如何使用，tutorials用来做技术介绍。README.md文件首先整个项目有一个用来介绍项目如何安装，其次每个项目简要介绍维护和安装说明，不应该包含太多技术细节，技术细节在docs目录分门别类展开。如果文档过长，应当能够拆分文档。
---
title: 文档标题
description: 简洁
---
2、所有的代码变动必须执行mvn clean install 命令，确保编译通过
3、代码注释要求：所有的代码必须提供注释，包括类、方法、关键变量、判断条件或语句，注释格式为javadoc，项目整体注释率不低于70%
4、项目采用Java17和SpringBoot3.4.0版本构建
5、采用Martin Fowler的重构方法论来拆分任务，维护代码修改，维护代码整洁，持续清理代码
6、对于使用技术，建议先通过deepwiki学习，再使用
7、如果输出内容过长，则采用分页追加写的方式活着拆分文件。编写文档时候，如果文档超过3000行或者30000字符，则需要拆分文件，拆分文件时，应当保证文件结构完整，例如：目录结构、文件名、文件内容等。
8、开发过程中应当使用Todo方式，对于复杂代码或者文档中的架构图，应当先标记todo占位，并下一次执行该任务。
