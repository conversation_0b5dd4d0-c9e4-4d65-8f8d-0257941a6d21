---
title: Trina Common 开发框架
description: 基于 Spring Boot 3.x 和 Spring Cloud 的企业级微服务开发框架，提供丰富的功能组件和开发工具，简化企业应用开发。
---

# Trina Common 开发框架

> 📚 **快速导航**: 如果您是第一次使用本框架，建议先查看下方的模块介绍来了解推荐的学习路径。

## 框架概述

Trina Common 是一个企业级微服务开发框架，基于 Spring Boot 3.x 和 Spring Cloud 构建，采用模块化架构设计，支持按需引入功能组件。框架提供了完整的微服务解决方案，包括服务治理、数据访问、安全认证、消息队列等核心功能。

### 核心特性

- **🏗️ 模块化架构**：高度模块化设计，支持按需引入
- **☁️ 云原生支持**：深度集成 Spring Cloud 生态
- **📋 标准化规范**：统一的编码和接口规范
- **🚀 开箱即用**：丰富的开箱即用组件
- **🏢 企业级特性**：多租户、国际化、安全框架
- **🔧 可扩展性**：良好的扩展点设计

## 技术栈

**核心框架**: Spring Boot 3.5.0, Spring Cloud 2025.0.0  
**服务治理**: Spring Cloud Gateway, Spring Security  
**数据存储**: MySQL 8.3.0, Redis, Elasticsearch 9.0.0, Neo4j  
**对象存储**: MinIO
**部署运维**: Docker, Kubernetes  

## 快速开始

### 环境要求
- JDK 17+
- Maven 3.8+
- Docker (可选)

### 项目引入

```xml
<parent>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-parent</artifactId>
    <version>4.0.0</version>
</parent>
```

详细的快速开始指南请参考 [快速开始教程](./tutorials/README.mdx)。

## 模块导航

### 🏗️ 基础模块

**核心组件和通用工具**

- **[trina-common-core](./guides/common-core-usage-guide.mdx)** - 核心工具类、通用接口、异常处理
- **[trina-common-excel](./guides/README.mdx)** - Excel 导入导出支持
- **[trina-common-sensitive](./guides/README.mdx)** - 敏感信息处理

### 🌐 Web 服务

**Web 应用和 API 服务相关组件**

- **[trina-web-starter](./guides/web-usage-guide.mdx)** - Web 应用支持，控制器、拦截器等
- **[trina-gateway-starter](./guides/gateway-usage-guide.mdx)** - API 网关支持
- **[trina-openapi-starter](./guides/openapi-usage-guide.mdx)** - OpenAPI 文档生成
- **[trina-xss-starter](./guides/xss-guide.mdx)** - XSS 防护
- **[trina-security-starter](./guides/README.mdx)** - 安全框架集成

### 💾 数据访问

**数据库和存储相关组件**

- **[trina-mybatis-starter](./guides/mybatis-usage-guide.mdx)** - MyBatis-Plus 集成增强
- **[trina-datasource-starter](./guides/datasource-usage-guide.mdx)** - 多数据源支持
- **[trina-redis-starter](./guides/redis-usage-guide.mdx)** - Redis 缓存支持
- **[trina-elasticsearch-starter](./guides/elasticsearch-usage-guide.mdx)** - Elasticsearch 集成
- **[trina-neo4j-starter](./guides/README.mdx)** - Neo4j 图数据库集成

### ☁️ 云服务

**云原生和存储服务组件**

- **[trina-minio-starter](./guides/minio-usage-guide.mdx)** - MinIO 对象存储集成
- **[trina-sftp-starter](./guides/sftp-usage-guide.mdx)** - SFTP 文件传输
- **[trina-kubernetes-starter](./guides/README.mdx)** - Kubernetes 集成

### 🔄 分布式服务

**微服务治理和分布式组件**

- **[trina-microservice-starter](./guides/README.mdx)** - 微服务基础框架
- **[trina-remote-starter](./guides/README.mdx)** - 远程调用支持
- **[trina-sequence-starter](./guides/sequence-usage-guide.mdx)** - 分布式 ID 生成

### 🌍 国际化与多租户

**多语言和多租户支持**

- **[trina-i18n-starter](./guides/README.mdx)** - 国际化支持
- **[trina-tenant-starter](./guides/README.mdx)** - 多租户支持

### 🔧 运维与监控

**运维工具和监控组件**

- **[trina-xxl-job-starter](./guides/xxl-job-usage-guide.mdx)** - XXL-Job 任务调度

## 学习路径

推荐的学习路径：

1. **基础入门**: [快速开始](./tutorials/README.mdx) → [核心概念](./guides/common-core-usage-guide.mdx)
2. **功能使用**: [Web 开发](./guides/web-usage-guide.mdx) → [数据访问](./guides/mybatis-usage-guide.mdx)
3. **微服务**: [服务治理](./guides/README.mdx)
4. **高级特性**: [安全认证](./guides/xss-guide.mdx) → [性能优化](./guides/redis-usage-guide.mdx)

## 版本信息

| 版本号 | 发布日期 | 主要特性 | 兼容性 |
|-------|---------|---------|-------|
| 4.0.0 | 2025-06 | Spring Boot 3.5.0 升级 | JDK 17+ |
| 3.0.0 | 2024-01 | 微服务架构优化 | JDK 17+ |
| 2.0.0 | 2023-05 | Spring Boot 3.0 升级 | JDK 17+ |
| 1.0.0 | 2022-09 | 首个稳定版本 | JDK 8+ |