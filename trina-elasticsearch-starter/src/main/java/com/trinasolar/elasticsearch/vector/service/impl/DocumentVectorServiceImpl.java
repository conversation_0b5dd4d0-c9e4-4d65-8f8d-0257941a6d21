package com.trinasolar.elasticsearch.vector.service.impl;

import com.trinasolar.elasticsearch.vector.entity.DocumentVectorEntity;
import com.trinasolar.elasticsearch.vector.service.DocumentVectorService;
import com.trinasolar.elasticsearch.vector.wrapper.VectorQueryWrapper;
import com.trinasolar.elasticsearch.vo.SearchResult;
import com.trinasolar.common.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 文档向量搜索服务实现类
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Service
public class DocumentVectorServiceImpl extends BaseVectorServiceImpl<DocumentVectorEntity> 
        implements DocumentVectorService {
    
    /**
     * 默认向量字段
     */
    private static final String DEFAULT_VECTOR_FIELD = "content_vector";
    
    /**
     * 默认相似度阈值
     */
    private static final double DEFAULT_SIMILARITY_THRESHOLD = 0.8;
    
    /**
     * 默认批处理大小
     */
    private static final int DEFAULT_BATCH_SIZE = 100;
    
    @Override
    public SearchResult<DocumentVectorEntity> semanticSearch(String query, int k) {
        try {
            // TODO: 这里应该调用向量化服务将查询文本转换为向量
            // 目前使用模拟向量进行演示
            float[] queryVector = generateMockQueryVector(query, 768);
            
            return vectorSearch(DEFAULT_VECTOR_FIELD, queryVector, k);
            
        } catch (Exception e) {
            log.error("语义搜索失败: query={}, k={}", query, k, e);
            return SearchResult.empty();
        }
    }
    
    @Override
    public SearchResult<DocumentVectorEntity> semanticSearchWithFilter(String query, int k, 
                                                                       String documentType, 
                                                                       String source, 
                                                                       String[] tags) {
        try {
            // TODO: 调用向量化服务
            float[] queryVector = generateMockQueryVector(query, 768);
            
            // 构建过滤条件
            VectorQueryWrapper<DocumentVectorEntity> filterWrapper = new VectorQueryWrapper<>();
            
            if (documentType != null && !documentType.trim().isEmpty()) {
                filterWrapper.eq("documentType", documentType);
            }
            
            if (source != null && !source.trim().isEmpty()) {
                filterWrapper.eq("source", source);
            }
            
            if (tags != null && tags.length > 0) {
                filterWrapper.in("tags", Arrays.asList(tags));
            }
            
            return vectorSearchWithFilter(DEFAULT_VECTOR_FIELD, queryVector, k, filterWrapper);
            
        } catch (Exception e) {
            log.error("带过滤条件的语义搜索失败: query={}, k={}", query, k, e);
            return SearchResult.empty();
        }
    }
    
    @Override
    public SearchResult<DocumentVectorEntity> hybridSearch(String query, int k, 
                                                          float textWeight, float vectorWeight) {
        try {
            // TODO: 调用向量化服务
            float[] queryVector = generateMockQueryVector(query, 768);
            
            return hybridSearch(query, DEFAULT_VECTOR_FIELD, queryVector, k, textWeight, vectorWeight);
            
        } catch (Exception e) {
            log.error("混合搜索失败: query={}, k={}", query, k, e);
            return SearchResult.empty();
        }
    }
    
    @Override
    public SearchResult<DocumentVectorEntity> multiFieldVectorSearch(String query, int k, 
                                                                    Map<String, Float> fieldWeights) {
        try {
            VectorQueryWrapper<DocumentVectorEntity> wrapper = new VectorQueryWrapper<>();
            
            // 为每个字段添加向量搜索
            for (Map.Entry<String, Float> entry : fieldWeights.entrySet()) {
                String fieldName = entry.getKey();
                Float weight = entry.getValue();
                
                // 根据字段获取对应维度的查询向量
                float[] queryVector = generateQueryVectorForField(query, fieldName);
                
                wrapper.vectorSearch(fieldName, queryVector, k, 
                                      Math.max(k * 10, 100), weight);
            }
            
            return vectorSearch(wrapper);
            
        } catch (Exception e) {
            log.error("多字段向量搜索失败: query={}, k={}", query, k, e);
            return SearchResult.empty();
        }
    }
    
    @Override
    public SearchResult<DocumentVectorEntity> findSimilarDocuments(String documentId, int k, String vectorField) {
        try {
            // 获取文档的向量
            float[] documentVector = getVector(documentId, vectorField);
            if (documentVector == null) {
                log.warn("文档向量不存在: documentId={}, vectorField={}", documentId, vectorField);
                return SearchResult.empty();
            }
            
            // 排除自身
            VectorQueryWrapper<DocumentVectorEntity> filterWrapper = new VectorQueryWrapper<>();
            filterWrapper.ne("id", documentId);
            
            return vectorSearchWithFilter(vectorField, documentVector, k, filterWrapper);
            
        } catch (Exception e) {
            log.error("查找相似文档失败: documentId={}, k={}, vectorField={}", documentId, k, vectorField, e);
            return SearchResult.empty();
        }
    }
    
    @Override
    public Map<String, SearchResult<DocumentVectorEntity>> batchFindSimilarDocuments(List<String> documentIds, 
                                                                                    int k, String vectorField) {
        Map<String, SearchResult<DocumentVectorEntity>> results = new HashMap<>();
        
        for (String documentId : documentIds) {
            SearchResult<DocumentVectorEntity> similarDocs = findSimilarDocuments(documentId, k, vectorField);
            results.put(documentId, similarDocs);
        }
        
        return results;
    }
    
    @Override
    public Map<Integer, List<DocumentVectorEntity>> clusterDocuments(String vectorField, 
                                                                   int clusterCount, 
                                                                   int maxIterations) {
        try {
            // TODO: 实现K-means聚类算法
            log.info("开始文档聚类: vectorField={}, clusterCount={}, maxIterations={}", 
                    vectorField, clusterCount, maxIterations);
            
            // 获取所有文档的向量
            List<DocumentVectorEntity> allDocuments = getAllDocuments();
            
            // 实现聚类逻辑
            Map<Integer, List<DocumentVectorEntity>> clusters = performKMeansClustering(
                allDocuments, vectorField, clusterCount, maxIterations);
            
            log.info("文档聚类完成: 聚类数={}, 文档总数={}", clusters.size(), allDocuments.size());
            return clusters;
            
        } catch (Exception e) {
            log.error("文档聚类失败: vectorField={}, clusterCount={}", vectorField, clusterCount, e);
            throw new BusinessException("文档聚类失败", e);
        }
    }
    
    @Override
    public List<List<DocumentVectorEntity>> findDuplicateDocuments(String vectorField, 
                                                                  double similarityThreshold) {
        try {
            log.info("开始查找重复文档: vectorField={}, threshold={}", vectorField, similarityThreshold);
            
            List<DocumentVectorEntity> allDocuments = getAllDocuments();
            List<List<DocumentVectorEntity>> duplicateGroups = new ArrayList<>();
            Set<String> processedIds = new HashSet<>();
            
            for (DocumentVectorEntity doc : allDocuments) {
                if (processedIds.contains(doc.getId())) {
                    continue;
                }
                
                List<DocumentVectorEntity> duplicates = findSimilarDocumentsAboveThreshold(
                    doc, allDocuments, vectorField, similarityThreshold);
                
                if (duplicates.size() > 1) {
                    duplicateGroups.add(duplicates);
                    duplicates.forEach(d -> processedIds.add(d.getId()));
                }
            }
            
            log.info("重复文档查找完成: 发现{}组重复文档", duplicateGroups.size());
            return duplicateGroups;
            
        } catch (Exception e) {
            log.error("查找重复文档失败: vectorField={}", vectorField, e);
            throw new BusinessException("查找重复文档失败", e);
        }
    }
    
    @Override
    public boolean vectorizeDocument(String documentId, String modelVersion) {
        try {
            DocumentVectorEntity document = getById(documentId);
            if (document == null) {
                log.warn("文档不存在: documentId={}", documentId);
                return false;
            }
            
            // TODO: 调用向量化服务生成向量
            // 这里使用模拟向量
            document.setTitleVector(generateMockVector(document.getTitle(), 768));
            document.setContentVector(generateMockVector(document.getContent(), 768));
            document.setSummaryVector(generateMockVector(document.getSummary(), 384));
            document.setMultimodalVector(generateMockVector(document.getFullText(), 512));
            
            document.markAsVectorized(modelVersion);
            
            // 更新文档
            updateById(document);
            
            log.info("文档向量化成功: documentId={}, modelVersion={}", documentId, modelVersion);
            return true;
            
        } catch (Exception e) {
            log.error("文档向量化失败: documentId={}, modelVersion={}", documentId, modelVersion, e);
            return false;
        }
    }
    
    @Override
    public int batchVectorizeDocuments(List<String> documentIds, String modelVersion) {
        int successCount = 0;
        
        for (String documentId : documentIds) {
            try {
                if (vectorizeDocument(documentId, modelVersion)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量向量化中单个文档失败: documentId={}", documentId, e);
            }
        }
        
        log.info("批量向量化完成: 成功={}, 总数={}", successCount, documentIds.size());
        return successCount;
    }
    
    @Override
    public long reVectorizeAllDocuments(String modelVersion, int batchSize) {
        try {
            long totalProcessed = 0;
            int offset = 0;
            
            while (true) {
                // 分批获取文档
                List<DocumentVectorEntity> batch = getDocumentsBatch(offset, batchSize);
                if (batch.isEmpty()) {
                    break;
                }
                
                // 批量向量化
                List<String> documentIds = batch.stream()
                    .map(DocumentVectorEntity::getId)
                    .collect(Collectors.toList());
                
                int processed = batchVectorizeDocuments(documentIds, modelVersion);
                totalProcessed += processed;
                
                offset += batchSize;
                
                log.info("重新向量化进度: 已处理={}, 当前批次={}", totalProcessed, processed);
            }
            
            log.info("重新向量化完成: 总处理数={}", totalProcessed);
            return totalProcessed;
            
        } catch (Exception e) {
            log.error("重新向量化失败: modelVersion={}", modelVersion, e);
            throw new BusinessException("重新向量化失败", e);
        }
    }
    
    @Override
    public List<DocumentVectorEntity> getUnvectorizedDocuments(int limit) {
        try {
            VectorQueryWrapper<DocumentVectorEntity> wrapper = new VectorQueryWrapper<>();
            wrapper.eq("vectorized", false);
            wrapper.size(limit);
            
            SearchResult<DocumentVectorEntity> result = search(wrapper);
            return result.getRecords();
            
        } catch (Exception e) {
            log.error("获取未向量化文档失败: limit={}", limit, e);
            throw new BusinessException("获取未向量化文档失败", e);
        }
    }
    
    @Override
    public Map<String, Object> getVectorizationStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 总文档数
            long totalDocs = count();
            
            // 已向量化文档数
            VectorQueryWrapper<DocumentVectorEntity> vectorizedWrapper = new VectorQueryWrapper<>();
            vectorizedWrapper.eq("vectorized", true);
            long vectorizedDocs = count(vectorizedWrapper);
            
            // 未向量化文档数
            long unvectorizedDocs = totalDocs - vectorizedDocs;
            
            // 向量化率
            double vectorizationRate = totalDocs > 0 ? (double) vectorizedDocs / totalDocs * 100 : 0;
            
            stats.put("total_documents", totalDocs);
            stats.put("vectorized_documents", vectorizedDocs);
            stats.put("unvectorized_documents", unvectorizedDocs);
            stats.put("vectorization_rate", vectorizationRate);
            
            // 各向量字段统计
            Map<String, Long> fieldStats = new HashMap<>();
            String[] vectorFields = {"title_vector", "content_vector", "summary_vector", "multimodal_vector"};
            
            for (String field : vectorFields) {
                VectorQueryWrapper<DocumentVectorEntity> fieldWrapper = new VectorQueryWrapper<>();
                fieldWrapper.isNotNull(field);
                long fieldCount = count(fieldWrapper);
                fieldStats.put(field, fieldCount);
            }
            
            stats.put("vector_field_stats", fieldStats);
            
        } catch (Exception e) {
            log.error("获取向量化统计信息失败", e);
            throw new BusinessException("获取向量化统计信息失败", e);
        }
        
        return stats;
    }
    
    @Override
    public Map<String, Double> evaluateDocumentQuality(String documentId) {
        Map<String, Double> quality = new HashMap<>();
        
        try {
            DocumentVectorEntity document = getById(documentId);
            if (document == null) {
                throw new BusinessException("文档不存在: " + documentId);
            }
            
            // 向量质量评估
            quality.put("vector_completeness", calculateVectorCompleteness(document));
            quality.put("vector_norm_consistency", calculateVectorNormConsistency(document));
            quality.put("content_length_score", calculateContentLengthScore(document));
            quality.put("metadata_completeness", calculateMetadataCompleteness(document));
            
            // 综合质量评分
            double overallQuality = quality.values().stream()
                .mapToDouble(Double::doubleValue)
                .average()
                .orElse(0.0);
            quality.put("overall_quality", overallQuality);
            
        } catch (Exception e) {
            log.error("文档质量评估失败: documentId={}", documentId, e);
            throw new BusinessException("文档质量评估失败", e);
        }
        
        return quality;
    }
    
    @Override
    public Map<String, Map<String, Double>> batchEvaluateDocumentQuality(List<String> documentIds) {
        Map<String, Map<String, Double>> results = new HashMap<>();
        
        for (String documentId : documentIds) {
            try {
                Map<String, Double> quality = evaluateDocumentQuality(documentId);
                results.put(documentId, quality);
            } catch (Exception e) {
                log.error("批量质量评估中单个文档失败: documentId={}", documentId, e);
                results.put(documentId, new HashMap<>());
            }
        }
        
        return results;
    }
    
    @Override
    public Map<String, Object> analyzeDocumentTopics(String vectorField, int topicCount) {
        Map<String, Object> analysis = new HashMap<>();
        
        try {
            // TODO: 实现主题分析逻辑
            // 1. 获取所有文档向量
            // 2. 使用聚类算法进行主题聚类
            // 3. 分析每个主题的特征词
            
            analysis.put("vector_field", vectorField);
            analysis.put("topic_count", topicCount);
            analysis.put("topics", new ArrayList<>());
            analysis.put("document_topic_mapping", new HashMap<>());
            
            log.info("文档主题分析完成: vectorField={}, topicCount={}", vectorField, topicCount);
            
        } catch (Exception e) {
            log.error("文档主题分析失败: vectorField={}", vectorField, e);
            throw new BusinessException("文档主题分析失败", e);
        }
        
        return analysis;
    }
    
    @Override
    public double[][] calculateSimilarityMatrix(List<String> documentIds, String vectorField) {
        try {
            int size = documentIds.size();
            double[][] matrix = new double[size][size];
            
            // 获取所有文档的向量
            Map<String, float[]> vectors = new HashMap<>();
            for (String documentId : documentIds) {
                float[] vector = getVector(documentId, vectorField);
                if (vector != null) {
                    vectors.put(documentId, vector);
                }
            }
            
            // 计算相似度矩阵
            for (int i = 0; i < size; i++) {
                for (int j = 0; j < size; j++) {
                    if (i == j) {
                        matrix[i][j] = 1.0;
                    } else {
                        String docId1 = documentIds.get(i);
                        String docId2 = documentIds.get(j);
                        
                        float[] vector1 = vectors.get(docId1);
                        float[] vector2 = vectors.get(docId2);
                        
                        if (vector1 != null && vector2 != null) {
                            matrix[i][j] = calculateSimilarity(vector1, vector2, "cosine");
                        } else {
                            matrix[i][j] = 0.0;
                        }
                    }
                }
            }
            
            return matrix;
            
        } catch (Exception e) {
            log.error("计算相似度矩阵失败: vectorField={}", vectorField, e);
            throw new BusinessException("计算相似度矩阵失败", e);
        }
    }
    
    @Override
    public Map<String, Object> generateVisualizationData(String vectorField, 
                                                        String algorithm, 
                                                        int dimensions, 
                                                        int sampleSize) {
        Map<String, Object> visualization = new HashMap<>();
        
        try {
            // TODO: 实现降维可视化
            // 1. 采样文档
            // 2. 获取向量
            // 3. 应用降维算法
            // 4. 生成可视化数据
            
            visualization.put("vector_field", vectorField);
            visualization.put("algorithm", algorithm);
            visualization.put("dimensions", dimensions);
            visualization.put("sample_size", sampleSize);
            visualization.put("points", new ArrayList<>());
            visualization.put("labels", new ArrayList<>());
            
            log.info("向量可视化数据生成完成: algorithm={}, dimensions={}", algorithm, dimensions);
            
        } catch (Exception e) {
            log.error("生成可视化数据失败: vectorField={}", vectorField, e);
            throw new BusinessException("生成可视化数据失败", e);
        }
        
        return visualization;
    }
    
    @Override
    public Map<String, Object> getSearchOptimizationSuggestions() {
        Map<String, Object> suggestions = new HashMap<>();
        
        try {
            // 分析索引状态
            Map<String, Object> indexStats = getVectorIndexStats();
            
            List<String> optimizations = new ArrayList<>();
            
            // 检查文档数量
            Long docCount = (Long) indexStats.get("document_count");
            if (docCount != null && docCount > 100000) {
                optimizations.add("考虑增加分片数量以提高搜索性能");
            }
            
            // 检查向量化率
            Map<String, Object> vectorStats = getVectorizationStats();
            Double vectorizationRate = (Double) vectorStats.get("vectorization_rate");
            if (vectorizationRate != null && vectorizationRate < 80) {
                optimizations.add("向量化率较低，建议完成剩余文档的向量化");
            }
            
            suggestions.put("optimizations", optimizations);
            suggestions.put("index_stats", indexStats);
            suggestions.put("vector_stats", vectorStats);
            
        } catch (Exception e) {
            log.error("获取搜索优化建议失败", e);
            throw new BusinessException("获取搜索优化建议失败", e);
        }
        
        return suggestions;
    }
    
    @Override
    public Map<String, Object> checkVectorIndexHealth() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            // 检查索引状态
            health.put("index_exists", indexExists());
            health.put("index_stats", getVectorIndexStats());
            health.put("vectorization_stats", getVectorizationStats());
            
            // 健康评分
            double healthScore = calculateIndexHealthScore(health);
            health.put("health_score", healthScore);
            
            String healthStatus = healthScore >= 0.8 ? "HEALTHY" : 
                                healthScore >= 0.6 ? "WARNING" : "CRITICAL";
            health.put("health_status", healthStatus);
            
        } catch (Exception e) {
            log.error("向量索引健康检查失败", e);
            throw new BusinessException("向量索引健康检查失败", e);
        }
        
        return health;
    }
    
    @Override
    public long cleanExpiredVectorCache(int expiredHours) {
        try {
            // TODO: 实现缓存清理逻辑
            log.info("清理过期向量缓存: expiredHours={}", expiredHours);
            
            // 清理逻辑
            clearVectorSearchCache("*");
            
            return 0; // 返回清理的缓存数量
            
        } catch (Exception e) {
            log.error("清理过期向量缓存失败: expiredHours={}", expiredHours, e);
            return 0;
        }
    }
    
    @Override
    public int warmupVectorSearchCache(List<String> commonQueries) {
        int warmedUp = 0;
        
        for (String query : commonQueries) {
            try {
                // 执行搜索以预热缓存
                semanticSearch(query, 10);
                warmedUp++;
            } catch (Exception e) {
                log.error("预热缓存失败: query={}", query, e);
            }
        }
        
        log.info("向量搜索缓存预热完成: 成功={}, 总数={}", warmedUp, commonQueries.size());
        return warmedUp;
    }
    
    // ==================== 私有辅助方法 ====================
    
    /**
     * 生成模拟查询向量
     */
    private float[] generateMockQueryVector(String query, int dimension) {
        // TODO: 这里应该调用真实的向量化服务
        // 目前使用查询文本的哈希值生成模拟向量
        Random random = new Random(query.hashCode());
        float[] vector = new float[dimension];
        for (int i = 0; i < dimension; i++) {
            vector[i] = (float)(random.nextGaussian() * 0.1);
        }
        return normalizeVector(vector);
    }
    
    /**
     * 根据字段生成查询向量
     */
    private float[] generateQueryVectorForField(String query, String fieldName) {
        int dimension = getDimensionForField(fieldName);
        return generateMockQueryVector(query, dimension);
    }
    
    /**
     * 获取字段对应的向量维度
     */
    private int getDimensionForField(String fieldName) {
        switch (fieldName) {
            case "title_vector":
            case "content_vector":
                return 768;
            case "summary_vector":
                return 384;
            case "multimodal_vector":
                return 512;
            default:
                return 768;
        }
    }
    
    /**
     * 生成模拟向量
     */
    private float[] generateMockVector(String text, int dimension) {
        if (text == null || text.trim().isEmpty()) {
            return null;
        }
        
        Random random = new Random(text.hashCode());
        float[] vector = new float[dimension];
        for (int i = 0; i < dimension; i++) {
            vector[i] = (float)(random.nextGaussian() * 0.1);
        }
        return normalizeVector(vector);
    }
    
    /**
     * 向量归一化
     */
    private float[] normalizeVector(float[] vector) {
        double norm = 0.0;
        for (float v : vector) {
            norm += v * v;
        }
        norm = Math.sqrt(norm);
        
        if (norm > 0) {
            for (int i = 0; i < vector.length; i++) {
                vector[i] = (float) (vector[i] / norm);
            }
        }
        
        return vector;
    }
    
    /**
     * 获取所有文档
     */
    private List<DocumentVectorEntity> getAllDocuments() {
        VectorQueryWrapper<DocumentVectorEntity> wrapper = new VectorQueryWrapper<>();
        wrapper.size(10000); // 限制数量避免内存问题
        SearchResult<DocumentVectorEntity> result = search(wrapper);
        return result.getRecords();
    }
    
    /**
     * 分批获取文档
     */
    private List<DocumentVectorEntity> getDocumentsBatch(int offset, int batchSize) {
        VectorQueryWrapper<DocumentVectorEntity> wrapper = new VectorQueryWrapper<>();
        wrapper.size(batchSize);
        // TODO: 实现分页逻辑
        SearchResult<DocumentVectorEntity> result = search(wrapper);
        return result.getRecords();
    }
    
    /**
     * 执行K-means聚类
     */
    private Map<Integer, List<DocumentVectorEntity>> performKMeansClustering(
            List<DocumentVectorEntity> documents, String vectorField, 
            int clusterCount, int maxIterations) {
        // TODO: 实现K-means聚类算法
        Map<Integer, List<DocumentVectorEntity>> clusters = new HashMap<>();
        
        // 简单的随机分配作为示例
        Random random = new Random();
        for (DocumentVectorEntity doc : documents) {
            int clusterId = random.nextInt(clusterCount);
            clusters.computeIfAbsent(clusterId, k -> new ArrayList<>()).add(doc);
        }
        
        return clusters;
    }
    
    /**
     * 查找相似度超过阈值的文档
     */
    private List<DocumentVectorEntity> findSimilarDocumentsAboveThreshold(
            DocumentVectorEntity targetDoc, List<DocumentVectorEntity> allDocuments, 
            String vectorField, double threshold) {
        List<DocumentVectorEntity> similar = new ArrayList<>();
        similar.add(targetDoc); // 包含自身
        
        float[] targetVector = targetDoc.getVectorByFieldName(vectorField);
        if (targetVector == null) {
            return similar;
        }
        
        for (DocumentVectorEntity doc : allDocuments) {
            if (doc.getId().equals(targetDoc.getId())) {
                continue;
            }
            
            float[] docVector = doc.getVectorByFieldName(vectorField);
            if (docVector != null) {
                double similarity = calculateSimilarity(targetVector, docVector, "cosine");
                if (similarity >= threshold) {
                    similar.add(doc);
                }
            }
        }
        
        return similar;
    }
    
    /**
     * 计算向量完整性
     */
    private double calculateVectorCompleteness(DocumentVectorEntity document) {
        int totalFields = 4; // 总向量字段数
        int completedFields = 0;
        
        if (document.getTitleVector() != null) completedFields++;
        if (document.getContentVector() != null) completedFields++;
        if (document.getSummaryVector() != null) completedFields++;
        if (document.getMultimodalVector() != null) completedFields++;
        
        return (double) completedFields / totalFields;
    }
    
    /**
     * 计算向量范数一致性
     */
    private double calculateVectorNormConsistency(DocumentVectorEntity document) {
        List<Double> norms = new ArrayList<>();
        
        if (document.getContentVector() != null) {
            norms.add(calculateVectorNorm(document.getContentVector()));
        }
        if (document.getTitleVector() != null) {
            norms.add(calculateVectorNorm(document.getTitleVector()));
        }
        
        if (norms.size() < 2) {
            return 1.0;
        }
        
        // 计算方差
        double mean = norms.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double variance = norms.stream()
            .mapToDouble(norm -> Math.pow(norm - mean, 2))
            .average().orElse(0.0);
        
        // 一致性评分（方差越小，一致性越高）
        return Math.max(0.0, 1.0 - variance);
    }
    
    /**
     * 计算向量范数
     */
    private double calculateVectorNorm(float[] vector) {
        double sum = 0.0;
        for (float v : vector) {
            sum += v * v;
        }
        return Math.sqrt(sum);
    }
    
    /**
     * 计算内容长度评分
     */
    private double calculateContentLengthScore(DocumentVectorEntity document) {
        String fullText = document.getFullText();
        if (fullText == null || fullText.trim().isEmpty()) {
            return 0.0;
        }
        
        int length = fullText.length();
        // 理想长度范围：500-5000字符
        if (length >= 500 && length <= 5000) {
            return 1.0;
        } else if (length < 500) {
            return (double) length / 500;
        } else {
            return Math.max(0.1, 1.0 - (length - 5000) / 10000.0);
        }
    }
    
    /**
     * 计算元数据完整性
     */
    private double calculateMetadataCompleteness(DocumentVectorEntity document) {
        int totalFields = 5; // 总元数据字段数
        int completedFields = 0;
        
        if (document.getTitle() != null && !document.getTitle().trim().isEmpty()) completedFields++;
        if (document.getDocumentType() != null && !document.getDocumentType().trim().isEmpty()) completedFields++;
        if (document.getSource() != null && !document.getSource().trim().isEmpty()) completedFields++;
        if (document.getTags() != null && document.getTags().length > 0) completedFields++;
        if (document.getLanguage() != null && !document.getLanguage().trim().isEmpty()) completedFields++;
        
        return (double) completedFields / totalFields;
    }
    
    /**
     * 计算索引健康评分
     */
    private double calculateIndexHealthScore(Map<String, Object> healthData) {
        double score = 0.0;
        
        // 索引存在性（20%）
        Boolean indexExists = (Boolean) healthData.get("index_exists");
        if (indexExists != null && indexExists) {
            score += 0.2;
        }
        
        // 向量化率（40%）
        Map<String, Object> vectorStats = (Map<String, Object>) healthData.get("vectorization_stats");
        if (vectorStats != null) {
            Double vectorizationRate = (Double) vectorStats.get("vectorization_rate");
            if (vectorizationRate != null) {
                score += 0.4 * (vectorizationRate / 100.0);
            }
        }
        
        // 文档数量（20%）
        Map<String, Object> indexStats = (Map<String, Object>) healthData.get("index_stats");
        if (indexStats != null) {
            Long docCount = (Long) indexStats.get("document_count");
            if (docCount != null && docCount > 0) {
                score += 0.2;
            }
        }
        
        // 基础功能可用性（20%）
        score += 0.2; // 假设基础功能正常

        return Math.min(1.0, score);
    }

    // ==================== 新增的向量搜索方法 ====================

    @Override
    public DocumentVectorEntity saveWithVector(DocumentVectorEntity entity, String titleField, String contentField) {
        try {
            log.info("保存文档并生成向量: {}", entity.getTitle());

            // TODO: 生成向量的逻辑
            log.warn("向量生成功能正在开发中");

            return save(entity);
        } catch (Exception e) {
            log.error("保存文档失败", e);
            throw new RuntimeException("保存文档失败", e);
        }
    }

    @Override
    public List<DocumentVectorEntity> saveBatchWithVector(List<DocumentVectorEntity> entities, String titleField, String contentField) {
        try {
            log.info("批量保存文档并生成向量，数量: {}", entities.size());

            // TODO: 批量生成向量的逻辑
            log.warn("批量向量生成功能正在开发中");

            saveBatch(entities);
            return entities;
        } catch (Exception e) {
            log.error("批量保存文档失败", e);
            throw new RuntimeException("批量保存文档失败", e);
        }
    }

    @Override
    public List<DocumentVectorEntity> vectorSearch(String queryText, String vectorField, float threshold, int size) {
        try {
            log.info("执行向量搜索: {}", queryText);
            log.warn("向量搜索功能正在开发中");
            return new java.util.ArrayList<>();
        } catch (Exception e) {
            log.error("向量搜索失败", e);
            throw new RuntimeException("向量搜索失败", e);
        }
    }

    @Override
    public List<DocumentVectorEntity> hybridSearch(String keyword, String[] keywordFields, String vectorField,
                                                  float keywordWeight, float vectorWeight, float threshold, int size) {
        try {
            log.info("执行混合搜索: {}", keyword);
            log.warn("混合搜索功能正在开发中");
            return new java.util.ArrayList<>();
        } catch (Exception e) {
            log.error("混合搜索失败", e);
            throw new RuntimeException("混合搜索失败", e);
        }
    }

    @Override
    public List<DocumentVectorEntity> findSimilarEntities(String entityId, String vectorField, float threshold, int size) {
        try {
            log.info("查找相似实体: {}", entityId);
            log.warn("相似实体查找功能正在开发中");
            return new java.util.ArrayList<>();
        } catch (Exception e) {
            log.error("查找相似实体失败", e);
            throw new RuntimeException("查找相似实体失败", e);
        }
    }
}