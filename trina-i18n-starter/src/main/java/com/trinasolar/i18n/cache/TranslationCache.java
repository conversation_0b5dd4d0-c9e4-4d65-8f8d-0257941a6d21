package com.trinasolar.i18n.cache;

import java.util.Map;
import java.util.Optional;

/**
 * 翻译缓存接口
 *
 * <AUTHOR>
 */
public interface TranslationCache {

    /**
     * 获取翻译
     *
     * @param key 翻译键
     * @param language 语言
     * @return 翻译结果，不存在则返回空
     */
    Optional<String> get(String key, String language);

    /**
     * 保存翻译
     *
     * @param key 翻译键
     * @param language 语言
     * @param value 翻译值
     */
    void put(String key, String language, String value);

    /**
     * 批量保存翻译
     *
     * @param translations 翻译映射
     * @param language 语言
     */
    void putAll(Map<String, String> translations, String language);

    /**
     * 清除指定语言的所有翻译缓存
     *
     * @param language 语言
     */
    void invalidate(String language);

    /**
     * 清除所有缓存
     */
    void invalidateAll();
} 