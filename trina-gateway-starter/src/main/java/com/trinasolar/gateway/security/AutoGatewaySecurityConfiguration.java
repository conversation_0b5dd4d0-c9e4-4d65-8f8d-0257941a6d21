package com.trinasolar.gateway.security;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.PackageVersion;
import com.fasterxml.jackson.datatype.jsr310.deser.*;
import com.fasterxml.jackson.datatype.jsr310.ser.*;
import com.github.benmanes.caffeine.cache.Cache;
import com.trinasolar.common.security.properties.IamCaffeineCacheProperties;
import com.trinasolar.common.security.properties.SecurityProperties;
import com.trinasolar.gateway.security.config.ApiPathInit;
import com.trinasolar.gateway.security.exception.GatewayGlobalExceptionHandler;
import com.trinasolar.gateway.security.exception.GatewayErrorHandler;
import com.trinasolar.gateway.security.filter.GatewaySecurityFilter;
import com.trinasolar.gateway.security.filter.GatewayWebSecurityFilter;
import com.trinasolar.gateway.security.properties.WebClientProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

import java.io.Serial;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.TimeZone;

/**
 * 网关安全自动配置
 * <p>
 * 配置 WebFlux 环境下的安全组件，包括 WebClient、缓存、过滤器等
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.REACTIVE)
@EnableConfigurationProperties({SecurityProperties.class, IamCaffeineCacheProperties.class, WebClientProperties.class})
public class AutoGatewaySecurityConfiguration {

    /**
     * 时区
     */
    private static final TimeZone TIME_ZONE = TimeZone.getTimeZone("GMT+8");

    private final WebClientProperties webClientProperties;

    /**
     * 日期时间格式
     */
    public static final String NORM_DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String NORM_DATE_PATTERN = "yyyy-MM-dd";
    public static final String NORM_TIME_PATTERN = "HH:mm:ss";

    /**
     * 日期时间格式化器
     */
    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(NORM_DATETIME_PATTERN);
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(NORM_DATE_PATTERN);
    public static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern(NORM_TIME_PATTERN);

    /**
     * 配置全局ObjectMapper
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        return Jackson2ObjectMapperBuilder.json()
                .timeZone(TIME_ZONE)
                .modules(new CustomJavaTimeModule())
                .featuresToDisable(
                        // 禁用将日期写为时间戳
                        SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
                        SerializationFeature.WRITE_DURATIONS_AS_TIMESTAMPS,
                        // 禁用遇到未知属性时抛出异常
                        DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,
                        SerializationFeature.FAIL_ON_EMPTY_BEANS
                )
                .featuresToEnable(
                        // 启用接受空字符串作为null
                        DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT,
                        // 添加友好的缩进格式
                        SerializationFeature.INDENT_OUTPUT
                )
                // 只序列化非null值
                .serializationInclusion(JsonInclude.Include.NON_NULL)
                .simpleDateFormat(NORM_DATETIME_PATTERN)
                .serializerByType(Long.class, ToStringSerializer.instance)
                .serializerByType(Long.TYPE, ToStringSerializer.instance)
                .findModulesViaServiceLoader(true)
                .build();
    }

    /**
     * 配置 WebClient 用于响应式 HTTP 调用
     * <p>
     * 配置连接池、超时、重试等参数，优化网络调用性能并避免 PrematureCloseException
     */
    @Bean
    public WebClient webClient() {
        WebClientProperties.ConnectionPool poolConfig = webClientProperties.getConnectionPool();
        WebClientProperties.Timeout timeoutConfig = webClientProperties.getTimeout();

        // 配置连接池，避免连接过早关闭
        ConnectionProvider connectionProvider = ConnectionProvider.builder(poolConfig.getName())
                .maxConnections(poolConfig.getMaxConnections())
                .maxIdleTime(poolConfig.getMaxIdleTime())
                .maxLifeTime(poolConfig.getMaxLifeTime())
                .pendingAcquireTimeout(poolConfig.getPendingAcquireTimeout())
                .evictInBackground(poolConfig.getEvictInBackground())
                .build();

        HttpClient httpClient = HttpClient.create(connectionProvider)
                .responseTimeout(timeoutConfig.getResponseTimeout())
                .option(io.netty.channel.ChannelOption.CONNECT_TIMEOUT_MILLIS, timeoutConfig.getConnectTimeout())
                .option(io.netty.channel.ChannelOption.SO_KEEPALIVE, true)           // 启用 TCP Keep-Alive
                .option(io.netty.channel.ChannelOption.TCP_NODELAY, true)            // 禁用 Nagle 算法
                .keepAlive(true)                                // 启用 HTTP Keep-Alive
                .wiretap(webClientProperties.isDebugEnabled()); // 根据配置启用网络日志

        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .codecs(configurer -> {
                    configurer.defaultCodecs().maxInMemorySize(2 * 1024 * 1024); // 2MB
                    configurer.defaultCodecs().enableLoggingRequestDetails(webClientProperties.isDebugEnabled());
                })
                .build();
    }

    @Bean
    public ApiPathInit getApiPathInit(SecurityProperties securityProperties,
                                      WebClient webClient,
                                      Cache<String, Object> iamCache) {
        return new ApiPathInit(securityProperties, webClient, iamCache);
    }

    /**
     * 配置 WebFilter 用于拦截所有 WebFlux 请求
     * <p>
     * 包括网关本身的 RestController 接口，确保完整的安全覆盖
     */
    @Bean
    public GatewaySecurityFilter gatewaySecurityFilter(ObjectMapper objectMapper,
                                                       SecurityProperties securityProperties,
                                                       WebClient webClient,
                                                       Cache<String, Object> iamCache) {
        log.info("Configuring GatewaySecurityFilter for complete security coverage");
        return new GatewaySecurityFilter(objectMapper, securityProperties, webClient, iamCache);
    }

    /**
     * 配置 WebFilter 用于拦截所有 WebFlux 请求
     * <p>
     * 包括网关本身的 RestController 接口，确保完整的安全覆盖
     */
    @Bean
    public GatewayWebSecurityFilter gatewayWebSecurityFilter(ObjectMapper objectMapper,
                                                             SecurityProperties securityProperties,
                                                             WebClient webClient,
                                                             Cache<String, Object> iamCache) {
        log.info("Configuring GatewayWebSecurityFilter for complete security coverage");
        return new GatewayWebSecurityFilter(objectMapper, securityProperties, webClient, iamCache);
    }

    /**
     * 配置全局异常处理器
     * <p>
     * 统一处理网关中的所有异常，确保返回统一的 R 结构
     */
    @Bean
    public GatewayGlobalExceptionHandler gatewayGlobalExceptionHandler(ObjectMapper objectMapper) {
        log.info("Configuring GatewayGlobalExceptionHandler for unified error response");
        return new GatewayGlobalExceptionHandler(objectMapper);
    }

    /**
     * 配置网关错误处理器
     * <p>
     * 处理 Spring Cloud Gateway 级别的错误，统一返回 R 结构
     */
    @Bean
    public GatewayErrorHandler gatewayErrorHandler(ObjectMapper objectMapper) {
        log.info("Configuring GatewayErrorHandler for gateway-level error handling");
        return new GatewayErrorHandler(objectMapper);
    }

    /**
     * Java时间模块配置
     *
     * <AUTHOR>
     * @since 3.0.0
     */
    public static class CustomJavaTimeModule extends SimpleModule {

        @Serial
        private static final long serialVersionUID = 1014815736771349614L;

        /**
         * 指定序列化规则
         */
        public CustomJavaTimeModule() {
            super(PackageVersion.VERSION);

            // ======================= 时间序列化规则 ===============================
            // yyyy-MM-dd HH:mm:ss
            this.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DATE_TIME_FORMATTER));
            // yyyy-MM-dd
            this.addSerializer(LocalDate.class, new LocalDateSerializer(DATE_FORMATTER));
            // HH:mm:ss
            this.addSerializer(LocalTime.class, new LocalTimeSerializer(TIME_FORMATTER));
            // Instant 类型序列化
            this.addSerializer(Instant.class, InstantSerializer.INSTANCE);
            // Duration 类型序列化
            this.addSerializer(Duration.class, DurationSerializer.INSTANCE);

            // ======================= 时间反序列化规则 ==============================
            // yyyy-MM-dd HH:mm:ss
            this.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DATE_TIME_FORMATTER));
            // yyyy-MM-dd
            this.addDeserializer(LocalDate.class, new LocalDateDeserializer(DATE_FORMATTER));
            // HH:mm:ss
            this.addDeserializer(LocalTime.class, new LocalTimeDeserializer(TIME_FORMATTER));
            // Instant 反序列化
            this.addDeserializer(Instant.class, InstantDeserializer.INSTANT);
            // Duration 反序列化
            this.addDeserializer(Duration.class, DurationDeserializer.INSTANCE);

        }
    }
}
