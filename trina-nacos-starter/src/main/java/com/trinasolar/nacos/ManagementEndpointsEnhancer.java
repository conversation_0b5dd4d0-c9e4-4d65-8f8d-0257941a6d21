package com.trinasolar.nacos;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.Ordered;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;

import java.util.HashMap;
import java.util.Map;

/**
 * 管理端点配置增强器
 * 自动配置Spring Boot Actuator端点
 * 基于Spring Boot官方management配置
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class ManagementEndpointsEnhancer implements EnvironmentPostProcessor, Ordered {

    private static final Logger logger = LoggerFactory.getLogger(ManagementEndpointsEnhancer.class);

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        try {
            // 处理管理端点配置
            processManagementConfiguration(environment);
            
            logger.info("✅ 管理端点配置增强处理完成");
        } catch (Exception e) {
            logger.warn("⚠️ 管理端点配置增强处理失败: {}", e.getMessage());
        }
    }

    /**
     * 处理管理端点配置
     */
    private void processManagementConfiguration(ConfigurableEnvironment environment) {
        // 获取管理端点配置，使用Spring Boot官方配置，如果没有则使用默认值
        boolean enableAllEndpoints = environment.getProperty("management.endpoints.web.exposure.include", String.class, "*").equals("*");
        boolean showHealthDetails = "always".equals(environment.getProperty("management.endpoint.health.show-details", "always"));

        // 构建管理端点配置
        Map<String, Object> managementProperties = new HashMap<>();

        // 只有在用户未明确配置时才设置默认值
        if (!environment.containsProperty("management.endpoints.web.exposure.include")) {
            // 启用所有端点
            managementProperties.put("management.endpoints.web.exposure.include", "*");
            managementProperties.put("management.endpoints.jmx.exposure.include", "*");
            
            // 启用特定端点
            managementProperties.put("management.endpoint.health.enabled", true);
            managementProperties.put("management.endpoint.info.enabled", true);
            managementProperties.put("management.endpoint.metrics.enabled", true);
            managementProperties.put("management.endpoint.env.enabled", true);
            managementProperties.put("management.endpoint.configprops.enabled", true);
            managementProperties.put("management.endpoint.loggers.enabled", true);
            managementProperties.put("management.endpoint.refresh.enabled", true);
            managementProperties.put("management.endpoint.beans.enabled", true);
            managementProperties.put("management.endpoint.threaddump.enabled", true);
            managementProperties.put("management.endpoint.heapdump.enabled", true);
            
            // Nacos相关端点
            managementProperties.put("management.endpoint.nacos-config.enabled", true);
            managementProperties.put("management.endpoint.nacos-discovery.enabled", true);
            managementProperties.put("management.endpoint.serviceregistry.enabled", true);
            
            // Sentinel相关端点
            managementProperties.put("management.endpoint.sentinel.enabled", true);
            
            // 负载均衡端点
            managementProperties.put("management.endpoint.loadbalancer.enabled", true);
            
            // 熔断器端点
            managementProperties.put("management.endpoint.circuitbreakers.enabled", true);
            managementProperties.put("management.endpoint.circuitbreakerevents.enabled", true);
            
            logger.info("📊 已启用所有管理端点");
        } else {
            logger.info("📊 使用用户配置的管理端点: {}", 
                environment.getProperty("management.endpoints.web.exposure.include"));
        }

        // 健康检查配置
        if (!environment.containsProperty("management.endpoint.health.show-details")) {
            managementProperties.put("management.endpoint.health.show-details", "always");
            managementProperties.put("management.endpoint.health.show-components", "always");
            
            // 启用各种健康检查
            managementProperties.put("management.health.nacos.enabled", true);
            managementProperties.put("management.health.sentinel.enabled", true);
            managementProperties.put("management.health.redis.enabled", true);
            managementProperties.put("management.health.diskspace.enabled", true);
            managementProperties.put("management.health.ping.enabled", true);
            managementProperties.put("management.health.db.enabled", true);
            
            logger.info("🏥 已启用详细健康检查信息");
        } else {
            logger.info("🏥 使用用户配置的健康检查详情: {}", 
                environment.getProperty("management.endpoint.health.show-details"));
        }

        // 配置属性增强（仅在未配置时设置）
        if (!environment.containsProperty("management.endpoint.configprops.show-values")) {
            managementProperties.put("management.endpoint.configprops.show-values", "always");
        }
        if (!environment.containsProperty("management.endpoint.env.show-values")) {
            managementProperties.put("management.endpoint.env.show-values", "always");
        }

        // 信息端点配置（仅在未配置时设置）
        if (!environment.containsProperty("management.info.env.enabled")) {
            managementProperties.put("management.info.env.enabled", true);
            managementProperties.put("management.info.java.enabled", true);
            managementProperties.put("management.info.os.enabled", true);
            managementProperties.put("management.info.git.enabled", true);
            managementProperties.put("management.info.build.enabled", true);
        }

        // 指标配置（仅在未配置时设置）
        if (!environment.containsProperty("management.metrics.web.server.auto-time-requests")) {
            managementProperties.put("management.metrics.web.server.auto-time-requests", true);
            managementProperties.put("management.metrics.web.client.auto-time-requests", true);
            managementProperties.put("management.metrics.distribution.percentiles-histogram.http.server.requests", true);
            managementProperties.put("management.metrics.distribution.percentiles.http.server.requests", "0.5,0.9,0.95,0.99");
        }

        // 日志配置（仅在未配置时设置）
        if (!environment.containsProperty("management.endpoint.loggers.enabled")) {
            managementProperties.put("management.endpoint.loggers.enabled", true);
        }

        // 安全配置（开发环境）
        String activeProfile = environment.getProperty("spring.profiles.active", "dev");
        if (!environment.containsProperty("management.security.enabled")) {
            if ("dev".equals(activeProfile) || "test".equals(activeProfile)) {
                // 开发和测试环境允许所有访问
                managementProperties.put("management.security.enabled", false);
                logger.info("🔓 开发/测试环境：已禁用管理端点安全限制");
            } else {
                // 生产环境启用安全限制
                managementProperties.put("management.security.enabled", true);
                logger.info("🔒 生产环境：已启用管理端点安全限制");
            }
        }

        // 只有在有需要设置的属性时才添加到环境中
        if (!managementProperties.isEmpty()) {
            MapPropertySource managementConfigSource = new MapPropertySource(
                "defaultManagementConfig", managementProperties);
            environment.getPropertySources().addLast(managementConfigSource);
            logger.info("📋 已添加默认管理端点配置");
        }

        logger.info("📊 管理端点配置信息:");
        logger.info("  - 启用所有端点: {}", enableAllEndpoints);
        logger.info("  - 显示健康详情: {}", showHealthDetails);
        logger.info("  - 当前环境: {}", activeProfile);
    }

    @Override
    public int getOrder() {
        // 在Sentinel配置增强器之后执行
        return Ordered.HIGHEST_PRECEDENCE + 30;
    }
}
