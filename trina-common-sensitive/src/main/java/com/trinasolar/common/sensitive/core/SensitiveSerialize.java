package com.trinasolar.common.sensitive.core;


import com.trinasolar.common.core.spring.SpringContextHolder;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.trinasolar.common.sensitive.annotation.Sensitive;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019-08-13
 * <p>
 * 脱敏序列化
 */
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class SensitiveSerialize extends JsonSerializer<String> implements ContextualSerializer {

    private Sensitive sensitive;

    @Override
    public void serialize(final String origin, final JsonGenerator jsonGenerator,
                          final SerializerProvider serializerProvider) throws IOException {

        if (StrUtil.isBlank(origin)) {
            jsonGenerator.writeString(origin);
            return;
        }

        // 判断是否有可以查看原文
        SensitiveService sensitiveService = SpringContextHolder.getBean(SensitiveService.class);
        if (!sensitiveService.isSensitive(sensitive)) {
            jsonGenerator.writeString(origin);
            return;
        }

        // 根据配置注解获取脱敏类型并处理
        String result = sensitive.type().getStrategy().apply(sensitive, origin);
        jsonGenerator.writeString(result);
    }

    @Override
    public JsonSerializer<?> createContextual(final SerializerProvider serializerProvider,
                                              final BeanProperty beanProperty) throws JsonMappingException {
        if (beanProperty != null) {
            if (Objects.equals(beanProperty.getType().getRawClass(), String.class)) {
                Sensitive sensitive = beanProperty.getAnnotation(Sensitive.class);
                if (sensitive == null) {
                    sensitive = beanProperty.getContextAnnotation(Sensitive.class);
                }
                if (sensitive != null) {
                    this.sensitive = sensitive;
                    return this;
                }
            }
            return serializerProvider.findValueSerializer(beanProperty.getType(), beanProperty);
        }
        return serializerProvider.findNullValueSerializer(null);
    }

}
