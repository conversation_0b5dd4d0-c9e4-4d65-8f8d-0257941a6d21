package com.trinasolar.gateway.security.sureness.matcher;


import com.trinasolar.gateway.security.sureness.Subject;
import com.trinasolar.gateway.security.sureness.exception.SurenessLoadDataException;
import com.trinasolar.gateway.security.sureness.exception.SurenessNoInitException;

/**
 * path-role  matcher interface
 *
 * <AUTHOR>
 * @since 2025-07-08 17:54
 **/
public interface TreePathRoleMatcher {

    /**
     * Use the targetUri in the subject to match the supported roles in the tree and fill in the subject
     *
     * @param subject After success, the required role will be inserted into the subject
     * @throws SurenessNoInitException when matcher not init
     */
    void matchRole(Subject subject);

    /**
     * build the pathRole match tree
     *
     * @throws SurenessNoInitException   when matcher not init
     * @throws SurenessLoadDataException when datasource not init
     */
    void buildTree();

    /**
     * rebuild the pathRole match tree
     *
     * @throws SurenessNoInitException   when matcher not init
     * @throws SurenessLoadDataException when datasource not init
     */
    void rebuildTree();

    /**
     * Determine whether the resource requested by this request is in the exclusion list
     * resource: requestUri===method
     *
     * @param request request
     * @return in the exclusion list return true, else false
     */
    boolean isExcludedResource(Subject request);
}
