package com.trinasolar.common.security.example;

import com.trinasolar.common.core.base.R;
import com.trinasolar.common.security.annotation.ColPermission;
import com.trinasolar.common.security.annotation.HasPermission;
import com.trinasolar.common.security.annotation.HasRole;
import com.trinasolar.common.security.util.SecurityUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 权限控制示例Controller
 * <p>
 * 演示如何使用权限注解进行功能权限和列级权限控制
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@RestController
@RequestMapping("/api/example")
public class ExampleController {

    /**
     * 角色权限示例 - 需要admin角色
     */
    @HasRole("admin")
    @GetMapping("/admin-only")
    public R<String> adminOnly() {
        return R.ok("只有admin角色可以访问此接口");
    }

    /**
     * 角色权限示例 - 需要admin或manager角色之一
     */
    @HasRole({"admin", "manager"})
    @GetMapping("/admin-or-manager")
    public R<String> adminOrManager() {
        return R.ok("admin或manager角色可以访问此接口");
    }

    /**
     * 角色权限示例 - 需要同时拥有admin和manager角色
     */
    @HasRole(value = {"admin", "manager"}, logical = HasRole.Logical.AND)
    @GetMapping("/admin-and-manager")
    public R<String> adminAndManager() {
        return R.ok("需要同时拥有admin和manager角色才能访问");
    }

    /**
     * 功能权限示例 - 需要user:view权限
     */
    @HasPermission("user:view")
    @GetMapping("/user-view")
    public R<String> userView() {
        return R.ok("拥有user:view权限可以访问此接口");
    }

    /**
     * 功能权限示例 - 需要user:add或user:edit权限之一
     */
    @HasPermission({"user:add", "user:edit"})
    @GetMapping("/user-modify")
    public R<String> userModify() {
        return R.ok("拥有user:add或user:edit权限可以访问此接口");
    }

    /**
     * 功能权限示例 - 需要同时拥有user:delete和user:admin权限
     */
    @HasPermission(value = {"user:delete", "user:admin"}, logical = HasPermission.Logical.AND)
    @GetMapping("/user-delete")
    public R<String> userDelete() {
        return R.ok("需要同时拥有user:delete和user:admin权限才能访问");
    }

    /**
     * 功能权限示例 - 需要同时拥有admin角色及user:delete和user:admin权限
     */
    @HasRole("admin")
    @HasPermission(value = {"user:delete", "user:admin"}, logical = HasPermission.Logical.AND)
    @GetMapping("/user-role-delete")
    public R<String> userDeleteAndRole() {
        return R.ok("需要同时拥有admin角色及user:delete和user:admin权限才能访问");
    }

    /**
     * 列级权限示例 - general角色不能看到salary和bonus字段
     */
    @ColPermission(role = "general", exc = {"salary", "bonus"})
    @GetMapping("/employees")
    public R<List<Employee>> getEmployees() {
        List<Employee> employees = Arrays.asList(
                new Employee("1", "张三", "开发部", "developer", new BigDecimal("10000"), new BigDecimal("2000"), "13800138001"),
                new Employee("2", "李四", "测试部", "tester", new BigDecimal("8000"), new BigDecimal("1500"), "13800138002"),
                new Employee("3", "王五", "产品部", "product", new BigDecimal("12000"), new BigDecimal("2500"), "13800138003")
        );
        return R.ok(employees);
    }

    /**
     * 列级权限示例 - admin角色只能看到salary和bonus字段
     */
    @ColPermission(role = "admin", inc = {"id", "name", "salary", "bonus"}, mode = ColPermission.Mode.INCLUDE)
    @GetMapping("/employee-salaries")
    public R<List<Employee>> getEmployeeSalaries() {
        List<Employee> employees = Arrays.asList(
                new Employee("1", "张三", "开发部", "developer", new BigDecimal("10000"), new BigDecimal("2000"), "13800138001"),
                new Employee("2", "李四", "测试部", "tester", new BigDecimal("8000"), new BigDecimal("1500"), "13800138002")
        );
        return R.ok(employees);
    }

    /**
     * 列级权限示例 - 多个角色的字段过滤
     */
    @ColPermission(roles = {"general", "user"}, exc = {"salary", "bonus", "phone"})
    @GetMapping("/public-employees")
    public R<List<Employee>> getPublicEmployees() {
        List<Employee> employees = Arrays.asList(
                new Employee("1", "张三", "开发部", "developer", new BigDecimal("10000"), new BigDecimal("2000"), "13800138001"),
                new Employee("2", "李四", "测试部", "tester", new BigDecimal("8000"), new BigDecimal("1500"), "13800138002")
        );
        return R.ok(employees);
    }

    /**
     * 获取当前用户信息示例
     */
    @GetMapping("/current-user")
    public R<UserInfo> getCurrentUser() {
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(SecurityUtils.getCurrentUserId());
        userInfo.setUsername(SecurityUtils.getCurrentUsername());
        userInfo.setDisplayName(SecurityUtils.getCurrentUserDisplayName());
        userInfo.setEmail(SecurityUtils.getCurrentUserEmail());
        userInfo.setRoles(SecurityUtils.getCurrentUserRoleCodes());
        userInfo.setPermissions(SecurityUtils.getCurrentUserPermissions());
        userInfo.setIsAdmin(SecurityUtils.isAdmin());
        userInfo.setIsAuthenticated(SecurityUtils.isAuthenticated());

        // 检查当前用户角色
        if (SecurityUtils.hasRole("admin")) {
            // 管理员逻辑
            log.info("管理员逻辑");
        }

        // 检查当前用户权限
        if (SecurityUtils.hasPermission("user:edit")) {
            // 有编辑权限的逻辑
            log.info("有用户编辑权限的逻辑");
        }
        return R.ok(userInfo);
    }

    /**
     * 员工信息模型
     */
    @Data
    public static class Employee {
        private String id;
        private String name;
        private String department;
        private String position;
        private BigDecimal salary;
        private BigDecimal bonus;
        private String phone;

        public Employee(String id, String name, String department, String position,
                        BigDecimal salary, BigDecimal bonus, String phone) {
            this.id = id;
            this.name = name;
            this.department = department;
            this.position = position;
            this.salary = salary;
            this.bonus = bonus;
            this.phone = phone;
        }
    }

    /**
     * 用户信息模型
     */
    @Data
    public static class UserInfo {
        private String userId;
        private String username;
        private String displayName;
        private String email;
        private List<String> roles;
        private List<String> permissions;
        private Boolean isAdmin;
        private Boolean isAuthenticated;
    }
}