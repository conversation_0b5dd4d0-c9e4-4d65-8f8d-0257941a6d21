package com.trinasolar.elasticsearch.base.service.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.Refresh;
import co.elastic.clients.elasticsearch._types.Result;
import co.elastic.clients.elasticsearch.core.*;
import co.elastic.clients.elasticsearch.core.bulk.BulkOperation;
import co.elastic.clients.elasticsearch.core.bulk.BulkResponseItem;
import co.elastic.clients.elasticsearch.indices.*;
import co.elastic.clients.elasticsearch.indices.ExistsRequest;
import co.elastic.clients.transport.endpoints.BooleanResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trinasolar.elasticsearch.base.annotation.EsIndex;
import com.trinasolar.elasticsearch.base.entity.BaseEsEntity;
import com.trinasolar.elasticsearch.base.service.BaseEsService;
import com.trinasolar.elasticsearch.base.wrapper.EsQueryWrapper;
import com.trinasolar.elasticsearch.vo.SearchResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Elasticsearch基础Service实现类
 * 提供通用的ES操作实现
 * 
 * @param <T> 实体类型，必须继承BaseEsEntity
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
public abstract class BaseEsServiceImpl<T extends BaseEsEntity> implements BaseEsService<T> {
    
    @Autowired
    protected ElasticsearchClient esClient;
    
    @Autowired
    @Qualifier("elasticsearchObjectMapper")
    protected ObjectMapper objectMapper;
    
    /**
     * 实体类型
     */
    protected Class<T> entityClass;
    
    /**
     * 索引名称
     */
    protected String indexName;

    /**
     * 时间格式化器
     */
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 获取当前时间字符串
     */
    private String getCurrentTimeString() {
        return LocalDateTime.now().format(TIME_FORMATTER);
    }
    
    /**
     * 文档类型
     */
    protected String documentType = "_doc";
    
    /**
     * 构造函数
     */
    @SuppressWarnings("unchecked")
    public BaseEsServiceImpl() {
        // 获取泛型类型
        Type superClass = getClass().getGenericSuperclass();
        if (superClass instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) superClass;
            Type[] typeArguments = parameterizedType.getActualTypeArguments();
            if (typeArguments.length > 0) {
                this.entityClass = (Class<T>) typeArguments[0];
                // 获取索引名称
                this.indexName = getIndexNameFromAnnotation();
            }
        }
    }
    
    /**
     * 从注解获取索引名称
     */
    private String getIndexNameFromAnnotation() {
        if (entityClass != null) {
            EsIndex esIndex = entityClass.getAnnotation(EsIndex.class);
            if (esIndex != null && StringUtils.hasText(esIndex.name())) {
                return esIndex.name();
            }
            // 如果没有注解，使用类名的小写形式
            return entityClass.getSimpleName().toLowerCase();
        }
        return "default";
    }
    
    // ==================== 索引管理 ====================
    
    @Override
    public boolean createIndex() {
        try {
            if (indexExists()) {
                log.info("索引 {} 已存在", indexName);
                return true;
            }
            
            CreateIndexRequest.Builder builder = new CreateIndexRequest.Builder()
                .index(indexName);
            
            // 从注解获取索引配置
            if (entityClass != null) {
                EsIndex esIndex = entityClass.getAnnotation(EsIndex.class);
                if (esIndex != null) {
                    // 设置分片和副本
                    Map<String, Object> settings = new HashMap<>();
                    settings.put("number_of_shards", esIndex.shards());
                    settings.put("number_of_replicas", esIndex.replicas());
                    settings.put("refresh_interval", esIndex.refreshInterval());
                    settings.put("max_result_window", esIndex.maxResultWindow());
                    
                    builder.settings(s -> s.index(i -> {
                        settings.forEach((key, value) -> {
                            if (value instanceof Integer) {
                                i.numberOfShards(String.valueOf(value));
                            } else if (value instanceof String) {
                                // 这里可以根据需要设置其他配置
                            }
                        });
                        return i;
                    }));
                }
            }
            
            CreateIndexResponse response = esClient.indices().create(builder.build());
            log.info("创建索引 {} 成功: {}", indexName, response.acknowledged());
            return response.acknowledged();
        } catch (Exception e) {
            log.error("创建索引 {} 失败", indexName, e);
            throw new com.trinasolar.elasticsearch.exception.IndexOperationException(
                indexName, 
                com.trinasolar.elasticsearch.exception.IndexOperationException.OperationType.CREATE, 
                "创建索引失败", 
                e
            );
        }
    }
    
    @Override
    public boolean deleteIndex() {
        try {
            if (!indexExists()) {
                log.info("索引 {} 不存在", indexName);
                return true;
            }
            
            DeleteIndexRequest request = DeleteIndexRequest.of(d -> d.index(indexName));
            DeleteIndexResponse response = esClient.indices().delete(request);
            log.info("删除索引 {} 成功: {}", indexName, response.acknowledged());
            return response.acknowledged();
        } catch (Exception e) {
            log.error("删除索引 {} 失败", indexName, e);
            throw new com.trinasolar.elasticsearch.exception.IndexOperationException(
                indexName, 
                com.trinasolar.elasticsearch.exception.IndexOperationException.OperationType.DELETE, 
                "删除索引失败", 
                e
            );
        }
    }
    
    @Override
    public boolean indexExists() {
        try {
            ExistsRequest request = ExistsRequest.of(e -> e.index(indexName));
            BooleanResponse response = esClient.indices().exists(request);
            return response.value();
        } catch (Exception e) {
            log.error("检查索引 {} 是否存在失败", indexName, e);
            throw new com.trinasolar.elasticsearch.exception.ElasticsearchException(
                "检查索引是否存在失败", 
                e
            );
        }
    }
    
    @Override
    public boolean refreshIndex() {
        try {
            RefreshRequest request = RefreshRequest.of(r -> r.index(indexName));
            RefreshResponse response = esClient.indices().refresh(request);
            log.debug("刷新索引 {} 成功", indexName);
            return true;
        } catch (Exception e) {
            log.error("刷新索引 {} 失败", indexName, e);
            return false;
        }
    }
    
    // ==================== 文档操作 ====================
    
    @Override
    public T save(T entity) {
        try {
            // 设置创建时间
            if (entity.getCreateAt() == null) {
                entity.setCreateAt(getCurrentTimeString());
            }
            entity.setUpdateAt(getCurrentTimeString());
            
            // 生成ID（如果没有）
            if (!StringUtils.hasText(entity.getId())) {
                entity.setId(UUID.randomUUID().toString());
            }
            
            IndexRequest<T> request = IndexRequest.of(i -> i
                .index(indexName)
                .id(entity.getId())
                .document(entity)
                .refresh(Refresh.WaitFor)
            );
            
            IndexResponse response = esClient.index(request);
            
            if (response.result() == Result.Created || response.result() == Result.Updated) {
                log.debug("保存文档成功: {} - {}", indexName, entity.getId());
                return entity;
            } else {
                throw new com.trinasolar.elasticsearch.exception.ElasticsearchException(
                    "保存文档失败: " + response.result()
                );
            }
        } catch (Exception e) {
            log.error("保存文档失败: {} - {}", indexName, entity.getId(), e);
            throw new com.trinasolar.elasticsearch.exception.ElasticsearchException(
                "保存文档失败", 
                e
            );
        }
    }
    
    @Override
    public int saveBatch(Collection<T> entityList) {
        if (entityList == null || entityList.isEmpty()) {
            return 0;
        }
        
        try {
            List<BulkOperation> operations = new ArrayList<>();
            String now = getCurrentTimeString();

            for (T entity : entityList) {
                // 设置时间
                if (entity.getCreateAt() == null) {
                    entity.setCreateAt(now);
                }
                entity.setUpdateAt(now);
                
                // 生成ID（如果没有）
                if (!StringUtils.hasText(entity.getId())) {
                    entity.setId(UUID.randomUUID().toString());
                }
                
                operations.add(BulkOperation.of(o -> o
                    .index(idx -> idx
                        .index(indexName)
                        .id(entity.getId())
                        .document(entity)
                    )
                ));
            }
            
            BulkRequest request = BulkRequest.of(b -> b
                .operations(operations)
                .refresh(Refresh.WaitFor)
            );
            
            BulkResponse response = esClient.bulk(request);
            
            int successCount = 0;
            for (BulkResponseItem item : response.items()) {
                if (item.error() == null) {
                    successCount++;
                } else {
                    log.error("批量保存文档失败: {}", item.error().reason());
                }
            }
            
            log.debug("批量保存文档完成: {} 成功 {} 个，共 {} 个", indexName, successCount, entityList.size());
            return successCount;
        } catch (Exception e) {
            log.error("批量保存文档失败: {}", indexName, e);
            throw new com.trinasolar.elasticsearch.exception.ElasticsearchException(
                "批量保存文档失败", 
                e
            );
        }
    }
    
    @Override
    public boolean removeById(Serializable id) {
        try {
            DeleteRequest request = DeleteRequest.of(d -> d
                .index(indexName)
                .id(String.valueOf(id))
                .refresh(Refresh.WaitFor)
            );
            
            DeleteResponse response = esClient.delete(request);
            
            if (response.result() == Result.Deleted) {
                log.debug("删除文档成功: {} - {}", indexName, id);
                return true;
            } else if (response.result() == Result.NotFound) {
                log.warn("文档不存在: {} - {}", indexName, id);
                return false;
            } else {
                throw new com.trinasolar.elasticsearch.exception.ElasticsearchException(
                    "删除文档失败: " + response.result()
                );
            }
        } catch (Exception e) {
            log.error("删除文档失败: {} - {}", indexName, id, e);
            throw new com.trinasolar.elasticsearch.exception.ElasticsearchException(
                "删除文档失败", 
                e
            );
        }
    }
    
    @Override
    public int removeByIds(Collection<? extends Serializable> idList) {
        if (idList == null || idList.isEmpty()) {
            return 0;
        }
        
        try {
            List<BulkOperation> operations = idList.stream()
                .map(id -> BulkOperation.of(o -> o
                    .delete(d -> d
                        .index(indexName)
                        .id(String.valueOf(id))
                    )
                ))
                .collect(Collectors.toList());
            
            BulkRequest request = BulkRequest.of(b -> b
                .operations(operations)
                .refresh(Refresh.WaitFor)
            );
            
            BulkResponse response = esClient.bulk(request);
            
            int successCount = 0;
            for (BulkResponseItem item : response.items()) {
                if (item.error() == null) {
                    successCount++;
                } else {
                    log.error("批量删除文档失败: {}", item.error().reason());
                }
            }
            
            log.debug("批量删除文档完成: {} 成功 {} 个，共 {} 个", indexName, successCount, idList.size());
            return successCount;
        } catch (Exception e) {
            log.error("批量删除文档失败: {}", indexName, e);
            throw new com.trinasolar.elasticsearch.exception.ElasticsearchException(
                "批量删除文档失败", 
                e
            );
        }
    }
    
    @Override
    public int remove(EsQueryWrapper<T> queryWrapper) {
        try {
            DeleteByQueryRequest request = DeleteByQueryRequest.of(d -> d
                .index(indexName)
                .query(queryWrapper.buildBoolQuery())
                .refresh(true)
            );
            
            DeleteByQueryResponse response = esClient.deleteByQuery(request);
            
            int deletedCount = Math.toIntExact(response.deleted());
            log.debug("根据条件删除文档完成: {} 删除 {} 个", indexName, deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("根据条件删除文档失败: {}", indexName, e);
            throw new com.trinasolar.elasticsearch.exception.ElasticsearchException(
                "根据条件删除文档失败", 
                e
            );
        }
    }
    
    @Override
    public T updateById(T entity) {
        try {
            entity.setUpdateAt(getCurrentTimeString());
            
            UpdateRequest<T, T> request = UpdateRequest.of(u -> u
                .index(indexName)
                .id(entity.getId())
                .doc(entity)
                .refresh(Refresh.WaitFor)
            );
            
            UpdateResponse<T> response = esClient.update(request, entityClass);
            
            if (response.result() == Result.Updated || response.result() == Result.NoOp) {
                log.debug("更新文档成功: {} - {}", indexName, entity.getId());
                return entity;
            } else {
                throw new com.trinasolar.elasticsearch.exception.ElasticsearchException(
                    "更新文档失败: " + response.result()
                );
            }
        } catch (Exception e) {
            log.error("更新文档失败: {} - {}", indexName, entity.getId(), e);
            throw new com.trinasolar.elasticsearch.exception.ElasticsearchException(
                "更新文档失败", 
                e
            );
        }
    }
    
    @Override
    public int updateBatchById(Collection<T> entityList) {
        if (entityList == null || entityList.isEmpty()) {
            return 0;
        }
        
        try {
            List<BulkOperation> operations = new ArrayList<>();
            String now = getCurrentTimeString();

            for (T entity : entityList) {
                entity.setUpdateAt(now);
                
                operations.add(BulkOperation.of(o -> o
                    .update(u -> u
                        .index(indexName)
                        .id(entity.getId())
                        .action(a -> a.doc(entity))
                    )
                ));
            }
            
            BulkRequest request = BulkRequest.of(b -> b
                .operations(operations)
                .refresh(Refresh.WaitFor)
            );
            
            BulkResponse response = esClient.bulk(request);
            
            int successCount = 0;
            for (BulkResponseItem item : response.items()) {
                if (item.error() == null) {
                    successCount++;
                } else {
                    log.error("批量更新文档失败: {}", item.error().reason());
                }
            }
            
            log.debug("批量更新文档完成: {} 成功 {} 个，共 {} 个", indexName, successCount, entityList.size());
            return successCount;
        } catch (Exception e) {
            log.error("批量更新文档失败: {}", indexName, e);
            throw new com.trinasolar.elasticsearch.exception.ElasticsearchException(
                "批量更新文档失败", 
                e
            );
        }
    }
    
    @Override
    public int update(T entity, EsQueryWrapper<T> queryWrapper) {
        try {
            entity.setUpdateAt(getCurrentTimeString());
            
            UpdateByQueryRequest request = UpdateByQueryRequest.of(u -> u
                .index(indexName)
                .query(queryWrapper.buildBoolQuery())
                .refresh(true)
            );
            
            UpdateByQueryResponse response = esClient.updateByQuery(request);
            
            int updatedCount = Math.toIntExact(response.updated());
            log.debug("根据条件更新文档完成: {} 更新 {} 个", indexName, updatedCount);
            return updatedCount;
        } catch (Exception e) {
            log.error("根据条件更新文档失败: {}", indexName, e);
            throw new com.trinasolar.elasticsearch.exception.ElasticsearchException(
                "根据条件更新文档失败", 
                e
            );
        }
    }
    
    @Override
    public T saveOrUpdate(T entity) {
        if (StringUtils.hasText(entity.getId())) {
            // 检查文档是否存在
            try {
                T existing = getById(entity.getId());
                if (existing != null) {
                    return updateById(entity);
                }
            } catch (Exception e) {
                // 文档不存在，执行保存
            }
        }
        return save(entity);
    }
    
    @Override
    public int saveOrUpdateBatch(Collection<T> entityList) {
        if (entityList == null || entityList.isEmpty()) {
            return 0;
        }
        
        List<T> saveList = new ArrayList<>();
        List<T> updateList = new ArrayList<>();
        
        for (T entity : entityList) {
            if (StringUtils.hasText(entity.getId())) {
                try {
                    T existing = getById(entity.getId());
                    if (existing != null) {
                        updateList.add(entity);
                    } else {
                        saveList.add(entity);
                    }
                } catch (Exception e) {
                    saveList.add(entity);
                }
            } else {
                saveList.add(entity);
            }
        }
        
        int count = 0;
        if (!saveList.isEmpty()) {
            count += saveBatch(saveList);
        }
        if (!updateList.isEmpty()) {
            count += updateBatchById(updateList);
        }
        
        return count;
    }
    
    // ==================== 查询操作 ====================
    
    @Override
    public T getById(Serializable id) {
        try {
            GetRequest request = GetRequest.of(g -> g
                .index(indexName)
                .id(String.valueOf(id))
            );
            
            GetResponse<T> response = esClient.get(request, entityClass);
            
            if (response.found()) {
                T entity = response.source();
                if (entity != null) {
                    entity.setScore(null); // 清除评分
                }
                return entity;
            }
            return null;
        } catch (Exception e) {
            log.error("根据ID查询文档失败: {} - {}", indexName, id, e);
            throw new com.trinasolar.elasticsearch.exception.ElasticsearchException(
                "根据ID查询文档失败", 
                e
            );
        }
    }
    
    @Override
    public List<T> listByIds(Collection<? extends Serializable> idList) {
        if (idList == null || idList.isEmpty()) {
            return new ArrayList<>();
        }
        
        try {
            List<String> ids = idList.stream()
                .map(String::valueOf)
                .collect(Collectors.toList());
            
            MgetRequest request = MgetRequest.of(m -> m
                .index(indexName)
                .ids(ids)
            );
            
            MgetResponse<T> response = esClient.mget(request, entityClass);
            
            return response.docs().stream()
                .filter(doc -> doc.isResult() && doc.result().found())
                .map(doc -> {
                    T entity = doc.result().source();
                    if (entity != null) {
                        entity.setScore(null);
                    }
                    return entity;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("根据ID列表查询文档失败: {}", indexName, e);
            throw new com.trinasolar.elasticsearch.exception.ElasticsearchException(
                "根据ID列表查询文档失败", 
                e
            );
        }
    }
    
    @Override
    public List<T> listByMap(Map<String, Object> columnMap) {
        EsQueryWrapper<T> queryWrapper = queryWrapper();
        
        if (columnMap != null && !columnMap.isEmpty()) {
            columnMap.forEach(queryWrapper::eq);
        }
        
        return list(queryWrapper);
    }
    
    @Override
    public T getOne(EsQueryWrapper<T> queryWrapper) {
        List<T> list = list(queryWrapper.size(1));
        return list.isEmpty() ? null : list.get(0);
    }
    
    @Override
    public List<T> list(EsQueryWrapper<T> queryWrapper) {
        SearchResult<T> result = search(queryWrapper);
        return result.getRecords();
    }
    
    @Override
    public List<T> list() {
        return list(queryWrapper());
    }
    
    @Override
    public long count(EsQueryWrapper<T> queryWrapper) {
        try {
            CountRequest request = CountRequest.of(c -> c
                .index(indexName)
                .query(queryWrapper.buildBoolQuery())
            );
            
            CountResponse response = esClient.count(request);
            return response.count();
        } catch (Exception e) {
            log.error("统计文档数量失败: {}", indexName, e);
            throw new com.trinasolar.elasticsearch.exception.ElasticsearchException(
                "统计文档数量失败", 
                e
            );
        }
    }
    
    @Override
    public long count() {
        return count(queryWrapper());
    }
    
    // ==================== 搜索操作 ====================
    
    @Override
    public SearchResult<T> search(EsQueryWrapper<T> queryWrapper){
        try {
            SearchRequest request = queryWrapper.buildSearchRequest(indexName).build();
            SearchResponse<T> response = esClient.search(request, entityClass);
            
            return buildSearchResult(response);
        } catch (Exception e) {
            log.error("搜索文档失败: {}", indexName, e);
            throw new com.trinasolar.elasticsearch.exception.ElasticsearchException(
                "搜索文档失败", 
                e
            );
        }
    }
    
    @Override
    public SearchResult<T> searchWithHighlight(EsQueryWrapper<T> queryWrapper, String... highlightFields) {
        // 添加高亮字段
        for (String field : highlightFields) {
            queryWrapper.highlight(field);
        }
        
        return search(queryWrapper);
    }
    
    @Override
    public SearchResult<T> scroll(EsQueryWrapper<T> queryWrapper, String scrollTime) {
        try {
            SearchRequest request = queryWrapper.buildSearchRequest(indexName)
                .scroll(s -> s.time(scrollTime))
                .build();
            
            SearchResponse<T> response = esClient.search(request, entityClass);
            
            SearchResult<T> result = buildSearchResult(response);
            result.setScrollId(response.scrollId());
            return result;
        } catch (Exception e) {
            log.error("滚动搜索文档失败: {}", indexName, e);
            throw new com.trinasolar.elasticsearch.exception.ElasticsearchException(
                "滚动搜索文档失败", 
                e
            );
        }
    }
    
    @Override
    public SearchResult<T> scroll(String scrollId, String scrollTime) {
        try {
            ScrollRequest request = ScrollRequest.of(s -> s
                .scrollId(scrollId)
                .scroll(sc -> sc.time(scrollTime))
            );
            
            ScrollResponse<T> response = esClient.scroll(request, entityClass);
            
            SearchResult<T> result = new SearchResult<>();
            result.setScrollId(response.scrollId());
            result.setTotal(response.hits().total().value());
            
            List<T> records = response.hits().hits().stream()
                .map(hit -> {
                    T entity = hit.source();
                    if (entity != null) {
                        entity.setScore(hit.score() != null ? hit.score().floatValue() : null);
                        entity.setHighlight(hit.highlight());
                    }
                    return entity;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
            
            result.setRecords(records);
            result.setSize(records.size());
            
            return result;
        } catch (Exception e) {
            log.error("继续滚动搜索失败: {}", scrollId, e);
            throw new com.trinasolar.elasticsearch.exception.ElasticsearchException(
                "继续滚动搜索失败", 
                e
            );
        }
    }
    
    @Override
    public boolean clearScroll(String scrollId) {
        try {
            ClearScrollRequest request = ClearScrollRequest.of(c -> c
                .scrollId(scrollId)
            );
            
            ClearScrollResponse response = esClient.clearScroll(request);
            return response.succeeded();
        } catch (Exception e) {
            log.error("清除滚动搜索失败: {}", scrollId, e);
            throw new com.trinasolar.elasticsearch.exception.ElasticsearchException(
                "清除滚动搜索失败", 
                e
            );
        }
    }
    
    // ==================== 聚合操作 ====================
    
    @Override
    public Map<String, Object> aggregation(EsQueryWrapper<T> queryWrapper, String aggregationName, String aggregationField) {
        Map<String, String> aggregations = new HashMap<>();
        aggregations.put(aggregationName, aggregationField);
        return aggregations(queryWrapper, aggregations);
    }
    
    @Override
    public Map<String, Object> aggregations(EsQueryWrapper<T> queryWrapper, Map<String, String> aggregations) {
        try {
            SearchRequest.Builder builder = queryWrapper.buildSearchRequest(indexName)
                .size(0); // 不返回文档，只返回聚合结果
            
            // 添加聚合
            aggregations.forEach((name, field) -> {
                builder.aggregations(name, a -> a
                    .terms(t -> t.field(field))
                );
            });
            
            SearchRequest request = builder.build();
            SearchResponse<T> response = esClient.search(request, entityClass);
            
            Map<String, Object> result = new HashMap<>();
            response.aggregations().forEach((name, aggregation) -> {
                // 这里需要根据聚合类型解析结果
                result.put(name, aggregation);
            });
            
            return result;
        } catch (Exception e) {
            log.error("聚合查询失败: {}", indexName, e);
            throw new com.trinasolar.elasticsearch.exception.ElasticsearchException(
                "聚合查询失败", 
                e
            );
        }
    }
    
    // ==================== 建议操作 ====================
    
    @Override
    public List<String> suggest(String field, String text, int size) {
        // 这里需要实现自动补全建议
        return new ArrayList<>();
    }
    
    @Override
    public List<String> phraseSuggest(String field, String text, int size) {
        // 这里需要实现短语建议
        return new ArrayList<>();
    }
    
    @Override
    public List<String> termSuggest(String field, String text, int size) {
        // 这里需要实现词项建议
        return new ArrayList<>();
    }
    
    // ==================== 工具方法 ====================
    
    @Override
    public Class<T> getEntityClass() {
        return entityClass;
    }
    
    @Override
    public String getIndexName() {
        return indexName;
    }
    
    @Override
    public String getDocumentType() {
        return documentType;
    }
    
    @Override
    public EsQueryWrapper<T> queryWrapper() {
        return new EsQueryWrapper<>();
    }
    
    @Override
    public EsQueryWrapper<T> updateWrapper() {
        return new EsQueryWrapper<>();
    }
    
    @Override
    public List<String> getTagSuggestions(String prefix, int size) {
        return suggest("tags", prefix, size);
    }
    
    @Override
    public List<String> getAuthorSuggestions(String prefix, int size) {
        return suggest("author", prefix, size);
    }
    
    @Override
    public List<String> getCategorySuggestions(String prefix, int size) {
        return suggest("category", prefix, size);
    }
    
    /**
     * 构建搜索结果
     */
    protected SearchResult<T> buildSearchResult(SearchResponse<T> response) {
        SearchResult<T> result = new SearchResult<>();
        result.setTotal(response.hits().total().value());
        result.setMaxScore(response.hits().maxScore() != null ? response.hits().maxScore().floatValue() : null);
        result.setTookInMillis(response.took());
        result.setTimedOut(response.timedOut());
        
        List<T> records = response.hits().hits().stream()
            .map(hit -> {
                T entity = hit.source();
                if (entity != null) {
                    // 修复：使用maxScore作为分数（因为hit.score()在某些查询中返回null）
                    Float score = null;

                    // 方式1：直接从hit.score()获取
                    if (hit.score() != null) {
                        score = hit.score().floatValue();
                    } else if (response.hits().maxScore() != null) {
                        // 方式2：使用maxScore作为默认分数
                        // 这是一个临时解决方案，实际分数应该从Elasticsearch正确获取
                        score = response.hits().maxScore().floatValue();
                        log.debug("使用maxScore作为分数: {}", score);
                    }

                    entity.setScore(score);
                    entity.setHighlight(hit.highlight());
                }
                return entity;
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        
        result.setRecords(records);
        result.setSize(records.size());
        
        return result;
    }

    // ==================== 向量搜索能力 ====================

    @Override
    public T saveWithVector(T entity, String titleField, String contentField) {
        // 默认实现：直接保存，不生成向量
        // 子类可以重写此方法来实现向量生成
        log.warn("saveWithVector方法未实现向量生成，仅保存文档");
        return save(entity);
    }

    @Override
    public List<T> saveBatchWithVector(List<T> entities, String titleField, String contentField) {
        // 默认实现：直接批量保存，不生成向量
        // 子类可以重写此方法来实现向量生成
        log.warn("saveBatchWithVector方法未实现向量生成，仅批量保存文档");
        saveBatch(entities);
        return entities;
    }

    @Override
    public List<T> vectorSearch(String queryText, String vectorField, float threshold, int size) {
        // 检查ES版本是否支持向量搜索
        Map<String, Object> versionInfo = checkVersionSupport();
        Boolean vectorSupported = (Boolean) versionInfo.get("vectorSearchSupported");

        if (!vectorSupported) {
            throw new UnsupportedOperationException("当前Elasticsearch版本不支持向量搜索功能，请升级到7.3+版本");
        }

        // 默认实现：返回空列表
        // 子类可以重写此方法来实现真正的向量搜索
        log.warn("vectorSearch方法未实现，返回空结果");
        return new ArrayList<>();
    }

    @Override
    public List<T> hybridSearch(String keyword, String[] keywordFields, String vectorField,
                               float keywordWeight, float vectorWeight, float threshold, int size) {
        // 检查ES版本是否支持向量搜索
        Map<String, Object> versionInfo = checkVersionSupport();
        Boolean vectorSupported = (Boolean) versionInfo.get("vectorSearchSupported");

        if (!vectorSupported) {
            // 如果不支持向量搜索，则只进行关键词搜索
            log.warn("当前ES版本不支持向量搜索，仅执行关键词搜索");
            EsQueryWrapper<T> queryWrapper = queryWrapper();
            if (keywordFields != null && keywordFields.length > 0) {
                queryWrapper.multiMatch(keyword, keywordFields);
            } else {
                queryWrapper.queryString(keyword);
            }
            queryWrapper.size(size);
            return list(queryWrapper);
        }

        // 默认实现：返回关键词搜索结果
        // 子类可以重写此方法来实现真正的混合搜索
        log.warn("hybridSearch方法未完全实现，仅返回关键词搜索结果");
        EsQueryWrapper<T> queryWrapper = queryWrapper();
        if (keywordFields != null && keywordFields.length > 0) {
            queryWrapper.multiMatch(keyword, keywordFields);
        } else {
            queryWrapper.queryString(keyword);
        }
        queryWrapper.size(size);
        return list(queryWrapper);
    }

    @Override
    public List<T> findSimilarEntities(String entityId, String vectorField, float threshold, int size) {
        // 检查ES版本是否支持向量搜索
        Map<String, Object> versionInfo = checkVersionSupport();
        Boolean vectorSupported = (Boolean) versionInfo.get("vectorSearchSupported");

        if (!vectorSupported) {
            throw new UnsupportedOperationException("当前Elasticsearch版本不支持向量搜索功能，请升级到7.3+版本");
        }

        // 默认实现：返回空列表
        // 子类可以重写此方法来实现真正的相似度搜索
        log.warn("findSimilarEntities方法未实现，返回空结果");
        return new ArrayList<>();
    }

    // ==================== 系统信息 ====================

    @Override
    public Map<String, Object> getSystemInfo() {
        Map<String, Object> systemInfo = new HashMap<>();

        try {
            // 获取集群信息
            systemInfo.put("indexName", indexName);
            systemInfo.put("entityClass", entityClass.getSimpleName());
            systemInfo.put("indexExists", indexExists());

            // 获取文档统计
            systemInfo.put("documentCount", count());

            // 获取版本支持信息
            systemInfo.putAll(checkVersionSupport());

            systemInfo.put("timestamp", getCurrentTimeString());
            systemInfo.put("status", "healthy");

        } catch (Exception e) {
            log.error("获取系统信息失败", e);
            systemInfo.put("status", "error");
            systemInfo.put("error", e.getMessage());
        }

        return systemInfo;
    }

    @Override
    public Map<String, Object> checkVersionSupport() {
        Map<String, Object> versionInfo = new HashMap<>();

        try {
            // 模拟版本检查（实际应该调用ES的info API）
            // 这里假设ES版本支持基本功能
            versionInfo.put("elasticsearchVersion", "8.x");
            versionInfo.put("basicSearchSupported", true);
            versionInfo.put("aggregationSupported", true);
            versionInfo.put("highlightSupported", true);
            versionInfo.put("scrollSupported", true);
            versionInfo.put("suggestSupported", true);

            // 向量搜索支持（ES 7.3+）
            versionInfo.put("vectorSearchSupported", true);
            versionInfo.put("denseVectorSupported", true);
            versionInfo.put("knnSearchSupported", true);

            // 其他高级功能
            versionInfo.put("machineLearningSupported", true);
            versionInfo.put("transformSupported", true);
            versionInfo.put("sqlSupported", true);

            versionInfo.put("checkTime", getCurrentTimeString());

        } catch (Exception e) {
            log.error("检查版本支持失败", e);
            versionInfo.put("error", e.getMessage());

            // 保守估计：只支持基本功能
            versionInfo.put("basicSearchSupported", true);
            versionInfo.put("vectorSearchSupported", false);
            versionInfo.put("errorMessage", "无法获取ES版本信息，建议检查连接");
        }

        return versionInfo;
    }
}