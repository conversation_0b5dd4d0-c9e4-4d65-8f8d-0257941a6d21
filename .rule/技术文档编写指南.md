---
title: 技术文档编写指南
description: 基于实践总结的技术文档编写最佳实践和规范
---

# 技术文档编写指南

## 前言

本指南基于实际项目中的文档编写经验总结，旨在规范技术文档的编写流程，避免重复创建文档，提高文档质量和维护效率。

## 核心原则

### 1. 一个主题一个文档

**原则**：每个技术主题只应有一个主要文档，避免信息分散和重复。

**实践**：
- ✅ 正确：`elasticsearch-usage-guide.mdx` 作为 Elasticsearch 的唯一使用指南
- ❌ 错误：同时存在 `elasticsearch-usage-guide.mdx`、`elasticsearch-service-correction.md`、`elasticsearch-configuration-fix.md` 等多个文档

### 2. 组件文档完整性要求

**强制要求**：每个技术组件必须至少包含两类文档：

1. **架构文档** (`docs/architecture/module-architecture.mdx`)
   - 技术架构设计
   - 设计原理和思想
   - 核心组件说明
   - 技术选型理由
   - 架构图和流程图

2. **使用指南** (`docs/guides/module-usage-guide.mdx`)
   - 快速开始指南
   - 配置方法详解
   - API 使用说明
   - 常见问题解决
   - 最佳实践建议

**检查标准**：
- [ ] 每个 starter 模块都有对应的架构文档
- [ ] 每个 starter 模块都有对应的使用指南
- [ ] 文档内容完整且准确
- [ ] 架构图清晰易懂

### 3. 文档分层结构

**架构层次**：
```
docs/
├── architecture/          # 架构设计文档
│   └── module-architecture.mdx
├── guides/               # 用户使用指南
│   └── module-usage-guide.mdx
├── tutorials/            # 技术教程
│   └── 15min-module.mdx
└── changelog/            # 开发日志
    └── module-changelog.md
```

**内容分工**：
- **architecture**：框架如何设计，技术架构，设计原理
- **guides**：用户如何使用，配置方法，API 调用
- **tutorials**：技术介绍，快速入门，最佳实践
- **changelog**：代码变更记录，功能添加与问题修复

### 4. 文档更新而非新建

**原则**：优先更新现有文档，而非创建新文档。

**实践**：
- ✅ 正确：在现有文档中添加"配置问题修正"章节
- ❌ 错误：创建单独的 `configuration-fix.md` 文档

## 文档编写流程

### 1. 文档编写思维链

**第一步：编写文档目录**
```markdown
# 模块名称架构文档/使用指南

## 1. 概述
## 2. 核心功能
## 3. 技术实现
## 4. 使用方法
## 5. 最佳实践
## 6. 常见问题
## 7. 参考资料
```

**第二步：编写文档内容**
- 按照目录结构逐章节编写
- 确保每个章节内容完整
- 添加必要的代码示例和配置
- 绘制架构图和流程图

**第三步：完善文档和更新链接**
- 检查文档内容准确性
- 更新相关文档的交叉引用
- 添加到导航文件 `_navigation.mdx`
- 更新 README 文件的链接

### 2. 架构图绘制规范

**优先使用 Mermaid 图表**：
```markdown
```mermaid
graph TD
    A[用户请求] --> B[Gateway]
    B --> C[Service]
    C --> D[Database]
```

**Mermaid 图表提示词**：
```
请使用 Mermaid 绘制架构图，要求：
1. 使用合适的图表类型（flowchart、sequence、class等）
2. 节点命名清晰，使用中文标签
3. 连接线表示数据流向或调用关系
4. 颜色搭配协调，重要节点突出显示
5. 图表布局合理，易于理解
```

**SVG 图表（复杂架构）**：
- 用于复杂的系统架构图
- 使用矢量格式，支持缩放
- 文件命名：`module-architecture.svg`
- 存放位置：`docs/images/`

**SVG 绘制提示词**：
```
请绘制 SVG 架构图，要求：
1. 使用标准的架构图符号
2. 组件分层清晰（展示层、业务层、数据层）
3. 连接线表示依赖关系和数据流
4. 颜色区分不同类型的组件
5. 添加必要的文字说明
6. 确保在不同尺寸下都清晰可读
```

## 文档编写规范

### 1. 文档结构模板

```markdown
---
title: 模块名称使用指南
description: 简洁的描述
---

# 模块名称使用指南

## 1. 快速开始
### 1.1 引入依赖
### 1.2 基础配置
### 1.3 基本使用

## 2. 核心功能
### 2.1 功能A
### 2.2 功能B

## 3. 高级特性
### 3.1 高级配置
### 3.2 性能优化

## 4. 常见问题
### 4.1 配置问题
### 4.2 使用问题
### 4.3 故障排查

## 5. 最佳实践

## 6. 参考资料
```

### 2. 配置示例规范

**完整性**：提供完整的配置示例
```yaml
# ✅ 正确：完整配置
module:
  enabled: true
  host: localhost
  port: 8080
  timeout: 5000  # 明确单位：毫秒
  pool:
    max-size: 100
```

**准确性**：确保配置与代码实现一致
```yaml
# ❌ 错误：与Properties类不匹配
module:
  connect-timeout: "10s"  # Properties期望int类型

# ✅ 正确：与Properties类匹配
module:
  connect-timeout: 10000  # int类型，毫秒
```

**绝对准确性要求**：
- 所有包路径必须与实际代码一致
- 所有类名和方法名必须准确无误
- 配置属性名称和类型必须匹配Properties类
- 依赖版本号必须与pom.xml一致
- 禁止出现任何虚构的包名或类名

### 3. 代码示例规范

**完整性**：包含必要的导入和异常处理
```java
// ✅ 正确：完整示例
import com.trinasolar.module.Service;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class ExampleService {
    
    @Autowired
    private ModuleService moduleService;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    public void example() {
        try {
            // 具体实现
            String result = moduleService.actualMethod("param");
        } catch (Exception e) {
            log.error("操作失败", e);
        }
    }
}
```

**准确性**：使用实际存在的方法
```java
// ❌ 错误：使用不存在的方法
moduleService.save(document);

// ✅ 正确：使用实际方法
moduleService.indexDocument("index", "id", documentJson);
```

**代码准确性检查清单**：
- [ ] 包路径与实际项目结构一致
- [ ] 类名在对应包中确实存在
- [ ] 方法名和参数与实际API匹配
- [ ] 注解使用正确（@Autowired、@Service等）
- [ ] 异常处理使用项目中实际的异常类
- [ ] 配置类属性与Properties类完全对应

## 文档维护流程

### 1. 问题发现

当发现文档问题时：
1. **评估影响范围**：是局部错误还是系统性问题
2. **确定修正方式**：更新现有文档 vs 重构文档结构
3. **制定修正计划**：优先级和修正顺序

### 2. 文档修正

**优先级顺序**：
1. 修正现有文档中的错误信息
2. 补充缺失的重要信息
3. 优化文档结构和可读性
4. 添加最佳实践和注意事项

**修正原则**：
- 保持文档的完整性和连贯性
- 确保所有示例都能正常运行
- 添加必要的警告和注意事项
- 提供问题排查指导

### 3. 质量检查

**内容检查**：
- [ ] 配置示例与代码实现一致
- [ ] 方法调用使用实际存在的API
- [ ] 包路径和类名正确
- [ ] 异常处理完整

**结构检查**：
- [ ] 章节层次清晰
- [ ] 内容逻辑连贯
- [ ] 代码格式规范
- [ ] 链接引用正确

## 常见问题和解决方案

### 1. 配置不一致问题

**问题**：文档中的配置示例与实际Properties类不匹配

**解决方案**：
1. 查看实际的Properties类定义
2. 确认配置属性的类型和命名
3. 提供准确的配置示例
4. 添加配置说明和注意事项

### 2. API调用错误问题

**问题**：文档中使用了不存在的方法

**解决方案**：
1. 查看实际的Service类实现
2. 确认可用的公共方法
3. 更新文档中的方法调用
4. 提供完整的使用示例

### 3. 文档分散问题

**问题**：同一主题存在多个文档

**解决方案**：
1. 合并相关内容到主文档
2. 删除重复和过时的文档
3. 建立清晰的文档导航
4. 添加文档间的交叉引用

### 4. MDX语法错误问题

**问题**：MDX文档在构建时出现语法解析错误，特别是涉及到JavaScript表达式的部分

**常见错误类型**：
1. 花括号 `{}` 被误解为JavaScript表达式
2. 美元符号 `$` 和模板字符串 `${...}` 被误解为变量引用
3. 特定变量名（如 `id`）被解析为未定义变量

**解决方案**：
1. **路径参数处理**：
   - ✅ 使用 `/:id` 代替 `/{id}` 表示路径参数
   - ✅ 使用反引号包裹含有变量的代码示例
   
2. **花括号转义**：
   - ✅ 使用 `{'{'}{'}'}` 来显示花括号字符
   - ❌ 避免直接使用未转义的 `{variable}` 格式
   
3. **美元符号处理**：
   - ✅ 在代码块中使用 `\$` 转义美元符号
   - ✅ 对于模板字符串，使用 `\${variable}` 转义
   
4. **代码块与表格**：
   - ✅ 在表格和代码块之间保留空行
   - ✅ 确保代码块使用正确的语言标识符（如 ```java）
   
5. **构建测试**：
   - ✅ 修改后立即运行构建测试
   - ✅ 检查构建错误信息，定位具体问题行号

**实际案例**：
```markdown
<!-- ❌ 错误：会被解析为JavaScript表达式 -->
| 操作 | 路径 |
|------|------|
| 获取用户 | /users/{id} |

<!-- ✅ 正确：使用冒号表示路径参数 -->
| 操作 | 路径 |
|------|------|
| 获取用户 | /users/:id |

<!-- ✅ 正确：转义花括号 -->
| 操作 | 路径 |
|------|------|
| 获取用户 | /users/{'{'}{'}'}id{'{'}{'}'}  |
```

**错误排查流程**：
1. 查看构建错误信息，特别关注 `ReferenceError: xxx is not defined` 类型错误
2. 定位问题文件和行号
3. 检查该行是否包含可能被解析为JavaScript表达式的内容
4. 使用适当的转义方法修复问题
5. 重新运行构建验证修复效果

## 技术文档编写提示词

### 核心提示词

```
作为技术文档编写专家，请遵循以下原则：

1. **组件完整性原则**：每个技术组件必须有架构文档和使用指南两类文档
2. **唯一性原则**：每个技术主题只维护一个主要文档，避免信息分散
3. **绝对准确性原则**：所有配置和代码示例必须与实际实现完全一致，禁止虚构
4. **完整性原则**：提供从配置到使用的完整流程，包含异常处理
5. **实用性原则**：所有示例都应该能够直接运行，解决实际问题
6. **维护性原则**：优先更新现有文档，而非创建新文档
7. **流程化原则**：按照"目录-内容-链接"的思维链编写文档
8. **MDX兼容性原则**：注意MDX语法特性，正确转义特殊字符和表达式

编写流程：
第一步：编写文档目录结构
第二步：按目录逐章节编写内容
第三步：完善文档并更新相关链接
第四步：检查MDX语法兼容性，特别是花括号和美元符号

编写时请：
- 首先检查现有文档，确定是更新还是新建
- 验证所有包路径、类名、方法名与实际代码一致
- 确认所有配置属性与Properties类完全匹配
- 提供完整的导入语句和异常处理
- 使用Mermaid或SVG绘制清晰的架构图
- 添加必要的注意事项和最佳实践
- 包含问题排查和解决方案
- 更新导航文件和相关文档链接
- 注意MDX特殊语法，正确转义花括号和美元符号
```

### 架构图绘制提示词

**Mermaid 架构图提示词**：
```
请使用Mermaid绘制[模块名称]的架构图，要求：
1. 图表类型：根据需要选择flowchart、sequence、class或component
2. 节点设计：使用清晰的中文标签，体现组件职责
3. 连接关系：用箭头表示数据流向、调用关系或依赖关系
4. 分层结构：明确展示不同层次（如Controller、Service、Repository）
5. 颜色编码：用不同颜色区分组件类型（如外部系统、核心组件、数据存储）
6. 布局优化：确保图表清晰易读，避免线条交叉
7. 关键路径：突出显示主要的业务流程路径
```

**SVG 架构图提示词**：
```
请绘制[模块名称]的SVG架构图，要求：
1. 标准符号：使用业界标准的架构图符号和图标
2. 分层清晰：明确区分展示层、业务层、数据层、基础设施层
3. 组件详细：每个组件包含名称、主要职责说明
4. 连接线条：用不同样式的线条表示不同类型的关系
5. 颜色方案：采用专业的配色方案，确保可读性
6. 尺寸适配：支持不同尺寸显示，保持清晰度
7. 文字说明：在关键位置添加简要的技术说明
8. 矢量格式：确保可以无损缩放
```

### 检查清单

编写完成后，请检查：

**技术准确性**：
- [ ] 配置属性名称和类型正确
- [ ] 方法调用使用实际API
- [ ] 包路径和类名准确
- [ ] 示例代码可以运行
- [ ] 依赖版本与pom.xml一致
- [ ] 注解使用正确
- [ ] 异常处理完整
- [ ] 架构图与实际架构匹配

**文档完整性**：
- [ ] 每个组件都有架构文档和使用指南
- [ ] 包含完整的使用流程
- [ ] 提供异常处理示例
- [ ] 包含常见问题解决方案
- [ ] 添加最佳实践建议
- [ ] 架构图清晰完整
- [ ] 导航链接已更新

**维护友好性**：
- [ ] 文档结构清晰
- [ ] 内容逻辑连贯
- [ ] 易于后续更新
- [ ] 避免重复信息

## 总结

技术文档的核心价值在于帮助用户快速、准确地使用技术组件。通过遵循"一个主题一个文档"的原则，确保配置和代码示例的准确性，提供完整的使用流程，我们可以创建高质量、易维护的技术文档。

记住：**好的文档不是写得多，而是写得对、写得全、写得清楚。每个组件都需要完整的文档体系，绝对不能出现技术细节错误。**