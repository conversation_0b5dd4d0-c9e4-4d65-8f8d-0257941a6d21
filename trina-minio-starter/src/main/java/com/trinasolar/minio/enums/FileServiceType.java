package com.trinasolar.minio.enums;

import java.util.Arrays;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName FileServiceType
 * @Description 文件服务类型枚举
 * <AUTHOR> cascade
 * @Date 2021/12/23, 2023/11/01 (更新)
 **/
@Getter
@AllArgsConstructor
public enum FileServiceType {
    /**
     * Minio服务类型
     */
    MINIO("minio", "minioTemplate"),
    
    /**
     * NAS服务类型
     */
    NAS("nas", "nasTemplate"),
    
    /**
     * S3协议服务类型
     */
    S3("s3", "s3Template"),
    ;
    
    /**
     * 服务类型标识
     */
    private final String type;
    
    /**
     * Spring Bean名称
     */
    private final String serviceName;

    /**
     * 根据类型获取对应的服务名称
     *
     * @param type 服务类型
     * @return 服务名称
     */
    public static String getServiceNameByType(String type) {
        return Arrays.stream(values())
            .filter(fileServiceType -> StringUtils.equals(type, fileServiceType.type))
            .map(FileServiceType::getServiceName)
            .findFirst()
            .get();
    }
}