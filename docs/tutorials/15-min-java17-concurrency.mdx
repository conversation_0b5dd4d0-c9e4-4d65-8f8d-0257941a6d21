---
title: "15分钟了解Java 17并发编程"
description: "快速掌握Java 17并发编程模型、最佳实践及虚拟线程前瞻"
date: "2024-05-22"
tags: ["Java", "Java 17", "并发编程", "多线程", "虚拟线程"]
---

# 15分钟了解Java 17并发编程

## 🔍 技术简介 (3分钟)

### Java中的并发编程演进

Java自诞生之日起就支持多线程编程，是最早内置并发支持的主流编程语言之一。随着硬件和应用架构的发展，Java的并发模型也在不断演进：

- **JDK 1.0-1.4**：基础的`Thread`类和`synchronized`关键字
- **JDK 5.0**：引入`java.util.concurrent`包，提供线程池、原子变量和并发集合
- **JDK 7**：增强`ForkJoinPool`用于并行计算
- **JDK 8**：引入`CompletableFuture`和并行流
- **JDK 9-16**：持续改进现有API
- **JDK 17**：作为LTS版本，优化并发工具性能和可用性
- **JDK 19-21**：引入革命性的虚拟线程（Project Loom）

### Java 17并发编程现状

Java 17作为一个长期支持版本，提供了一套成熟且高性能的并发编程工具集。虽然革命性的虚拟线程特性在Java 17中尚未出现（它在Java 19中作为预览特性首次引入，并在Java 21中正式发布），但Java 17已经提供了强大的并发支持：

1. **强大的线程池**：通过`Executors`框架实现高效线程管理
2. **丰富的同步工具**：锁、信号量、栅栏、计数器等同步原语
3. **无锁数据结构**：原子变量和并发集合
4. **异步编程模型**：`CompletableFuture`支持组合式异步编程
5. **结构化并发雏形**：为未来的结构化并发API奠定基础

### 核心概念

- **线程**：Java中的线程是轻量级进程，是执行的最小单元
- **同步**：协调多个线程对共享资源的访问
- **并发**：多个任务交替执行
- **并行**：多个任务同时执行
- **死锁**：两个或多个线程互相等待对方持有的资源
- **线程安全**：多线程环境下正确执行的代码
- **阻塞与非阻塞**：线程执行时是否需要等待

## 💡 核心特性 (5分钟)

### 1. 线程池与执行器框架

Java 17的`java.util.concurrent`包提供了完善的线程池实现，帮助开发者管理线程生命周期：

```java
// 创建固定大小的线程池
ExecutorService fixedPool = Executors.newFixedThreadPool(10);

// 创建缓存线程池（按需创建线程）
ExecutorService cachedPool = Executors.newCachedThreadPool();

// 创建单线程执行器
ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();

// 创建支持定时和周期任务的线程池
ScheduledExecutorService scheduledPool = Executors.newScheduledThreadPool(5);

// 创建并行工作窃取池
ExecutorService workStealingPool = Executors.newWorkStealingPool();

// 提交任务
Future<String> future = fixedPool.submit(() -> {
    // 执行耗时操作
    Thread.sleep(1000);
    return "任务完成";
});

// 获取结果（阻塞）
String result = future.get();
```

Java 17中`ExecutorService`接口增加了`close()`方法，实现了`AutoCloseable`接口，可以使用try-with-resources语法自动关闭：

```java
try (ExecutorService executor = Executors.newFixedThreadPool(10)) {
    // 使用执行器
    executor.submit(() -> System.out.println("任务执行中"));
    // 自动关闭执行器
}
```

### 2. 并发集合

Java 17提供了丰富的线程安全集合类，用于多线程环境下的数据处理：

```java
// 线程安全的List
List<String> syncList = Collections.synchronizedList(new ArrayList<>());
List<String> concurrentList = new CopyOnWriteArrayList<>();

// 线程安全的Map
Map<String, Integer> concurrentMap = new ConcurrentHashMap<>();

// 线程安全的Set
Set<String> concurrentSet = new ConcurrentHashMap<String, Boolean>().keySet();
Set<String> copyOnWriteSet = new CopyOnWriteArraySet<>();

// 线程安全的Queue
Queue<String> concurrentQueue = new ConcurrentLinkedQueue<>();
BlockingQueue<String> blockingQueue = new LinkedBlockingQueue<>();
```

### 3. 原子变量与无锁编程

通过`java.util.concurrent.atomic`包中的原子类，可以实现无锁的线程安全操作：

```java
// 原子整数
AtomicInteger counter = new AtomicInteger(0);
counter.incrementAndGet(); // 原子+1
counter.addAndGet(5);      // 原子+5

// 原子引用
AtomicReference<User> userRef = new AtomicReference<>(new User("初始用户"));
userRef.compareAndSet(oldUser, newUser); // CAS操作

// 原子数组
AtomicIntegerArray atomicArray = new AtomicIntegerArray(10);
atomicArray.getAndIncrement(5); // 索引5的元素原子+1

// 原子字段更新器
AtomicReferenceFieldUpdater<User, Address> updater = 
    AtomicReferenceFieldUpdater.newUpdater(User.class, Address.class, "address");
updater.compareAndSet(user, oldAddress, newAddress);
```

### 4. CompletableFuture异步编程

`CompletableFuture`提供了强大的异步编程模型，支持链式操作和组合：

```java
CompletableFuture<String> future1 = CompletableFuture.supplyAsync(() -> {
    // 异步执行任务1
    return "结果1";
});

CompletableFuture<String> future2 = CompletableFuture.supplyAsync(() -> {
    // 异步执行任务2
    return "结果2";
});

// 组合两个异步操作
CompletableFuture<String> combined = future1.thenCombine(future2, 
    (result1, result2) -> result1 + " + " + result2);

// 添加异步转换
CompletableFuture<Integer> transformed = combined.thenApply(String::length);

// 添加完成后的回调
transformed.thenAccept(length -> System.out.println("结果长度: " + length));

// 异常处理
transformed.exceptionally(ex -> {
    System.err.println("发生错误: " + ex.getMessage());
    return -1;
});

// 等待所有任务完成
CompletableFuture.allOf(future1, future2).join();
```

### 5. 同步工具类

Java 17提供了多种同步工具，用于线程间协调：

```java
// 可重入锁
ReentrantLock lock = new ReentrantLock();
lock.lock();
try {
    // 临界区
} finally {
    lock.unlock();
}

// 读写锁
ReadWriteLock rwLock = new ReentrantReadWriteLock();
rwLock.readLock().lock();  // 多个线程可以同时获取读锁
rwLock.writeLock().lock(); // 写锁是独占的

// 条件变量
Condition condition = lock.newCondition();
condition.await();     // 等待信号
condition.signal();    // 发送信号给一个等待的线程

// 信号量
Semaphore semaphore = new Semaphore(5); // 允许5个并发访问
semaphore.acquire();   // 获取许可
semaphore.release();   // 释放许可

// 倒计时门闩
CountDownLatch latch = new CountDownLatch(3); // 需要3个信号
latch.countDown();     // 递减计数
latch.await();         // 等待计数到0

// 循环栅栏
CyclicBarrier barrier = new CyclicBarrier(3); // 3个线程同步点
barrier.await();       // 等待所有线程到达
```

## 📊 应用场景 (3分钟)

### 1. Web应用服务器

在处理HTTP请求时，每个请求通常需要处理数据库查询、文件操作或调用其他服务，这些操作都可能阻塞线程。Java 17的并发框架提供了有效管理大量并发请求的解决方案：

```java
// Web服务器处理请求的伪代码
ExecutorService executor = Executors.newFixedThreadPool(100);

server.onRequest(request -> {
    executor.submit(() -> {
        try {
            // 1. 解析请求
            RequestData data = parseRequest(request);
            
            // 2. 异步调用数据库
            CompletableFuture<UserData> userFuture = 
                CompletableFuture.supplyAsync(() -> database.findUser(data.getUserId()));
            
            // 3. 异步调用外部服务
            CompletableFuture<ProductData> productFuture = 
                CompletableFuture.supplyAsync(() -> externalService.getProduct(data.getProductId()));
            
            // 4. 组合结果
            CompletableFuture<Response> responseFuture = userFuture.thenCombine(
                productFuture, (user, product) -> createResponse(user, product));
            
            // 5. 发送响应
            responseFuture.thenAccept(response -> request.complete(response));
        } catch (Exception e) {
            request.completeExceptionally(e);
        }
    });
});
```

### 2. 大数据处理

处理大量数据时，可以利用并行流或自定义并行算法提高处理效率：

```java
// 使用并行流处理大数据
List<Transaction> transactions = loadLargeTransactionList();

// 并行统计分析
Map<String, Double> totalByCategory = transactions.parallelStream()
    .collect(Collectors.groupingBy(
        Transaction::getCategory,
        Collectors.summingDouble(Transaction::getAmount)
    ));

// 自定义并行任务
class TransactionProcessor extends RecursiveTask<Map<String, Double>> {
    private final List<Transaction> transactions;
    private final int threshold = 1000;
    
    // 使用ForkJoinPool处理
    // ...
}

ForkJoinPool pool = ForkJoinPool.commonPool();
Map<String, Double> result = pool.invoke(new TransactionProcessor(transactions));
```

### 3. 实时监控系统

监控系统需要同时处理多个数据源的输入，并在数据变化时快速响应：

```java
// 实时监控系统的伪代码
ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(5);

// 定期收集数据
scheduler.scheduleAtFixedRate(() -> {
    CompletableFuture.supplyAsync(() -> collectMetrics())
        .thenAccept(metrics -> updateDashboard(metrics))
        .exceptionally(ex -> {
            logError("指标收集失败", ex);
            return null;
        });
}, 0, 5, TimeUnit.SECONDS);

// 设置阈值监控
ConcurrentMap<String, Double> thresholds = new ConcurrentHashMap<>();
BlockingQueue<Alert> alertQueue = new LinkedBlockingQueue<>();

// 警报处理线程
Thread alertProcessor = new Thread(() -> {
    while (!Thread.currentThread().isInterrupted()) {
        try {
            Alert alert = alertQueue.take();
            notifyAdmin(alert);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
});
alertProcessor.start();
```

### 4. 微服务通信

微服务架构中，服务间通信常常需要并发处理多个请求/响应：

```java
// 微服务调用的伪代码
CompletableFuture<CustomerData> getCustomerData(String customerId) {
    return CompletableFuture.supplyAsync(() -> {
        // 调用客户服务
        return customerClient.getCustomer(customerId);
    });
}

CompletableFuture<List<OrderData>> getCustomerOrders(String customerId) {
    return CompletableFuture.supplyAsync(() -> {
        // 调用订单服务
        return orderClient.getOrdersByCustomer(customerId);
    });
}

// 合并请求
CompletableFuture<CustomerProfile> getCustomerProfile(String customerId) {
    CompletableFuture<CustomerData> customerFuture = getCustomerData(customerId);
    CompletableFuture<List<OrderData>> ordersFuture = getCustomerOrders(customerId);
    
    return customerFuture.thenCombine(ordersFuture, (customer, orders) -> {
        return new CustomerProfile(customer, orders);
    });
}
```

## 🚀 快速入门 (4分钟)

### 基础线程操作

Java 17基本的线程创建和使用：

```java
// 创建线程
Thread thread = new Thread(() -> {
    System.out.println("线程执行中: " + Thread.currentThread().getName());
    try {
        Thread.sleep(1000);
    } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
    }
    System.out.println("线程执行完成");
});

// 设置线程名称
thread.setName("worker-thread");

// 启动线程
thread.start();

// 等待线程完成
thread.join();
```

### 线程池最佳实践

高效使用线程池的关键点：

```java
// 创建自定义线程池
ThreadPoolExecutor executor = new ThreadPoolExecutor(
    5,                      // 核心线程数
    10,                     // 最大线程数
    60, TimeUnit.SECONDS,   // 空闲线程存活时间
    new ArrayBlockingQueue<>(100),  // 工作队列
    new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
);

// 自定义线程工厂
executor.setThreadFactory(r -> {
    Thread t = new Thread(r);
    t.setName("custom-worker-" + t.getId());
    t.setUncaughtExceptionHandler((thread, ex) -> {
        System.err.println("线程 " + thread.getName() + " 发生未捕获异常: " + ex.getMessage());
    });
    return t;
});

// 提交任务
for (int i = 0; i < 20; i++) {
    final int taskId = i;
    executor.submit(() -> {
        System.out.println("执行任务 " + taskId + " 在线程 " + Thread.currentThread().getName());
        return "任务 " + taskId + " 结果";
    });
}

// 关闭线程池
executor.shutdown();
if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
    executor.shutdownNow();
}
```

### 异步任务组合

使用`CompletableFuture`处理复杂的异步任务流程：

```java
// 模拟API调用
CompletableFuture<String> getUserData(long userId) {
    return CompletableFuture.supplyAsync(() -> {
        // 模拟API调用延迟
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        return "User:" + userId;
    });
}

CompletableFuture<List<String>> getUserPosts(String userData) {
    return CompletableFuture.supplyAsync(() -> {
        // 模拟API调用延迟
        try {
            Thread.sleep(700);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        return List.of("Post1 by " + userData, "Post2 by " + userData);
    });
}

CompletableFuture<List<String>> getPostComments(String post) {
    return CompletableFuture.supplyAsync(() -> {
        // 模拟API调用延迟
        try {
            Thread.sleep(300);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        return List.of("Comment1 on " + post, "Comment2 on " + post);
    });
}

// 组合调用
CompletableFuture<List<String>> allComments = getUserData(123)
    .thenCompose(userData -> getUserPosts(userData))
    .thenCompose(posts -> {
        List<CompletableFuture<List<String>>> commentFutures = 
            posts.stream()
                .map(post -> getPostComments(post))
                .collect(Collectors.toList());
                
        return CompletableFuture.allOf(
                commentFutures.toArray(new CompletableFuture[0])
            )
            .thenApply(v -> commentFutures.stream()
                .flatMap(f -> f.join().stream())
                .collect(Collectors.toList())
            );
    });

// 获取结果
List<String> comments = allComments.join();
comments.forEach(System.out::println);
```

### 并发编程模式与实践

几种常见的并发编程模式：

```java
// 1. 生产者-消费者模式
class ProducerConsumerExample {
    private final BlockingQueue<String> queue = new LinkedBlockingQueue<>(10);
    
    public void producer() {
        try {
            for (int i = 0; i < 20; i++) {
                queue.put("Item-" + i);
                System.out.println("生产: Item-" + i);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    public void consumer() {
        try {
            while (true) {
                String item = queue.take();
                System.out.println("消费: " + item);
                
                if (item.equals("Item-19")) {
                    break;
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    public void start() {
        new Thread(this::producer).start();
        new Thread(this::consumer).start();
    }
}

// 2. 读写锁模式
class ReadWriteExample {
    private final Map<String, String> data = new HashMap<>();
    private final ReadWriteLock lock = new ReentrantReadWriteLock();
    
    public String read(String key) {
        lock.readLock().lock();
        try {
            return data.get(key);
        } finally {
            lock.readLock().unlock();
        }
    }
    
    public void write(String key, String value) {
        lock.writeLock().lock();
        try {
            data.put(key, value);
        } finally {
            lock.writeLock().unlock();
        }
    }
}

// 3. 工作窃取模式
class WorkStealingExample {
    public void process(List<String> items) {
        ForkJoinPool pool = ForkJoinPool.commonPool();
        pool.invoke(new ProcessTask(items, 0, items.size()));
    }
    
    static class ProcessTask extends RecursiveAction {
        private final List<String> items;
        private final int start;
        private final int end;
        private static final int THRESHOLD = 10;
        
        public ProcessTask(List<String> items, int start, int end) {
            this.items = items;
            this.start = start;
            this.end = end;
        }
        
        @Override
        protected void compute() {
            if (end - start <= THRESHOLD) {
                // 处理小批量数据
                for (int i = start; i < end; i++) {
                    System.out.println("处理: " + items.get(i));
                }
            } else {
                // 拆分任务
                int mid = start + (end - start) / 2;
                invokeAll(
                    new ProcessTask(items, start, mid),
                    new ProcessTask(items, mid, end)
                );
            }
        }
    }
}
```

### 虚拟线程前瞻 (Java 19+)

尽管虚拟线程不是Java 17的一部分，但作为学习参考，这里简要介绍Java 21中虚拟线程的用法：

```java
// Java 21的虚拟线程
// 创建并启动单个虚拟线程
Thread vThread = Thread.startVirtualThread(() -> {
    System.out.println("这是一个虚拟线程: " + Thread.currentThread());
});

// 使用线程构建器
Thread.Builder builder = Thread.ofVirtual().name("vt-worker-");
Thread vt = builder.start(() -> {
    System.out.println("自定义名称的虚拟线程: " + Thread.currentThread().getName());
});

// 创建虚拟线程执行器
try (var executor = Executors.newVirtualThreadPerTaskExecutor()) {
    // 提交大量任务，每个任务都在自己的虚拟线程中运行
    IntStream.range(0, 10_000).forEach(i -> {
        executor.submit(() -> {
            Thread.sleep(100); // 虚拟线程可以高效处理阻塞操作
            return i;
        });
    });
    // 执行器关闭时会等待所有任务完成
}
```

## 总结

Java 17提供了一套完善的并发编程工具，能够满足大多数高并发应用场景的需求。尽管革命性的虚拟线程特性在Java 17中尚未引入（它在Java 19中首次预览，并在Java 21中正式发布），但Java 17的并发框架已经足够强大和高效。

通过合理使用线程池、并发集合、原子变量、CompletableFuture和同步工具，开发者可以构建高性能、可扩展的并发应用。同时，了解未来Java并发的发展方向（如虚拟线程），有助于规划应用的长期技术路线。

在实际开发中，应注重线程安全性、避免死锁、合理处理异常，并根据应用的特点选择合适的并发模式和工具。无论是Web服务器、大数据处理、实时监控系统还是微服务架构，Java 17的并发框架都能提供可靠的支持。 