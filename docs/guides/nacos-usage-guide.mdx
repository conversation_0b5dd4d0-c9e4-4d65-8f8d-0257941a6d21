---
title: <PERSON>cos Starter 使用指南
description: Trina Nacos Starter 的快速开始、配置方法与常见问题
---

# 1. 快速开始

## 1.1 引入依赖
在 `pom.xml` 中添加如下依赖：
```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-nacos-starter</artifactId>
    <version>4.0.0</version>
</dependency>
```

## 1.2 基础配置
在 `application.yml` 中配置应用名和环境：
```yaml
spring:
  application:
    name: your-application-name  # 必须配置
  profiles:
    active: dev  # 可选，默认为dev
```

## 1.3 nacos.yaml 示例
在 `src/main/resources/nacos.yaml` 中：
```yaml
spring:
  application:
    group: ${NACOS_GROUP:DEFAULT_GROUP}
    namespace: ${spring.application.name}-${spring.profiles.active}
  config:
    import:
      - optional:nacos:application-${spring.profiles.active:dev}.yaml
      - optional:nacos:${spring.application.name:application}-${spring.profiles.active:dev}.yaml
  cloud:
    nacos:
      username: ${NACOS_USERNAME:nacos}
      password: ${NACOS_PASSWORD:nacos}
      discovery:
        server-addr: ${NACOS_HOST:127.0.0.1}:${NACOS_PORT:8848}
        namespace: ${spring.application.namespace:public}
        group: ${spring.application.group:DEFAULT_GROUP}
        enabled: true
        register-enabled: true
        heart-beat-interval: 5000
        heart-beat-timeout: 15000
        ip-delete-timeout: 30000
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ${spring.cloud.nacos.discovery.namespace}
        group: ${spring.cloud.nacos.discovery.group}
        file-extension: ${NACOS_FILE_EXTENSION:yaml}
        refresh-enabled: true
        timeout: 3000
        max-retry: 10
        config-long-poll-timeout: 46000
        config-retry-time: 2333
        enable-remote-sync-config: false
        extension-configs:
          - data-id: ${spring.application.name:application}-${spring.profiles.active:dev}.${spring.cloud.nacos.config.file-extension:yaml}
            group: ${spring.application.group:DEFAULT_GROUP}
            refresh: true
        shared-configs:
          - data-id: common-config-${spring.profiles.active:dev}.${spring.cloud.nacos.config.file-extension:yaml}
            group: ${spring.application.group:DEFAULT_GROUP}
            refresh: true
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
```

# 2. 核心功能
- 自动处理配置加载顺序，支持占位符解析
- 自动生成命名空间，支持多环境隔离
- 启动时自动检测 Nacos 连接可用性
- 配置验证与详细日志输出

# 3. 高级特性
- 支持通过环境变量灵活配置 Nacos 地址、账号、命名空间
- 配置验证工具类，自动检测配置完整性和占位符
- 启用详细日志：
  ```yaml
  trina:
    nacos:
      verbose-logging: true
  ```

# 4. 常见问题
- 启动报错：检查 Nacos 地址、账号、命名空间是否正确
- 配置未生效：确认配置加载顺序，避免循环依赖
- 连接失败：使用 NacosConnectionChecker 工具类诊断网络与权限
- 占位符未解析：确保 application.yml 先于 nacos.yaml 加载

# 5. 最佳实践
- 项目 application.yml 必须配置 spring.application.name
- 推荐通过环境变量注入 Nacos 地址、账号、密码，便于多环境部署
- 使用 nacos.yaml 提供默认配置，避免循环依赖
- 关注启动日志，及时发现配置或连接问题
- 配置优先级：动态属性 > application.yml > nacos.yaml > Nacos 远程配置

# 6. 参考资料
- [Spring Cloud Alibaba Nacos 官方文档](https://nacos.io/zh-cn/docs/quick-start-spring-cloud.html)
- [Trina nacos 架构文档](../architecture/nacos-architecture.mdx)