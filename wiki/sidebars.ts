import type {SidebarsConfig} from '@docusaurus/plugin-content-docs';

// This runs in Node.js - Don't use client-side code here (browser APIs, JSX...)

/**
 * Creating a sidebar enables you to:
 - create an ordered group of docs
 - render a sidebar for each doc of that group
 - provide next/previous navigation

 The sidebars can be generated from the filesystem, or explicitly defined here.

 Create as many sidebars as you want.
 */
const sidebars: SidebarsConfig = {
  // By default, Docusaurus generates a sidebar from the docs folder structure
  tutorialSidebar: [
    'README',
    {
      type: 'category',
      label: '架构设计',
      items: [{type: 'autogenerated', dirName: 'architecture'}],
    },
    {
      type: 'category', 
      label: '使用指南',
      items: [{type: 'autogenerated', dirName: 'guides'}],
    },
    {
      type: 'category',
      label: '教程',
      items: [{type: 'autogenerated', dirName: 'tutorials'}],
    },
  ],
  
  // Trina脚手架独立侧边栏
  scaffoldSidebar: [
    {
      type: 'category',
      label: 'Trina-Backend脚手架',
      items: [{type: 'autogenerated', dirName: 'scaffold'}],
    },
  ],
  
  // 前端脚手架独立侧边栏
  frontendSidebar: [
    {
      type: 'doc',
      id: 'frontend/README',
      label: '前端脚手架',
    },
  ],
};

export default sidebars;
