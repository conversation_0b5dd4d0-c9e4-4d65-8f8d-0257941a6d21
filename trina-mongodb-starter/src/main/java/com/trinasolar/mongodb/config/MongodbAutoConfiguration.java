package com.trinasolar.mongodb.config;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.MongoTransactionManager;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;
import org.springframework.data.mongodb.core.convert.DefaultDbRefResolver;
import org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.util.StringUtils;

import java.util.concurrent.TimeUnit;

/**
 * MongoDB自动配置类
 */
@AutoConfiguration
@EnableConfigurationProperties(MongodbProperties.class)
@EnableMongoRepositories(basePackages = "com.trinasolar.**.repository")
public class MongodbAutoConfiguration {

    private final MongodbProperties properties;

    public MongodbAutoConfiguration(MongodbProperties properties) {
        this.properties = properties;
    }

    /**
     * 获取数据库名称
     */
    protected String getDatabaseName() {
        return properties.getDatabase();
    }

    /**
     * 创建MongoDB客户端
     */
    @Bean
    @ConditionalOnMissingBean
    public MongoClient mongoClient() {
        MongoClientSettings.Builder builder = MongoClientSettings.builder();
        
        // 设置连接字符串
        if (StringUtils.hasText(properties.getUri())) {
            builder.applyConnectionString(new ConnectionString(properties.getUri()));
        } else {
            // 如果没有提供URI，则使用单独的配置项
            StringBuilder sb = new StringBuilder("mongodb://");
            
            // 添加用户名和密码
            if (StringUtils.hasText(properties.getUsername()) && StringUtils.hasText(properties.getPassword())) {
                sb.append(properties.getUsername())
                  .append(":")
                  .append(properties.getPassword())
                  .append("@");
            }
            
            // 添加主机和端口
            sb.append(properties.getHost())
              .append(":")
              .append(properties.getPort())
              .append("/")
              .append(properties.getDatabase());
            
            // 添加其他参数
            boolean hasParams = false;
            
            // 添加认证数据库
            if (StringUtils.hasText(properties.getAuthenticationDatabase())) {
                sb.append("?authSource=").append(properties.getAuthenticationDatabase());
                hasParams = true;
            }
            
            // 添加认证机制
            if (StringUtils.hasText(properties.getAuthenticationMechanism())) {
                sb.append(hasParams ? "&" : "?")
                  .append("authMechanism=")
                  .append(properties.getAuthenticationMechanism());
                hasParams = true;
            }
            
            // 添加副本集
            if (StringUtils.hasText(properties.getReplicaSet())) {
                sb.append(hasParams ? "&" : "?")
                  .append("replicaSet=")
                  .append(properties.getReplicaSet());
                hasParams = true;
            }
            
            // 添加SSL设置
            if (properties.isSsl()) {
                sb.append(hasParams ? "&" : "?")
                  .append("ssl=true");
            }
            
            builder.applyConnectionString(new ConnectionString(sb.toString()));
        }
        
        // 设置连接池配置
        builder.applyToConnectionPoolSettings(builder1 -> {
            builder1.maxSize(properties.getMaxConnectionPoolSize())
                   .minSize(properties.getMinConnectionPoolSize());
        });
        
        // 设置超时配置
        builder.applyToSocketSettings(builder1 -> {
            builder1.connectTimeout((int) properties.getConnectTimeout().toMillis(), TimeUnit.MILLISECONDS)
                   .readTimeout((int) properties.getSocketTimeout().toMillis(), TimeUnit.MILLISECONDS);
        });
        
        return MongoClients.create(builder.build());
    }

    /**
     * 创建MongoDB数据库工厂
     */
    @Bean
    @ConditionalOnMissingBean
    public MongoDatabaseFactory mongoDbFactory() {
        return new SimpleMongoClientDatabaseFactory(mongoClient(), getDatabaseName());
    }

    /**
     * 创建MongoDB操作模板
     */
    @Bean
    @ConditionalOnMissingBean
    public MongoTemplate mongoTemplate() {
        MongoDatabaseFactory factory = mongoDbFactory();
        
        // 创建自定义的Converter，去除_class字段
        MappingMongoConverter converter = new MappingMongoConverter(new DefaultDbRefResolver(factory), new MongoMappingContext());
        converter.setTypeMapper(new DefaultMongoTypeMapper(null));
        
        return new MongoTemplate(factory, converter);
    }

    /**
     * 创建MongoDB事务管理器
     */
    @Bean
    @ConditionalOnProperty(name = "trina.mongodb.enable-transaction", havingValue = "true")
    public MongoTransactionManager transactionManager(MongoDatabaseFactory dbFactory) {
        return new MongoTransactionManager(dbFactory);
    }
}