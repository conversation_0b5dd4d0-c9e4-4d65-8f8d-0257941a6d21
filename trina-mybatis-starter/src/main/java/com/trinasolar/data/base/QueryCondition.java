package com.trinasolar.data.base;

import com.baomidou.mybatisplus.core.enums.SqlKeyword;
import lombok.Getter;
import lombok.Setter;

/**
 * 查询条件对象
 */
@Setter
@Getter
public class QueryCondition {
    /**
     * 字段名
     */
    private String field;

    /**
     * 条件类型
     */
    private QueryConditionType type;

    /**
     * 条件值
     */
    private Object value;

    public QueryCondition() {
    }

    public QueryCondition(String field, QueryConditionType type, Object value) {
        this.field = field;
        this.type = type;
        this.value = value;
    }

    /**
     * 从字符串类型创建查询条件
     */
    public QueryCondition(String field, String typeStr, Object value) {
        this(field, QueryConditionType.fromString(typeStr), value);
    }

    /**
     * 获取对应的SqlKeyword
     */
    public SqlKeyword getSqlKeyword() {
        return type != null ? type.getSqlKeyword() : null;
    }
} 