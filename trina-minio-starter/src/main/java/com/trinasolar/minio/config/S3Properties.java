package com.trinasolar.minio.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @ClassName S3Properties
 * @Description S3配置属性类
 * <AUTHOR>
 * @Date 2023/11/01
 **/
@Data
@ConfigurationProperties(prefix = "s3")
public class S3Properties {
    /**
     * S3服务端点URL
     */
    private String endpoint;
    
    /**
     * 区域
     */
    private String region;
    
    /**
     * 访问密钥
     */
    private String accessKey;
    
    /**
     * 秘密密钥
     */
    private String secretKey;
    
    /**
     * 存储桶名称
     */
    private String bucketName;
    
    /**
     * 是否使用路径样式访问
     * true: http://s3.amazonaws.com/bucket
     * false: http://bucket.s3.amazonaws.com/
     */
    private Boolean pathStyleAccess = true;
    
    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectionTimeout = 10000;
    
    /**
     * 请求超时时间（毫秒）
     */
    private Integer requestTimeout = 30000;
}
