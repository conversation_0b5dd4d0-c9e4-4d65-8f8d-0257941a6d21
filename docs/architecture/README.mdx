---
title: 架构设计文档
description: Trina Common 框架架构设计文档索引
---

# 架构设计文档

本目录包含 Trina Common 框架的架构设计文档，帮助您深入理解框架的设计理念和技术实现。

## 系统架构

Trina Framework 采用模块化设计，提供了完整的企业级开发解决方案。

![系统架构层次图](/docs/images/architecture-diagram.svg)

系统架构分为三个主要层次：
1. **前端层**：包括前端主框架和各种前端应用
2. **后端层**：通过API网关连接多个后端服务应用
3. **基础组件库**：由TrinaCommon和TrinaDesignUI组成

要了解更多系统架构细节，请参阅[Trina架构详细设计](/docs/architecture/trina-architecture)。


## 推荐阅读顺序

### 1. 设计理念（必读）
- **[设计原则](./principles.mdx)** - 框架核心设计哲学
- **[设计模式](./design-principles.mdx)** - 设计模式在框架中的应用

### 2. 整体架构
- **[Trina架构概览](./trina-architecture.mdx)** - 框架整体架构设计
- **[模块依赖关系](./module-dependencies.mdx)** - 各模块间的依赖关系图
- **[依赖管理](./dependencies.mdx)** - 第三方依赖管理策略

> 💡**[系统架构层次图](./trina-architecture.mdx#系统架构层次图)**，直观展示了从前端到基础组件的完整系统层次结构。

### 3. 核心组件架构
- **[核心组件架构](./common-core-architecture.mdx)** - 核心组件设计详解
- **[Web架构](./web-architecture.mdx)** - Web层架构设计

## 🗂️ 分类浏览

### 数据访问层
- **[MyBatis架构](./mybatis-architecture.mdx)** - ORM框架集成架构
- **[Redis架构](./redis-architecture.mdx)** - 缓存层架构设计
- **[Elasticsearch架构](./elasticsearch-architecture.mdx)** - 搜索引擎架构

### 存储与文件
- **[MinIO架构](./minio-architecture.mdx)** - 对象存储架构

### 分布式服务
- **[远程调用架构](./remote-architecture.mdx)** - RPC调用架构
- **[微服务架构](./microservice-architecture.mdx)** - 微服务基础框架架构
- **[Seata架构](./seata-architecture.mdx)** - 分布式事务架构设计

## 按角色阅读

### 架构师
重点关注：
1. [设计原则](./principles.mdx)
2. [Trina架构概览](./trina-architecture.mdx)
3. [模块依赖关系](./module-dependencies.mdx)
4. [依赖管理](./dependencies.mdx)

### 技术负责人
重点关注：
1. [设计模式](./design-principles.mdx)
2. [核心组件架构](./common-core-architecture.mdx)
3. [Web架构](./web-architecture.mdx)
4. 各组件具体架构文档

### 高级开发者
重点关注：
1. 具体组件架构文档

## 📋 文档状态

| 文档 | 状态 | 最后更新 |
|------|------|----------|
| 设计原则 | ✅ 完整 | 2024-01 |
| Trina架构概览 | ✅ 完整 | 2024-01 |
| 模块依赖关系 | ✅ 完整 | 2025-06 |
| 依赖管理 | ✅ 完整 | 2025-06 |
| 核心组件架构 | ✅ 完整 | 2024-01 |
| Web架构 | ✅ 完整 | 2024-01 |
| MyBatis架构 | ✅ 完整 | 2024-01 |
| Redis架构 | ✅ 完整 | 2024-01 |
| 其他组件架构 | 🚧 持续更新 | - |

## 💡 阅读建议

1. **首次阅读**：建议按照推荐顺序从设计理念开始
2. **深入学习**：根据实际使用的组件选择对应的架构文档
3. **问题排查**：结合架构文档理解组件工作原理
4. **扩展开发**：参考架构设计进行功能扩展