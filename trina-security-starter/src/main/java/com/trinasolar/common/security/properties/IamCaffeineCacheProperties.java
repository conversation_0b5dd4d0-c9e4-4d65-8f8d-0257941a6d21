package com.trinasolar.common.security.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.time.Duration;

/**
 * Caffeine缓存配置属性
 * <p>
 * 支持通过配置文件灵活调整缓存参数，便于不同环境使用不同配置
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@RefreshScope
@ConfigurationProperties(prefix = "trina.iam.cache")
public class IamCaffeineCacheProperties {

    /**
     * 是否启用IAM缓存
     */
    private boolean enabled = true;

    /**
     * 初始容量
     * 建议设置为预估并发用户数
     */
    private int initialCapacity = 512;

    /**
     * 最大缓存条目数
     * 建议不超过JVM堆内存的5%对应的条目数
     */
    private long maximumSize = 10000;

    /**
     * 写入后过期时间
     * 认证信息建议5-10分钟，保证安全性
     */
    private Duration expireAfterWrite = Duration.ofMinutes(5);

    /**
     * 访问后过期时间
     * 清理长时间未访问的数据，节省内存
     */
    private Duration expireAfterAccess = Duration.ofMinutes(10);

    /**
     * 是否启用弱引用key
     */
    private boolean weakKeys = true;

    /**
     * 是否启用统计
     */
    private boolean recordStats = true;
}
