import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';
import styles from './styles.module.css';

interface TechItem {
  name: string;
  category: string;
  size: number;
  color: string;
  icon?: string;
}

const techData: TechItem[] = [
  // 核心框架
  { name: 'Spring Boot 3.4', category: 'core', size: 100, color: '#ff6b6b', icon: '🚀' },
  { name: 'Java 17', category: 'core', size: 90, color: '#ff5252', icon: '💻' },
  { name: 'Spring Cloud 2023', category: 'core', size: 85, color: '#ee5a24', icon: '☁️' },
  { name: 'Maven', category: 'core', size: 70, color: '#d63031', icon: '📦' },
  
  // 数据访问
  { name: 'MyBatis-Plus', category: 'data', size: 80, color: '#4ecdc4', icon: '🗄️' },
  { name: 'Redis', category: 'data', size: 85, color: '#26d0ce', icon: '⚡' },
  { name: 'Elasticsearch', category: 'data', size: 75, color: '#44a08d', icon: '🔍' },
  { name: 'MongoDB', category: 'data', size: 70, color: '#2d3748', icon: '🗃️' },
  
  // 服务治理
  { name: 'Sentinel', category: 'service', size: 75, color: '#a8e6cf', icon: '🛡️' },
  { name: 'Seata', category: 'service', size: 70, color: '#88d8a3', icon: '🔄' },
  { name: 'Gateway', category: 'service', size: 80, color: '#7fcdcd', icon: '🚪' },
  { name: 'OpenFeign', category: 'service', size: 65, color: '#6bb6ff', icon: '🔗' },
  
  // 基础设施
  { name: 'MinIO', category: 'infra', size: 70, color: '#ffd93d', icon: '📁' },
  { name: 'RabbitMQ', category: 'infra', size: 75, color: '#ffbe0b', icon: '📨' },
  { name: 'XXL-Job', category: 'infra', size: 65, color: '#fb8500', icon: '⏰' },
  { name: 'SFTP', category: 'infra', size: 60, color: '#ff6b6b', icon: '📤' },
];

const TechStackD3: React.FC = () => {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!svgRef.current || !containerRef.current) return;

    const container = containerRef.current;
    const svg = d3.select(svgRef.current);
    
    // 清除之前的内容
    svg.selectAll('*').remove();
    
    // 获取容器尺寸
    const containerRect = container.getBoundingClientRect();
    const width = Math.max(800, containerRect.width);
    const height = 600;
    
    svg.attr('width', width).attr('height', height);
    
    // 创建力导向图模拟
    const simulation = d3.forceSimulation(techData as any)
      .force('charge', d3.forceManyBody().strength(-300))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius((d: any) => d.size / 2 + 10))
      .force('x', d3.forceX(width / 2).strength(0.1))
      .force('y', d3.forceY(height / 2).strength(0.1));

    // 创建渐变定义
    const defs = svg.append('defs');
    
    techData.forEach((tech, i) => {
      const gradient = defs.append('radialGradient')
        .attr('id', `gradient-${i}`)
        .attr('cx', '30%')
        .attr('cy', '30%');
      
      gradient.append('stop')
        .attr('offset', '0%')
        .attr('stop-color', d3.color(tech.color)?.brighter(0.5)?.toString() || tech.color);
      
      gradient.append('stop')
        .attr('offset', '100%')
        .attr('stop-color', tech.color);
    });

    // 创建节点组
    const nodes = svg.selectAll('.tech-node')
      .data(techData)
      .enter()
      .append('g')
      .attr('class', 'tech-node')
      .style('cursor', 'pointer');

    // 添加圆形背景
    nodes.append('circle')
      .attr('r', (d: any) => d.size / 2)
      .attr('fill', (d: any, i: number) => `url(#gradient-${i})`)
      .attr('stroke', '#fff')
      .attr('stroke-width', 3)
      .style('filter', 'drop-shadow(0 4px 8px rgba(0,0,0,0.2))');

    // 添加图标
    nodes.append('text')
      .attr('class', 'tech-icon')
      .attr('text-anchor', 'middle')
      .attr('dy', '-0.2em')
      .style('font-size', (d: any) => `${Math.max(16, d.size / 4)}px`)
      .style('pointer-events', 'none')
      .text((d: any) => d.icon || '⚙️');

    // 添加技术名称
    nodes.append('text')
      .attr('class', 'tech-name')
      .attr('text-anchor', 'middle')
      .attr('dy', '1.5em')
      .style('font-size', (d: any) => `${Math.max(10, d.size / 8)}px`)
      .style('font-weight', '600')
      .style('fill', '#fff')
      .style('text-shadow', '1px 1px 2px rgba(0,0,0,0.8)')
      .style('pointer-events', 'none')
      .text((d: any) => d.name);

    // 添加交互效果
    nodes
      .on('mouseover', function(event, d: any) {
        const currentTransform = d3.select(this).attr('transform') || `translate(${d.x}, ${d.y})`;
        d3.select(this)
          .transition()
          .duration(200)
          .attr('transform', `${currentTransform} scale(1.2)`);
        
        d3.select(this).select('circle')
          .transition()
          .duration(200)
          .style('filter', 'drop-shadow(0 8px 16px rgba(0,0,0,0.4))');
      })
      .on('mouseout', function(event, d: any) {
        const currentTransform = d3.select(this).attr('transform') || `translate(${d.x}, ${d.y})`;
        const baseTransform = currentTransform.replace(/ scale\([^)]*\)/, '');
        d3.select(this)
          .transition()
          .duration(200)
          .attr('transform', baseTransform);
        
        d3.select(this).select('circle')
          .transition()
          .duration(200)
          .style('filter', 'drop-shadow(0 4px 8px rgba(0,0,0,0.2))');
      })
      .on('click', function(event, d: any) {
        // 点击效果：创建涟漪动画
        const ripple = svg.append('circle')
          .attr('cx', d.x)
          .attr('cy', d.y)
          .attr('r', 0)
          .attr('fill', 'none')
          .attr('stroke', d.color)
          .attr('stroke-width', 2)
          .attr('opacity', 1);
        
        ripple.transition()
          .duration(600)
          .attr('r', d.size)
          .attr('opacity', 0)
          .remove();
      });

    // 更新节点位置
    simulation.on('tick', () => {
      nodes.each(function(d: any) {
        const element = d3.select(this);
        const currentTransform = element.attr('transform') || '';
        const scaleMatch = currentTransform.match(/scale\([^)]*\)/);
        const scaleTransform = scaleMatch ? ` ${scaleMatch[0]}` : '';
        element.attr('transform', `translate(${d.x}, ${d.y})${scaleTransform}`);
      });
    });

    // 添加拖拽功能
    const drag = d3.drag<SVGGElement, any>()
      .on('start', (event, d: any) => {
        if (!event.active) simulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
      })
      .on('drag', (event, d: any) => {
        d.fx = event.x;
        d.fy = event.y;
      })
      .on('end', (event, d: any) => {
        if (!event.active) simulation.alphaTarget(0);
        d.fx = null;
        d.fy = null;
      });

    nodes.call(drag as any);

    // 响应式处理
    const handleResize = () => {
      const newRect = container.getBoundingClientRect();
      const newWidth = Math.max(800, newRect.width);
      const newHeight = 600;
      
      svg.attr('width', newWidth).attr('height', newHeight);
      simulation.force('center', d3.forceCenter(newWidth / 2, newHeight / 2));
      simulation.alpha(0.3).restart();
    };

    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      simulation.stop();
    };
  }, []);

  return (
    <div className={styles.techStackContainer} ref={containerRef}>
      <div className={styles.header}>
        <h2 className={styles.title}>🛠️ 技术栈</h2>
        <p className={styles.subtitle}>基于现代化技术构建的企业级微服务框架</p>
      </div>
      
      <div className={styles.legend}>
        <div className={styles.legendItem}>
          <span className={styles.legendColor} style={{backgroundColor: '#ff6b6b'}}></span>
          <span>核心框架</span>
        </div>
        <div className={styles.legendItem}>
          <span className={styles.legendColor} style={{backgroundColor: '#4ecdc4'}}></span>
          <span>数据访问</span>
        </div>
        <div className={styles.legendItem}>
          <span className={styles.legendColor} style={{backgroundColor: '#a8e6cf'}}></span>
          <span>服务治理</span>
        </div>
        <div className={styles.legendItem}>
          <span className={styles.legendColor} style={{backgroundColor: '#ffd93d'}}></span>
          <span>基础设施</span>
        </div>
      </div>
      
      <div className={styles.svgContainer}>
        <svg ref={svgRef} className={styles.svg}></svg>
      </div>
      
      <div className={styles.stats}>
        <div className={styles.stat}>
          <div className={styles.statNumber}>20+</div>
          <div className={styles.statLabel}>核心组件</div>
        </div>
        <div className={styles.stat}>
          <div className={styles.statNumber}>8</div>
          <div className={styles.statLabel}>业务域</div>
        </div>
        <div className={styles.stat}>
          <div className={styles.statNumber}>95%</div>
          <div className={styles.statLabel}>开源技术</div>
        </div>
      </div>
    </div>
  );
};

export default TechStackD3;