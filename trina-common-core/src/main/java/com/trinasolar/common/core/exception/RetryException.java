package com.trinasolar.common.core.exception;

/**
 * 只有在抛出次异常后，才会进行重试
 */
public class RetryException extends RuntimeException {
    private static final long serialVersionUID = 1L;
    private Integer errorCode;
    private String errorName;
    private String errorMessage;
    private String description;

    public RetryException() {
        super();
    }

    public RetryException(String description, String message) {
        super(message);
        this.errorMessage = message;
        this.description = description;
    }

    public RetryException(String message) {
        super(message);
    }

    public RetryException(Integer errorCode) {
        super();
        this.errorCode = errorCode;
    }

    public RetryException(Throwable cause) {
        super(cause);
    }

    public RetryException(String message, Throwable cause) {
        super(message, cause);
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public String getErrorName() {
        return errorName;
    }

    public void setErrorName(String errorName) {
        this.errorName = errorName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
