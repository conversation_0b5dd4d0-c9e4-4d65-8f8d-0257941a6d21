package com.trinasolar.minio.config;

import com.trinasolar.minio.api.MinioEndpoint;
import com.trinasolar.minio.service.FileTemplate;
import com.trinasolar.minio.service.MinioTemplate;
import com.trinasolar.minio.service.S3Template;
import lombok.AllArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * file 自动配置类
 *
 * <AUTHOR>
 * @date 2023/11/01
 */
@AllArgsConstructor
@EnableConfigurationProperties({FileProperties.class, MinioProperties.class, S3Properties.class})
public class FileAutoConfiguration {
    private final MinioProperties minioProperties;
    private final S3Properties s3Properties;

    /**
     * 配置 MinioTemplate
     *
     * @return MinioTemplate 实例
     */
    @Bean
    @ConditionalOnProperty(prefix = "file.server", name = "type", havingValue = "minio")
    FileTemplate minioTemplate() {
        FileTemplate template = new MinioTemplate(
                minioProperties.getUrl(),
                minioProperties.getAccessKey(),
                minioProperties.getSecretKey(),
                minioProperties.getBucketName()
        );
        return template;
    }

    /**
     * 配置 S3Template
     *
     * @return S3Template 实例
     */
    @Bean
    @ConditionalOnProperty(prefix = "file.server", name = "type", havingValue = "s3")
    FileTemplate s3Template() {
        return new S3Template(s3Properties);
    }

    /**
     * 配置 MinioEndpoint
     *
     * @param template FileTemplate 实例
     * @return MinioEndpoint 实例
     */
    @Bean
    @ConditionalOnProperty(prefix = "file.server", name = "type", havingValue = "minio")
    public MinioEndpoint minioEndpoint(FileTemplate template) {
        return new MinioEndpoint(template);
    }


}
