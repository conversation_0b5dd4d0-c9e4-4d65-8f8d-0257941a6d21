---
title: Elasticsearch 使用指南
description: 新版 Trina Elasticsearch 模块的使用方法与最佳实践
---

# Elasticsearch 使用指南

本指南详细介绍了如何在项目中使用新版 Trina Elasticsearch 模块，该模块基于 `elasticsearch-rest-client` 实现，提供了轻量级、高性能的 Elasticsearch 集成方案。

## 1. 快速开始

### 1.1 引入依赖

在项目的 `pom.xml` 中添加以下依赖：

```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-elasticsearch-starter</artifactId>
    <version>4.0.0</version>
</dependency>
```

### 1.2 基础配置

在 `application.yml` 中配置 Elasticsearch 连接信息：

```yaml
spring:
  elasticsearch:
    uris: *************:31001
    username: elastic
    password: Ab123456
    connection-timeout: 5000
    socket-timeout: 60000
    ssl: false
```

## 2. 常见问题解决

### 2.1 配置问题修正

#### 配置格式错误

**问题描述**：启动时遇到配置绑定错误
```
Failed to bind properties under 'elasticsearch.connect-timeout' to int:
    Property: elasticsearch.connect-timeout
    Value: "10s"
    Reason: failed to convert java.lang.String to int
```

**解决方案**：所有超时配置必须使用毫秒数值，不支持字符串格式

**错误配置**：
```yaml
elasticsearch:
  connect-timeout: "10s"          # ❌ 错误：字符串格式
  socket-timeout: "30s"           # ❌ 错误：字符串格式
```

**正确配置**：
```yaml
elasticsearch:
  connect-timeout: 10000           # ✅ 正确：10秒 = 10000毫秒
  socket-timeout: 30000            # ✅ 正确：30秒 = 30000毫秒
```

#### 配置属性说明

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `hosts` | String[] | ["localhost:9200"] | Elasticsearch服务器地址列表 |
| `connect-timeout` | int | 5000 | 连接超时时间(毫秒) |
| `socket-timeout` | int | 30000 | Socket超时时间(毫秒) |
| `connection-request-timeout` | int | 1000 | 连接请求超时时间(毫秒) |
| `max-connections` | int | 100 | 最大连接数 |
| `max-connections-per-route` | int | 10 | 每个路由的最大连接数 |
| `username` | String | null | 用户名(可选) |
| `password` | String | null | 密码(可选) |
| `api-key` | String | null | API密钥(可选) |
| `ssl` | boolean | false | 是否启用SSL |

#### 时间单位转换参考

| 时间单位 | 毫秒值 | 配置示例 |
|---------|--------|----------|
| 1秒 | 1000 | `connect-timeout: 1000` |
| 5秒 | 5000 | `connect-timeout: 5000` |
| 10秒 | 10000 | `connect-timeout: 10000` |
| 30秒 | 30000 | `socket-timeout: 30000` |
| 1分钟 | 60000 | `socket-timeout: 60000` |

### 2.2 服务使用问题

#### 正确的包路径

```java
// 正确的导入
import com.trinasolar.elasticsearch.config.ElasticsearchService;
import com.trinasolar.elasticsearch.utils.ElasticsearchUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
```

#### 实际可用的方法

| 功能 | 正确方法 | 错误方法 |
|------|----------|----------|
| 保存文档 | `indexDocument(index, id, json)` | `save(document)` |
| 查询文档 | `getDocument(index, id)` | `findById(id)` |
| 批量操作 | `bulk(requests)` | `saveBatch(documents)` |
| 更新文档 | `updateDocument(index, id, json)` | `updateById(id, document)` |
| 删除文档 | `deleteDocument(index, id)` | `deleteById(id)` |

#### 重要注意事项

1. **异常处理**：所有方法都可能抛出 `IOException`，需要适当处理
2. **ID 必须设置**：保存文档时必须设置文档ID
3. **JSON 转换**：需要手动进行对象与JSON之间的转换
4. **结果解析**：查询结果需要手动解析JSON响应

## 3. 定义文档实体

### 3.1 创建文档实体类

所有 Elasticsearch 文档实体应继承 `ArticleEntity` 类：

```java
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@EsIndex(name = "article")
public class ArticleEntity extends BaseEsEntity {

    /**
     * 文章标题
     */
    @EsField(
        type = FieldType.TEXT,
        analyzer = "ik_max_word",
        searchAnalyzer = "ik_smart"
    )
    private String title;

    /**
     * 文章内容
     */
    @EsField(
        type = FieldType.TEXT,
        analyzer = "ik_max_word",
        searchAnalyzer = "ik_smart"
    )
    private String content;

    /**
     * 文章摘要
     */
    @EsField(
        type = FieldType.TEXT,
        analyzer = "ik_max_word",
        searchAnalyzer = "ik_smart"
    )
    private String summary;

    /**
     * 作者
     */
    @EsField(type = FieldType.KEYWORD)
    private String author;

    /**
     * 文章分类
     */
    @EsField(type = FieldType.KEYWORD)
    private String category;

    /**
     * 文章标签
     */
    @EsField(type = FieldType.KEYWORD)
    private String[] tags;

    /**
     * 文章状态
     */
    @EsField(type = FieldType.KEYWORD)
    private String status;

    /**
     * 阅读次数
     */
    @EsField(type = FieldType.LONG)
    private Long readCount;

    /**
     * 点赞次数
     */
    @EsField(type = FieldType.LONG)
    private Long likeCount;

    /**
     * 发布时间
     */
    @EsField(type = FieldType.KEYWORD, index = false)
    private String publishTime;

    /**
     * 文章来源
     */
    @EsField(type = FieldType.KEYWORD)
    private String source;

    /**
     * 文章URL
     */
    @EsField(type = FieldType.KEYWORD, index = false)
    private String url;

    // ==================== 业务方法 ====================

    /**
     * 发布文章
     */
    public void publish() {
        this.status = "published";
        this.publishTime = "2025-06-25T21:30:00";
    }

    /**
     * 草稿状态
     */
    public void draft() {
        this.status = "draft";
    }

    /**
     * 增加阅读次数
     */
    public void incrementReadCount() {
        this.readCount = (this.readCount == null ? 0 : this.readCount) + 1;
    }

    /**
     * 增加点赞次数
     */
    public void incrementLikeCount() {
        this.likeCount = (this.likeCount == null ? 0 : this.likeCount) + 1;
    }

    /**
     * 检查是否已发布
     */
    public boolean isPublished() {
        return "published".equals(this.status);
    }

    /**
     * 检查是否为草稿
     */
    public boolean isDraft() {
        return "draft".equals(this.status);
    }

    /**
     * 获取标签字符串
     */
    public String getTagsString() {
        if (tags == null || tags.length == 0) {
            return "";
        }
        return String.join(",", tags);
    }

    /**
     * 设置标签字符串
     */
    public void setTagsString(String tagsString) {
        if (tagsString == null || tagsString.trim().isEmpty()) {
            this.tags = new String[0];
        } else {
            this.tags = tagsString.split(",");
        }
    }

    /**
     * 获取用于向量化的标题文本
     */
    public String getTitleForVectorization() {
        return this.title;
    }

    /**
     * 获取用于向量化的内容文本
     */
    public String getContentForVectorization() {
        StringBuilder text = new StringBuilder();
        if (this.title != null) {
            text.append(this.title).append(" ");
        }
        if (this.summary != null) {
            text.append(this.summary).append(" ");
        }
        if (this.content != null) {
            // 只取内容的前1000个字符用于向量化
            String contentPart = this.content.length() > 1000 ?
                this.content.substring(0, 1000) : this.content;
            text.append(contentPart);
        }
        return text.toString().trim();
    }

    /**
     * 检查是否需要重新生成向量
     */
    public boolean needsVectorRegeneration() {
        return !hasContentVector() || !hasTitleVector();
    }

    /**
     * 获取简短摘要
     */
    public String getShortSummary() {
        if (summary != null && summary.length() > 100) {
            return summary.substring(0, 100) + "...";
        }
        return summary;
    }

    /**
     * 获取内容预览
     */
    public String getContentPreview() {
        if (content != null && content.length() > 200) {
            return content.substring(0, 200) + "...";
        }
        return content;
    }

    /**
     * 检查文章是否有效
     */
    public boolean isValid() {
        return title != null && !title.trim().isEmpty() &&
               content != null && !content.trim().isEmpty() &&
               author != null && !author.trim().isEmpty();
    }

    /**
     * 获取文章统计信息
     */
    public String getStatistics() {
        return String.format("阅读:%d, 点赞:%d",
            readCount != null ? readCount : 0,
            likeCount != null ? likeCount : 0);
    }
}
```


## 3. 使用 BaseEsService

### 3.1 注入 ElasticsearchService

新架构使用统一的 `ArticleService` 来处理所有 Elasticsearch 操作：

```java
public interface ArticleService extends BaseEsService<ArticleEntity> {

    // ==================== 业务特定方法 ====================

    /**
     * 根据标题搜索文章
     *
     * @param title 标题关键词
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    SearchResult<ArticleEntity> searchByTitle(String title, Integer page, Integer size);

    /**
     * 根据内容搜索文章
     *
     * @param content 内容关键词
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    SearchResult<ArticleEntity> searchByContent(String content, Integer page, Integer size);

    /**
     * 全文搜索
     *
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    SearchResult<ArticleEntity> fullTextSearch(String keyword, Integer page, Integer size);

    /**
     * 根据作者搜索
     *
     * @param author 作者
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    SearchResult<ArticleEntity> searchByAuthor(String author, Integer page, Integer size);

    /**
     * 根据分类搜索
     *
     * @param category 分类
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    SearchResult<ArticleEntity> searchByCategory(String category, Integer page, Integer size);

    /**
     * 根据标签搜索
     *
     * @param tags 标签数组
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    SearchResult<ArticleEntity> searchByTags(String[] tags, Integer page, Integer size);

    /**
     * 获取热门文章
     *
     * @param size 返回数量
     * @return 热门文章列表
     */
    List<ArticleEntity> getPopularArticles(Integer size);

    /**
     * 获取最新文章
     *
     * @param size 返回数量
     * @return 最新文章列表
     */
    List<ArticleEntity> getLatestArticles(Integer size);

    /**
     * 获取推荐文章
     *
     * @param size 返回数量
     * @return 推荐文章列表
     */
    List<ArticleEntity> getRecommendedArticles(Integer size);

    // ==================== 向量搜索便捷方法 ====================

    /**
     * 保存文章并自动生成向量
     *
     * @param article 文章实体
     * @return 保存后的文章
     */
    ArticleEntity saveArticleWithVector(ArticleEntity article);

    /**
     * 批量保存文章并自动生成向量
     *
     * @param articles 文章列表
     * @return 保存后的文章列表
     */
    List<ArticleEntity> saveArticlesWithVector(List<ArticleEntity> articles);

    /**
     * 语义搜索文章（基于内容向量）
     *
     * @param queryText 查询文本
     * @param threshold 相似度阈值
     * @param size 返回数量
     * @return 搜索结果
     */
    List<ArticleEntity> semanticSearchByContent(String queryText, float threshold, int size);

    /**
     * 语义搜索文章（基于标题向量）
     *
     * @param queryText 查询文本
     * @param threshold 相似度阈值
     * @param size 返回数量
     * @return 搜索结果
     */
    List<ArticleEntity> semanticSearchByTitle(String queryText, float threshold, int size);

    /**
     * 混合搜索（关键词 + 语义）
     *
     * @param keyword 关键词
     * @param keywordWeight 关键词权重
     * @param vectorWeight 向量权重
     * @param threshold 向量相似度阈值
     * @param size 返回数量
     * @return 搜索结果
     */
    List<ArticleEntity> hybridSearchArticles(String keyword, float keywordWeight,
                                           float vectorWeight, float threshold, int size);

    /**
     * 查找相似文章
     *
     * @param articleId 文章ID
     * @param threshold 相似度阈值
     * @param size 返回数量
     * @return 相似文章列表
     */
    List<ArticleEntity> findSimilarArticles(String articleId, float threshold, int size);

    // ==================== 统计分析 ====================

    /**
     * 统计文章总数
     *
     * @return 文章总数
     */
    long getTotalArticleCount();

    /**
     * 统计已发布文章数
     *
     * @return 已发布文章数
     */
    long getPublishedArticleCount();

    /**
     * 统计草稿文章数
     *
     * @return 草稿文章数
     */
    long getDraftArticleCount();

    /**
     * 按分类统计文章数
     *
     * @return 分类统计结果
     */
    Map<String, Long> getArticleCountByCategory();

    /**
     * 按作者统计文章数
     *
     * @return 作者统计结果
     */
    Map<String, Long> getArticleCountByAuthor();

    // ==================== 文档处理 ====================

    /**
     * 从文件内容创建文章
     *
     * @param fileName 文件名
     * @param fileContent 文件内容
     * @param author 作者
     * @return 创建的文章
     */
    ArticleEntity createArticleFromFile(String fileName, String fileContent, String author);

    /**
     * 批量导入文档
     *
     * @param documents 文档内容列表
     * @param author 作者
     * @return 导入的文章列表
     */
    List<ArticleEntity> batchImportDocuments(List<String> documents, String author);

    /**
     * 分块导入大文档
     *
     * @param title 文档标题
     * @param content 文档内容
     * @param author 作者
     * @param chunkSize 分块大小
     * @return 导入的文章列表
     */
    List<ArticleEntity> importLargeDocument(String title, String content, String author, int chunkSize);
}
```

### 3.2 创建服务接口

```java
@Service
public class ArticleService {

    @Autowired
    private ElasticsearchService elasticsearchService;

    private static final String INDEX_NAME = "articles";

    /**
     * 保存文章
     */
    public String saveArticle(ArticleDocument article) {
        return elasticsearchService.save(INDEX_NAME, article);
    }

    /**
     * 根据ID获取文章
     */
    public Optional<ArticleDocument> getArticleById(String id) {
        return elasticsearchService.findById(INDEX_NAME, id, ArticleDocument.class);
    }

    /**
     * 搜索文章
     */
    public List<ArticleDocument> searchArticles(String keyword) {
        // 具体实现将在查询章节介绍
        return null;
    }
}
```

### 3.3 创建服务实现

```java
@Slf4j
@Service
public class ArticleServiceImpl extends BaseEsServiceImpl<ArticleEntity> implements ArticleService {

    public ArticleServiceImpl() {
        super();
    }

    // ==================== 基础搜索方法 ====================

    @Override
    public SearchResult<ArticleEntity> searchByTitle(String title, Integer page, Integer size) {
        try {
            log.info("根据标题搜索文章: {}", title);

            EsQueryWrapper<ArticleEntity> wrapper = new EsQueryWrapper<>();
            wrapper.match("title", title);
            wrapper.eq("status", "published");
            wrapper.page(page != null ? page : 0, size != null ? size : 10);
            wrapper.orderByDesc("publishTime");

            SearchResult<ArticleEntity> result = search(wrapper);
            log.info("根据标题搜索完成，找到 {} 条记录", result.getTotal());

            return result;

        } catch (Exception e) {
            log.error("根据标题搜索文章失败", e);
            throw new RuntimeException("搜索失败", e);
        }
    }

    @Override
    public SearchResult<ArticleEntity> searchByContent(String content, Integer page, Integer size) {
        try {
            log.info("根据内容搜索文章: {}", content);

            EsQueryWrapper<ArticleEntity> wrapper = new EsQueryWrapper<>();
            wrapper.match("content", content);
            wrapper.eq("status", "published");
            wrapper.page(page != null ? page : 0, size != null ? size : 10);
            wrapper.orderByDesc("publishTime");

            SearchResult<ArticleEntity> result = search(wrapper);
            log.info("根据内容搜索完成，找到 {} 条记录", result.getTotal());

            return result;

        } catch (Exception e) {
            log.error("根据内容搜索文章失败", e);
            throw new RuntimeException("搜索失败", e);
        }
    }

    @Override
    public SearchResult<ArticleEntity> fullTextSearch(String keyword, Integer page, Integer size) {
        try {
            log.info("全文搜索文章: {}", keyword);

            EsQueryWrapper<ArticleEntity> wrapper = new EsQueryWrapper<>();
            wrapper.multiMatch(keyword, "title", "content", "summary");
            wrapper.eq("status", "published");
            wrapper.page(page != null ? page : 0, size != null ? size : 10);
            wrapper.orderByDesc("_score");

            SearchResult<ArticleEntity> result = search(wrapper);
            log.info("全文搜索完成，找到 {} 条记录", result.getTotal());

            return result;

        } catch (Exception e) {
            log.error("全文搜索文章失败", e);
            throw new RuntimeException("搜索失败", e);
        }
    }

    @Override
    public SearchResult<ArticleEntity> searchByAuthor(String author, Integer page, Integer size) {
        try {
            log.info("根据作者搜索文章: {}", author);

            EsQueryWrapper<ArticleEntity> wrapper = new EsQueryWrapper<>();
            wrapper.eq("author", author);
            wrapper.eq("status", "published");
            wrapper.page(page != null ? page : 0, size != null ? size : 10);
            wrapper.orderByDesc("publishTime");

            SearchResult<ArticleEntity> result = search(wrapper);
            log.info("根据作者搜索完成，找到 {} 条记录", result.getTotal());

            return result;

        } catch (Exception e) {
            log.error("根据作者搜索文章失败", e);
            throw new RuntimeException("搜索失败", e);
        }
    }

    @Override
    public SearchResult<ArticleEntity> searchByCategory(String category, Integer page, Integer size) {
        try {
            log.info("根据分类搜索文章: {}", category);

            EsQueryWrapper<ArticleEntity> wrapper = new EsQueryWrapper<>();
            wrapper.eq("category", category);
            wrapper.eq("status", "published");
            wrapper.page(page != null ? page : 0, size != null ? size : 10);
            wrapper.orderByDesc("publishTime");

            SearchResult<ArticleEntity> result = search(wrapper);
            log.info("根据分类搜索完成，找到 {} 条记录", result.getTotal());

            return result;

        } catch (Exception e) {
            log.error("根据分类搜索文章失败", e);
            throw new RuntimeException("搜索失败", e);
        }
    }

    @Override
    public SearchResult<ArticleEntity> searchByTags(String[] tags, Integer page, Integer size) {
        try {
            log.info("根据标签搜索文章: {}", String.join(",", tags));

            EsQueryWrapper<ArticleEntity> wrapper = new EsQueryWrapper<>();
            wrapper.in("tags", Arrays.asList(tags));
            wrapper.eq("status", "published");
            wrapper.page(page != null ? page : 0, size != null ? size : 10);
            wrapper.orderByDesc("publishTime");

            SearchResult<ArticleEntity> result = search(wrapper);
            log.info("根据标签搜索完成，找到 {} 条记录", result.getTotal());

            return result;

        } catch (Exception e) {
            log.error("根据标签搜索文章失败", e);
            throw new RuntimeException("搜索失败", e);
        }
    }

    // ==================== 推荐和热门文章 ====================

    @Override
    public List<ArticleEntity> getPopularArticles(Integer size) {
        try {
            EsQueryWrapper<ArticleEntity> wrapper = new EsQueryWrapper<>();
            wrapper.eq("status", "published");
            wrapper.orderByDesc("readCount");
            wrapper.page(0, size != null ? size : 10);

            SearchResult<ArticleEntity> result = search(wrapper);
            return result.getRecords();
        } catch (Exception e) {
            log.error("获取热门文章失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ArticleEntity> getLatestArticles(Integer size) {
        try {
            EsQueryWrapper<ArticleEntity> wrapper = new EsQueryWrapper<>();
            wrapper.eq("status", "published");
            wrapper.orderByDesc("publishTime");
            wrapper.page(0, size != null ? size : 10);

            SearchResult<ArticleEntity> result = search(wrapper);
            return result.getRecords();
        } catch (Exception e) {
            log.error("获取最新文章失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ArticleEntity> getRecommendedArticles(Integer size) {
        try {
            EsQueryWrapper<ArticleEntity> wrapper = new EsQueryWrapper<>();
            wrapper.eq("status", "published");
            wrapper.orderByDesc("likeCount");
            wrapper.page(0, size != null ? size : 10);

            SearchResult<ArticleEntity> result = search(wrapper);
            return result.getRecords();
        } catch (Exception e) {
            log.error("获取推荐文章失败", e);
            return new ArrayList<>();
        }
    }

    // ==================== 向量搜索方法（暂时模拟实现）====================

    @Override
    public ArticleEntity saveArticleWithVector(ArticleEntity article) {
        try {
            log.info("保存文章并生成向量: {}", article.getTitle());

            // TODO: 集成真实的向量生成服务
            // 这里先模拟向量生成
            if (StringUtils.hasText(article.getTitleForVectorization())) {
                float[] titleVector = generateMockVector(article.getTitleForVectorization());
                article.setTitleVector(titleVector);
            }

            if (StringUtils.hasText(article.getContentForVectorization())) {
                float[] contentVector = generateMockVector(article.getContentForVectorization());
                article.setContentVector(contentVector);
            }

            ArticleEntity savedArticle = save(article);
            log.info("文章保存成功: {}", savedArticle.getId());

            return savedArticle;

        } catch (Exception e) {
            log.error("保存文章失败: {}", article.getTitle(), e);
            throw new RuntimeException("保存文章失败", e);
        }
    }

    @Override
    public List<ArticleEntity> saveArticlesWithVector(List<ArticleEntity> articles) {
        try {
            log.info("批量保存文章并生成向量，数量: {}", articles.size());

            List<ArticleEntity> processedArticles = new ArrayList<>();

            for (ArticleEntity article : articles) {
                // 生成向量
                if (StringUtils.hasText(article.getTitleForVectorization())) {
                    float[] titleVector = generateMockVector(article.getTitleForVectorization());
                    article.setTitleVector(titleVector);
                }

                if (StringUtils.hasText(article.getContentForVectorization())) {
                    float[] contentVector = generateMockVector(article.getContentForVectorization());
                    article.setContentVector(contentVector);
                }

                processedArticles.add(article);
            }

            // 批量保存
            saveBatch(processedArticles);
            log.info("批量保存文章成功，数量: {}", processedArticles.size());

            return processedArticles;

        } catch (Exception e) {
            log.error("批量保存文章失败", e);
            throw new RuntimeException("批量保存文章失败", e);
        }
    }

    // ==================== 向量搜索方法（暂时模拟实现）====================

    @Override
    public List<ArticleEntity> semanticSearchByContent(String queryText, float threshold, int size) {
        try {
            log.info("语义搜索文章（内容）: {}, 阈值: {}", queryText, threshold);

            // TODO: 实现真正的向量搜索
            log.warn("向量搜索功能正在开发中，返回关键词搜索结果");
            SearchResult<ArticleEntity> result = fullTextSearch(queryText, 0, size);
            return result.getRecords();

        } catch (Exception e) {
            log.error("语义搜索失败", e);
            throw new RuntimeException("语义搜索失败", e);
        }
    }

    @Override
    public List<ArticleEntity> semanticSearchByTitle(String queryText, float threshold, int size) {
        try {
            log.info("语义搜索文章（标题）: {}, 阈值: {}", queryText, threshold);

            // TODO: 实现真正的向量搜索
            log.warn("向量搜索功能正在开发中，返回标题搜索结果");
            SearchResult<ArticleEntity> result = searchByTitle(queryText, 0, size);
            return result.getRecords();

        } catch (Exception e) {
            log.error("语义搜索失败", e);
            throw new RuntimeException("语义搜索失败", e);
        }
    }

    @Override
    public List<ArticleEntity> hybridSearchArticles(String keyword, float keywordWeight,
                                                   float vectorWeight, float threshold, int size) {
        try {
            log.info("混合搜索文章: {}, 关键词权重: {}, 向量权重: {}",
                keyword, keywordWeight, vectorWeight);

            // TODO: 实现真正的混合搜索
            log.warn("混合搜索功能正在开发中，返回全文搜索结果");
            SearchResult<ArticleEntity> result = fullTextSearch(keyword, 0, size);
            return result.getRecords();

        } catch (Exception e) {
            log.error("混合搜索失败", e);
            throw new RuntimeException("混合搜索失败", e);
        }
    }

    @Override
    public List<ArticleEntity> findSimilarArticles(String articleId, float threshold, int size) {
        try {
            log.info("查找相似文章: {}, 阈值: {}", articleId, threshold);

            // 获取目标文章
            ArticleEntity targetArticle = getById(articleId);
            if (targetArticle == null) {
                throw new RuntimeException("文章不存在: " + articleId);
            }

            // TODO: 实现真正的相似度搜索
            log.warn("相似文章搜索功能正在开发中，返回同分类文章");
            if (targetArticle.getCategory() != null) {
                SearchResult<ArticleEntity> result = searchByCategory(targetArticle.getCategory(), 0, size);
                return result.getRecords().stream()
                    .filter(article -> !article.getId().equals(articleId))
                    .limit(size)
                    .toList();
            }

            return new ArrayList<>();

        } catch (Exception e) {
            log.error("查找相似文章失败: {}", articleId, e);
            throw new RuntimeException("查找相似文章失败", e);
        }
    }

    // ==================== 统计分析 ====================

    @Override
    public long getTotalArticleCount() {
        try {
            return count();
        } catch (Exception e) {
            log.error("统计文章总数失败", e);
            return 0;
        }
    }

    @Override
    public long getPublishedArticleCount() {
        try {
            EsQueryWrapper<ArticleEntity> wrapper = new EsQueryWrapper<>();
            wrapper.eq("status", "published");
            return count(wrapper);
        } catch (Exception e) {
            log.error("统计已发布文章数失败", e);
            return 0;
        }
    }

    @Override
    public long getDraftArticleCount() {
        try {
            EsQueryWrapper<ArticleEntity> wrapper = new EsQueryWrapper<>();
            wrapper.eq("status", "draft");
            return count(wrapper);
        } catch (Exception e) {
            log.error("统计草稿文章数失败", e);
            return 0;
        }
    }

    @Override
    public Map<String, Long> getArticleCountByCategory() {
        // TODO: 实现聚合查询
        log.warn("按分类统计功能正在开发中");
        return new HashMap<>();
    }

    @Override
    public Map<String, Long> getArticleCountByAuthor() {
        // TODO: 实现聚合查询
        log.warn("按作者统计功能正在开发中");
        return new HashMap<>();
    }

    // ==================== 文档处理 ====================

    @Override
    public ArticleEntity createArticleFromFile(String fileName, String fileContent, String author) {
        try {
            log.info("从文件创建文章: {}", fileName);

            ArticleEntity article = new ArticleEntity();
            article.setTitle(fileName);
            article.setContent(fileContent);
            article.setAuthor(author);
            article.setStatus("published");
            article.setSource("file_import");
            article.setReadCount(0L);
            article.setLikeCount(0L);
            article.setCategory("导入文档");
            article.setTags(new String[]{"导入", "文档"});
            article.publish();

            // 生成摘要（取前300个字符）
            if (fileContent.length() > 300) {
                article.setSummary(fileContent.substring(0, 300) + "...");
            } else {
                article.setSummary(fileContent);
            }

            return article;

        } catch (Exception e) {
            log.error("从文件创建文章失败: {}", fileName, e);
            throw new RuntimeException("创建文章失败", e);
        }
    }

    @Override
    public List<ArticleEntity> batchImportDocuments(List<String> documents, String author) {
        try {
            log.info("批量导入文档，数量: {}", documents.size());

            List<ArticleEntity> articles = new ArrayList<>();
            for (int i = 0; i < documents.size(); i++) {
                String content = documents.get(i);
                String title = "导入文档_" + (i + 1);
                ArticleEntity article = createArticleFromFile(title, content, author);
                articles.add(article);
            }

            return saveArticlesWithVector(articles);

        } catch (Exception e) {
            log.error("批量导入文档失败", e);
            throw new RuntimeException("批量导入失败", e);
        }
    }

    @Override
    public List<ArticleEntity> importLargeDocument(String title, String content, String author, int chunkSize) {
        try {
            log.info("分块导入大文档: {}, 块大小: {}", title, chunkSize);

            List<ArticleEntity> articles = new ArrayList<>();

            // 将大文档分块
            int totalLength = content.length();
            int chunkCount = (totalLength + chunkSize - 1) / chunkSize;

            for (int i = 0; i < chunkCount; i++) {
                int start = i * chunkSize;
                int end = Math.min(start + chunkSize, totalLength);
                String chunkContent = content.substring(start, end);

                String chunkTitle = String.format("%s (第%d部分)", title, i + 1);
                ArticleEntity article = createArticleFromFile(chunkTitle, chunkContent, author);
                article.setCategory("分块文档");
                article.setTags(new String[]{"分块", "导入", title});

                articles.add(article);
            }

            // 批量保存
            return saveArticlesWithVector(articles);

        } catch (Exception e) {
            log.error("分块导入大文档失败", e);
            throw new RuntimeException("分块导入失败", e);
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 生成模拟向量
     */
    private float[] generateMockVector(String text) {
        // 使用文本哈希作为种子，确保相同文本生成相同向量
        Random random = new Random(text.hashCode());

        float[] vector = new float[1024];
        for (int i = 0; i < 1024; i++) {
            vector[i] = (random.nextFloat() - 0.5f) * 2.0f; // 范围 [-1, 1]
        }

        // 归一化向量
        float norm = 0.0f;
        for (float v : vector) {
            norm += v * v;
        }
        norm = (float) Math.sqrt(norm);

        if (norm > 0) {
            for (int i = 0; i < vector.length; i++) {
                vector[i] /= norm;
            }
        }

        return vector;
    }

    // ==================== 实现BaseEsService的向量搜索方法 ====================

    @Override
    public List<ArticleEntity> findSimilarEntities(String entityId, String vectorField, float threshold, int size) {
        try {
            log.info("查找相似实体: {}, 向量字段: {}, 阈值: {}", entityId, vectorField, threshold);

            // 获取目标实体
            ArticleEntity targetEntity = getById(entityId);
            if (targetEntity == null) {
                throw new RuntimeException("实体不存在: " + entityId);
            }

            // TODO: 实现真正的向量相似度搜索
            log.warn("向量相似度搜索功能正在开发中，返回同分类文章");
            if (targetEntity.getCategory() != null) {
                SearchResult<ArticleEntity> result = searchByCategory(targetEntity.getCategory(), 0, size);
                return result.getRecords().stream()
                    .filter(article -> !article.getId().equals(entityId))
                    .limit(size)
                    .toList();
            }

            return new ArrayList<>();

        } catch (Exception e) {
            log.error("查找相似实体失败: {}", entityId, e);
            throw new RuntimeException("查找相似实体失败", e);
        }
    }

    @Override
    public List<ArticleEntity> hybridSearch(String keyword, String[] keywordFields, String vectorField,
                                          float keywordWeight, float vectorWeight, float threshold, int size) {
        try {
            log.info("混合搜索: {}, 关键词权重: {}, 向量权重: {}", keyword, keywordWeight, vectorWeight);

            // TODO: 实现真正的混合搜索
            log.warn("混合搜索功能正在开发中，返回全文搜索结果");
            SearchResult<ArticleEntity> result = fullTextSearch(keyword, 0, size);
            return result.getRecords();

        } catch (Exception e) {
            log.error("混合搜索失败", e);
            throw new RuntimeException("混合搜索失败", e);
        }
    }

    @Override
    public List<ArticleEntity> vectorSearch(String queryText, String vectorField, float threshold, int size) {
        try {
            log.info("向量搜索: {}, 向量字段: {}, 阈值: {}", queryText, vectorField, threshold);

            // TODO: 实现真正的向量搜索
            log.warn("向量搜索功能正在开发中，返回全文搜索结果");
            SearchResult<ArticleEntity> result = fullTextSearch(queryText, 0, size);
            return result.getRecords();

        } catch (Exception e) {
            log.error("向量搜索失败", e);
            throw new RuntimeException("向量搜索失败", e);
        }
    }

    @Override
    public List<ArticleEntity> saveBatchWithVector(List<ArticleEntity> entities, String titleField, String contentField) {
        try {
            log.info("批量保存实体并生成向量，数量: {}", entities.size());

            // 使用已有的方法
            return saveArticlesWithVector(entities);

        } catch (Exception e) {
            log.error("批量保存实体失败", e);
            throw new RuntimeException("批量保存实体失败", e);
        }
    }

    @Override
    public ArticleEntity saveWithVector(ArticleEntity entity, String titleField, String contentField) {
        try {
            log.info("保存实体并生成向量: {}", entity.getTitle());

            // 使用已有的方法
            return saveArticleWithVector(entity);

        } catch (Exception e) {
            log.error("保存实体失败", e);
            throw new RuntimeException("保存实体失败", e);
        }
    }
}
```

### 3.4 创建接口层

```java
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/articles")
public class ArticleController extends BaseEsController<ArticleService, ArticleEntity> {

    private final ArticleService articleService;
    @PostMapping
    public R<ArticleEntity> save(@RequestBody ArticleEntity entity) {
        ArticleEntity result = articleService.save(entity);
        return R.ok(result, "保存成功");
    }

    // ==================== 业务特定搜索接口 ====================

    @GetMapping("/search/title")
    public R<SearchResult<ArticleEntity>> searchByTitle(
            @RequestParam String title,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        try {
            log.info("根据标题搜索文章: {}", title);
            SearchResult<ArticleEntity> result = baseEsService.searchByTitle(title, page, size);
            return R.ok(result, "搜索成功");
        } catch (Exception e) {
            log.error("根据标题搜索文章失败", e);
            return R.failed("搜索失败: " + e.getMessage());
        }
    }

    @GetMapping("/search/content")
    public R<SearchResult<ArticleEntity>> searchByContent(
            @RequestParam String content,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        try {
            log.info("根据内容搜索文章: {}", content);
            SearchResult<ArticleEntity> result = baseEsService.searchByContent(content, page, size);
            return R.ok(result, "搜索成功");
        } catch (Exception e) {
            log.error("根据内容搜索文章失败", e);
            return R.failed("搜索失败: " + e.getMessage());
        }
    }

    @GetMapping("/search/author")
    public R<SearchResult<ArticleEntity>> searchByAuthor(
            @RequestParam String author,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        try {
            log.info("根据作者搜索文章: {}", author);
            SearchResult<ArticleEntity> result = baseEsService.searchByAuthor(author, page, size);
            return R.ok(result, "搜索成功");
        } catch (Exception e) {
            log.error("根据作者搜索文章失败", e);
            return R.failed("搜索失败: " + e.getMessage());
        }
    }

    @GetMapping("/search/category")
    public R<SearchResult<ArticleEntity>> searchByCategory(
            @RequestParam String category,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        try {
            log.info("根据分类搜索文章: {}", category);
            SearchResult<ArticleEntity> result = baseEsService.searchByCategory(category, page, size);
            return R.ok(result, "搜索成功");
        } catch (Exception e) {
            log.error("根据分类搜索文章失败", e);
            return R.failed("搜索失败: " + e.getMessage());
        }
    }

    @GetMapping("/search/tags")
    public R<SearchResult<ArticleEntity>> searchByTags(
            @RequestParam String[] tags,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        try {
            log.info("根据标签搜索文章: {}", String.join(",", tags));
            SearchResult<ArticleEntity> result = baseEsService.searchByTags(tags, page, size);
            return R.ok(result, "搜索成功");
        } catch (Exception e) {
            log.error("根据标签搜索文章失败", e);
            return R.failed("搜索失败: " + e.getMessage());
        }
    }

    // ==================== 推荐和热门文章接口 ====================

    @GetMapping("/popular")
    public R<List<ArticleEntity>> getPopularArticles(
            @RequestParam(defaultValue = "10") Integer size) {
        try {
            log.info("获取热门文章，数量: {}", size);
            List<ArticleEntity> articles = baseEsService.getPopularArticles(size);
            return R.ok(articles, "获取热门文章成功");
        } catch (Exception e) {
            log.error("获取热门文章失败", e);
            return R.failed("获取失败: " + e.getMessage());
        }
    }

    @GetMapping("/latest")
    public R<List<ArticleEntity>> getLatestArticles(
            @RequestParam(defaultValue = "10") Integer size) {
        try {
            log.info("获取最新文章，数量: {}", size);
            List<ArticleEntity> articles = baseEsService.getLatestArticles(size);
            return R.ok(articles, "获取最新文章成功");
        } catch (Exception e) {
            log.error("获取最新文章失败", e);
            return R.failed("获取失败: " + e.getMessage());
        }
    }

    @GetMapping("/recommended")
    public R<List<ArticleEntity>> getRecommendedArticles(
            @RequestParam(defaultValue = "10") Integer size) {
        try {
            log.info("获取推荐文章，数量: {}", size);
            List<ArticleEntity> articles = baseEsService.getRecommendedArticles(size);
            return R.ok(articles, "获取推荐文章成功");
        } catch (Exception e) {
            log.error("获取推荐文章失败", e);
            return R.failed("获取失败: " + e.getMessage());
        }
    }

    // ==================== 文档导入接口 ====================

    @PostMapping("/import/file")
    public R<ArticleEntity> importFile(
            @RequestParam String fileName,
            @RequestBody String fileContent,
            @RequestParam(defaultValue = "系统导入") String author) {
        try {
            log.info("导入文件: {}", fileName);
            ArticleEntity article = baseEsService.createArticleFromFile(fileName, fileContent, author);
            ArticleEntity savedArticle = baseEsService.saveArticleWithVector(article);
            return R.ok(savedArticle, "文件导入成功");
        } catch (Exception e) {
            log.error("导入文件失败", e);
            return R.failed("导入失败: " + e.getMessage());
        }
    }

    @PostMapping("/import/large-document")
    public R<List<ArticleEntity>> importLargeDocument(
            @RequestParam String title,
            @RequestBody String content,
            @RequestParam(defaultValue = "系统导入") String author,
            @RequestParam(defaultValue = "5000") Integer chunkSize) {
        try {
            log.info("分块导入大文档: {}, 块大小: {}", title, chunkSize);
            List<ArticleEntity> articles = baseEsService.importLargeDocument(title, content, author, chunkSize);
            return R.ok(articles, "大文档导入成功");
        } catch (Exception e) {
            log.error("分块导入大文档失败", e);
            return R.failed("导入失败: " + e.getMessage());
        }
    }

    // ==================== 统计接口 ====================

    @GetMapping("/statistics")
    public R<Map<String, Object>> getStatistics() {
        try {
            log.info("获取文章统计信息");

            Map<String, Object> statistics = Map.of(
                "totalCount", baseEsService.getTotalArticleCount(),
                "publishedCount", baseEsService.getPublishedArticleCount(),
                "draftCount", baseEsService.getDraftArticleCount(),
                "categoryStats", baseEsService.getArticleCountByCategory(),
                "authorStats", baseEsService.getArticleCountByAuthor()
            );

            return R.ok(statistics, "获取统计信息成功");
        } catch (Exception e) {
            log.error("获取文章统计失败", e);
            return R.failed("获取统计失败: " + e.getMessage());
        }
    }
}
```

## 6. 总结

本指南详细介绍了基于新架构的 Trina Elasticsearch 模块的使用方法，新架构具有以下特点和优势：

### 6.1 新架构特点

- **统一服务接口**：通过 `ElasticsearchService` 提供统一的操作接口
- **原生 DSL 支持**：直接使用 Elasticsearch 原生查询语法，更加灵活
- **简化的实体设计**：继承 `BaseDocument`，减少注解依赖
- **类型安全**：泛型支持，编译时类型检查
- **更好的性能**：减少中间层转换，提高查询效率

### 6.2 核心功能覆盖

- **基础配置**：简化的配置方式，支持多种连接参数
- **文档操作**：完整的 CRUD 操作，支持批量处理
- **高级查询**：分页、高亮、聚合、复杂条件查询等功能
- **向量搜索**：支持 KNN 搜索、向量相似度搜索和混合搜索
- **索引管理**：索引的创建、删除、刷新、映射管理等操作
- **最佳实践**：性能优化和常见问题解决方案

### 6.3 使用建议

1. **查询优化**：
   - 优先使用 `filter` 查询，避免不必要的评分计算
   - 合理使用分页，深度分页时使用 `search_after`
   - 只返回必要的字段，减少网络传输

2. **索引设计**：
   - 合理设置字段类型和分析器
   - 控制字段数量，避免映射爆炸
   - 使用索引模板管理多个相似索引

3. **性能监控**：
   - 定期监控查询性能和集群健康状态
   - 使用 `profile` API 分析慢查询
   - 合理设置刷新间隔和分片数量

4. **错误处理**：
   - 实现适当的异常处理和重试机制
   - 监控连接池状态和资源使用情况
   - 建立完善的日志记录和告警机制

### 6.4 迁移指南

从旧架构迁移到新架构时，主要变化包括：

- 移除 Spring Data Elasticsearch 依赖的注解
- 使用 `ElasticsearchService` 替代 Repository 模式
- 查询语法从 `BaseEsQuery` 改为原生 DSL Map
- 实体类继承 `BaseDocument` 而非使用 `@Document` 注解

通过合理使用新架构的功能，可以构建更加高效、稳定和可维护的搜索系统。

如有其他问题，请参考 Elasticsearch 官方文档或联系技术支持团队。