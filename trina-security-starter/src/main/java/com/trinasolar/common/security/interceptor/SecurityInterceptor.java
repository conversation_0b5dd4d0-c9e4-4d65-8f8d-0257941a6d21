package com.trinasolar.common.security.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.trinasolar.common.core.constant.CacheConstants;
import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.common.security.context.SecurityContextHolder;
import com.trinasolar.common.security.domain.IamUserInfo;
import com.trinasolar.common.security.domain.SecurityContext;
import com.trinasolar.common.security.properties.SecurityProperties;
import com.trinasolar.common.security.service.PermissionService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;
import org.springframework.util.PathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * 安全拦截器
 * <p>
 * iamCache只缓存token验证结果，设置适当过期时间
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SecurityInterceptor extends OncePerRequestFilter {

    private static final PathMatcher PATH_MATCHER = new AntPathMatcher();

    private final ObjectMapper objectMapper;
    private final SecurityProperties securityProperties;
    private final RestTemplate restTemplate;
    private final Cache<String, Object> iamCache;
    private final PermissionService permissionService;

    /**
     * 默认白名单，注意为包含contextPath的路径
     */
    public static final List<String> DEFAULT_WHITE_URLS = List.of(
            "/api/scf/token",
            "/swagger-ui/**", "/swagger-resources/**", "/webjars/**", "/v3/api-docs/**", "/v2/api-docs/**",
            "favicon.ico", "/error", "/actuator/**", "health"
    );

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String path = request.getRequestURI();
        String contextPath = request.getContextPath();
        String authorization = request.getHeader(HttpHeaders.AUTHORIZATION);
        try {
            log.debug("Security interceptor processing request: {} with authorization: {}", path,
                    authorization != null ? "Bearer ***" : "null");

            // 检查是否已经通过WebFilter处理过认证
            // 如果SecurityContext已经设置，说明WebFilter已经处理过认证，直接放行
            if (SecurityContextHolder.getContext() != null &&
                    SecurityContextHolder.getContext().getAuthentication() != null &&
                    SecurityContextHolder.getContext().getAuthentication().isAuthenticated()) {
                log.debug("Request already authenticated by SecurityInterceptor, skipping authentication");
                filterChain.doFilter(request, response);
                return;
            }

            // 1. 默认白名单处理
            if (matchWhiteUrl(DEFAULT_WHITE_URLS, path, contextPath)) {
                filterChain.doFilter(request, response);
                return;
            }

            // 2. uri 白名单处理
            List<String> whiteUrls = securityProperties.getWhiteUris();
            if (matchWhiteUrl(whiteUrls, path, contextPath)) {
                filterChain.doFilter(request, response);
                return;
            }

            // 3. 验证token是否有效

            if (StringUtils.hasText(authorization) && authorization.startsWith(SecurityConstant.TOKEN_PREFIX)) {
                if (!verifyToken(authorization)) {
                    log.error("Token verification failed for request: {}", path);
                    writeErrorMessage(response, "token 无效，验证失败");
                    return;
                }
            } else {
                writeErrorMessage(response, "token 为空");
                return;
            }
            filterChain.doFilter(request, response);
        } catch (Exception e) {
            log.error("Token verification exception for request: {}", path, e);
            writeErrorMessage(response, "token 验证异常");
        }
    }

    /**
     * 匹配白名单URL
     */
    private boolean matchWhiteUrl(List<String> whiteUrls, String path, String contextPath) {
        if (CollectionUtils.isEmpty(whiteUrls)) {
            return false;
        }

        for (String whiteUrl : whiteUrls) {
            String pattern = StringUtils.hasText(contextPath) ? contextPath + whiteUrl : whiteUrl;
            if (PATH_MATCHER.match(pattern, path)) {
                log.debug("Request {} matched white URL pattern: {}", path, pattern);
                return true;
            }
        }
        return false;
    }

    /**
     * 验证token
     * <p>
     * iamCache只缓存token信息，避免重复远程验证
     */
    private boolean verifyToken(String authorization) {
        try {
            String token = authorization.substring(SecurityConstant.TOKEN_PREFIX.length());
            String tokenCacheKey = CacheConstants.buildTokenInfoKey(token);

            // 先检查token缓存
            Object cachedResult = iamCache.getIfPresent(tokenCacheKey);
            if (cachedResult != null) {
                return true;
            }

            // 缓存未命中，远程验证token
            return verifyTokenRemotelyAndSetContext(authorization);

        } catch (Exception e) {
            log.error("Error verifying token and setting context", e);
            return false;
        }
    }

    /**
     * 远程验证token并设置安全上下文
     * 1. 获取 IAM 用户信息
     * 2. 获取用户权限信息
     * 3. 构建安全上下文SecurityContextHolder存储完整的安全上下文信息
     */
    private boolean verifyTokenRemotelyAndSetContext(String authorization) {
        try {
            String url = securityProperties.getEnvUrl() + SecurityConstant.USERINFO_PATH;

            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.set(HttpHeaders.AUTHORIZATION, authorization);

            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    url, HttpMethod.GET, requestEntity, String.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                String body = response.getBody();
                IamUserInfo userInfo = objectMapper.readValue(body, IamUserInfo.class);
                log.debug("Remote get userInfo: {}", userInfo);

                // 构建安全上下文
                String token = authorization.substring(SecurityConstant.TOKEN_PREFIX.length());
                SecurityContext securityContext = buildSecurityContextFromUserInfo(userInfo, token);

                // 填充权限信息
                fillPermissionsToContext(securityContext, authorization);

                String tokenCacheKey = CacheConstants.buildTokenInfoKey(token);
                iamCache.put(tokenCacheKey, token);
                // 设置到ThreadLocal
                SecurityContextHolder.setContext(securityContext);

                log.debug("Security context set from remote for user: {}", userInfo.getUid());
                return true;
            }

            log.error("Remote userinfo request failed with status: {}", response.getStatusCode());
            return false;

        } catch (Exception e) {
            log.error("Remote token verification failed", e);
            return false;
        }
    }

    /**
     * 构建安全上下文（从IamUserInfo）
     */
    private SecurityContext buildSecurityContextFromUserInfo(IamUserInfo userInfo, String token) {
        SecurityContext context = new SecurityContext();

        // 设置用户主体信息
        SecurityContext.UserPrincipal principal = new SecurityContext.UserPrincipal();
        principal.setUserId(userInfo.getUid());
        principal.setUsername(userInfo.getCn());
        principal.setDisplayName(userInfo.getDisplayname());
        principal.setEmail(userInfo.getEmail());
        principal.setDeptId(userInfo.getDepartmentnumber());
        principal.setEmployeeNumber(userInfo.getEmployeenumber());
        principal.setPostType(userInfo.getPosttype());
        principal.setLocation(userInfo.getLocation());
        principal.setFilled(true);
        context.setPrincipal(principal);

        // 设置认证信息
        SecurityContext.Authentication authentication = new SecurityContext.Authentication();
        authentication.setAccessToken(token);
        authentication.setTokenType("Bearer");
        authentication.setAuthenticated(true);
        authentication.setAuthenticatedAt(LocalDateTime.now());
        context.setAuthentication(authentication);
        return context;
    }

    /**
     * 填充权限信息到安全上下文
     */
    private void fillPermissionsToContext(SecurityContext context, String token) {
        if (context.getPrincipal() == null || context.getPrincipal().getUserId() == null) {
            return;
        }

        String userId = context.getPrincipal().getUserId();

        // 设置授权信息
        SecurityContext.Authorization authorization = permissionService.getUserPermissions(token, userId);
        authorization.setPermissionsLoadedAt(LocalDateTime.now());
        context.setAuthorization(authorization);

        log.debug("Permissions loaded for user: {} - roles: {}, permissions: {}",
                userId, authorization.getRoleCodes().size(), authorization.getPermissions().size());
    }

    /**
     * 写入错误消息
     *
     * @param response 响应
     * @param message  消息
     * @throws IOException IOException
     */
    private void writeErrorMessage(HttpServletResponse response, String message) throws IOException {
        String authorizeUrl = securityProperties.getEnvUrl() + SecurityConstant.AUTHORIZE_PATH + securityProperties.getClientId() + SecurityConstant.REDIRECT_URI + securityProperties.getRedirectUrl();
        HashMap<String, Object> result = new HashMap<>();
        result.put("code", 1);
        result.put("msg", message);
        result.put("data", authorizeUrl);

        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write(objectMapper.writeValueAsString(result));
        response.getWriter().flush();
    }
}
