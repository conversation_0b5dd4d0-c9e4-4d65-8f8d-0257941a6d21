package com.trinasolar.common.core.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat; // Retained for Date formatting performance

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat; // Used for specific Calendar/Date operations and some parsing
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.*;

/**
 * 综合时间工具类.
 * 优先使用 java.time API (LocalDate, LocalDateTime, etc.).
 * 继承自 org.apache.commons.lang3.time.DateUtils 以便利用其已有功能.
 */
@Slf4j
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    //<editor-fold desc="常量定义">
    public static final String YYYY = "yyyy";
    public static final String YYYY_MM = "yyyy-MM";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String YYYYMMDD = "yyyyMMdd";
    public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String HH_MM_SS = "HH:mm:ss";

    public static final DateTimeFormatter YYYY_MM_DD_FORMATTER = DateTimeFormatter.ofPattern(YYYY_MM_DD);
    public static final DateTimeFormatter YYYY_MM_DD_HH_MM_SS_FORMATTER = DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS);
    public static final DateTimeFormatter YYYYMMDD_FORMATTER = DateTimeFormatter.ofPattern(YYYYMMDD);
    public static final DateTimeFormatter YYYYMMDDHHMMSS_FORMATTER = DateTimeFormatter.ofPattern(YYYYMMDDHHMMSS);

    private static final String[] DEFAULT_PARSE_PATTERNS = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM",
            "yyyyMMddHHmmss", "yyyyMMdd"
    };
    //</editor-fold>

    //<editor-fold desc="私有构造函数，防止实例化">
    private DateUtils() {
        // 工具类不应被实例化
    }
    //</editor-fold>

    //<editor-fold desc="获取当前时间">

    /**
     * 获取当前 Date 型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前 LocalDateTime 型日期时间
     *
     * @return LocalDateTime 当前日期时间
     */
    public static LocalDateTime getNowLocalDateTime() {
        return LocalDateTime.now();
    }

    /**
     * 获取当前 LocalDate 型日期
     *
     * @return LocalDate 当前日期
     */
    public static LocalDate getNowLocalDate() {
        return LocalDate.now();
    }

    /**
     * 获取当前日期字符串, 格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getCurrentDateStr() {
        return LocalDate.now().format(YYYY_MM_DD_FORMATTER);
    }

    /**
     * 获取当前日期时间字符串, 格式为yyyy-MM-dd HH:mm:ss
     *
     * @return String
     */
    public static String getCurrentDateTimeStr() {
        return LocalDateTime.now().format(YYYY_MM_DD_HH_MM_SS_FORMATTER);
    }

    /**
     * 获取当前日期时间字符串, 格式为yyyyMMddHHmmss
     *
     * @return String
     */
    public static String getCurrentCompactDateTimeStr() {
        return LocalDateTime.now().format(YYYYMMDDHHMMSS_FORMATTER);
    }

    /**
     * 获取当前日期时间字符串, 使用指定格式
     *
     * @param format 格式
     * @return String
     */
    public static String getCurrentDateTimeStr(String format) {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern(format));
    }

    /**
     * 获取服务器启动时间
     *
     * @return Date 服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }
    //</editor-fold>

    //<editor-fold desc="格式化 (对象 -> 字符串)">

    /**
     * 格式化 Date 对象为字符串
     *
     * @param date    Date 对象
     * @param pattern 格式, e.g., "yyyy-MM-dd HH:mm:ss"
     * @return 格式化后的字符串, 或 null 如果 date 为 null
     */
    public static String format(Date date, String pattern) {
        if (date == null) {
            return null;
        }
        String finalPattern = StringUtils.isBlank(pattern) ? YYYY_MM_DD_HH_MM_SS : pattern;
        return FastDateFormat.getInstance(finalPattern).format(date);
    }

    /**
     * 格式化 Date 对象为 "yyyy-MM-dd" 格式字符串
     *
     * @param date Date 对象
     * @return 格式化后的字符串, 或 null 如果 date 为 null
     */
    public static String formatToDateStr(Date date) {
        return format(date, YYYY_MM_DD);
    }

    /**
     * 格式化 Date 对象为 "yyyy-MM-dd HH:mm:ss" 格式字符串
     *
     * @param date Date 对象
     * @return 格式化后的字符串, 或 null 如果 date 为 null
     */
    public static String formatToDateTimeStr(Date date) {
        return format(date, YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 格式化 LocalDateTime 对象为字符串
     *
     * @param localDateTime LocalDateTime 对象
     * @param pattern       格式, e.g., "yyyy-MM-dd HH:mm:ss"
     * @return 格式化后的字符串, 或 null 如果 localDateTime 为 null
     */
    public static String format(LocalDateTime localDateTime, String pattern) {
        if (localDateTime == null) {
            return null;
        }
        String finalPattern = StringUtils.isBlank(pattern) ? YYYY_MM_DD_HH_MM_SS : pattern;
        return localDateTime.format(DateTimeFormatter.ofPattern(finalPattern));
    }

    /**
     * 格式化 LocalDateTime 对象为 "yyyy-MM-dd HH:mm:ss" 字符串
     *
     * @param localDateTime LocalDateTime 对象
     * @return 格式化后的字符串, 或 null 如果 localDateTime 为 null
     */
    public static String formatToDateTimeStr(LocalDateTime localDateTime) {
        return format(localDateTime, YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 格式化 LocalDate 对象为字符串
     *
     * @param localDate LocalDate 对象
     * @param pattern   格式, e.g., "yyyy-MM-dd"
     * @return 格式化后的字符串, 或 null 如果 localDate 为 null
     */
    public static String format(LocalDate localDate, String pattern) {
        if (localDate == null) {
            return null;
        }
        String finalPattern = StringUtils.isBlank(pattern) ? YYYY_MM_DD : pattern;
        return localDate.format(DateTimeFormatter.ofPattern(finalPattern));
    }

    /**
     * 格式化 LocalDate 对象为 "yyyy-MM-dd" 字符串
     *
     * @param localDate LocalDate 对象
     * @return 格式化后的字符串, 或 null 如果 localDate 为 null
     */
    public static String formatToDateStr(LocalDate localDate) {
        return format(localDate, YYYY_MM_DD);
    }

    /**
     * 格式化 LocalDate 为 "yyyy" 年份字符串
     *
     * @param localDate LocalDate 对象
     * @return 年份字符串, 或 null 如果 localDate 为 null
     */
    public static String formatToYearStr(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        return localDate.format(DateTimeFormatter.ofPattern(YYYY));
    }

    /**
     * 格式化 LocalDate 为 "yyyyMMdd" 字符串
     *
     * @param localDate LocalDate 对象
     * @return yyyyMMdd 格式字符串, 或 null 如果 localDate 为 null
     */
    public static String formatToYYYYMMDDStr(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        return localDate.format(YYYYMMDD_FORMATTER);
    }

    /**
     * 将 "yyyy-MM-dd" 格式的 LocalDate 转换为 "dd-MM-yyyy" 格式字符串
     *
     * @param localDate LocalDate对象
     * @return "dd-MM-yyyy" 格式字符串, 或 null 如果 localDate 为 null
     */
    public static String formatLocalDateToDdMmYyyy(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        return localDate.format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static String getDatePath() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static String getCompactDatePath() {
        return LocalDate.now().format(YYYYMMDD_FORMATTER);
    }
    //</editor-fold>

    //<editor-fold desc="解析 (字符串 -> 对象)">

    /**
     * 解析日期字符串为 Date 对象 (自动匹配多种格式)
     *
     * @param str 日期字符串
     * @return Date 对象, 或 null 如果解析失败
     */
    public static Date parseDate(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        try {
            return parseDate(str, DEFAULT_PARSE_PATTERNS);
        } catch (ParseException e) {
            log.warn("Failed to parse date string '{}' with default patterns", str, e);
            return null;
        }
    }

    /**
     * 解析日期字符串为 Date 对象 (使用指定格式)
     *
     * @param str     日期字符串
     * @param pattern 格式
     * @return Date 对象, 或 null 如果解析失败
     */
    public static Date parseDate(String str, String pattern) {
        if (StringUtils.isBlank(str) || StringUtils.isBlank(pattern)) {
            return null;
        }
        try {
            return new SimpleDateFormat(pattern).parse(str);
        } catch (ParseException e) {
            log.warn("Failed to parse date string '{}' with pattern '{}'", str, pattern, e);
            return null;
        }
    }

    /**
     * 解析日期时间字符串为 LocalDateTime 对象
     *
     * @param str     日期时间字符串
     * @param pattern 格式
     * @return LocalDateTime 对象, 或 null 如果解析失败
     */
    public static LocalDateTime parseLocalDateTime(String str, String pattern) {
        if (StringUtils.isBlank(str) || StringUtils.isBlank(pattern)) {
            return null;
        }
        try {
            return LocalDateTime.parse(str, DateTimeFormatter.ofPattern(pattern));
        } catch (DateTimeParseException e) {
            log.warn("Failed to parse LocalDateTime string '{}' with pattern '{}'", str, pattern, e);
            return null;
        }
    }

    /**
     * 解析 "yyyy-MM-dd HH:mm:ss" 格式的字符串为 LocalDateTime 对象
     *
     * @param str 日期时间字符串
     * @return LocalDateTime 对象, 或 null 如果解析失败
     */
    public static LocalDateTime parseLocalDateTimeFromDefaultFormat(String str) {
        return parseLocalDateTime(str, YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 解析日期字符串为 LocalDate 对象
     *
     * @param str     日期字符串
     * @param pattern 格式
     * @return LocalDate 对象, 或 null 如果解析失败
     */
    public static LocalDate parseLocalDate(String str, String pattern) {
        if (StringUtils.isBlank(str) || StringUtils.isBlank(pattern)) {
            return null;
        }
        try {
            return LocalDate.parse(str, DateTimeFormatter.ofPattern(pattern));
        } catch (DateTimeParseException e) {
            log.warn("Failed to parse LocalDate string '{}' with pattern '{}'", str, pattern, e);
            return null;
        }
    }

    /**
     * 解析 "yyyy-MM-dd" 格式的字符串为 LocalDate 对象
     *
     * @param str 日期字符串
     * @return LocalDate 对象, 或 null 如果解析失败
     */
    public static LocalDate parseLocalDateFromDefaultFormat(String str) {
        return parseLocalDate(str, YYYY_MM_DD);
    }


    /**
     * 将 "dd-MM-yyyy" 格式字符串转换为 LocalDate 对象
     *
     * @param str "dd-MM-yyyy" 格式的日期字符串
     * @return LocalDate 对象, 或 null 如果解析失败
     */
    public static LocalDate parseLocalDateFromDdMmYyyy(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        return parseLocalDate(str, "dd-MM-yyyy");
    }

    /**
     * 转换日期字符串格式
     * @param dateStr 日期字符串
     * @param beforePattern 原始格式
     * @param afterPattern 目标格式
     * @return 转换后格式的日期字符串, 或原始字符串如果转换失败或输入为空
     */
    public static String reformatDateString(String dateStr, String beforePattern, String afterPattern) {
        if (StringUtils.isAnyBlank(dateStr, beforePattern, afterPattern)) {
            return dateStr;
        }
        try {
            // 尝试使用 java.time first for robustness
            // If it contains time, use LocalDateTime, otherwise LocalDate
            if (beforePattern.contains("H") || beforePattern.contains("h") || beforePattern.contains("m") || beforePattern.contains("s")) {
                LocalDateTime localDateTime = LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern(beforePattern, Locale.ENGLISH));
                return localDateTime.format(DateTimeFormatter.ofPattern(afterPattern));
            } else {
                LocalDate localDate = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(beforePattern, Locale.ENGLISH));
                return localDate.format(DateTimeFormatter.ofPattern(afterPattern));
            }
        } catch (DateTimeParseException e) {
            log.warn("Failed to reformat date string '{}' from '{}' to '{}' using java.time. Trying SimpleDateFormat.", dateStr, beforePattern, afterPattern, e);
            // Fallback to SimpleDateFormat for broader compatibility with unusual patterns or locales
            try {
                SimpleDateFormat sdfBefore = new SimpleDateFormat(beforePattern, Locale.ENGLISH);
                Date date = sdfBefore.parse(dateStr);
                SimpleDateFormat sdfAfter = new SimpleDateFormat(afterPattern);
                return sdfAfter.format(date);
            } catch (ParseException pe) {
                log.error("Failed to reformat date string '{}' from '{}' to '{}' using SimpleDateFormat as well.", dateStr, beforePattern, afterPattern, pe);
                return dateStr; // Return original on failure
            }
        }
    }

    /**
     * 解析日期字符串和时间字符串（HH:mm:ss）到 LocalDateTime
     * 如果 dateStr 本身是 "yyyy-MM-dd HH:mm:ss" 格式, timeOfDay 会被忽略
     *
     * @param dateStr 日期部分字符串 (e.g., "2023-10-26") 或完整日期时间字符串
     * @param timeOfDay 时间部分字符串 (e.g., " 15:30:00" or "15:30:00")
     * @return LocalDateTime 对象, 或 null 如果解析失败
     */
    public static LocalDateTime parseToLocalDateTime(String dateStr, String timeOfDay) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        try {
            // Check if dateStr is already a full datetime string
            return parseLocalDateTime(dateStr, YYYY_MM_DD_HH_MM_SS);
        } catch (Exception e) {
            // If not, assume it's a date string and append timeOfDay
            try {
                LocalDate localDate = parseLocalDate(dateStr, YYYY_MM_DD);
                if (localDate != null && StringUtils.isNotBlank(timeOfDay)) {
                    LocalTime localTime = LocalTime.parse(timeOfDay.trim(), DateTimeFormatter.ofPattern(HH_MM_SS));
                    return LocalDateTime.of(localDate, localTime);
                } else if (localDate != null) {
                    return localDate.atStartOfDay(); // Default to start of day if timeOfDay is blank
                }
                return null;
            } catch (Exception ex) {
                log.warn("Failed to parse date string '{}' and time string '{}' to LocalDateTime", dateStr, timeOfDay, ex);
                return null;
            }
        }
    }
    //</editor-fold>

    //<editor-fold desc="类型转换 (Date <-> java.time)">

    /**
     * Date 转换为 LocalDateTime
     *
     * @param date Date 对象
     * @return LocalDateTime 对象, 或 null 如果 date 为 null
     */
    public static LocalDateTime toLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * Date 转换为 LocalDate
     *
     * @param date Date 对象
     * @return LocalDate 对象, 或 null 如果 date 为 null
     */
    public static LocalDate toLocalDate(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    /**
     * LocalDateTime 转换为 Date
     *
     * @param localDateTime LocalDateTime 对象
     * @return Date 对象, 或 null 如果 localDateTime 为 null
     */
    public static Date toDate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * LocalDate 转换为 Date (时间部分为午夜00:00:00)
     *
     * @param localDate LocalDate 对象
     * @return Date 对象, 或 null 如果 localDate 为 null
     */
    public static Date toDate(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }
    //</editor-fold>

    //<editor-fold desc="日期计算与操作">

    /**
     * 获取两个 LocalDate 日期之间的所有日期列表 (包含开始和结束日期)
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 日期列表. 如果 endDate 在 startDate 之前, 返回空列表.
     */
    public static List<LocalDate> getDatesBetween(LocalDate startDate, LocalDate endDate) {
        List<LocalDate> dates = new ArrayList<>();
        if (startDate == null || endDate == null || endDate.isBefore(startDate)) {
            return dates;
        }
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            dates.add(currentDate);
            currentDate = currentDate.plusDays(1);
        }
        return dates;
    }

    /**
     * 计算两个日期时间之间相差的天数 (忽略时间部分)
     *
     * @param startInclusive 开始日期时间 (包含)
     * @param endExclusive   结束日期时间 (不包含)
     * @return 相差天数. 如果 endExclusive 在 startInclusive 之前, 返回负数.
     */
    public static long daysBetween(LocalDateTime startInclusive, LocalDateTime endExclusive) {
        if (startInclusive == null || endExclusive == null) {
            return 0; // Or throw IllegalArgumentException
        }
        return ChronoUnit.DAYS.between(startInclusive.toLocalDate(), endExclusive.toLocalDate());
    }

    /**
     * 计算两个日期之间相差的天数 (忽略时间部分)
     *
     * @param startInclusive 开始日期 (包含)
     * @param endExclusive   结束日期 (不包含)
     * @return 相差天数. 如果 endExclusive 在 startInclusive 之前, 返回负数.
     */
    public static long daysBetween(LocalDate startInclusive, LocalDate endExclusive) {
        if (startInclusive == null || endExclusive == null) {
            return 0; // Or throw IllegalArgumentException
        }
        return ChronoUnit.DAYS.between(startInclusive, endExclusive);
    }

    /**
     * 计算两个 Date 之间的时间差，并格式化为 "x天y小时z分钟"
     *
     * @param endDate 结束时间
     * @param nowDate 开始时间
     * @return 格式化的时间差字符串
     */
    public static String formatDuration(Date endDate, Date nowDate) {
        if (endDate == null || nowDate == null) {
            return "";
        }
        long diff = endDate.getTime() - nowDate.getTime();
        if (diff < 0) {
            // Or handle negative duration appropriately
            return "结束时间早于开始时间";
        }

        long days = diff / (1000 * 60 * 60 * 24);
        long hours = (diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60);
        long minutes = (diff % (1000 * 60 * 60)) / (1000 * 60);
        // long seconds = (diff % (1000 * 60)) / 1000;

        return days + "天" + hours + "小时" + minutes + "分钟";
    }

    /**
     * 将毫秒数转换为 "x分y秒" 格式的字符串
     *
     * @param milliseconds 毫秒数
     * @return "x分y秒" 格式的字符串
     */
    public static String formatMillisecondsToMinutesSeconds(long milliseconds) {
        if (milliseconds < 0) {
            return "0分0秒"; // Or handle negative duration
        }
        Duration duration = Duration.ofMillis(milliseconds);
        long minutes = duration.toMinutes();
        long seconds = duration.minusMinutes(minutes).getSeconds();
        return minutes + "分" + seconds + "秒";
    }

    /**
     * 获取指定日期所在年份的第几周 (周一为一周的开始)
     *
     * @param date Date 对象
     * @return 年度的第几周
     */
    public static int getWeekOfYear(Date date) {
        if (date == null) return 0; // Or throw
        return toLocalDate(date).get(WeekFields.ISO.weekOfWeekBasedYear());
    }

    /**
     * 获取指定日期所在年份的第几周 (周一为一周的开始)
     *
     * @param localDate LocalDate 对象
     * @return 年度的第几周
     */
    public static int getWeekOfYear(LocalDate localDate) {
        if (localDate == null) return 0; // Or throw
        return localDate.get(WeekFields.ISO.weekOfWeekBasedYear());
    }

    /**
     * 获取指定日期所在周的开始日期 (周一)
     *
     * @param date Date 对象
     * @return LocalDate 类型的周开始日期
     */
    public static LocalDate getStartOfWeek(Date date) {
        if (date == null) return null;
        return toLocalDate(date).with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
    }

    /**
     * 获取指定日期所在周的开始日期 (周一)
     *
     * @param localDate LocalDate 对象
     * @return LocalDate 类型的周开始日期
     */
    public static LocalDate getStartOfWeek(LocalDate localDate) {
        if (localDate == null) return null;
        return localDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
    }

    /**
     * 获取指定日期所在周的结束日期 (周日)
     *
     * @param date Date 对象
     * @return LocalDate 类型的周结束日期
     */
    public static LocalDate getEndOfWeek(Date date) {
        if (date == null) return null;
        return toLocalDate(date).with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
    }

    /**
     * 获取指定日期所在周的结束日期 (周日)
     *
     * @param localDate LocalDate 对象
     * @return LocalDate 类型的周结束日期
     */
    public static LocalDate getEndOfWeek(LocalDate localDate) {
        if (localDate == null) return null;
        return localDate.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
    }

    /**
     * 获取指定日期所在月份的第一天
     *
     * @param date Date 对象
     * @return LocalDate 类型的月份第一天
     */
    public static LocalDate getStartOfMonth(Date date) {
        if (date == null) return null;
        return toLocalDate(date).with(TemporalAdjusters.firstDayOfMonth());
    }

    /**
     * 获取指定日期所在月份的第一天
     *
     * @param localDate LocalDate 对象
     * @return LocalDate 类型的月份第一天
     */
    public static LocalDate getStartOfMonth(LocalDate localDate) {
        if (localDate == null) return null;
        return localDate.with(TemporalAdjusters.firstDayOfMonth());
    }

    /**
     * 获取指定日期所在月份的最后一天
     *
     * @param date Date 对象
     * @return LocalDate 类型的月份最后一天
     */
    public static LocalDate getEndOfMonth(Date date) {
        if (date == null) return null;
        return toLocalDate(date).with(TemporalAdjusters.lastDayOfMonth());
    }

    /**
     * 获取指定日期所在月份的最后一天
     *
     * @param localDate LocalDate 对象
     * @return LocalDate 类型的月份最后一天
     */
    public static LocalDate getEndOfMonth(LocalDate localDate) {
        if (localDate == null) return null;
        return localDate.with(TemporalAdjusters.lastDayOfMonth());
    }

    /**
     * 获取指定日期的年份
     *
     * @param date Date 对象
     * @return 年份
     */
    public static int getYear(Date date) {
        if (date == null) throw new IllegalArgumentException("Date cannot be null");
        return toLocalDate(date).getYear();
    }

    /**
     * 获取指定日期的月份 (1-12)
     *
     * @param date Date 对象
     * @return 月份 (1-12)
     */
    public static int getMonth(Date date) {
        if (date == null) throw new IllegalArgumentException("Date cannot be null");
        return toLocalDate(date).getMonthValue();
    }

    //</editor-fold>

    //<editor-fold desc="日期/时间校验与比较">

    /**
     * 校验日期字符串是否为合法的 "yyyy-MM-dd" 格式
     *
     * @param dateStr 日期字符串
     * @return true 如果合法, false 如果不合法或为空
     */
    public static boolean isValidLocalDate(String dateStr) {
        return isValidLocalDate(dateStr, YYYY_MM_DD);
    }

    /**
     * 校验日期字符串是否为指定格式的合法日期
     *
     * @param dateStr 日期字符串
     * @param pattern 格式
     * @return true 如果合法, false 如果不合法或为空
     */
    public static boolean isValidLocalDate(String dateStr, String pattern) {
        if (StringUtils.isAnyBlank(dateStr, pattern)) {
            return false;
        }
        try {
            LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(pattern));
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    /**
     * 校验日期时间字符串是否为合法的 "yyyy-MM-dd HH:mm:ss" 格式
     *
     * @param dateTimeStr 日期时间字符串
     * @return true 如果合法, false 如果不合法或为空
     */
    public static boolean isValidLocalDateTime(String dateTimeStr) {
        return isValidLocalDateTime(dateTimeStr, YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 校验日期时间字符串是否为指定格式的合法日期时间
     *
     * @param dateTimeStr 日期时间字符串
     * @param pattern     格式
     * @return true 如果合法, false 如果不合法或为空
     */
    public static boolean isValidLocalDateTime(String dateTimeStr, String pattern) {
        if (StringUtils.isAnyBlank(dateTimeStr, pattern)) {
            return false;
        }
        try {
            LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern(pattern));
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    /**
     * 判断指定日期时间是否在本周 (周一到周日)
     *
     * @param localDateTime 要判断的日期时间
     * @return true 如果在本周, false 如果不在或输入为null
     */
    public static boolean isThisWeek(LocalDateTime localDateTime) {
        if (localDateTime == null) return false;
        LocalDate date = localDateTime.toLocalDate();
        LocalDate today = LocalDate.now();
        return getWeekOfYear(date) == getWeekOfYear(today) && date.getYear() == today.getYear();
    }

    /**
     * 判断指定日期时间是否在本月
     *
     * @param localDateTime 要判断的日期时间
     * @return true 如果在本月, false 如果不在或输入为null
     */
    public static boolean isThisMonth(LocalDateTime localDateTime) {
        if (localDateTime == null) return false;
        LocalDate date = localDateTime.toLocalDate();
        LocalDate today = LocalDate.now();
        return date.getMonthValue() == today.getMonthValue() && date.getYear() == today.getYear();
    }

    /**
     * 判断指定日期是否为下一周的日期 (基于当前日期)
     *
     * @param localDate 要判断的日期
     * @return true 如果是下一周, false 如果不是或输入为null
     */
    public static boolean isNextWeek(LocalDate localDate) {
        if (localDate == null) return false;
        LocalDate today = LocalDate.now();
        // WeekFields.ISO starts week on Monday and week-based-year.
        WeekFields weekFields = WeekFields.ISO;
        int targetWeek = localDate.get(weekFields.weekOfWeekBasedYear());
        int targetYear = localDate.get(weekFields.weekBasedYear());

        LocalDate startOfThisWeek = today.with(weekFields.dayOfWeek(), 1);
        LocalDate startOfNextWeek = startOfThisWeek.plusWeeks(1);

        int nextWeekNumber = startOfNextWeek.get(weekFields.weekOfWeekBasedYear());
        int nextWeekYear = startOfNextWeek.get(weekFields.weekBasedYear());

        return targetWeek == nextWeekNumber && targetYear == nextWeekYear;
    }

    /**
     * 判断指定日期是否为下一月的日期 (基于当前日期)
     *
     * @param localDate 要判断的日期
     * @return true 如果是下一月, false 如果不是或输入为null
     */
    public static boolean isNextMonth(LocalDate localDate) {
        if (localDate == null) return false;
        LocalDate today = LocalDate.now();
        YearMonth targetYearMonth = YearMonth.from(localDate);
        YearMonth nextMonthFromToday = YearMonth.from(today).plusMonths(1);
        return targetYearMonth.equals(nextMonthFromToday);
    }
    //</editor-fold>

    //<editor-fold desc="Deprecated or less preferred methods (for compatibility or specific cases)">
    // Example: methods from original files that might be kept for specific legacy reasons
    // but new development should use the java.time based versions.
    // Add them here if necessary, with @Deprecated tag and explanation.

    /**
     * 格式化日期时间戳
     * @deprecated 已废弃, 请使用 {@link #format(Date, String)} 或 {@link #format(LocalDateTime, String)}
     */
    @Deprecated
    public static String formatDate(long dateTime, String pattern) {
        return format(new Date(dateTime), pattern);
    }

    /**
     * 得到日期字符串 默认格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
    public static String formatDate(Date date, String pattern) {
        String formatDate = null;
        if (date != null) {
//			if (StringUtils.isNotBlank(pattern)) {
//				formatDate = DateFormatUtils.format(date, pattern);
//			} else {
//				formatDate = DateFormatUtils.format(date, "yyyy-MM-dd");
//			}
            if (StringUtils.isBlank(pattern)) {
                pattern = "yyyy-MM-dd";
            }
            formatDate = FastDateFormat.getInstance(pattern).format(date);
        }
        return formatDate;
    }
}