---
title: MyBatis Starter 架构文档
description: Trina MyBatis Starter 的架构设计、核心实现与扩展机制
---

# 1. 概述
Trina MyBatis Starter 基于 MyBatis-Plus，提供企业级数据访问层自动装配、分页、多租户、数据权限、审计字段、动态表名等能力，极大提升开发效率和一致性。

# 2. 核心功能
- 自动装配 MyBatis-Plus 及常用插件
- 分页、乐观锁、动态表名、批量操作
- 多租户与数据权限拦截器
- 审计字段自动填充（创建/更新人、时间）
- 通用 CRUD、条件构造、复杂查询
- 支持自定义 TypeHandler、拦截器扩展

# 3. 技术实现
- `MybatisPlusConfiguration` 自动注入分页、乐观锁、数据权限等插件
- `MybatisProperties` 支持 yml 配置自动绑定
- `MybatisPlusMetaObjectHandler` 实现审计字段自动填充
- `DataScopeInnerInterceptor` 实现数据权限过滤
- `TenantLineInnerInterceptor` 支持多租户隔离
- 支持自定义 TypeHandler、动态表名、SQL 日志等扩展

# 4. 架构图
```mermaid
flowchart TD
    A[应用启动] --> B[MybatisPlusConfiguration]
    B --> C[自动注入插件]
    C --> D[分页/乐观锁/动态表名/多租户/数据权限]
    D --> E[BaseService/Mapper/Controller]
    E --> F[数据库操作]
    C --> G[MetaObjectHandler]
    G --> H[审计字段自动填充]
    style C fill:#E3EFFF,stroke:#4A90E2
    style D fill:#FFF7E3,stroke:#F5A623
```

# 5. 最佳实践
- 统一继承 BaseEntity、BaseService、BaseController，减少重复代码
- 推荐使用 Lambda 条件构造器，避免硬编码字段名
- 分页查询建议设置合理的最大单页条数
- 审计字段自动填充，提升数据可追溯性
- 类型处理器与数据权限拦截器按需扩展

# 6. 常见问题
- 配置未生效：检查 mapper-locations 路径与包名是否一致
- 分页无效：确认分页插件已注入
- 多租户隔离失败：检查 tenant-column 配置
- 自动填充无效：确认实体字段注解与 MetaObjectHandler 实现
- SQL 报错：检查自定义 SQL 与参数类型

# 7. 参考资料
- [MyBatis-Plus 官方文档](https://baomidou.com/)
- [Trina MyBatis 使用指南](../guides/mybatis-usage-guide.mdx)