package com.trinasolar.web.advice;

import com.trinasolar.common.core.base.R;
import com.trinasolar.common.core.exception.AuthenticationException;
import com.trinasolar.common.core.exception.AuthorizationException;
import com.trinasolar.common.core.exception.BusinessException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

/**
 * 全局异常处理
 */
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    @ExceptionHandler(AuthenticationException.class)
    @ResponseBody
    public ResponseEntity<R<String>> authenticationException(AuthenticationException e) {
        log.error("登录已失效:", e);
        return new ResponseEntity<>(R.failed(e.getMessage()), HttpStatus.UNAUTHORIZED);
    }

    @ExceptionHandler(AuthorizationException.class)
    @ResponseBody
    public ResponseEntity<R<String>> authorizationException(AuthorizationException e) {
        log.error("授权失败:", e);
        return new ResponseEntity<>(R.failed(e.getMessage()), HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler(NoResourceFoundException.class)
    public void handleNoResourceFoundException(NoResourceFoundException ex, HttpServletResponse response) {
        // 如果是请求favicon.ico，直接返回204 No Content
        if (ex.getResourcePath().equals("favicon.ico")) {
            response.setStatus(HttpServletResponse.SC_NO_CONTENT);
            return;
        }
        // 其他资源未找到的情况，返回404
        response.setStatus(HttpServletResponse.SC_NOT_FOUND);
    }

    @ExceptionHandler(value = {IllegalArgumentException.class, MethodArgumentTypeMismatchException.class})
    @ResponseBody
    public ResponseEntity<R<String>> exceptionHandler(IllegalArgumentException e) {
        log.error("IllegalArgumentException:", e);
        return new ResponseEntity<>(R.failed("请求输入参数有误，请检查输入内容"), HttpStatus.BAD_REQUEST);
    }

    /**
     * http exception advice
     *
     * @param e e
     * @return {@link ResponseEntity }<{@link R }<{@link String }>>
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseBody
    public ResponseEntity<R<String>> businessException(BusinessException e) {
        HttpStatus status = HttpStatus.OK;
        log.error("BusinessException:", e);
        return new ResponseEntity<>(R.failed(e.getMessage()), status);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseBody
    public ResponseEntity<R<String>> httpMessageNotReadableException(HttpMessageNotReadableException e) {
        log.error("HttpMessageNotReadableException:", e);
        return new ResponseEntity<>(R.failed("请求输入参数有误，请检查输入内容"), HttpStatus.BAD_REQUEST);
    }


    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public ResponseEntity<R<String>> methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
        BindingResult result = e.getBindingResult();
        StringBuilder rs = new StringBuilder();
        if (result.hasErrors()) {
            for (ObjectError p : result.getAllErrors()) {
                FieldError fieldError = (FieldError) p;
                rs.append(fieldError.getField()).append(fieldError.getDefaultMessage()).append(".");
            }
        }

        return new ResponseEntity<>(R.failed(rs.toString()), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(NumberFormatException.class)
    @ResponseBody
    public ResponseEntity<R<String>> numberFormatException(NumberFormatException e) {
        log.error("NumberFormatException:", e);
        return new ResponseEntity<>(R.failed("参数格式有误，请检查输入内容"), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler({Exception.class, Throwable.class})
    public ResponseEntity<Object> handleException(Exception e) {
        log.error("接口执行错误：{}", e.getMessage(), e);
        return new ResponseEntity<>(R.failed("系统执行错误，请联系管理员！"), HttpStatus.INTERNAL_SERVER_ERROR);
    }

}
