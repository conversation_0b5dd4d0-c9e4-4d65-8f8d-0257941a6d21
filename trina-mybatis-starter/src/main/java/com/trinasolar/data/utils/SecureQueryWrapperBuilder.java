package com.trinasolar.data.utils;

import com.trinasolar.data.base.BaseQuery;
import com.trinasolar.data.base.QueryCondition;
import com.trinasolar.data.base.ConditionGroup;
import com.trinasolar.data.base.QueryConditionType;
import com.trinasolar.data.base.LogicalOperator;
import com.trinasolar.data.base.QueryMode;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import lombok.experimental.UtilityClass;
import com.baomidou.mybatisplus.annotation.TableField;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.Collection;
import java.lang.reflect.Field;

/**
 * 安全的查询构建器 - 防止SQL注入
 *
 * <p>在QueryWrapperBuilder的基础上增加字段验证和映射功能</p>
 * <p>通过验证字段是否存在于实体类中，防止SQL注入攻击</p>
 * <p>支持简单模式和高级模式的查询条件构建：</p>
 * <ul>
 *   <li>简单模式：处理query实体字段和排序条件</li>
 *   <li>高级模式：在简单模式基础上，额外处理条件组(conditionsGroups)</li>
 * </ul>
 */
@UtilityClass
public class SecureQueryWrapperBuilder {

    /**
     * 构建安全的查询包装器
     *
     * @param query 查询条件
     * @param entityClass 实体类Class
     * @param <T> 实体类泛型
     * @return 安全的查询包装器
     */
    public <T> QueryWrapper<T> build(BaseQuery<T> query, Class<T> entityClass) {
        if (query == null) {
            return null;
        }
        
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        EntityFieldMapper<T> fieldMapper = new EntityFieldMapper<>(entityClass);

        // 处理查询实体
        processQueryEntity(wrapper, query, entityClass);
        
        // 处理条件组
        if (!query.getConditionsGroups().isEmpty()) {
            applyConditionGroups(wrapper, query, fieldMapper);
        }

        // 处理排序
        applyOrderBy(wrapper, query, fieldMapper);

        return wrapper;
    }
    
    /**
     * 处理查询实体对象中的字段
     * 
     * <p>将实体对象中的非空字段提取为等值查询条件</p>
     * <p>处理步骤：</p>
     * <ol>
     *   <li>设置实体对象（用于表名解析）</li>
     *   <li>通过反射获取实体对象中的所有非空字段</li>
     *   <li>将字段名映射为数据库列名</li>
     *   <li>添加为等值查询条件</li>
     * </ol>
     * 
     * @param wrapper 查询包装器
     * @param query 查询对象
     * @param entityClass 实体类Class
     * @param <T> 实体类型
     */
    private <T> void processQueryEntity(QueryWrapper<T> wrapper, BaseQuery<T> query, Class<T> entityClass) {
        if (query.getQuery() == null) {
            return;
        }
        
        wrapper.setEntity(query.getQuery());
        
        // 反射提取非空字段值并应用为条件
        T queryEntity = query.getQuery();
        Class<?> entityType = queryEntity.getClass();
        EntityFieldMapper<T> queryMapper = new EntityFieldMapper<>(entityClass);
        
        // 处理实体类层次结构中的字段
        Class<?> currentClass = entityType;
        while (currentClass != null && !currentClass.equals(Object.class)) {
            for (Field field : currentClass.getDeclaredFields()) {
                field.setAccessible(true);
                try {
                    Object value = field.get(queryEntity);
                    if (value != null && !Collection.class.isAssignableFrom(field.getType()) 
                        && !Map.class.isAssignableFrom(field.getType())) {
                        // 跳过标记为非数据库字段的属性
                        if (field.isAnnotationPresent(TableField.class)) {
                            TableField tableField = field.getAnnotation(TableField.class);
                            if (!tableField.exist()) {
                                continue;
                            }
                        }
                        
                        String fieldName = field.getName();
                        try {
                            String columnName = queryMapper.validateAndMapToColumn(fieldName);
                            wrapper.eq(columnName, value);
                        } catch (IllegalArgumentException e) {
                            // 字段不存在于实体类中，跳过
                        }
                    }
                } catch (IllegalAccessException e) {
                    // 无法访问字段，跳过
                }
            }
            currentClass = currentClass.getSuperclass();
        }
    }
    
    /**
     * 处理条件组，将条件组转换为SQL条件
     * 
     * <p>如果只有一个条件组，直接应用其条件；如果有多个条件组，使用 AND 逻辑连接它们</p>
     *
     * @param wrapper 查询包装器
     * @param query 查询对象
     * @param fieldMapper 字段映射器
     * @param <T> 实体类型
     */
    private <T> void applyConditionGroups(QueryWrapper<T> wrapper, BaseQuery<T> query, EntityFieldMapper<T> fieldMapper) {
        if (query.getConditionsGroups().isEmpty()) {
            return;
        }

        // Filter out empty condition groups
        List<ConditionGroup> validGroups = new ArrayList<>(query.getConditionsGroups());
        validGroups.removeIf(group -> group == null || CollectionUtils.isEmpty(group.getConditions()));
        
        if (validGroups.isEmpty()) {
            return;
        }

        // If only one condition group, directly apply its conditions
        if (validGroups.size() == 1) {
            applyConditionGroup(wrapper, validGroups.get(0), fieldMapper);
            return;
        }
        
        // Multiple condition groups, use outer logical join
        validGroups.forEach(group -> {
            wrapper.and(w -> applyConditionGroup(w, group, fieldMapper));
        });
    }

    /**
     * 应用单个条件组中的条件
     * 
     * <p>根据条件组的连接类型（AND/OR）创建嵌套的查询条件</p>
     * 
     * @param wrapper 查询包装器
     * @param group 条件组
     * @param fieldMapper 字段映射器
     * @param <T> 实体类型
     */
    private <T> void applyConditionGroup(QueryWrapper<T> wrapper, ConditionGroup group, EntityFieldMapper<T> fieldMapper) {
        if (group == null || CollectionUtils.isEmpty(group.getConditions())) {
            return;
        }
        
        // Make a copy of conditions and filter out those with null fields, types, or invalid values
        List<QueryCondition> validConditions = new ArrayList<>(group.getConditions());
        validConditions.removeIf(condition -> 
            condition == null || 
            condition.getField() == null || 
            condition.getType() == null
        );
        
        // If no valid conditions after filtering, don't add anything to avoid empty parentheses
        if (validConditions.isEmpty()) {
            return;
        }
        
        if (group.getJoinType() == LogicalOperator.AND) {
            wrapper.and(w -> {
                applyGroupConditions(w, validConditions, fieldMapper);
            });
        } else {
            wrapper.or(w -> {
                applyGroupConditions(w, validConditions, fieldMapper);
            });
        }
    }
        
    /**
     * 应用条件组中的所有条件
     * 
     * @param wrapper 查询包装器
     * @param conditions 条件列表
     * @param fieldMapper 字段映射器
     * @param <T> 实体类型
     */
    private <T> void applyGroupConditions(QueryWrapper<T> wrapper, List<QueryCondition> conditions, EntityFieldMapper<T> fieldMapper) {
        if (conditions == null || conditions.isEmpty()) {
            return;
        }
        for (QueryCondition condition : conditions) {
            String columnName = fieldMapper.validateAndMapToColumn(condition.getField());
            applyCondition(wrapper, columnName, condition);
        }
    }

    private <T> void applyOrderBy(QueryWrapper<T> wrapper, BaseQuery<T> query, EntityFieldMapper<T> fieldMapper) {
        // 处理升序字段
        if (query.getAsc() != null && query.getAsc().length > 0) {
            for (String field : query.getAsc()) {
                if (StringUtils.hasText(field)) {
                    try {
                        String columnName = fieldMapper.validateAndMapToColumn(field);
                        wrapper.orderByAsc(columnName);
                    } catch (IllegalArgumentException e) {
                        throw new IllegalArgumentException("Error applying ascending sort on field '" + field + "': " + e.getMessage(), e);
                    }
                }
            }
        }
        
        // 处理降序字段
        if (query.getDesc() != null && query.getDesc().length > 0) {
            for (String field : query.getDesc()) {
                if (StringUtils.hasText(field)) {
                    try {
                        String columnName = fieldMapper.validateAndMapToColumn(field);
                        wrapper.orderByDesc(columnName);
                    } catch (IllegalArgumentException e) {
                        throw new IllegalArgumentException("Error applying descending sort on field '" + field + "': " + e.getMessage(), e);
                    }
                }
            }
        }
    }

    /**
     * 应用单个查询条件
     * 
     * <p>根据条件类型（等于、不等于、大于、小于等）生成对应的SQL条件</p>
     * <p>对于需要值的条件，会先验证值是否为空</p>
     * 
     * @param wrapper 查询包装器
     * @param columnName 列名
     * @param condition 查询条件
     * @param <T> 实体类型
     */
    private <T> void applyCondition(QueryWrapper<T> wrapper, String columnName, QueryCondition condition) {
        if (condition == null || condition.getType() == null) {
            return;
        }
        
        Object value = condition.getValue();
        QueryConditionType type = condition.getType();
        
        // 对于需要值的条件类型，验证值不为空
        switch (type) {
            case EQUAL:
                if (value != null) {
                    wrapper.eq(columnName, value);
                }
                break;
            case NOT_EQUAL:
                if (value != null) {
                    wrapper.ne(columnName, value);
                }
                break;
            case GREATER_THAN:
                if (value != null) {
                    wrapper.gt(columnName, value);
                }
                break;
            case GREATER_THAN_OR_EQUAL:
                if (value != null) {
                    wrapper.ge(columnName, value);
                }
                break;
            case LESS_THAN:
                if (value != null) {
                    wrapper.lt(columnName, value);
                }
                break;
            case LESS_THAN_OR_EQUAL:
                if (value != null) {
                    wrapper.le(columnName, value);
                }
                break;
            case LIKE:
            case CONTAINS:
                if (value != null) {
                    wrapper.like(columnName, value);
                }
                break;
            case NOT_LIKE:
                if (value != null) {
                    wrapper.notLike(columnName, value);
                }
                break;
            case IN:
                if (value instanceof List) {
                    List<?> list = new ArrayList<>((List<?>) value);
                    list.removeIf(item -> item == null);
                    if (!list.isEmpty()) {
                        wrapper.in(columnName, list);
                    }
                }
                break;
            case NOT_IN:
                if (value instanceof List) {
                    List<?> list = new ArrayList<>((List<?>) value);
                    list.removeIf(item -> item == null);
                    if (!list.isEmpty()) {
                        wrapper.notIn(columnName, list);
                    }
                }
                break;
            case IS_NULL:
                wrapper.isNull(columnName);
                break;
            case IS_NOT_NULL:
                wrapper.isNotNull(columnName);
                break;
            case BETWEEN:
                if (value instanceof List && ((List<?>) value).size() == 2) {
                    List<?> range = (List<?>) value;
                    if (range.get(0) != null && range.get(1) != null) {
                        wrapper.between(columnName, range.get(0), range.get(1));
                    }
                } else if (value instanceof String) {
                    // Handle case where between value is provided as a comma-separated string
                    String strValue = (String) value;
                    if (strValue.contains(",")) {
                        String[] parts = strValue.split(",", 2);
                        if (parts.length == 2) {
                            wrapper.between(columnName, parts[0].trim(), parts[1].trim());
                        }
                    }
                }
                break;
            default:
                throw new IllegalArgumentException("Unsupported condition type: " + type);
        }
    }

    /**
     * 构建高级查询条件
     * 
     * <p>构建流程：</p>
     * <ol>
     *   <li>添加默认条件（如果有）</li>
     *   <li>处理query对象中的字段（提取非空字段作为等值查询条件）</li>
     *   <li>处理排序条件</li>
     *   <li>根据查询模式判断是否处理条件组：</li>
     *   <ul>
     *     <li>简单模式：仅使用上述条件</li>
     *     <li>高级模式：额外处理条件组(conditionsGroups)</li>
     *   </ul>
     * </ol>
     *
     * @param query 查询条件
     * @param defaultConditions 默认条件
     * @param entityClass 实体类Class
     * @param <T> 实体类泛型
     * @return 查询条件包装器
     */
    public <T> QueryWrapper<T> buildAdvanced(BaseQuery<T> query, Map<String, Object> defaultConditions, Class<T> entityClass) {
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        EntityFieldMapper<T> fieldMapper = new EntityFieldMapper<>(entityClass);
        
        // 添加默认条件
        if (defaultConditions != null && !defaultConditions.isEmpty()) {
            defaultConditions.forEach((field, value) -> {
                if (value != null && StringUtils.hasText(field)) {
                    String columnName = fieldMapper.validateAndMapToColumn(field);
                    wrapper.eq(columnName, value);
                }
            });
        }
        
        // 如果没有query，直接返回只带默认条件的包装器
        if (query == null) {
            return wrapper;
        }
        
        // 处理查询实体
        processQueryEntity(wrapper, query, entityClass);
        
        // 处理排序条件
        applyOrderBy(wrapper, query, fieldMapper);
        
        // 判断是否是简单模式
        boolean isSimpleMode = determineQueryMode(query);
        
        // 简单模式直接返回
        if (isSimpleMode) {
            return wrapper;
        }
        
        // 高级模式下处理条件组
        if (!CollectionUtils.isEmpty(query.getConditionsGroups())) {
            applyConditionGroups(wrapper, query, fieldMapper);
        }
        
        return wrapper;
    }
    
    /**
     * 确定查询模式
     * 
     * <p>判断逻辑：</p>
     * <ol>
     *   <li>如果明确指定了 SIMPLE 模式，直接返回 true</li>
     *   <li>如果明确指定了 ADVANCED 模式，直接返回 false</li>
     *   <li>如果是 AUTO 模式，根据是否有复杂条件来决定</li>
     * </ol>
     * 
     * @param query 查询对象
     * @return true表示简单模式，false表示高级模式
     */
    private <T> boolean determineQueryMode(BaseQuery<T> query) {
        // 如果明确指定了模式，直接返回
        if (QueryMode.SIMPLE.equals(query.getQueryMode())) {
            return true;
        }
        if (QueryMode.ADVANCED.equals(query.getQueryMode())) {
            return false;
        }
        
        // 自动模式下，检测查询特性来决定模式
        return !hasComplexConditions(query);
    }
    
    /**
     * 检查查询是否包含复杂条件
     * 
     * <p>判断标准：是否包含条件组</p>
     * 
     * @param query 查询对象
     * @return 是否包含复杂条件
     */
    private <T> boolean hasComplexConditions(BaseQuery<T> query) {
        return !CollectionUtils.isEmpty(query.getConditionsGroups());
    }
} 