---
title: Trina OpenAPI Starter 架构文档
description: Trina OpenAPI Starter 组件的技术架构、设计原理与实现说明
---

# Trina OpenAPI Starter 架构文档

## 1. 概述

Trina OpenAPI Starter 是 Trina Common 框架的 API 文档自动化组件，基于 SpringDoc OpenAPI 3.x，支持微服务环境下的 API 文档聚合、OAuth2 安全、元数据注册等能力。该模块通过注解和自动配置，极大简化了企业级项目的 API 文档管理和前后端协作。

## 2. 核心功能

- 一键启用 OpenAPI/Swagger 文档（@EnableOpenApi 注解）
- 支持微服务网关聚合与服务元数据注册
- 支持 OAuth2 安全认证（Password 模式）
- 支持多环境灵活开关（dev/test 开启，prod 默认关闭）
- 支持自定义文档元信息（标题、描述、作者等）
- 支持包扫描、路径过滤、服务排除等高级配置

## 3. 技术实现

### 3.1 主要依赖

| 依赖组件 | 版本 | 说明 |
|----------|------|------|
| springdoc-openapi-starter-webmvc-api | 2.2.15 | OpenAPI 3.0 文档生成 |
| springdoc-openapi-starter-webmvc-ui  | 2.2.15 | Swagger UI 前端页面 |
| spring-cloud-commons                | 最新   | 微服务元数据注册 |
| trina-common-core                   | 4.0.0  | 基础工具支持 |

### 3.2 关键类与结构

- `@EnableOpenApi` 注解：一键启用 OpenAPI 能力，自动导入配置
- `SwaggerProperties`：核心配置属性，支持 yml 配置
- `OpenAPIDefinition`：OpenAPI 文档对象，动态注入元信息与安全配置
- `OpenAPIDefinitionImportSelector`：根据注解参数动态注册 OpenAPI 相关 Bean
- `OpenAPIMetadataConfiguration`：微服务元数据注册，便于网关聚合

### 3.3 配置原理

- 通过 `@EnableOpenApi` 注解自动导入配置类和属性绑定
- 支持 `swagger.enabled` 控制文档开关，默认仅开发/测试环境开启
- 支持 `swagger.basePackage`、`basePath`、`excludePath`、`gateway`、`tokenUrl`、`scope` 等丰富配置
- 支持 OAuth2 Password 模式安全集成
- 支持服务元数据注册，便于网关聚合 API 文档

## 4. 架构图

### 4.1 组件架构

```mermaid
graph TD
    A[应用主类] -->> B[EnableOpenApi 注解]
    B -->> C{OpenAPIDefinitionImportSelector}
    C -- 注册OpenAPIDefinition --> D[OpenAPIDefinition]
    C -- 注册OpenAPIMetadataConfiguration --> E[OpenAPIMetadataConfiguration]
    D -- 依赖 --> F[SwaggerProperties]
    E -- 依赖 --> F
    D -- 生成 --> G[OpenAPI 文档]
    E -- 注册 --> H[服务元数据]
    G -- 展示 --> I[Swagger UI]
    H -- 网关聚合 --> J[API Gateway]
```

### 4.2 数据流与调用流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant App as SpringBoot应用
    participant OpenAPI as OpenAPIDefinition
    participant UI as Swagger UI
    participant Gateway as 网关

    User->>App: 访问 /v3/api-docs
    App->>OpenAPI: 读取配置与元数据
    OpenAPI-->>App: 返回 OpenAPI JSON
    App-->>UI: 渲染 Swagger UI 页面
    Gateway->>App: 拉取服务元数据
    App-->>Gateway: 注册 spring-doc 元数据
```

## 5. 最佳实践

- 推荐仅在开发/测试环境开启 Swagger，生产环境通过 `swagger.enabled=false` 关闭
- 配置 `swagger.basePackage` 精确扫描业务包，提升文档质量
- 配置 `swagger.gateway` 以支持微服务网关聚合
- 配置 OAuth2 相关参数，保护敏感 API
- 使用 `excludePath` 排除无需暴露的接口
- 结合 CI/CD 自动化校验 API 文档完整性

## 6. 常见问题

### 6.1 生产环境如何关闭 Swagger？
- 配置 `swagger.enabled=false`，或通过 Spring Profile 控制

### 6.2 如何支持 OAuth2 认证？
- 配置 `swagger.tokenUrl`、`swagger.scope`，并确保后端支持 Password 模式

### 6.3 网关如何聚合 API 文档？
- 配置 `swagger.gateway`，并确保服务元数据注册成功

### 6.4 如何自定义文档信息？
- 配置 `swagger.title`、`swagger.description`、`swagger.version`、`swagger.author`

## 7. 参考资料

- [SpringDoc 官方文档](https://springdoc.org/)
- [OpenAPI 3.0 规范](https://swagger.io/specification/)
- [Trina Common Core 架构文档](./common-core-architecture.mdx)
- [Trina OpenAPI 使用指南](../guides/openapi-usage-guide.mdx) 