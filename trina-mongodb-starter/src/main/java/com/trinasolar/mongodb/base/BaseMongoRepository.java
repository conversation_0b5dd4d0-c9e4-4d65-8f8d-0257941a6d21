package com.trinasolar.mongodb.base;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.repository.NoRepositoryBean;

import java.util.List;

/**
 * MongoDB基础仓库接口
 * 扩展MongoRepository，提供自定义查询方法
 *
 * @param <T> 实体类型
 */
@NoRepositoryBean
public interface BaseMongoRepository<T extends BaseMongoEntity> extends MongoRepository<T, String> {

    /**
     * 根据自定义查询条件查找文档
     *
     * @param query 查询条件
     * @return 文档列表
     */
    List<T> findByQuery(BaseMongoQuery<T> query);

    /**
     * 根据自定义查询条件统计文档数量
     *
     * @param query 查询条件
     * @return 文档数量
     */
    long countByQuery(BaseMongoQuery<T> query);

    /**
     * 根据自定义查询条件删除文档
     *
     * @param query 查询条件
     * @return 删除的文档数量
     */
    long deleteByQuery(BaseMongoQuery<T> query);

    /**
     * 分页查询
     *
     * @param query 查询条件
     * @param clazz 实体类型
     * @return 分页结果
     */
    PageResult<T> findPage(BaseMongoQuery<T> query, Class<T> clazz);

    /**
     * 逻辑删除文档（将deleted字段设置为1）
     *
     * @param id 文档ID
     * @return 是否删除成功
     */
    boolean softDelete(String id);

    /**
     * 批量逻辑删除文档
     *
     * @param ids 文档ID列表
     * @return 删除的文档数量
     */
    long softDeleteBatch(List<String> ids);

    /**
     * 根据查询条件逻辑删除文档
     *
     * @param query 查询条件
     * @return 删除的文档数量
     */
    long softDeleteByQuery(BaseMongoQuery<T> query);
} 