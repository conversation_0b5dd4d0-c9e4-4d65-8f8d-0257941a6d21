package com.trinasolar.i18n.cache;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.trinasolar.i18n.config.I18nProperties;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;

/**
 * 翻译缓存实现类
 *
 * <AUTHOR>
 */
public class TranslationCacheImpl implements TranslationCache {

    private static final Logger log = LoggerFactory.getLogger(TranslationCacheImpl.class);
    
    private final I18nProperties properties;
    private final Map<String, Cache<String, String>> languageCaches = new ConcurrentHashMap<>();

    public TranslationCacheImpl(I18nProperties properties) {
        this.properties = properties;
    }

    @Override
    public Optional<String> get(String key, String language) {
        if (key == null || language == null) {
            return Optional.empty();
        }
        
        try {
            String value = getOrCreateCache(language).getIfPresent(key);
            return Optional.ofNullable(value);
        } catch (Exception e) {
            log.error("Failed to get translation from cache for key '{}' in language '{}'", key, language, e);
            return Optional.empty();
        }
    }

    @Override
    public void put(String key, String language, String value) {
        if (key == null || language == null || value == null) {
            return;
        }
        
        try {
            getOrCreateCache(language).put(key, value);
        } catch (Exception e) {
            log.error("Failed to put translation to cache for key '{}' in language '{}'", key, language, e);
        }
    }

    @Override
    public void putAll(Map<String, String> translations, String language) {
        if (translations == null || translations.isEmpty() || language == null) {
            return;
        }
        
        try {
            Cache<String, String> cache = getOrCreateCache(language);
            translations.forEach(cache::put);
        } catch (Exception e) {
            log.error("Failed to put all translations to cache for language '{}'", language, e);
        }
    }

    @Override
    public void invalidate(String language) {
        if (language == null) {
            return;
        }
        
        Cache<String, String> cache = languageCaches.remove(language);
        if (cache != null) {
            cache.invalidateAll();
        }
    }

    @Override
    public void invalidateAll() {
        languageCaches.values().forEach(Cache::invalidateAll);
        languageCaches.clear();
    }

    /**
     * 获取或创建指定语言的缓存
     *
     * @param language 语言
     * @return 缓存实例
     */
    private Cache<String, String> getOrCreateCache(String language) {
        return languageCaches.computeIfAbsent(language, k -> buildCache());
    }

    /**
     * 构建缓存
     *
     * @return 缓存实例
     */
    private Cache<String, String> buildCache() {
        CacheBuilder<Object, Object> builder = CacheBuilder.newBuilder();
        
        // 设置最大缓存条目数
        if (properties.getCache().getMaximumSize() > 0) {
            builder.maximumSize(properties.getCache().getMaximumSize());
        }
        
        // 设置写入后过期时间
        if (properties.getCache().getExpireAfterWrite() != null) {
            builder.expireAfterWrite(properties.getCache().getExpireAfterWrite());
        }
        
        return builder.build();
    }
}