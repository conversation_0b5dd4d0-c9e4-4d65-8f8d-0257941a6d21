# Trina I18n Starter

Trina I18n Starter 是一个用于微服务国际化的 Spring Boot Starter，提供了统一的翻译功能，支持从文件、缓存和翻译API获取翻译内容。

## 功能特点

- 支持从文件、缓存和翻译API获取翻译内容
- 支持多语言切换
- 支持自定义翻译文件路径
- 支持异步保存新翻译结果
- 支持通过注解标记不需要翻译的字段
- 高性能内存缓存
- 可扩展的翻译API适配

## 快速开始

### 添加依赖

```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-i18n-starter</artifactId>
    <version>${trina.version}</version>
</dependency>
```

### 配置参数

在 `application.yml` 中添加以下配置：

```yaml
trina:
  i18n:
    enabled: true                         # 是否启用国际化功能
    file-path: i18n                       # 翻译文件目录路径
    default-language: zh-CN               # 默认语言
    supported-languages:                  # 支持的语言列表
      - zh-CN
      - en-US
    api:
      enabled: false                      # 是否启用翻译API
      url: https://api.example.com/translate  # 翻译API地址
      app-key: your-api-key               # API密钥
      app-secret: your-api-secret         # API密钥
      timeout: 5s                         # 超时时间
      retry-count: 3                      # 重试次数
    cache:
      enabled: true                       # 是否启用缓存
      maximum-size: 10000                 # 最大缓存条目数
      expire-after-write: 24h             # 缓存过期时间
    async:
      core-pool-size: 2                   # 线程池核心线程数
      max-pool-size: 5                    # 线程池最大线程数
      queue-capacity: 100                 # 队列容量
      thread-name-prefix: i18n-async-     # 线程名称前缀
    interceptor:
      enabled: true                       # 是否启用拦截器
      language-param: lang                # 语言参数名
      prefer-header: true                 # 是否优先使用Header中的语言
      language-header: Accept-Language    # 语言Header名
      exclude-paths:                      # 排除的路径
        - /swagger-ui/**
        - /v3/api-docs/**
```

### 创建翻译文件

在资源目录下创建翻译文件，默认路径为 `i18n`：

**i18n/zh-CN.json**：

```json
{
  "hello": "你好",
  "world": "世界",
  "welcome": "欢迎使用Trina I18n Starter"
}
```

**i18n/en-US.json**：

```json
{
  "hello": "Hello",
  "world": "World",
  "welcome": "Welcome to Trina I18n Starter"
}
```

### 使用I18nService

在代码中注入`I18nService`并使用：

```java
@Service
public class MyService {
    
    private final I18nService i18nService;
    
    public MyService(I18nService i18nService) {
        this.i18nService = i18nService;
    }
    
    public String getWelcomeMessage() {
        return i18nService.getMessage("welcome");
    }
    
    public String getWelcomeMessageInEnglish() {
        return i18nService.getMessage("welcome", "en-US");
    }
    
    public String getCustomMessage(String key, String defaultMessage) {
        return i18nService.getMessage(key, defaultMessage);
    }
}
```

### 标记不需要翻译的字段

使用 `@NoTranslation` 注解标记不需要翻译的字段：

```java
public class User {
    
    private String id;
    private String name;
    
    @NoTranslation
    private String email;
    
    // Getters and setters
}
```

## 工作原理

Trina I18n Starter 通过以下方式工作：

1. **拦截器提取语言信息**：从请求中提取语言信息，可以从Header、参数或Locale中获取。
2. **多级翻译查找**：按照缓存->原始文件->[language].json->新翻译文件->new-[language].json->翻译API的顺序查找翻译。
3. **异步保存新翻译**：通过API获取的新翻译结果会异步保存到新翻译文件中。
4. **高性能缓存**：使用内存缓存提高翻译查找性能。

## 更多功能

### 手动设置当前语言

```java
i18nService.setCurrentLanguage("en-US");
```

### 获取当前语言

```java
String currentLanguage = i18nService.getCurrentLanguage();
```

### 重新加载翻译文件

```java
i18nService.reloadTranslations();
```

### 获取所有翻译

```java
Map<String, String> allTranslations = i18nService.getAllMessages("zh-CN");
```

## 自定义扩展

### 自定义翻译API客户端

实现 `TranslationApiClient` 接口并注册为Bean：

```java
@Component
public class CustomTranslationApiClient implements TranslationApiClient {
    // 实现接口方法
}
```

### 自定义翻译缓存

实现 `TranslationCache` 接口并注册为Bean：

```java
@Component
public class CustomTranslationCache implements TranslationCache {
    // 实现接口方法
}
```

## 注意事项

- 确保翻译文件是有效的JSON格式
- 在高并发环境下，可能需要调整异步任务的线程池参数
- 如果使用翻译API，建议设置合理的超时时间和重试次数
- 对于不需要翻译的字段，使用 `@NoTranslation` 注解标记 