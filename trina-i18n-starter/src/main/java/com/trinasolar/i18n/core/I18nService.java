package com.trinasolar.i18n.core;

import java.util.Map;

/**
 * 国际化服务接口
 *
 * <AUTHOR>
 */
public interface I18nService {

    /**
     * 获取指定语言的翻译
     *
     * @param key 翻译键
     * @param lang 语言
     * @return 翻译结果，如果未找到则返回原始键
     */
    String getMessage(String key, String lang);

    /**
     * 获取指定语言的翻译，带有默认值
     *
     * @param key 翻译键
     * @param defaultMessage 默认消息
     * @param language 语言
     * @return 翻译结果，如果未找到则返回默认消息
     */
    String getMessage(String key, String defaultMessage, String language);

    /**
     * 获取当前语言的翻译
     *
     * @param key 翻译键
     * @return 翻译结果，如果未找到则返回原始键
     */
    String getMessage(String key);

    /**
     * 获取当前语言的翻译，带有默认值
     *
     * @param key 翻译键
     * @param defMsg 默认消息
     * @return 翻译结果，如果未找到则返回默认消息
     */
    String getMessageWithDefault(String key, String defMsg);

    /**
     * 获取指定语言的所有翻译
     *
     * @param language 语言
     * @return 翻译映射
     */
    Map<String, String> getAllMessages(String language);

    /**
     * 重新加载翻译文件
     */
    void reloadTranslations();

    /**
     * 设置当前线程的语言
     *
     * @param language 语言
     */
    void setCurrentLanguage(String language);

    /**
     * 获取当前线程的语言
     *
     * @return 当前语言
     */
    String getCurrentLanguage();

    /**
     * 清除当前线程的语言
     */
    void clearCurrentLanguage();
}