package com.trinasolar.elasticsearch.vector.service;

import com.trinasolar.elasticsearch.vector.entity.DocumentVectorEntity;
import com.trinasolar.elasticsearch.vo.SearchResult;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 文档向量搜索服务接口
 * 提供文档的向量化存储、搜索和管理功能
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface DocumentVectorService extends BaseVectorService<DocumentVectorEntity> {
    
    /**
     * 根据文档内容进行语义搜索
     * 
     * @param query 查询文本
     * @param k 返回结果数量
     * @return 搜索结果
     */
    SearchResult<DocumentVectorEntity> semanticSearch(String query, int k);
    
    /**
     * 根据文档内容进行语义搜索（带过滤条件）
     * 
     * @param query 查询文本
     * @param k 返回结果数量
     * @param documentType 文档类型过滤
     * @param source 文档来源过滤
     * @param tags 标签过滤
     * @return 搜索结果
     */
    SearchResult<DocumentVectorEntity> semanticSearchWithFilter(String query, int k, 
                                                               String documentType, 
                                                               String source, 
                                                               String[] tags);
    
    /**
     * 混合搜索（结合关键词搜索和语义搜索）
     * 
     * @param query 查询文本
     * @param k 返回结果数量
     * @param textWeight 文本搜索权重
     * @param vectorWeight 向量搜索权重
     * @return 搜索结果
     */
    SearchResult<DocumentVectorEntity> hybridSearch(String query, int k, 
                                                   float textWeight, float vectorWeight);
    
    /**
     * 多字段向量搜索
     * 同时搜索标题、内容、摘要等多个向量字段
     * 
     * @param query 查询文本
     * @param k 返回结果数量
     * @param fieldWeights 各字段权重配置
     * @return 搜索结果
     */
    SearchResult<DocumentVectorEntity> multiFieldVectorSearch(String query, int k, 
                                                             Map<String, Float> fieldWeights);
    
    /**
     * 相似文档推荐
     * 根据给定文档ID，推荐相似的文档
     * 
     * @param documentId 文档ID
     * @param k 推荐数量
     * @param vectorField 使用的向量字段
     * @return 相似文档列表
     */
    SearchResult<DocumentVectorEntity> findSimilarDocuments(String documentId, int k, String vectorField);
    
    /**
     * 批量相似文档推荐
     * 
     * @param documentIds 文档ID列表
     * @param k 每个文档的推荐数量
     * @param vectorField 使用的向量字段
     * @return 相似文档映射
     */
    Map<String, SearchResult<DocumentVectorEntity>> batchFindSimilarDocuments(List<String> documentIds, 
                                                                             int k, String vectorField);
    
    /**
     * 文档聚类
     * 基于向量相似性对文档进行聚类
     * 
     * @param vectorField 使用的向量字段
     * @param clusterCount 聚类数量
     * @param maxIterations 最大迭代次数
     * @return 聚类结果
     */
    Map<Integer, List<DocumentVectorEntity>> clusterDocuments(String vectorField, 
                                                             int clusterCount, 
                                                             int maxIterations);
    
    /**
     * 文档去重
     * 基于向量相似性检测和去除重复文档
     * 
     * @param vectorField 使用的向量字段
     * @param similarityThreshold 相似度阈值
     * @return 重复文档组
     */
    List<List<DocumentVectorEntity>> findDuplicateDocuments(String vectorField, 
                                                           double similarityThreshold);
    
    /**
     * 文档向量化
     * 为文档生成向量表示
     * 
     * @param documentId 文档ID
     * @param modelVersion 模型版本
     * @return 是否成功
     */
    boolean vectorizeDocument(String documentId, String modelVersion);
    
    /**
     * 批量文档向量化
     * 
     * @param documentIds 文档ID列表
     * @param modelVersion 模型版本
     * @return 成功向量化的文档数量
     */
    int batchVectorizeDocuments(List<String> documentIds, String modelVersion);
    
    /**
     * 重新向量化所有文档
     * 
     * @param modelVersion 新的模型版本
     * @param batchSize 批处理大小
     * @return 处理的文档数量
     */
    long reVectorizeAllDocuments(String modelVersion, int batchSize);
    
    /**
     * 获取未向量化的文档
     * 
     * @param limit 限制数量
     * @return 未向量化的文档列表
     */
    List<DocumentVectorEntity> getUnvectorizedDocuments(int limit);
    
    /**
     * 获取向量化统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getVectorizationStats();
    
    /**
     * 文档质量评估
     * 基于向量质量评估文档质量
     * 
     * @param documentId 文档ID
     * @return 质量评分
     */
    Map<String, Double> evaluateDocumentQuality(String documentId);
    
    /**
     * 批量文档质量评估
     * 
     * @param documentIds 文档ID列表
     * @return 质量评分映射
     */
    Map<String, Map<String, Double>> batchEvaluateDocumentQuality(List<String> documentIds);
    
    /**
     * 文档主题分析
     * 基于向量聚类分析文档主题
     * 
     * @param vectorField 使用的向量字段
     * @param topicCount 主题数量
     * @return 主题分析结果
     */
    Map<String, Object> analyzeDocumentTopics(String vectorField, int topicCount);
    
    /**
     * 文档相似度矩阵计算
     * 
     * @param documentIds 文档ID列表
     * @param vectorField 使用的向量字段
     * @return 相似度矩阵
     */
    double[][] calculateSimilarityMatrix(List<String> documentIds, String vectorField);
    
    /**
     * 文档向量可视化数据
     * 使用降维算法（如t-SNE、PCA）生成可视化数据
     * 
     * @param vectorField 使用的向量字段
     * @param algorithm 降维算法（tsne、pca）
     * @param dimensions 目标维度（2或3）
     * @param sampleSize 采样大小
     * @return 可视化数据
     */
    Map<String, Object> generateVisualizationData(String vectorField, 
                                                  String algorithm, 
                                                  int dimensions, 
                                                  int sampleSize);
    
    /**
     * 搜索性能优化建议
     * 
     * @return 优化建议
     */
    Map<String, Object> getSearchOptimizationSuggestions();
    
    /**
     * 向量索引健康检查
     * 
     * @return 健康检查结果
     */
    Map<String, Object> checkVectorIndexHealth();
    
    /**
     * 清理过期向量缓存
     * 
     * @param expiredHours 过期小时数
     * @return 清理的缓存数量
     */
    long cleanExpiredVectorCache(int expiredHours);
    
    /**
     * 预热向量搜索缓存
     * 
     * @param commonQueries 常用查询列表
     * @return 预热的查询数量
     */
    int warmupVectorSearchCache(List<String> commonQueries);
}