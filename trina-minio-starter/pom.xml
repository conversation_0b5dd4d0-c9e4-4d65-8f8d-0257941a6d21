<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.trinasolar</groupId>
        <artifactId>trina-common</artifactId>
        <version>4.0.0</version>
    </parent>

    <artifactId>trina-minio-starter</artifactId>
    <packaging>jar</packaging>
    <description>Minio文件系统依赖</description>

    <properties>
    </properties>

    <dependencies>
        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
        </dependency>
        <!--常用工具类 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <!-- io常用工具类 -->
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        
        <!-- OpenAPI/Swagger文档 -->
        <dependency>
            <groupId>com.trinasolar</groupId>
            <artifactId>trina-openapi-starter</artifactId>
            <version>${common.version}</version>
        </dependency>
        
        <!-- AWS S3 SDK -->
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3</artifactId>
        </dependency>
    </dependencies>
</project>
