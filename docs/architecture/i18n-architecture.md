# Trina多语言国际化模块架构设计

## 1. 模块概述

Trina I18n Starter 模块提供了一套完整的国际化解决方案，支持多种翻译来源、动态语言切换和高性能缓存机制。模块采用了分层设计，易于扩展和定制。

## 2. 架构图

```
┌───────────────────────┐
│     I18nService       │
└───────────┬───────────┘
            │
            ▼
┌───────────────────────┐
│   I18nServiceImpl     │
└─────┬──────┬─────┬────┘
      │      │     │
      ▼      ▼     ▼
┌──────┐ ┌─────┐ ┌─────┐
│缓存层│ │文件层│ │API层│
└──────┘ └─────┘ └─────┘
```

## 3. 核心组件设计

### 3.1 配置模块

配置模块主要由`I18nProperties`类定义，包含以下配置项：

- 基本配置：enabled、filePath、defaultLanguage、supportedLanguages
- API配置：api.enabled、api.url、api.appKey、api.appSecret等
- 缓存配置：cache.enabled、cache.maximumSize、cache.expireAfterWrite等
- 异步配置：async.corePoolSize、async.maxPoolSize等
- 拦截器配置：interceptor.enabled、interceptor.languageParam等

自动配置类`I18nAutoConfiguration`负责根据配置初始化和注册各个组件。

### 3.2 服务层

**I18nService接口**：定义了多语言模块的核心功能，包括获取翻译、设置当前语言等方法。

**I18nServiceImpl实现类**：实现了I18nService接口，负责协调缓存、文件和API三层来获取翻译内容。实现了多级查找策略：

1. 首先从缓存中查找
2. 缓存未命中时从原始翻译文件查找
3. 仍未找到时从新翻译文件查找
4. 最后尝试调用翻译API

### 3.3 缓存层

**TranslationCache接口**：定义了缓存的基本操作，包括添加、获取和清除缓存。

**TranslationCacheImpl实现类**：基于内存实现的高性能缓存，支持配置最大缓存条目数和过期时间。

### 3.4 文件层

**TranslationFileLoader接口**：负责从文件系统加载翻译内容。

**TranslationFileLoaderImpl实现类**：实现了从JSON文件加载翻译的功能，支持原始翻译文件和新翻译文件。

**TranslationFileSaver接口**：负责将翻译内容保存到文件系统。

**TranslationFileSaverImpl实现类**：实现了将新翻译异步保存到文件系统的功能。

### 3.5 API层

**TranslationApiClient接口**：定义了调用翻译API的接口。

**TranslationApiClientImpl实现类**：实现了调用外部翻译API的功能，支持配置超时和重试机制。

### 3.6 Web层

**I18nInterceptor拦截器**：拦截Web请求，从请求中提取语言信息并设置到当前上下文。

**I18nWebConfig配置类**：注册I18nInterceptor到Spring MVC。

### 3.7 注解支持

**NoTranslation注解**：用于标记不需要翻译的字段，可以应用于字段或方法。

## 4. 数据流程

### 4.1 语言提取流程

```
HTTP请求 → I18nInterceptor → 
  → 尝试从Header获取语言
  → 尝试从参数获取语言
  → 尝试从Locale获取语言
  → 使用默认语言
  → 设置到I18nService和LocaleContextHolder
```

### 4.2 翻译查找流程

```
I18nService.getMessage(key, lang) →
  → 从TranslationCache获取
  → 未命中时从TranslationFileLoader加载原始文件
  → 未找到时从TranslationFileLoader加载新翻译文件
  → 仍未找到且API启用时调用TranslationApiClient
  → 获取到新翻译时异步保存到文件系统
  → 返回翻译结果或默认值
```

## 5. 扩展点设计

模块提供了多个扩展点，支持自定义实现：

1. **自定义翻译API**：实现TranslationApiClient接口
2. **自定义缓存实现**：实现TranslationCache接口
3. **自定义文件加载**：实现TranslationFileLoader接口
4. **自定义文件保存**：实现TranslationFileSaver接口

## 6. 线程安全设计

### 6.1 ThreadLocal语言存储

当前语言使用ThreadLocal存储，确保在多线程环境下的线程安全：

```java
private static final ThreadLocal<String> CURRENT_LANGUAGE = new ThreadLocal<>();
```

### 6.2 缓存线程安全

缓存使用线程安全的ConcurrentHashMap实现：

```java
private final ConcurrentMap<String, ConcurrentMap<String, String>> cache;
```

### 6.3 异步任务处理

新翻译保存使用异步线程池处理，避免I/O操作阻塞主线程：

```java
@Async("i18nAsyncExecutor")
public void saveTranslation(String key, String value, String language) {
    // 异步保存实现
}
```

## 7. 性能优化

### 7.1 多级缓存策略

- 使用内存缓存减少文件I/O和API调用
- 支持配置缓存大小和过期时间，平衡内存占用和性能

### 7.2 异步保存新翻译

- 使用异步线程池保存新翻译，避免阻塞请求线程
- 可配置线程池参数，适应不同的并发场景

### 7.3 懒加载机制

- 翻译文件采用懒加载机制，首次访问时才加载
- 减少启动时间和内存占用

## 8. 可靠性设计

### 8.1 降级策略

当翻译查找失败时，采用以下降级策略：

1. 缓存未命中 → 尝试文件加载
2. 文件未找到 → 尝试API调用
3. API调用失败 → 返回默认值或原始键

### 8.2 API调用重试机制

翻译API调用支持配置重试次数和超时时间，提高可靠性：

```java
int retryCount = properties.getApi().getRetryCount();
Duration timeout = properties.getApi().getTimeout();
```

### 8.3 文件操作异常处理

文件操作使用try-catch块捕获异常，确保即使文件操作失败也不会影响主流程：

```java
try {
    // 文件操作
} catch (IOException e) {
    log.error("Failed to load translation file: {}", e.getMessage(), e);
    return Collections.emptyMap();
}
```

## 9. 部署与扩展建议

### 9.1 单体应用部署

对于单体应用，建议将翻译文件放在资源目录下，启用内存缓存以提高性能。

### 9.2 微服务部署

对于微服务架构：

1. **集中式翻译管理**：考虑将翻译文件集中管理，通过API提供服务
2. **分布式缓存**：可以扩展缓存接口，使用Redis等分布式缓存
3. **配置中心集成**：将配置项放在配置中心，支持动态更新

### 9.3 高可用建议

1. **多级缓存**：应用内存缓存 + 分布式缓存
2. **翻译API高可用**：API层实现负载均衡和熔断机制
3. **异步任务监控**：监控异步保存任务的执行情况 