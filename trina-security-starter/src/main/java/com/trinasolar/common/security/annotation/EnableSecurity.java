package com.trinasolar.common.security.annotation;


import com.trinasolar.common.security.AutoSecurityConfiguration;
import com.trinasolar.common.security.cache.IamCaffeineCacheConfiguration;
import com.trinasolar.common.security.endpoint.AuthEndpoint;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * 启用安全管控
 *
 * <AUTHOR>
 * @date 2025-06-20 13:53
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@Import({AutoSecurityConfiguration.class, IamCaffeineCacheConfiguration.class, AuthEndpoint.class})
public @interface EnableSecurity {
}
