package com.trinasolar.stream.template;

import com.trinasolar.stream.enums.MessagePriority;
import com.trinasolar.stream.exception.MessageSendException;
import com.trinasolar.stream.message.StreamMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.util.StringUtils;

import java.util.concurrent.CompletableFuture;

/**
 * 统一消息发送模板
 *
 * <AUTHOR>
 * @since 4.0.0
 */
public class StreamTemplate {

    private static final Logger log = LoggerFactory.getLogger(StreamTemplate.class);

    private final StreamBridge streamBridge;

    public StreamTemplate(StreamBridge streamBridge) {
        this.streamBridge = streamBridge;
    }

    /**
     * 发送消息
     *
     * @param destination 目标队列/主题
     * @param payload     消息内容
     * @param <T>         消息类型
     * @return 发送结果
     */
    public <T> boolean send(String destination, T payload) {
        return send(new StreamMessage<>(destination, payload));
    }

    /**
     * 发送消息（带优先级）
     *
     * @param destination 目标队列/主题
     * @param payload     消息内容
     * @param priority    消息优先级
     * @param <T>         消息类型
     * @return 发送结果
     */
    public <T> boolean send(String destination, T payload, MessagePriority priority) {
        StreamMessage<T> message = new StreamMessage<>(destination, payload);
        message.withPriority(priority);
        return send(message);
    }

    /**
     * 发送消息（带标签）
     *
     * @param destination 目标队列/主题
     * @param payload     消息内容
     * @param tag         消息标签
     * @param <T>         消息类型
     * @return 发送结果
     */
    public <T> boolean send(String destination, T payload, String tag) {
        StreamMessage<T> message = new StreamMessage<>(destination, payload);
        message.withTag(tag);
        return send(message);
    }

    /**
     * 发送延时消息
     *
     * @param destination 目标队列/主题
     * @param payload     消息内容
     * @param delayMs     延时毫秒数
     * @param <T>         消息类型
     * @return 发送结果
     */
    public <T> boolean sendDelay(String destination, T payload, long delayMs) {
        StreamMessage<T> message = new StreamMessage<>(destination, payload);
        message.addHeader("x-delay", delayMs);
        return send(message);
    }

    /**
     * 异步发送消息
     *
     * @param destination 目标队列/主题
     * @param payload     消息内容
     * @param <T>         消息类型
     * @return CompletableFuture
     */
    public <T> CompletableFuture<Boolean> sendAsync(String destination, T payload) {
        return CompletableFuture.supplyAsync(() -> send(destination, payload));
    }

    /**
     * 发送StreamMessage
     *
     * @param streamMessage 消息对象
     * @param <T>           消息类型
     * @return 发送结果
     */
    public <T> boolean send(StreamMessage<T> streamMessage) {
        try {
            validateMessage(streamMessage);

            // 设置链路追踪ID
            if (!StringUtils.hasText(streamMessage.getTraceId())) {
                streamMessage.setTraceId(generateTraceId());
            }

            // 构建Spring Cloud Stream消息
            Message<T> message = buildMessage(streamMessage);

            // 发送消息
            boolean result = streamBridge.send(streamMessage.getDestination(), message);

            if (result) {
                log.info("消息发送成功: messageId={}, destination={}, traceId={}",
                        streamMessage.getMessageId(), streamMessage.getDestination(), streamMessage.getTraceId());
            } else {
                log.error("消息发送失败: messageId={}, destination={}, traceId={}",
                        streamMessage.getMessageId(), streamMessage.getDestination(), streamMessage.getTraceId());
            }

            return result;

        } catch (Exception e) {
            log.error("消息发送异常: messageId={}, destination={}, error={}",
                    streamMessage.getMessageId(), streamMessage.getDestination(), e.getMessage(), e);
            throw new MessageSendException("发送消息失败", e);
        }
    }

    /**
     * 验证消息
     */
    private <T> void validateMessage(StreamMessage<T> message) {
        if (message == null) {
            throw new IllegalArgumentException("消息不能为空");
        }
        if (!StringUtils.hasText(message.getDestination())) {
            throw new IllegalArgumentException("消息目标不能为空");
        }
        if (message.getPayload() == null) {
            throw new IllegalArgumentException("消息内容不能为空");
        }
        if (message.isExpired()) {
            throw new IllegalArgumentException("消息已过期");
        }
    }

    /**
     * 构建Spring Cloud Stream消息
     */
    private <T> Message<T> buildMessage(StreamMessage<T> streamMessage) {
        MessageBuilder<T> builder = MessageBuilder.withPayload(streamMessage.getPayload());

        // 添加消息头
        builder.setHeader("messageId", streamMessage.getMessageId());
        builder.setHeader("traceId", streamMessage.getTraceId());
        builder.setHeader("createTime", streamMessage.getCreateTime());
        builder.setHeader("priority", streamMessage.getPriority().getLevel());

        if (StringUtils.hasText(streamMessage.getTag())) {
            builder.setHeader("tag", streamMessage.getTag());
        }

        if (StringUtils.hasText(streamMessage.getGroup())) {
            builder.setHeader("group", streamMessage.getGroup());
        }

        if (streamMessage.getExpireTime() != null) {
            builder.setHeader("expireTime", streamMessage.getExpireTime());
        }

        // 添加自定义头信息
        if (streamMessage.getHeaders() != null && !streamMessage.getHeaders().isEmpty()) {
            streamMessage.getHeaders().forEach(builder::setHeader);
        }

        return builder.build();
    }

    /**
     * 生成链路追踪ID
     */
    private String generateTraceId() {
        return System.currentTimeMillis() + "-" + Thread.currentThread().getId();
    }
}
