package com.trinasolar.nacos;

import com.trinasolar.nacos.utils.ConfigurationValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.boot.env.YamlPropertySourceLoader;
import org.springframework.core.Ordered;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Nacos统一环境处理器（优化版）
 * <p>
 * 整合了配置导入和Profile处理功能，提供一站式的Nacos环境配置
 * <p>
 * 主要功能：
 * 1. 确保正确的配置加载顺序
 * 2. 从nacos.yaml加载基础配置并解析占位符
 * 3. 自动配置spring.profiles.active和namespace
 * 4. 自动导入Nacos配置文件
 * 5. 设置默认的Nacos配置参数
 * 6. 遵循约定优于配置原则
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class NacosEnvironmentProcessor implements EnvironmentPostProcessor, Ordered {

    private static final Logger logger = LoggerFactory.getLogger(NacosEnvironmentProcessor.class);
    private static final String DEFAULT_PROFILES_ACTIVE = "dev";
    private static final String PROPERTY_SOURCE_NAME = "nacosEnvironmentProcessor";
    private static final String NACOS_YAML_PROPERTY_SOURCE_NAME = "nacos.yaml";

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        // 1. 此时 application.properties 已加载完成
        try {
            logger.info("🚀 开始处理Nacos环境配置...");

            Map<String, Object> properties = new HashMap<>();

            // 1. 首先处理Profile配置（确保后续配置能正确解析）
            processProfilesFirst(environment, properties);

            // 2. 加载nacos.yaml基础配置
            loadNacosYamlConfiguration(environment);

            // 3. 处理Namespace配置（基于已设置的Profile）
            processNamespaceConfiguration(environment, properties);

            // 4. 处理Nacos配置导入
            processNacosConfigImport(environment, properties);

            // 5. 设置默认配置
            setDefaultNacosProperties(environment, properties);

            // 6. 添加属性到环境（优先级最高）
            if (!properties.isEmpty()) {
                MapPropertySource propertySource = new MapPropertySource(PROPERTY_SOURCE_NAME, properties);
                environment.getPropertySources().addFirst(propertySource);
                logger.info("✅ Nacos环境配置处理完成，共设置 {} 个属性", properties.size());
            }

            // 7. 验证配置（开发环境或启用详细日志时）
            validateConfiguration(environment);

        } catch (Exception e) {
            logger.error("❌ Nacos环境配置处理失败", e);
        }
    }

    /**
     * 首先处理Profile配置，确保后续配置解析正确
     */
    private void processProfilesFirst(ConfigurableEnvironment environment, Map<String, Object> properties) {
        String activeProfile = environment.getProperty("spring.profiles.active");

        // 如果没有设置spring.profiles.active，设置默认值
        if (!StringUtils.hasText(activeProfile)) {
            properties.put("spring.profiles.active", DEFAULT_PROFILES_ACTIVE);
            logger.info("📋 设置默认Profile: {}", DEFAULT_PROFILES_ACTIVE);
        }
    }

    /**
     * 加载nacos.yaml配置文件
     * 注意：此时spring.application.name和spring.profiles.active应该已经可用
     */
    private void loadNacosYamlConfiguration(ConfigurableEnvironment environment) {
        try {
            Resource resource = new ClassPathResource(NACOS_YAML_PROPERTY_SOURCE_NAME);
            if (!resource.exists()) {
                logger.debug("📋 nacos.yaml文件不存在，跳过加载");
                return;
            }

            YamlPropertySourceLoader loader = new YamlPropertySourceLoader();
            List<PropertySource<?>> propertySources = loader.load(NACOS_YAML_PROPERTY_SOURCE_NAME, resource);

            for (PropertySource<?> propertySource : propertySources) {
                // 添加到环境中，但优先级较低（在应用配置之后）
                environment.getPropertySources().addLast(propertySource);
                logger.info("📄 成功加载nacos.yaml配置文件");
            }

        } catch (IOException e) {
            logger.warn("⚠️ 加载nacos.yaml配置文件失败: {}", e.getMessage());
        }
    }

    /**
     * 处理Namespace配置
     */
    private void processNamespaceConfiguration(ConfigurableEnvironment environment, Map<String, Object> properties) {
        String applicationName = environment.getProperty("spring.application.name");
        String activeProfile = environment.getProperty("spring.profiles.active", DEFAULT_PROFILES_ACTIVE);
        String namespace = environment.getProperty("spring.cloud.nacos.config.namespace");

        // 如果没有设置namespace，根据应用名和Profile自动生成
        if (!StringUtils.hasText(namespace) && StringUtils.hasText(applicationName)) {
            String autoNamespace = applicationName + "-" + activeProfile;
            properties.put("spring.cloud.nacos.config.namespace", autoNamespace);
            properties.put("spring.cloud.nacos.discovery.namespace", autoNamespace);
            properties.put("spring.application.namespace", autoNamespace);
            logger.info("📋 自动设置Nacos命名空间: {}", autoNamespace);
        } else if (StringUtils.hasText(namespace)) {
            // 确保应用命名空间属性与Nacos配置一致
            properties.put("spring.application.namespace", namespace);
        }
    }

    /**
     * 处理Nacos配置导入
     */
    private void processNacosConfigImport(ConfigurableEnvironment environment, Map<String, Object> properties) {
        // 检查是否启用了Nacos
        boolean nacosEnabled = environment.getProperty("spring.cloud.nacos.config.enabled", Boolean.class, true);
        if (!nacosEnabled) {
            logger.info("📋 Nacos配置未启用，跳过配置导入处理");
            return;
        }

        String serverAddr = environment.getProperty("spring.cloud.nacos.config.server-addr");
        if (!StringUtils.hasText(serverAddr)) {
            logger.info("📋 未配置Nacos服务器地址，跳过配置导入处理");
            return;
        }

        String applicationName = environment.getProperty("spring.application.name");
        if (!StringUtils.hasText(applicationName)) {
            logger.warn("⚠️ 未配置spring.application.name，无法自动导入应用特定配置");
            return;
        }

        // 获取配置参数
        String group = environment.getProperty("spring.cloud.nacos.config.group",
                environment.getProperty("spring.application.group", "DEFAULT_GROUP"));
        String fileExtension = environment.getProperty("spring.cloud.nacos.config.file-extension", "yaml");
        String activeProfile = environment.getProperty("spring.profiles.active", DEFAULT_PROFILES_ACTIVE);

        // 1. 添加spring.config.import配置（Spring Boot 2.4+）
        if (!environment.containsProperty("spring.config.import")) {
            properties.put("spring.config.import", "optional:nacos:");
            logger.info("📄 添加spring.config.import配置");
        }

        // 2. 配置共享配置（通用配置）- 如果nacos.yaml中没有配置
        configureSharedConfigsIfNeeded(environment, properties, group, activeProfile);

        // 3. 配置扩展配置（应用特定配置）- 如果nacos.yaml中没有配置
        configureExtensionConfigsIfNeeded(environment, properties, applicationName, group, fileExtension, activeProfile);
    }

    /**
     * 配置共享配置（如果需要）
     */
    private void configureSharedConfigsIfNeeded(ConfigurableEnvironment environment, Map<String, Object> properties,
                                                String group, String activeProfile) {
        // 检查是否已经在nacos.yaml中配置了shared-configs
        if (!environment.containsProperty("spring.cloud.nacos.config.shared-configs[0].data-id")) {
            String commonConfigDataId = "common-config-" + activeProfile + ".yaml";
            properties.put("spring.cloud.nacos.config.shared-configs[0].data-id", commonConfigDataId);
            properties.put("spring.cloud.nacos.config.shared-configs[0].group", group);
            properties.put("spring.cloud.nacos.config.shared-configs[0].refresh", true);
            logger.info("📄 配置共享配置: {}", commonConfigDataId);
        } else {
            logger.debug("📄 共享配置已在nacos.yaml中配置，跳过自动配置");
        }
    }

    /**
     * 配置扩展配置（如果需要）
     */
    private void configureExtensionConfigsIfNeeded(ConfigurableEnvironment environment, Map<String, Object> properties,
                                                   String applicationName, String group, String fileExtension, String activeProfile) {
        // 检查是否已经在nacos.yaml中配置了extension-configs
        if (!environment.containsProperty("spring.cloud.nacos.config.extension-configs[0].data-id")) {
            String appConfigDataId = applicationName + "-" + activeProfile + "." + fileExtension;
            properties.put("spring.cloud.nacos.config.extension-configs[0].data-id", appConfigDataId);
            properties.put("spring.cloud.nacos.config.extension-configs[0].group", group);
            properties.put("spring.cloud.nacos.config.extension-configs[0].refresh", true);
            logger.info("📄 配置应用配置: {}", appConfigDataId);
        } else {
            logger.debug("📄 扩展配置已在nacos.yaml中配置，跳过自动配置");
        }
    }

    /**
     * 设置默认的Nacos配置
     */
    private void setDefaultNacosProperties(ConfigurableEnvironment environment, Map<String, Object> properties) {
        // 设置默认的服务器地址（如果未配置）
        if (!environment.containsProperty("spring.cloud.nacos.config.server-addr") &&
                !environment.containsProperty("spring.cloud.nacos.discovery.server-addr")) {

            String defaultServerAddr = environment.getProperty("NACOS_HOST", "127.0.0.1") + ":" +
                    environment.getProperty("NACOS_PORT", "8848");

            properties.put("spring.cloud.nacos.config.server-addr", defaultServerAddr);
            properties.put("spring.cloud.nacos.discovery.server-addr", defaultServerAddr);
            logger.info("📋 设置默认Nacos服务器地址: {}", defaultServerAddr);
        }

        // 设置默认的用户名密码（如果未配置）
        if (!environment.containsProperty("spring.cloud.nacos.username")) {
            properties.put("spring.cloud.nacos.username", "nacos");
            properties.put("spring.cloud.nacos.password", "nacos");
            logger.debug("📋 设置默认Nacos认证信息");
        }

        // 设置默认的文件扩展名
        if (!environment.containsProperty("spring.cloud.nacos.config.file-extension")) {
            properties.put("spring.cloud.nacos.config.file-extension", "yaml");
        }

        // 启用配置刷新
        if (!environment.containsProperty("spring.cloud.nacos.config.refresh-enabled")) {
            properties.put("spring.cloud.nacos.config.refresh-enabled", true);
        }

        // 启用服务注册
        if (!environment.containsProperty("spring.cloud.nacos.discovery.register-enabled")) {
            properties.put("spring.cloud.nacos.discovery.register-enabled", true);
        }
    }

    /**
     * 验证配置
     */
    private void validateConfiguration(ConfigurableEnvironment environment) {
        try {
            // 检查是否启用详细日志或处于开发环境
            String activeProfile = environment.getProperty("spring.profiles.active", DEFAULT_PROFILES_ACTIVE);
            boolean verboseLogging = environment.getProperty("trina.nacos.verbose-logging", Boolean.class, false);
            boolean isDev = "dev".equals(activeProfile);

            if (verboseLogging || isDev) {
                logger.info("🔍 开始验证Nacos配置...");
                ConfigurationValidator.ValidationResult result = ConfigurationValidator.validateNacosConfiguration(environment);
                result.logResults();

                if (verboseLogging) {
                    ConfigurationValidator.printConfigurationInfo(environment);
                }
            }
        } catch (Exception e) {
            logger.warn("⚠️ 配置验证失败: {}", e.getMessage());
        }
    }

    @Override
    public int getOrder() {
        // 在默认的配置处理器之前执行，但在系统属性处理器之后
        // 确保能够读取到spring.application.name等基础配置
        return Ordered.HIGHEST_PRECEDENCE + 20;
    }
}
