package com.trinasolar.gateway.security.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * CORS 跨域配置属性
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@ConfigurationProperties(prefix = "trina.gateway.cors")
public class CorsProperties {

    /**
     * 是否启用 CORS 过滤器
     */
    private boolean enabled = true;

    /**
     * 允许的域名列表
     */
    private List<String> allowedOrigins = List.of(
            "http://localhost:3000",
            "http://localhost:8080",
            "http://127.0.0.1:3000",
            "http://127.0.0.1:8080"
    );

    /**
     * 允许的请求头
     */
    private String allowedHeaders = "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma, Content-Language, X-Request-Id, X-Device-Type, X-Device-Version, X-Tenant-Id, X-App-Id, X-Org-Id, X-User-Id";

    /**
     * 允许的请求方法
     */
    private String allowedMethods = "GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD";

    /**
     * 暴露的响应头
     */
    private String exposedHeaders = "Cache-Control, Content-Language, Content-Length, Content-Disposition, X-Request-ID, Location, ETag, Last-Modified, WWW-Authenticate";

    /**
     * 预检请求缓存时间（秒）
     */
    private long maxAge = 3600;

    /**
     * 是否允许携带凭证
     */
    private boolean allowCredentials = true;

    /**
     * 是否支持子域名匹配
     */
    private boolean allowSubdomains = true;

    /**
     * 允许的顶级域名列表（用于子域名匹配）
     */
    private List<String> allowedDomains = List.of("trinasolar.com", "trinasolar.com.cn", "trinasolar.cn");
}