package com.trinasolar.openfeign.config;


import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trinasolar.common.core.base.R;
import com.trinasolar.openfeign.exception.FeignDecodeIOException;
import feign.Response;
import feign.Util;
import feign.codec.ErrorDecoder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * Feign 错误信息解码器
 *
 * <AUTHOR>
 * @since 2025-06-11 14:52
 **/
@Slf4j
@RequiredArgsConstructor
public class FeignErrorDecoder implements ErrorDecoder {

    private final ObjectMapper objectMapper;

    @Override
    public Exception decode(String methodKey, Response response) {

        try {
            String content = Util.toString(response.body().asReader(StandardCharsets.UTF_8));
            R<String> result = R.failed("Feign 远程调用" + methodKey + " 出错");
            JavaType javaType = objectMapper.getTypeFactory().constructParametricType(R.class, String.class);
            R<String> object = objectMapper.readValue(content, javaType);
            if (ObjectUtils.isNotEmpty(object)) {
                result = object;
            }
            return new FeignRemoteCallExceptionWrapper(result);
        } catch (IOException e) {
            log.error("[Feign invoke [{}] error decoder convert result catch io exception.", methodKey, e);
            return new FeignDecodeIOException();
        }
    }
}
