package com.trinasolar.data.base;

import lombok.Getter;

/**
 * 逻辑操作符枚举
 * 用于定义查询条件之间的逻辑关系，避免字符串直接传递导致的SQL注入风险
 */
@Getter
public enum LogicalOperator {
    /**
     * 与操作，对应SQL中的AND
     */
    AND("AND"),

    /**
     * 或操作，对应SQL中的OR
     */
    OR("OR");

    /**
     * -- GETTER --
     * 获取对应的SQL关键字
     */
    private final String sqlKeyword;

    LogicalOperator(String sqlKeyword) {
        this.sqlKeyword = sqlKeyword;
    }

    /**
     * 将字符串安全转换为枚举
     *
     * @param value 字符串值
     * @return 对应的枚举值，如果无法转换则默认返回AND
     */
    public static LogicalOperator fromString(String value) {
        if (value == null) {
            return AND;
        }

        try {
            return valueOf(value.toUpperCase());
        } catch (IllegalArgumentException e) {
            // 无效的值，返回默认值
            return AND;
        }
    }
} 