package com.trinasolar.i18n;

import com.trinasolar.i18n.api.TranslationApiClient;
import com.trinasolar.i18n.cache.TranslationCache;
import com.trinasolar.i18n.cache.TranslationCacheImpl;
import com.trinasolar.i18n.config.I18nProperties;
import com.trinasolar.i18n.core.I18nService;
import com.trinasolar.i18n.core.I18nServiceImpl;
import com.trinasolar.i18n.file.TranslationFileLoader;
import com.trinasolar.i18n.file.TranslationFileLoaderImpl;
import com.trinasolar.i18n.file.TranslationFileSaver;
import com.trinasolar.i18n.file.TranslationFileSaverImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.core.task.SyncTaskExecutor;

import java.util.HashSet;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * I18nService测试类
 */
public class I18nServiceTest {

    private I18nProperties properties;
    private TranslationCache translationCache;
    private TranslationFileLoader translationFileLoader;
    private TranslationFileSaver translationFileSaver;
    private TranslationApiClient translationApiClient;
    private I18nService i18nService;

    @BeforeEach
    public void setup() {
        // 创建配置
        properties = new I18nProperties();
        properties.setDefaultLanguage("zh-CN");
        properties.setFilePath("i18n");
        HashSet<String> supportedLanguages = new HashSet<>();
        supportedLanguages.add("zh-CN");
        supportedLanguages.add("en-US");
        properties.setSupportedLanguages(supportedLanguages);

        // 创建模拟对象
        translationCache = Mockito.mock(TranslationCacheImpl.class);
        translationFileLoader = Mockito.mock(TranslationFileLoaderImpl.class);
        
        // 使用SyncTaskExecutor包装为AsyncTaskExecutor
        AsyncTaskExecutor syncAsyncExecutor = new AsyncTaskExecutor() {
            private final SyncTaskExecutor executor = new SyncTaskExecutor();
            
            @Override
            public void execute(Runnable task) {
                executor.execute(task);
            }
            
            @Override
            public void execute(Runnable task, long startTimeout) {
                executor.execute(task);
            }
            
            @Override
            public Future<?> submit(Runnable task) {
                executor.execute(task);
                return null;
            }
            
            @Override
            public <T> Future<T> submit(Callable<T> task) {
                return null;
            }
        };
        
        translationFileSaver = new TranslationFileSaverImpl(properties, syncAsyncExecutor);
        translationApiClient = Mockito.mock(TranslationApiClient.class);

        // 创建I18nService
        i18nService = new I18nServiceImpl(
                properties,
                translationCache,
                translationFileLoader,
                translationFileSaver,
                translationApiClient
        );
    }

    @Test
    public void testGetMessageFromCache() {
        // 设置模拟数据
        when(translationCache.get(eq("hello"), eq("zh-CN"))).thenReturn(java.util.Optional.of("你好"));

        // 测试
        String message = i18nService.getMessage("hello", "zh-CN");
        assertEquals("你好", message);
    }

    @Test
    public void testGetMessageFromFile() {
        // 设置模拟数据
        when(translationCache.get(any(), any())).thenReturn(java.util.Optional.empty());
        when(translationFileLoader.loadTranslation(eq("hello"), eq("zh-CN"))).thenReturn(java.util.Optional.of("你好"));

        // 测试
        String message = i18nService.getMessage("hello", "zh-CN");
        assertEquals("你好", message);
    }

    @Test
    public void testGetMessageFromApi() {
        // 设置模拟数据
        when(translationCache.get(any(), any())).thenReturn(java.util.Optional.empty());
        when(translationFileLoader.loadTranslation(any(), any())).thenReturn(java.util.Optional.empty());
        when(translationApiClient.isAvailable()).thenReturn(true);
        when(translationApiClient.translate(eq("hello"), eq("zh-CN"), eq("en-US")))
                .thenReturn(java.util.Optional.of("hello"));

        // 测试
        String message = i18nService.getMessage("hello", "hello", "en-US");
        assertEquals("hello", message); // 由于模拟的API客户端没有正确配置，将返回默认消息
    }

    @Test
    public void testGetMessageWithDefaultMessage() {
        // 设置模拟数据
        when(translationCache.get(any(), any())).thenReturn(java.util.Optional.empty());
        when(translationFileLoader.loadTranslation(any(), any())).thenReturn(java.util.Optional.empty());
        when(translationApiClient.isAvailable()).thenReturn(false);

        // 测试
        String message = i18nService.getMessage("unknown", "默认消息", "zh-CN");
        assertEquals("默认消息", message);
    }

    @Test
    public void testGetMessageWithCurrentLanguage() {
        // 设置模拟数据
        when(translationCache.get(eq("hello"), eq("zh-CN"))).thenReturn(java.util.Optional.of("你好"));
        i18nService.setCurrentLanguage("zh-CN");

        // 测试
        String message = i18nService.getMessage("hello");
        assertEquals("你好", message);

        // 清除当前语言
        i18nService.clearCurrentLanguage();
    }
}