---
title: "15分钟了解Arthas Java诊断工具"
description: "快速掌握Arthas的核心功能、常见应用场景及实践指南"
date: "2024-05-26"
tags: ["Java", "诊断工具", "Arthas", "性能调优", "线上问题排查"]
---

# 15分钟了解Arthas Java诊断工具

## 🔍 技术简介 (3分钟)

### 什么是Arthas？

Arthas（阿尔萨斯）是阿里巴巴在2018年9月开源的Java诊断工具，专为解决Java应用线上问题而设计。作为一款非侵入式的诊断工具，它允许开发者在不修改代码、不重启服务的情况下，实时诊断线上问题，大大提高问题排查效率。

Arthas采用命令行交互模式，支持丰富的Tab自动补全功能，简化了操作流程。同时，它还提供了Web Console界面，方便远程诊断和结果分析。作为目前GitHub上最受欢迎的Java诊断工具之一，Arthas已经获得了超过36K的星标。

### 基本原理

Arthas基于Java的Instrumentation机制，通过动态修改字节码（bytecode）的方式，在不重启JVM的情况下，实现对正在运行的Java程序的监控和诊断。其核心实现包括：

1. **Java Agent技术**：利用`-javaagent`参数或Attach API动态加载Agent
2. **ASM字节码操作框架**：实现方法增强，插入监控代码
3. **Java Instrumentation API**：实现类的重定义（retransform）
4. **JVM TI（Tool Interface）**：获取JVM内部信息

Arthas的工作流程如下：
1. 连接目标JVM进程
2. 加载Arthas Agent
3. 执行诊断命令
4. 通过动态字节码增强收集数据
5. 展示诊断结果

### 与其他工具的对比

| 工具 | 特点 | 适用场景 |
|-----|------|---------|
| **Arthas** | 功能全面、无侵入、实时操作 | 线上问题快速定位，方法级性能分析 |
| **JProfiler** | 图形界面、资源消耗较大 | 开发环境性能分析 |
| **BTrace** | 脚本式、功能相对有限 | 简单场景的动态追踪 |
| **JConsole/VisualVM** | JDK自带、功能基础 | 基础监控、内存分析 |

## 💡 核心功能 (4分钟)

### 1. 全局监控

- **dashboard**：实时查看系统的运行状况，包括CPU使用率、内存使用、线程状态等
- **thread**：查看JVM的线程堆栈信息，定位线程CPU使用率高的原因
- **jvm**：查看JVM的详细信息，包括内存使用、GC情况、VM参数等

```bash
# 显示全局监控面板
dashboard
# 查看CPU占用TOP N线程
thread -n 3
# 检查是否存在死锁
thread -b
```

### 2. 类和方法级分析

- **sc/sm**：查找JVM中已加载的类和方法信息
- **jad**：反编译指定已加载类的源码，确认代码运行版本
- **mc/retransform**：内存编译和热更新类，实现不重启应用的代码修复

```bash
# 查找类的加载来源
sc -d javax.servlet.http.HttpServlet
# 反编译类
jad com.example.UserService
# 查看类的方法信息
sm com.example.UserService get*
```

### 3. 方法执行分析

- **monitor**：监控方法执行情况，包括调用次数、成功率、RT等
- **watch**：观察方法的入参、返回值和异常信息
- **trace**：跟踪方法调用链路，并统计各环节的耗时
- **stack**：查看方法的调用堆栈，了解调用来源

```bash
# 监控方法执行统计信息，每5秒输出一次
monitor -c 5 com.example.UserService findById
# 观察方法的入参和返回值
watch com.example.UserService findById '{params, returnObj}' -x 2
# 跟踪方法调用链路
trace com.example.UserService findById
```

### 4. 高级功能

- **tt**：时间隧道，记录方法每次调用的入参和返回信息，可回放历史调用
- **profiler**：生成火焰图，直观展示CPU或内存热点
- **heapdump**：生成堆转储文件，用于内存泄漏分析
- **vmtool**：查找内存中的对象实例

```bash
# 记录方法调用
tt -t com.example.UserService findById
# 生成CPU火焰图
profiler start
# 30秒后停止profiler并生成svg格式结果
profiler stop --format svg
```

## 📊 应用场景 (3分钟)

### 1. CPU飙升问题排查

**问题**：生产环境突然CPU使用率飙升，但无法确定具体原因。

**解决方案**：
1. 使用`dashboard`查看整体资源使用情况
2. 使用`thread -n 3`查看CPU占用率最高的线程
3. 使用`thread [ID]`查看高CPU线程的堆栈
4. 使用`trace`跟踪可疑方法的执行路径和耗时

**示例**：
```bash
# 假设发现ID为12的线程CPU使用率最高
thread 12
# 跟踪可疑方法
trace com.example.service.OrderService processOrder
```

### 2. 内存泄漏分析

**问题**：应用运行一段时间后内存持续增长，最终导致OOM。

**解决方案**：
1. 使用`dashboard`监控内存使用趋势
2. 使用`heapdump`生成堆转储文件
3. 使用`vmtool`查找大量存在的可疑对象
4. 使用MAT等工具分析堆转储文件

**示例**：
```bash
# 生成堆转储文件
heapdump /tmp/dump.hprof
# 查找特定类的实例
vmtool --action getInstances --className com.example.model.Order --limit 10
```

### 3. 线上代码热修复

**问题**：线上代码发现严重bug，但不能立即重启服务。

**解决方案**：
1. 使用`jad`反编译出有问题的类
2. 修改代码，修复bug
3. 使用`mc`将修改后的代码编译成class文件
4. 使用`retransform`将新的class文件加载到JVM中

**示例**：
```bash
# 反编译类
jad --source-only com.example.UserService > UserService.java
# 编辑修复代码...
# 内存编译
mc -c 5a7b68e UserService.java -d /tmp
# 热更新类
retransform /tmp/com/example/UserService.class
```

### 4. 接口性能分析

**问题**：某个接口响应时间过长，需要找出性能瓶颈。

**解决方案**：
1. 使用`monitor`监控接口方法的调用频率和RT
2. 使用`trace`跟踪方法执行链路，找出耗时较长的子调用
3. 使用`profiler`生成火焰图，直观展示热点

**示例**：
```bash
# 监控接口方法
monitor -c 5 com.example.controller.OrderController getOrderDetail
# 跟踪方法调用链路
trace com.example.controller.OrderController getOrderDetail '#cost > 200'
# 生成火焰图
profiler start --event cpu
# 等待30秒后停止
profiler stop
```

## 🚀 快速入门 (5分钟)

### 环境准备

Arthas支持JDK 6+（推荐JDK 8及以上），适用于Linux、macOS和Windows平台。

**前置条件**：
- 目标应用需要运行在JVM上
- 用户对目标进程有足够的操作权限
- 确保网络可达（如需远程诊断）

### 安装

**方式1：使用arthas-boot（推荐）**

```bash
# 下载启动器
curl -O https://arthas.aliyun.com/arthas-boot.jar
# 启动Arthas（自动检测Java进程）
java -jar arthas-boot.jar
```

**方式2：使用as.sh脚本**

```bash
# 一键安装
curl -L https://arthas.aliyun.com/install.sh | sh
# 启动（自动检测Java进程）
./as.sh
```

**方式3：在Spring Boot应用中集成**

添加依赖：
```xml
<dependency>
    <groupId>com.taobao.arthas</groupId>
    <artifactId>arthas-spring-boot-starter</artifactId>
    <version>${arthas.version}</version>
</dependency>
```

配置application.properties：
```properties
arthas.enabled=true
arthas.telnet-port=3658
arthas.http-port=8563
```

### 启动与连接

1. 运行安装命令后，Arthas会列出当前系统上的Java进程
2. 输入进程序号（非PID）选择要诊断的目标进程
3. Arthas会自动注入到目标进程并启动交互终端

```bash
$ java -jar arthas-boot.jar
[INFO] arthas-boot version: 3.6.7
[INFO] Found existing java process, please choose one and press RETURN.
* [1]: 12345 com.example.MyApplication
  [2]: 23456 org.apache.catalina.startup.Bootstrap
```

### 常用命令快速上手

| 命令类型 | 常用命令 | 用途 |
|--------|---------|------|
| 基础命令 | help, cls, session, version | 获取帮助、清屏、查看会话、版本信息 |
| 进程相关 | dashboard, thread, jvm, sysprop | 系统状态、线程状态、JVM信息、系统属性 |
| 类相关 | sc, sm, jad, mc, retransform | 查找类、查找方法、反编译、内存编译、热更新 |
| 方法相关 | watch, trace, stack, monitor, tt | 观察方法、跟踪调用、查看调用栈、监控方法、时间隧道 |
| 增强工具 | profiler, heapdump, vmtool | 火焰图、堆转储、内存分析 |

**入门示例**：排查高CPU问题

```bash
# 1. 查看系统整体状况
dashboard

# 2. 查看高CPU线程
thread -n 3

# 3. 假设发现ID为16的线程CPU较高，查看其栈信息
thread 16

# 4. 假设发现可疑方法，跟踪其执行情况
trace com.example.service.OrderService processOrder

# 5. 查看方法执行统计信息
monitor -c 5 com.example.service.OrderService processOrder
```

### Web Console使用

Arthas启动后会自动启动Web Console，默认访问地址：http://127.0.0.1:8563/

Web Console提供与命令行相同的功能，但有更友好的界面：
1. 支持历史命令查看和重复执行
2. 结果以表格、树形结构等方式展示
3. 支持结果下载和分享

### 退出与停止

```bash
# 退出当前会话，保持Arthas在目标进程中运行
quit

# 完全退出Arthas，并卸载所有增强类
shutdown
```

## 总结

Arthas作为一款功能强大的Java诊断工具，通过其非侵入式的特性和丰富的命令，使Java开发者能够在不中断服务的情况下对线上问题进行排查和分析。它的出现大大减少了线上问题排查的时间和成本，使"看不见"的JVM内部状态变得可见可控。

对于Java开发者和运维人员来说，熟练掌握Arthas不仅能提高问题排查效率，还能加深对JVM运行机制的理解。建议在日常开发和维护中持续实践，不断探索Arthas的更多高级功能，让它成为解决Java线上问题的得力助手。

## 相关资源

- 官方文档：[https://arthas.aliyun.com/doc/](https://arthas.aliyun.com/doc/)
- GitHub仓库：[https://github.com/alibaba/arthas](https://github.com/alibaba/arthas)
- 在线教程：[https://arthas.aliyun.com/doc/arthas-tutorials.html](https://arthas.aliyun.com/doc/arthas-tutorials.html)
- 用户案例：[https://github.com/alibaba/arthas/issues?q=label%3Auser-case](https://github.com/alibaba/arthas/issues?q=label%3Auser-case) 