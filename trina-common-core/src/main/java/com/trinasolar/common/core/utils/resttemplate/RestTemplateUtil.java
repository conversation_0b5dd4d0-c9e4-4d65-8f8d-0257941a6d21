package com.trinasolar.common.core.utils.resttemplate;

import com.trinasolar.common.core.base.R;
import com.trinasolar.common.core.utils.http.HttpUtils;
import com.alibaba.fastjson2.JSONObject;
import lombok.experimental.UtilityClass;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.Map;


/**
 * ClassName: RestTemplateUtil <br/>
 * Function: 业务类的父类. <br/>
 * date: 2020/10/26 9:41  <br/>
 * author: wangyang
 */
@UtilityClass
public class RestTemplateUtil {

    private static final Logger log = LoggerFactory.getLogger(HttpUtils.class);

    /**
     * Desc 调用内部微服务接口 <br/>
     * Param [url, paramsJson] <br/>
     * Return {@link R} <br/>
     * Date 2020/10/26 9:52 <br/>
     * Author wangyang <br/>
     */
    public R post(String url, JSONObject paramsJson, Map<String, Object> headParam) {
        //入参校验
        if (url == null) {
            log.error("url不能为空");
            return R.failed("url不能为空");
        }
        if (paramsJson == null) {
            log.error("paramsJson不能为空");
            return R.failed("paramsJson不能为空");
        }
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<JSONObject> exchange;
        //构建请求头
        HttpHeaders headers = new HttpHeaders();
        if (headParam != null) {
            //设置各种头信息
            for (Map.Entry<String, Object> set : headParam.entrySet()) {
                String key = set.getKey();
                String value = set.getValue() == null ? "" : set.getValue().toString();
                headers.set(key, value);
            }
        }
        try {
            URI uri = UriComponentsBuilder.fromUriString(url).build().toUri();
            RequestEntity<String> requestEntity = RequestEntity.post(uri)
                    .accept(MediaType.APPLICATION_JSON)
                    .headers(headers)
                    .body(paramsJson.toString());
            exchange = restTemplate.exchange(requestEntity, JSONObject.class);
        } catch (Exception ex) {
            log.error("调用微服务接口出现异常,接口地址:" + url, ex);
            return R.failed("调用微服务接口出现异常:" + ex.getMessage() + ",接口地址:" + url);
        }
        return R.ok(exchange);
    }

}
