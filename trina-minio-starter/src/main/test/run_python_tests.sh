#!/bin/bash

# 获取脚本所在的目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &> /dev/null && pwd)"

# Python测试脚本的路径
PYTHON_TEST_SCRIPT="${SCRIPT_DIR}/task_api_test.py"

# 检查pip是否安装
if ! command -v pip &> /dev/null && ! command -v pip3 &> /dev/null; then
    echo "错误：未找到pip或pip3命令。请先安装pip。"
    exit 1
fi

# 确定使用pip还是pip3
PIP_CMD="pip"
if command -v pip3 &> /dev/null; then
    PIP_CMD="pip3"
fi

# 安装requests库
echo "正在安装/更新 requests 库..."
${PIP_CMD} install --upgrade requests

# 检查requests库是否安装成功
if ! python3 -c "import requests" &> /dev/null; then
    echo "错误：requests 库安装失败。请检查pip安装和网络连接。"
    exit 1
fi

# 检查Python测试脚本是否存在
if [ ! -f "${PYTHON_TEST_SCRIPT}" ]; then
    echo "错误：未找到Python测试脚本 ${PYTHON_TEST_SCRIPT}"
    exit 1
fi

# 执行Python测试脚本
echo "正在执行Python测试脚本..."
python3 "${PYTHON_TEST_SCRIPT}"

echo "脚本执行完毕。" 