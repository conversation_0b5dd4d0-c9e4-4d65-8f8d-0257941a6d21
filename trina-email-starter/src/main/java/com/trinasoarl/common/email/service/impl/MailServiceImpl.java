package com.trinasoarl.common.email.service.impl;

import com.trinasoarl.common.email.config.MailProperties;
import com.trinasoarl.common.email.exception.MailException;
import com.trinasoarl.common.email.model.EmailRequest;
import com.trinasoarl.common.email.service.MailService;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import java.util.Objects;


public class MailServiceImpl implements MailService {

    private static final Logger logger = LoggerFactory.getLogger(MailServiceImpl.class);

    private final JavaMailSender mailSender;

    private final MailProperties mailProperties;

    public MailServiceImpl(JavaMailSender mailSender, MailProperties mailProperties) {
        this.mailSender = mailSender;
        this.mailProperties = mailProperties;
        logger.info("Loading mailService Successfully!");
    }

    @Override
    public Boolean sendMail(EmailRequest emailRequest) throws MailException {
        checkToUsers(emailRequest.getUsers());
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message,true);
            helper.setFrom(mailProperties.getFrom());
            helper.setTo(emailRequest.getUsers());
            if (!Objects.isNull(emailRequest.getCcUsers()) && emailRequest.getCcUsers().length > 0){
                helper.setCc(emailRequest.getCcUsers());
            }
            helper.setSubject(emailRequest.getSubject());
            if (emailRequest.getHtml()){
                helper.setText(emailRequest.getMsg(), true);
            } else {
                helper.setText(emailRequest.getMsg());
            }
            if(Objects.nonNull(emailRequest.getFile())){
                helper.addAttachment(emailRequest.getFile().getName(), emailRequest.getFile());  // 添加附件
            }
            mailSender.send(message);
            return true;
        } catch (MessagingException e) {
            logger.error("邮件发送失败", e.getMessage());
            return false;
        }
    }


    /**
     * 收件人不能为空
     * @param toUsers
     */
    private void checkToUsers(String[] toUsers) {
        if (Objects.isNull(toUsers) || toUsers.length == 0) {
            throw new MailException("收件人不能为空");
        }
    }

}