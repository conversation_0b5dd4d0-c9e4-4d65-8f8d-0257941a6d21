package com.trinasolar.mongodb.base;

import com.trinasolar.common.core.base.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * MongoDB基础控制器
 * 提供标准的RESTful API接口
 *
 * @param <S> 服务类型
 * @param <T> 实体类型
 */
@Tag(name = "MongoDB基础操作")
public abstract class BaseMongoController<S extends BaseMongoService<T>, T extends BaseMongoEntity> {

    protected final S service;

    /**
     * 构造函数
     *
     * @param service 服务实例
     */
    protected BaseMongoController(S service) {
        this.service = service;
    }

    /**
     * 根据ID获取文档
     *
     * @param id 文档ID
     * @return 文档信息
     */
    @Operation(summary = "根据ID获取文档")
    @GetMapping("/{id}")
    public R<T> getById(@Parameter(description = "文档ID", required = true) @PathVariable String id) {
        Optional<T> optional = service.findById(id);
        return optional.isPresent() ?
                R.ok(optional.get()) :
                R.failed("文档不存在");
    }

    /**
     * 查询所有文档
     *
     * @return 文档列表
     */
    @Operation(summary = "查询所有文档")
    @GetMapping
    public R<List<T>> list() {
        return R.ok(service.findAll());
    }

    /**
     * 分页查询
     *
     * @param query 查询条件
     * @return 分页结果
     */
    @Operation(summary = "分页查询文档")
    @PostMapping("/page")
    public R<PageResult<T>> page(@RequestBody BaseMongoQuery<T> query) {
        return R.ok(service.findPage(query));
    }

    /**
     * 创建文档
     *
     * @param entity 文档实体
     * @return 创建结果
     */
    @Operation(summary = "创建文档")
    @PostMapping
    public R<T> create(@RequestBody T entity) {
        return R.ok(service.save(entity));
    }

    /**
     * 批量创建文档
     *
     * @param entities 文档实体列表
     * @return 创建结果
     */
    @Operation(summary = "批量创建文档")
    @PostMapping("/batch")
    public R<List<T>> batchCreate(@RequestBody List<T> entities) {
        return R.ok(service.saveAll(entities));
    }

    /**
     * 更新文档
     *
     * @param id     文档ID
     * @param entity 文档实体
     * @return 更新结果
     */
    @Operation(summary = "更新文档")
    @PutMapping("/{id}")
    public R<T> update(@Parameter(description = "文档ID", required = true) @PathVariable String id,
                       @RequestBody T entity) {
        entity.setId(id);
        return R.ok(service.save(entity));
    }

    /**
     * 部分更新文档
     *
     * @param id           文档ID
     * @param updateFields 更新字段
     * @return 更新结果
     */
    @Operation(summary = "部分更新文档")
    @PatchMapping("/{id}")
    public R<T> partialUpdate(@Parameter(description = "文档ID", required = true) @PathVariable String id,
                              @RequestBody Map<String, Object> updateFields) {
        T updated = service.partialUpdate(id, updateFields);
        return updated != null ? R.ok(updated) : R.failed("文档不存在或更新失败");
    }

    /**
     * 根据ID删除文档
     *
     * @param id 文档ID
     * @return 删除结果
     */
    @Operation(summary = "根据ID删除文档")
    @DeleteMapping("/{id}")
    public R<Void> delete(@Parameter(description = "文档ID", required = true) @PathVariable String id) {
        service.deleteById(id);
        return R.ok();
    }

    /**
     * 根据查询条件删除文档
     *
     * @param query 查询条件
     * @return 删除结果
     */
    @Operation(summary = "根据条件删除文档")
    @PostMapping("/delete-by-query")
    public R<Long> deleteByQuery(@RequestBody BaseMongoQuery<T> query) {
        long count = service.deleteByQuery(query);
        return R.ok(count);
    }

    /**
     * 统计文档数量
     *
     * @return 文档数量
     */
    @Operation(summary = "查询文档数量")
    @GetMapping("/count")
    public R<Long> count() {
        return R.ok(service.count());
    }

    /**
     * 根据条件统计文档数量
     *
     * @param query 查询条件
     * @return 文档数量
     */
    @Operation(summary = "根据条件查询文档数量")
    @PostMapping("/count-by-query")
    public R<Long> countByQuery(@RequestBody BaseMongoQuery<T> query) {
        return R.ok(service.countByQuery(query));
    }

    /**
     * 逻辑删除文档
     *
     * @param id 文档ID
     * @return 删除结果
     */
    @Operation(summary = "逻辑删除文档")
    @PutMapping("/{id}/soft-delete")
    public R<Boolean> softDelete(@Parameter(description = "文档ID", required = true) @PathVariable String id) {
        boolean success = service.softDelete(id);
        return R.ok(success);
    }

    /**
     * 批量逻辑删除文档
     *
     * @param ids 文档ID列表
     * @return 删除结果
     */
    @Operation(summary = "批量逻辑删除文档")
    @PutMapping("/batch-soft-delete")
    public R<Long> batchSoftDelete(@RequestBody List<String> ids) {
        long count = service.softDeleteBatch(ids);
        return R.ok(count);
    }
} 