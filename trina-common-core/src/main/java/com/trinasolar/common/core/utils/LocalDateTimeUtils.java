package com.trinasolar.common.core.utils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * <AUTHOR>
 * @usage 时间转化工具类
 * @date 2021/10/7 9:06 下午
 */
public class LocalDateTimeUtils {

    /**
     * 转化为时间戳
     *
     * @param ldt
     * @return
     */
    public static long toTimestamp(LocalDateTime ldt) {
        return ldt.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 转化为时间
     *
     * @param timestamp
     * @return
     */
    public static LocalDateTime toDateTime(long timestamp) {
        Instant instant = Instant.ofEpochMilli(timestamp);
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }
}
