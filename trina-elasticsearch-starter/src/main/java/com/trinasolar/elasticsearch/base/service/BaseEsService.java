package com.trinasolar.elasticsearch.base.service;

import com.trinasolar.elasticsearch.base.entity.BaseEsEntity;
import com.trinasolar.elasticsearch.base.wrapper.EsQueryWrapper;
import com.trinasolar.elasticsearch.vo.SearchResult;

import java.io.IOException;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Elasticsearch基础Service接口
 * 提供通用的ES操作方法
 * 
 * @param <T> 实体类型，必须继承BaseEsEntity
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface BaseEsService<T extends BaseEsEntity> {
    
    // ==================== 索引管理 ====================
    
    /**
     * 创建索引
     * 
     * @return 是否创建成功
     * @throws IOException IO异常
     */
    boolean createIndex();
    
    /**
     * 删除索引
     * 
     * @return 是否删除成功
     * @throws IOException IO异常
     */
    boolean deleteIndex();
    
    /**
     * 索引是否存在
     * 
     * @return 是否存在
     * @throws IOException IO异常
     */
    boolean indexExists();
    
    /**
     * 刷新索引
     * 
     * @return 是否刷新成功
     * @throws IOException IO异常
     */
    boolean refreshIndex();
    
    // ==================== 文档操作 ====================
    
    /**
     * 保存文档
     * 
     * @param entity 实体对象
     * @return 保存后的实体对象
     * @throws IOException IO异常
     */
    T save(T entity);
    
    /**
     * 批量保存文档
     * 
     * @param entityList 实体对象列表
     * @return 保存成功的数量
     * @throws IOException IO异常
     */
    int saveBatch(Collection<T> entityList);
    
    /**
     * 根据ID删除文档
     * 
     * @param id 文档ID
     * @return 是否删除成功
     * @throws IOException IO异常
     */
    boolean removeById(Serializable id);
    
    /**
     * 根据ID列表批量删除文档
     * 
     * @param idList ID列表
     * @return 删除成功的数量
     * @throws IOException IO异常
     */
    int removeByIds(Collection<? extends Serializable> idList);
    
    /**
     * 根据条件删除文档
     * 
     * @param queryWrapper 查询条件
     * @return 删除成功的数量
     * @throws IOException IO异常
     */
    int remove(EsQueryWrapper<T> queryWrapper);
    
    /**
     * 更新文档
     * 
     * @param entity 实体对象
     * @return 更新后的实体对象
     * @throws IOException IO异常
     */
    T updateById(T entity);
    
    /**
     * 批量更新文档
     * 
     * @param entityList 实体对象列表
     * @return 更新成功的数量
     * @throws IOException IO异常
     */
    int updateBatchById(Collection<T> entityList);
    
    /**
     * 根据条件更新文档
     * 
     * @param entity 更新的实体对象
     * @param queryWrapper 查询条件
     * @return 更新成功的数量
     * @throws IOException IO异常
     */
    int update(T entity, EsQueryWrapper<T> queryWrapper);
    
    /**
     * 保存或更新文档
     * 
     * @param entity 实体对象
     * @return 保存或更新后的实体对象
     * @throws IOException IO异常
     */
    T saveOrUpdate(T entity);
    
    /**
     * 批量保存或更新文档
     * 
     * @param entityList 实体对象列表
     * @return 保存或更新成功的数量
     * @throws IOException IO异常
     */
    int saveOrUpdateBatch(Collection<T> entityList);
    
    // ==================== 查询操作 ====================
    
    /**
     * 根据ID查询文档
     * 
     * @param id 文档ID
     * @return 实体对象
     * @throws IOException IO异常
     */
    T getById(Serializable id);
    
    /**
     * 根据ID列表批量查询文档
     * 
     * @param idList ID列表
     * @return 实体对象列表
     * @throws IOException IO异常
     */
    List<T> listByIds(Collection<? extends Serializable> idList);
    
    /**
     * 根据Map条件查询文档列表
     * 
     * @param columnMap 查询条件Map
     * @return 实体对象列表
     * @throws IOException IO异常
     */
    List<T> listByMap(Map<String, Object> columnMap);
    
    /**
     * 根据条件查询单个文档
     * 
     * @param queryWrapper 查询条件
     * @return 实体对象
     * @throws IOException IO异常
     */
    T getOne(EsQueryWrapper<T> queryWrapper);
    
    /**
     * 根据条件查询文档列表
     * 
     * @param queryWrapper 查询条件
     * @return 实体对象列表
     * @throws IOException IO异常
     */
    List<T> list(EsQueryWrapper<T> queryWrapper);
    
    /**
     * 查询所有文档
     * 
     * @return 实体对象列表
     * @throws IOException IO异常
     */
    List<T> list();
    
    /**
     * 根据条件查询文档数量
     * 
     * @param queryWrapper 查询条件
     * @return 文档数量
     * @throws IOException IO异常
     */
    long count(EsQueryWrapper<T> queryWrapper);
    
    /**
     * 查询所有文档数量
     * 
     * @return 文档数量
     * @throws IOException IO异常
     */
    long count();
    
    // ==================== 搜索操作 ====================
    
    /**
     * 搜索文档（带分页）
     * 
     * @param queryWrapper 查询条件
     * @return 搜索结果
     * @throws IOException IO异常
     */
    SearchResult<T> search(EsQueryWrapper<T> queryWrapper);
    
    /**
     * 搜索文档（带分页和高亮）
     * 
     * @param queryWrapper 查询条件
     * @param highlightFields 高亮字段
     * @return 搜索结果
     * @throws IOException IO异常
     */
    SearchResult<T> searchWithHighlight(EsQueryWrapper<T> queryWrapper, String... highlightFields);
    
    /**
     * 滚动搜索文档
     * 
     * @param queryWrapper 查询条件
     * @param scrollTime 滚动时间
     * @return 搜索结果
     * @throws IOException IO异常
     */
    SearchResult<T> scroll(EsQueryWrapper<T> queryWrapper, String scrollTime);
    
    /**
     * 继续滚动搜索
     * 
     * @param scrollId 滚动ID
     * @param scrollTime 滚动时间
     * @return 搜索结果
     * @throws IOException IO异常
     */
    SearchResult<T> scroll(String scrollId, String scrollTime);
    
    /**
     * 清除滚动搜索
     * 
     * @param scrollId 滚动ID
     * @return 是否清除成功
     * @throws IOException IO异常
     */
    boolean clearScroll(String scrollId);
    
    // ==================== 聚合操作 ====================
    
    /**
     * 聚合查询
     * 
     * @param queryWrapper 查询条件
     * @param aggregationName 聚合名称
     * @param aggregationField 聚合字段
     * @return 聚合结果
     * @throws IOException IO异常
     */
    Map<String, Object> aggregation(EsQueryWrapper<T> queryWrapper, String aggregationName, String aggregationField);
    
    /**
     * 多字段聚合查询
     * 
     * @param queryWrapper 查询条件
     * @param aggregations 聚合配置Map
     * @return 聚合结果
     * @throws IOException IO异常
     */
    Map<String, Object> aggregations(EsQueryWrapper<T> queryWrapper, Map<String, String> aggregations);
    
    // ==================== 建议操作 ====================
    
    /**
     * 自动补全建议
     * 
     * @param field 字段名
     * @param text 输入文本
     * @param size 返回数量
     * @return 建议列表
     * @throws IOException IO异常
     */
    List<String> suggest(String field, String text, int size);
    
    /**
     * 短语建议
     * 
     * @param field 字段名
     * @param text 输入文本
     * @param size 返回数量
     * @return 建议列表
     * @throws IOException IO异常
     */
    List<String> phraseSuggest(String field, String text, int size);
    
    /**
     * 词项建议
     * 
     * @param field 字段名
     * @param text 输入文本
     * @param size 返回数量
     * @return 建议列表
     * @throws IOException IO异常
     */
    List<String> termSuggest(String field, String text, int size);
    
    /**
     * 获取标签建议
     * 
     * @param prefix 前缀
     * @param size 返回数量
     * @return 建议列表
     */
    List<String> getTagSuggestions(String prefix, int size);
    
    /**
     * 获取作者建议
     * 
     * @param prefix 前缀
     * @param size 返回数量
     * @return 建议列表
     */
    List<String> getAuthorSuggestions(String prefix, int size);
    
    /**
     * 获取分类建议
     * 
     * @param prefix 前缀
     * @param size 返回数量
     * @return 建议列表
     */
    List<String> getCategorySuggestions(String prefix, int size);
    
    // ==================== 工具方法 ====================
    
    /**
     * 获取实体类型
     * 
     * @return 实体类型
     */
    Class<T> getEntityClass();
    
    /**
     * 获取索引名称
     * 
     * @return 索引名称
     */
    String getIndexName();
    
    /**
     * 获取文档类型
     * 
     * @return 文档类型
     */
    String getDocumentType();
    
    /**
     * 创建查询构建器
     * 
     * @return 查询构建器
     */
    EsQueryWrapper<T> queryWrapper();
    
    /**
     * 创建更新构建器
     *
     * @return 更新构建器
     */
    EsQueryWrapper<T> updateWrapper();

    // ==================== 向量搜索能力 ====================

    /**
     * 保存实体并生成向量
     *
     * @param entity 实体对象
     * @param titleField 标题字段名
     * @param contentField 内容字段名
     * @return 保存后的实体
     */
    T saveWithVector(T entity, String titleField, String contentField);

    /**
     * 批量保存实体并生成向量
     *
     * @param entities 实体列表
     * @param titleField 标题字段名
     * @param contentField 内容字段名
     * @return 保存后的实体列表
     */
    List<T> saveBatchWithVector(List<T> entities, String titleField, String contentField);

    /**
     * 文本向量搜索
     *
     * @param queryText 查询文本
     * @param vectorField 向量字段名（content_vector 或 title_vector）
     * @param threshold 相似度阈值
     * @param size 返回数量
     * @return 搜索结果
     */
    List<T> vectorSearch(String queryText, String vectorField, float threshold, int size);

    /**
     * 混合搜索（关键词 + 向量）
     *
     * @param keyword 关键词
     * @param keywordFields 关键词搜索字段
     * @param vectorField 向量字段名
     * @param keywordWeight 关键词权重
     * @param vectorWeight 向量权重
     * @param threshold 向量相似度阈值
     * @param size 返回数量
     * @return 搜索结果
     */
    List<T> hybridSearch(String keyword, String[] keywordFields, String vectorField,
                        float keywordWeight, float vectorWeight, float threshold, int size);

    /**
     * 查找相似实体
     *
     * @param entityId 实体ID
     * @param vectorField 向量字段名
     * @param threshold 相似度阈值
     * @param size 返回数量
     * @return 相似实体列表
     */
    List<T> findSimilarEntities(String entityId, String vectorField, float threshold, int size);

    // ==================== 系统信息 ====================

    /**
     * 获取系统信息
     *
     * @return 系统信息
     */
    Map<String, Object> getSystemInfo();

    /**
     * 检查版本支持情况
     *
     * @return 版本支持信息
     */
    Map<String, Object> checkVersionSupport();
}