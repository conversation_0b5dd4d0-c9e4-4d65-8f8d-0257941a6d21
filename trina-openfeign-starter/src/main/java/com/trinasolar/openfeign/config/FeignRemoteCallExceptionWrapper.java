package com.trinasolar.openfeign.config;


import com.trinasolar.common.core.base.R;

import java.io.Serial;

/**
 * Feign Fallback 错误统一封装器
 *
 * <AUTHOR>
 * @since 2025-06-11 16:23
 **/
public class FeignRemoteCallExceptionWrapper extends RuntimeException {

    @Serial
    private static final long serialVersionUID = -4659108069047847688L;

    private final R<String> result;

    public FeignRemoteCallExceptionWrapper(R<String> result) {
        this.result = result;
    }

    public FeignRemoteCallExceptionWrapper(String message, R<String> result) {
        super(message);
        this.result = result;
    }

    public FeignRemoteCallExceptionWrapper(String message, Throwable cause, R<String> result) {
        super(message, cause);
        this.result = result;
    }

    public FeignRemoteCallExceptionWrapper(Throwable cause, R<String> result) {
        super(cause);
        this.result = result;
    }

    public FeignRemoteCallExceptionWrapper(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace, R<String> result) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.result = result;
    }
}
