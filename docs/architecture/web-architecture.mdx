---
title: Web 模块架构设计
description: trina-web-starter 是 Trina Common 框架的 Web 层基础模块，提供了 Web 应用开发中的核心功能，包括全局异常处理、HTTP 拦截器、跨域配置、JSON 序列化配置等。该模块基于 Spring Boot Web 构建，采用 MVC 架构模式，为微服务提供统一的 Web 层解决方案。
---

## 技术架构

### 整体架构图

```mermaid
graph TB
    subgraph "Web 模块架构"
        A[客户端请求] --> B[拦截器层]
        B --> C[控制器层]
        C --> D[业务逻辑层]
        D --> E[异常处理层]
        E --> F[响应处理层]
        F --> G[客户端响应]
        
        subgraph "拦截器层 (Interceptor)"
            B1[HttpInterceptor - HTTP请求拦截]
            B2[RequestUtil - 请求工具]
        end
        
        subgraph "配置层 (Configuration)"
            C1[WebConfiguration - Web配置]
            C2[CORS配置 - 跨域处理]
            C3[Jackson配置 - JSON序列化]
            C4[消息转换器配置]
        end
        
        subgraph "异常处理层 (Advice)"
            E1[GlobalExceptionHandler - 全局异常处理]
            E2[业务异常处理]
            E3[系统异常处理]
            E4[验证异常处理]
        end
    end
```

## 核心组件设计

### 1. Web 配置架构 (WebConfiguration)

#### 技术实现

```java
@Configuration
public class WebConfiguration implements WebMvcConfigurer {
    
    @Value("${spring.jackson.date-format:yyyy-MM-dd HH:mm:ss}")
    private String pattern;
    
    // 拦截器配置
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new HttpInterceptor())
                .addPathPatterns("/**");
    }
    
    // 跨域配置
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
            .allowedOriginPatterns("*")
            .allowedMethods("GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS")
            .allowedHeaders("*")
            .allowCredentials(true)
            .maxAge(3600);
    }
}
```

#### 设计特点

1. **统一配置管理**：
   - 集中管理 Web 层所有配置
   - 支持外部化配置
   - 提供合理的默认值

2. **跨域支持**：
   - 支持所有来源的跨域请求
   - 配置常用的 HTTP 方法
   - 支持凭证传递
   - 设置合理的缓存时间

3. **拦截器集成**：
   - 自动注册 HTTP 拦截器
   - 支持全路径拦截
   - 可扩展的拦截器链

### 2. JSON 序列化配置

#### 核心技术实现

```java
@Bean
@Primary
@ConditionalOnMissingBean(ObjectMapper.class)
public ObjectMapper jacksonObjectMapper(Jackson2ObjectMapperBuilder builder) {
    ObjectMapper objectMapper = builder.createXmlMapper(false).build();
    
    // 解决雪花算法ID精度丢失问题
    SimpleModule simpleModule = new SimpleModule();
    simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
    simpleModule.addSerializer(BigInteger.class, ToStringSerializer.instance);
    objectMapper.registerModule(simpleModule);
    
    // 允许JSON字符串中包含未转义的控制字符
    objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
    
    return objectMapper;
}
```

#### 序列化策略

```mermaid
flowchart LR
    A[Java对象] --> B{数据类型判断}
    B -->|Long/BigInteger| C[转换为String]
    B -->|LocalDateTime| D[格式化为指定格式]
    B -->|其他类型| E[默认序列化]
    C --> F[JSON输出]
    D --> F
    E --> F
```

**技术特性**：

1. **精度保护**：
   - Long 类型自动转换为 String，避免前端精度丢失
   - BigInteger 类型同样转换为 String
   - 解决雪花算法 ID 在前端的精度问题

2. **日期时间处理**：
   - 支持自定义日期格式
   - LocalDateTime 自动序列化/反序列化
   - 统一的时间格式标准

3. **容错性**：
   - 允许未转义的控制字符
   - 提高 JSON 解析的兼容性

### 3. HTTP 拦截器架构

#### 拦截器实现

```java
@Slf4j
public class HttpInterceptor implements HandlerInterceptor {
    
    private static final String TOKEN_KEY = "Authorization";
    private static final String HEADER_ORGAN_ID = "Amp-Organ-Id";
    
    @Override
    public boolean preHandle(HttpServletRequest request, 
                           HttpServletResponse response, 
                           Object handler) throws Exception {
        // 请求信息收集
        String requestURI = request.getRequestURI();
        String token = request.getHeader(TOKEN_KEY);
        String organId = request.getHeader(HEADER_ORGAN_ID);
        String requestMethod = request.getMethod();
        String queryString = request.getQueryString();
        String ipAddress = RequestUtil.getHttpServletRequestIpAddress(request);
        
        // 请求日志记录
        log.info("HTTP REQUEST INFO ==> URL:{} Method:{} Query:{} IP:{} OrganId:{} TOKEN:{}", 
                requestURI, requestMethod, queryString, ipAddress, organId, token);
        
        return true;
    }
}
```

#### 请求信息收集架构

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Interceptor as HTTP拦截器
    participant RequestUtil as 请求工具
    participant Logger as 日志系统
    participant Controller as 控制器
    
    Client->>Interceptor: HTTP请求
    Interceptor->>RequestUtil: 获取真实IP
    RequestUtil-->>Interceptor: 返回IP地址
    Interceptor->>Logger: 记录请求信息
    Interceptor->>Controller: 继续处理请求
    Controller-->>Client: 返回响应
```

**功能特性**：

1. **请求追踪**：
   - 记录请求 URL、方法、参数
   - 提取认证令牌和组织 ID
   - 获取客户端真实 IP 地址

2. **安全审计**：
   - 记录所有 HTTP 请求
   - 支持安全分析和审计
   - 便于问题排查和监控

### 4. IP 地址获取机制

#### 技术实现

```java
public static String getHttpServletRequestIpAddress(HttpServletRequest request) {
    // 优先级顺序获取真实IP
    String ip = request.getHeader("x-forwarded-for");
    
    if (isInvalidIp(ip)) {
        ip = request.getHeader("Proxy-Client-IP");
    }
    
    if (isInvalidIp(ip)) {
        ip = request.getHeader("WL-Proxy-Client-IP");
    }
    
    if (isInvalidIp(ip)) {
        ip = request.getRemoteAddr();
    }
    
    // 处理多IP情况
    if (ip.contains(",")) {
        ip = ip.split(",")[0];
    }
    
    // IPv6本地地址转换
    return "0:0:0:0:0:0:0:1".equals(ip) ? "127.0.0.1" : ip;
}
```

#### IP 获取策略

```mermaid
flowchart TD
    A[开始获取IP] --> B{检查 x-forwarded-for}
    B -->|有效| I[返回IP]
    B -->|无效| C{检查 Proxy-Client-IP}
    C -->|有效| I
    C -->|无效| D{检查 WL-Proxy-Client-IP}
    D -->|有效| I
    D -->|无效| E[使用 RemoteAddr]
    E --> F{是否包含逗号}
    F -->|是| G[取第一个IP]
    F -->|否| H{是否为IPv6本地地址}
    G --> H
    H -->|是| J[转换为127.0.0.1]
    H -->|否| I
    J --> I
```

**技术特点**：

1. **代理穿透**：
   - 支持多级代理环境
   - 按优先级获取真实 IP
   - 处理负载均衡器场景

2. **多IP处理**：
   - 处理 X-Forwarded-For 多IP情况
   - 取第一个有效 IP 地址
   - 支持 IPv6 地址转换

### 5. 全局异常处理架构

#### 异常处理层次

```mermaid
classDiagram
    class GlobalExceptionHandler {
        +authException(AuthException) ResponseEntity
        +businessException(BusinessException) ResponseEntity
        +unauthorizedException(UnauthorizedException) ResponseEntity
        +validationException(MethodArgumentNotValidException) ResponseEntity
        +illegalArgumentException(IllegalArgumentException) ResponseEntity
        +numberFormatException(NumberFormatException) ResponseEntity
        +generalException(Exception) ResponseEntity
    }
    
    class ExceptionType {
        <<enumeration>>
        AUTH_EXCEPTION
        BUSINESS_EXCEPTION
        UNAUTHORIZED_EXCEPTION
        VALIDATION_EXCEPTION
        SYSTEM_EXCEPTION
    }
```

#### 核心异常处理实现

```java
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    
    // 认证异常处理
    @ExceptionHandler(AuthException.class)
    @ResponseBody
    public ResponseEntity authException(AuthException e) {
        log.error("登录已失效:", e);
        return new ResponseEntity<>(BaseResult.failed("登录已失效"), 
                                  HttpStatus.UNAUTHORIZED);
    }
    
    // 业务异常处理
    @ExceptionHandler(BusinessException.class)
    @ResponseBody
    public ResponseEntity handler(BusinessException e) {
        log.error("BusinessException:", e);
        return new ResponseEntity<>(BaseResult.failed(e.getMessage()), 
                                  HttpStatus.OK);
    }
    
    // 参数验证异常处理
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public ResponseEntity methodArgumentNotValidExceptionHandler(
            MethodArgumentNotValidException e) {
        BindingResult result = e.getBindingResult();
        StringBuilder rs = new StringBuilder();
        
        if (result.hasErrors()) {
            for (ObjectError error : result.getAllErrors()) {
                FieldError fieldError = (FieldError) error;
                rs.append(fieldError.getField())
                  .append(fieldError.getDefaultMessage())
                  .append(".");
            }
        }
        
        return new ResponseEntity<>(BaseResult.failed(rs.toString()), 
                                  HttpStatus.BAD_REQUEST);
    }
}
```

#### 异常处理流程

```mermaid
sequenceDiagram
    participant Controller as 控制器
    participant Service as 业务服务
    participant ExceptionHandler as 异常处理器
    participant Logger as 日志系统
    participant Client as 客户端
    
    Controller->>Service: 调用业务方法
    Service-->>Controller: 抛出异常
    Controller->>ExceptionHandler: 异常传播
    ExceptionHandler->>Logger: 记录异常日志
    ExceptionHandler->>ExceptionHandler: 构造错误响应
    ExceptionHandler-->>Client: 返回统一错误格式
```

**异常处理特性**：

1. **分类处理**：
   - 认证异常：返回 401 状态码
   - 业务异常：返回 200 状态码，错误信息在响应体
   - 验证异常：返回 400 状态码，详细验证错误
   - 系统异常：返回 500 状态码，通用错误信息

2. **统一响应格式**：
   - 所有异常都返回 BaseResult 格式
   - 包含错误码、错误消息和数据
   - 便于前端统一处理

3. **日志记录**：
   - 记录异常堆栈信息
   - 便于问题排查和监控
   - 支持日志级别控制

### 6. 消息转换器配置

#### HTTP 消息转换架构

```java
@Override
public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
    // 字节数组转换器
    ByteArrayHttpMessageConverter byteArrayConverter = 
        new ByteArrayHttpMessageConverter();
    byteArrayConverter.setSupportedMediaTypes(
        Arrays.asList(MediaType.APPLICATION_OCTET_STREAM));
    
    // JSON转换器
    MappingJackson2HttpMessageConverter jsonConverter = 
        new MappingJackson2HttpMessageConverter();
    jsonConverter.setDefaultCharset(StandardCharsets.UTF_8);
    
    converters.add(byteArrayConverter);
    converters.add(jsonConverter);
}
```

**转换器特性**：

1. **多媒体类型支持**：
   - JSON 数据转换
   - 字节数组转换
   - 文件上传下载支持

2. **字符编码**：
   - 统一使用 UTF-8 编码
   - 避免中文乱码问题

3. **性能优化**：
   - 合理的转换器顺序
   - 避免不必要的转换

## 安全机制

### 1. 请求安全

```java
// 防止资源未找到异常暴露系统信息
@ExceptionHandler(NoResourceFoundException.class)
public void handleNoResourceFoundException(NoResourceFoundException ex, 
                                         HttpServletResponse response) {
    if (ex.getResourcePath() != null && 
        ex.getResourcePath().equals("favicon.ico")) {
        response.setStatus(HttpServletResponse.SC_NO_CONTENT);
        return;
    }
    response.setStatus(HttpServletResponse.SC_NOT_FOUND);
}
```

### 2. 跨域安全

- **受控的跨域策略**：允许指定的来源和方法
- **凭证支持**：安全地传递认证信息
- **预检请求处理**：正确处理 OPTIONS 请求

### 3. 异常信息安全

- **敏感信息过滤**：避免在错误响应中暴露系统内部信息
- **统一错误格式**：防止通过错误信息推断系统结构
- **日志安全**：敏感信息不记录到日志中

## 性能优化

### 1. JSON 序列化优化

```java
// 配置 Jackson 性能优化
objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
```

### 2. 拦截器性能

- **轻量级处理**：拦截器中只做必要的处理
- **异步日志**：使用异步方式记录请求日志
- **缓存机制**：缓存常用的请求信息

### 3. 异常处理性能

- **快速失败**：尽早发现和处理异常
- **异常缓存**：缓存常见异常的处理结果
- **堆栈优化**：控制异常堆栈的深度

## 监控和诊断

### 1. 请求监控

```java
// 请求指标收集
@Component
public class WebMetrics {
    private final Counter requestCounter;
    private final Timer requestTimer;
    
    public void recordRequest(String uri, String method, long duration) {
        requestCounter.increment(
            Tags.of("uri", uri, "method", method));
        requestTimer.record(duration, TimeUnit.MILLISECONDS);
    }
}
```

### 2. 异常监控

- **异常统计**：统计各类异常的发生频率
- **异常趋势**：监控异常发生的趋势变化
- **告警机制**：异常率超过阈值时触发告警

### 3. 性能监控

- **响应时间**：监控接口响应时间
- **吞吐量**：监控系统处理能力
- **资源使用**：监控内存和 CPU 使用情况

## 扩展机制

### 1. 拦截器扩展

```java
// 自定义拦截器接口
public interface CustomInterceptor extends HandlerInterceptor {
    int getOrder();  // 执行顺序
    boolean supports(HttpServletRequest request);  // 是否支持该请求
}
```

### 2. 异常处理扩展

```java
// 自定义异常处理器
public interface CustomExceptionHandler {
    boolean supports(Exception exception);
    ResponseEntity handle(Exception exception);
    int getOrder();
}
```

### 3. 消息转换器扩展

- **自定义转换器**：支持特殊数据格式的转换
- **转换器链**：支持多个转换器的组合使用
- **条件转换**：根据请求类型选择合适的转换器

## 最佳实践

### 1. 异常处理最佳实践

- **明确的异常分类**：区分业务异常和系统异常
- **有意义的错误消息**：提供用户友好的错误信息
- **完整的日志记录**：记录足够的上下文信息
- **安全的错误响应**：避免暴露敏感系统信息

### 2. 性能优化最佳实践

- **合理的拦截器链**：避免过多的拦截器影响性能
- **高效的JSON序列化**：选择合适的序列化策略
- **缓存机制**：缓存常用的配置和数据
- **异步处理**：对于耗时操作使用异步处理

### 3. 安全最佳实践

- **输入验证**：严格验证所有输入参数
- **输出编码**：防止 XSS 攻击
- **错误信息过滤**：避免信息泄露
- **访问控制**：实现细粒度的权限控制

## 总结

`trina-web-starter` 模块通过精心设计的架构，为 Web 应用提供了完整的基础设施。其全局异常处理、HTTP 拦截器、JSON 序列化配置和跨域支持等功能，大大简化了 Web 开发的复杂性。模块采用的分层架构、统一配置和扩展机制，确保了系统的可维护性和可扩展性，为构建高质量的微服务应用奠定了坚实的基础。