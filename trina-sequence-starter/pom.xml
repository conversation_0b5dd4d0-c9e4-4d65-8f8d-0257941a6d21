<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.trinasolar</groupId>
        <artifactId>trina-common</artifactId>
        <version>4.0.0</version>
    </parent>
    <artifactId>trina-sequence-starter</artifactId>
    <packaging>jar</packaging>
    <description>分布式发号器</description>

    <dependencies>
        <dependency>
            <groupId>com.trinasolar</groupId>
            <artifactId>trina-common-core</artifactId>
            <version>${common.version}</version>
        </dependency>
    </dependencies>
</project>
