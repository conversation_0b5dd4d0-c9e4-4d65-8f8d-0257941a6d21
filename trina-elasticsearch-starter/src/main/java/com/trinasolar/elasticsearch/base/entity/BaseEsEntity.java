package com.trinasolar.elasticsearch.base.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.trinasolar.elasticsearch.base.annotation.EsField;
import com.trinasolar.elasticsearch.base.annotation.EsId;
import com.trinasolar.elasticsearch.base.annotation.FieldType;
import com.trinasolar.elasticsearch.base.annotation.IdType;
import lombok.Data;

import java.io.Serializable;
// import java.time.LocalDateTime;

/**
 * Elasticsearch实体基类
 * 提供通用字段和基础功能
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
public abstract class BaseEsEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 文档ID
     */
    @EsId(type = IdType.ASSIGN_UUID)
    @JsonProperty("id")
    private String id;
    
    /**
     * 创建时间
     */
    @EsField(
        type = FieldType.KEYWORD,
        index = false
    )
    @JsonProperty("create_at")
    private String createAt;
    
    /**
     * 更新时间
     */
    @EsField(
        type = FieldType.KEYWORD,
        index = false
    )
    @JsonProperty("update_at")
    private String updateAt;
    
    /**
     * 创建人ID
     */
    @EsField(type = FieldType.KEYWORD)
    @JsonProperty("create_by")
    private String createBy;
    
    /**
     * 更新人ID
     */
    @EsField(type = FieldType.KEYWORD)
    @JsonProperty("update_by")
    private String updateBy;
    
    /**
     * 逻辑删除标识（0：未删除，1：已删除）
     */
    @EsField(type = FieldType.INTEGER)
    @JsonProperty("deleted")
    private Integer deleted = 0;
    
    /**
     * 版本号（用于乐观锁）
     */
    @EsField(type = FieldType.LONG)
    @JsonProperty("version")
    private Long version = 1L;
    
    /**
     * 租户ID（多租户支持）
     */
    @EsField(type = FieldType.KEYWORD)
    @JsonProperty("tenant_id")
    private String tenantId;
    
    /**
     * 文档评分（搜索时使用）
     */
    @EsField(ignore = true)
    @JsonProperty("_score")
    private Float score;
    
    /**
     * 高亮信息（搜索时使用）
     */
    @EsField(ignore = true)
    @JsonProperty("_highlight")
    private Object highlight;
    
    /**
     * 获取索引名称
     * 子类可以重写此方法来动态指定索引名称
     * 
     * @return 索引名称
     */
    public String getIndexName() {
        return null;
    }
    
    /**
     * 获取文档类型
     * 兼容ES 6.x及以下版本
     * 
     * @return 文档类型
     */
    public String getDocumentType() {
        return "_doc";
    }
    
    /**
     * 获取路由值
     * 用于分片路由
     * 
     * @return 路由值
     */
    public String getRouting() {
        return null;
    }
    
    /**
     * 获取父文档ID
     * 用于父子关系
     * 
     * @return 父文档ID
     */
    public String getParent() {
        return null;
    }
    
    /**
     * 获取管道ID
     * 用于索引时数据处理
     * 
     * @return 管道ID
     */
    public String getPipeline() {
        return null;
    }
    
    /**
     * 获取刷新策略
     * 
     * @return 刷新策略
     */
    public String getRefresh() {
        return null;
    }
    
    /**
     * 获取超时时间
     * 
     * @return 超时时间
     */
    public String getTimeout() {
        return null;
    }
    
    /**
     * 获取等待活跃分片数
     * 
     * @return 等待活跃分片数
     */
    public String getWaitForActiveShards() {
        return null;
    }
    
    /**
     * 是否只有在文档不存在时才创建
     * 
     * @return 是否只创建
     */
    public Boolean getOpTypeCreate() {
        return null;
    }
    
    /**
     * 获取版本类型
     * 
     * @return 版本类型
     */
    public String getVersionType() {
        return null;
    }
    
    /**
     * 获取序列号
     * 
     * @return 序列号
     */
    public Long getSeqNo() {
        return null;
    }
    
    /**
     * 获取主要术语
     *
     * @return 主要术语
     */
    public Long getPrimaryTerm() {
        return null;
    }

    // ==================== 向量搜索支持 ====================

    /**
     * 内容向量（用于语义搜索）
     * 子类可以选择性使用
     */
    @EsField(
        type = FieldType.DENSE_VECTOR,
        dims = 1024
    )
    @JsonProperty("content_vector")
    private float[] contentVector;

    /**
     * 标题向量（用于语义搜索）
     * 子类可以选择性使用
     */
    @EsField(
        type = FieldType.DENSE_VECTOR,
        dims = 1024
    )
    @JsonProperty("title_vector")
    private float[] titleVector;

    /**
     * 获取内容向量
     */
    public float[] getContentVector() {
        return contentVector;
    }

    /**
     * 设置内容向量
     */
    public void setContentVector(float[] contentVector) {
        this.contentVector = contentVector;
    }

    /**
     * 获取标题向量
     */
    public float[] getTitleVector() {
        return titleVector;
    }

    /**
     * 设置标题向量
     */
    public void setTitleVector(float[] titleVector) {
        this.titleVector = titleVector;
    }

    /**
     * 检查是否有内容向量
     */
    public boolean hasContentVector() {
        return contentVector != null && contentVector.length > 0;
    }

    /**
     * 检查是否有标题向量
     */
    public boolean hasTitleVector() {
        return titleVector != null && titleVector.length > 0;
    }
}