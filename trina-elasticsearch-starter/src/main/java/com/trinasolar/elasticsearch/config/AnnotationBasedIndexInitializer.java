package com.trinasolar.elasticsearch.config;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.indices.CreateIndexRequest;
import co.elastic.clients.elasticsearch.indices.CreateIndexResponse;
import co.elastic.clients.elasticsearch.indices.ExistsRequest;
// import co.elastic.clients.elasticsearch._types.mapping.DenseVectorSimilarity;
import co.elastic.clients.elasticsearch._types.mapping.Property;
import co.elastic.clients.elasticsearch._types.mapping.TypeMapping;
import com.trinasolar.elasticsearch.base.annotation.*;
import com.trinasolar.elasticsearch.vector.annotation.VectorField;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 基于注解的Elasticsearch索引自动初始化器
 * 扫描所有带有@EsIndex注解的实体类，自动创建对应的索引
 * 
 * <AUTHOR>
 * @since 2025-06-25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AnnotationBasedIndexInitializer implements CommandLineRunner {
    
    private final ElasticsearchClient elasticsearchClient;
    
    /**
     * 需要扫描的包路径
     */
    private static final String[] SCAN_PACKAGES = {
        "com.trinasolar.elasticsearch.example",
        "com.trinasolar.elasticsearch",
        "com.trinasolar"
    };
    
    @Override
    public void run(String... args) throws Exception {
        log.info("🔍 开始基于注解自动初始化Elasticsearch索引...");
        
        // 扫描所有带有@EsIndex注解的实体类
        Set<Class<?>> entityClasses = scanEsIndexEntities();
        
        log.info("📋 发现 {} 个带有@EsIndex注解的实体类", entityClasses.size());
        
        // 为每个实体类创建索引
        for (Class<?> entityClass : entityClasses) {
            try {
                createIndexForEntity(entityClass);
            } catch (Exception e) {
                log.error("❌ 为实体类 {} 创建索引失败", entityClass.getSimpleName(), e);
            }
        }
        
        log.info("✅ 基于注解的Elasticsearch索引初始化完成");
    }
    
    /**
     * 扫描所有带有@EsIndex注解的实体类
     */
    private Set<Class<?>> scanEsIndexEntities() {
        ClassPathScanningCandidateComponentProvider scanner = 
            new ClassPathScanningCandidateComponentProvider(false);
        
        // 添加@EsIndex注解过滤器
        scanner.addIncludeFilter(new AnnotationTypeFilter(EsIndex.class));
        
        Set<Class<?>> entityClasses = new java.util.HashSet<>();
        
        // 扫描指定包路径
        for (String packageName : SCAN_PACKAGES) {
            Set<BeanDefinition> candidates = scanner.findCandidateComponents(packageName);
            
            for (BeanDefinition candidate : candidates) {
                try {
                    Class<?> clazz = Class.forName(candidate.getBeanClassName());
                    entityClasses.add(clazz);
                    log.debug("🔍 发现实体类: {}", clazz.getSimpleName());
                } catch (ClassNotFoundException e) {
                    log.warn("⚠️ 无法加载类: {}", candidate.getBeanClassName());
                }
            }
        }
        
        return entityClasses;
    }
    
    /**
     * 为指定实体类创建索引
     */
    private void createIndexForEntity(Class<?> entityClass) throws IOException {
        EsIndex esIndex = entityClass.getAnnotation(EsIndex.class);
        if (esIndex == null) {
            log.warn("⚠️ 实体类 {} 没有@EsIndex注解", entityClass.getSimpleName());
            return;
        }
        
        final String indexName = StringUtils.hasText(esIndex.name()) ?
            esIndex.name() : entityClass.getSimpleName().toLowerCase();
        
        // 检查索引是否已存在
        boolean exists = elasticsearchClient.indices().exists(
            ExistsRequest.of(e -> e.index(indexName))
        ).value();
        
        if (exists) {
            log.info("📋 索引已存在: {} (实体: {})", indexName, entityClass.getSimpleName());
            return;
        }
        
        log.info("🔨 创建索引: {} (实体: {})", indexName, entityClass.getSimpleName());
        
        // 构建索引映射
        TypeMapping.Builder mappingBuilder = new TypeMapping.Builder();
        Map<String, Property> properties = buildPropertiesFromEntity(entityClass);
        
        for (Map.Entry<String, Property> entry : properties.entrySet()) {
            mappingBuilder.properties(entry.getKey(), entry.getValue());
        }
        
        // 创建索引
        CreateIndexRequest.Builder requestBuilder = new CreateIndexRequest.Builder()
            .index(indexName)
            .mappings(mappingBuilder.build());
        
        // 设置索引配置
        if (esIndex.shards() > 0 || esIndex.replicas() >= 0) {
            Map<String, Object> settings = new HashMap<>();
            if (esIndex.shards() > 0) {
                settings.put("number_of_shards", esIndex.shards());
            }
            if (esIndex.replicas() >= 0) {
                settings.put("number_of_replicas", esIndex.replicas());
            }
            if (StringUtils.hasText(esIndex.refreshInterval())) {
                settings.put("refresh_interval", esIndex.refreshInterval());
            }
            if (esIndex.maxResultWindow() > 0) {
                settings.put("max_result_window", esIndex.maxResultWindow());
            }
            
            // 注意：这里需要根据实际的Elasticsearch客户端API来设置settings
            // 当前版本可能需要不同的设置方式
        }
        
        CreateIndexResponse response = elasticsearchClient.indices().create(requestBuilder.build());
        
        if (response.acknowledged()) {
            log.info("✅ 索引创建成功: {} (实体: {})", indexName, entityClass.getSimpleName());
        } else {
            log.error("❌ 索引创建失败: {} (实体: {})", indexName, entityClass.getSimpleName());
        }
    }
    
    /**
     * 从实体类构建Elasticsearch属性映射
     */
    private Map<String, Property> buildPropertiesFromEntity(Class<?> entityClass) {
        Map<String, Property> properties = new HashMap<>();
        
        // 遍历所有字段（包括父类字段）
        Class<?> currentClass = entityClass;
        while (currentClass != null && currentClass != Object.class) {
            Field[] fields = currentClass.getDeclaredFields();
            
            for (Field field : fields) {
                String fieldName = field.getName();
                
                // 跳过静态字段和合成字段
                if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) || 
                    field.isSynthetic()) {
                    continue;
                }
                
                Property property = buildPropertyFromField(field);
                if (property != null) {
                    properties.put(fieldName, property);
                    log.debug("🔧 添加字段映射: {} -> {}", fieldName, property);
                }
            }
            
            currentClass = currentClass.getSuperclass();
        }
        
        return properties;
    }
    
    /**
     * 从字段构建Elasticsearch属性
     */
    private Property buildPropertyFromField(Field field) {
        // 检查@EsField注解
        EsField esField = field.getAnnotation(EsField.class);
        if (esField != null) {
            return buildPropertyFromEsField(esField);
        }
        
        // 检查@VectorField注解
        VectorField vectorField = field.getAnnotation(VectorField.class);
        if (vectorField != null) {
            return buildPropertyFromVectorField(vectorField);
        }
        
        // 检查@EsId注解
        EsId esId = field.getAnnotation(EsId.class);
        if (esId != null) {
            return Property.of(p -> p.keyword(k -> k));
        }
        
        // 根据字段类型推断映射
        return buildPropertyFromFieldType(field);
    }
    
    /**
     * 从@EsField注解构建属性
     */
    private Property buildPropertyFromEsField(EsField esField) {
        switch (esField.type()) {
            case TEXT:
                return Property.of(p -> p.text(t -> {
                    // 检查分析器是否可用，如果不可用则使用默认分析器
                    if (StringUtils.hasText(esField.analyzer()) && isAnalyzerAvailable(esField.analyzer())) {
                        t.analyzer(esField.analyzer());
                    } else if (StringUtils.hasText(esField.analyzer())) {
                        log.warn("分析器 {} 不可用，使用默认分析器", esField.analyzer());
                        t.analyzer("standard");
                    }
                    if (StringUtils.hasText(esField.searchAnalyzer()) && isAnalyzerAvailable(esField.searchAnalyzer())) {
                        t.searchAnalyzer(esField.searchAnalyzer());
                    } else if (StringUtils.hasText(esField.searchAnalyzer())) {
                        log.warn("搜索分析器 {} 不可用，使用默认分析器", esField.searchAnalyzer());
                        t.searchAnalyzer("standard");
                    }
                    return t;
                }));
            case KEYWORD:
                return Property.of(p -> p.keyword(k -> k));
            case LONG:
                return Property.of(p -> p.long_(l -> l));
            case INTEGER:
                return Property.of(p -> p.integer(i -> i));
            case DOUBLE:
                return Property.of(p -> p.double_(d -> d));
            case FLOAT:
                return Property.of(p -> p.float_(f -> f));
            case DATE:
                return Property.of(p -> p.date(d -> d));
            case BOOLEAN:
                return Property.of(p -> p.boolean_(b -> b));
            default:
                return Property.of(p -> p.keyword(k -> k));
        }
    }
    
    /**
     * 从@VectorField注解构建向量属性
     */
    private Property buildPropertyFromVectorField(VectorField vectorField) {
        // 暂时简化，只设置维度，不设置相似度
        // 在Elasticsearch 8.11.0中，DenseVectorSimilarity的API可能有变化
        return Property.of(p -> p.denseVector(v -> v
            .dims(vectorField.dims())
            // .similarity(similarity) // 暂时注释掉
        ));
    }
    
    /**
     * 根据字段类型推断属性映射
     */
    private Property buildPropertyFromFieldType(Field field) {
        Class<?> fieldType = field.getType();
        
        if (fieldType == String.class) {
            return Property.of(p -> p.keyword(k -> k));
        } else if (fieldType == Integer.class || fieldType == int.class) {
            return Property.of(p -> p.integer(i -> i));
        } else if (fieldType == Long.class || fieldType == long.class) {
            return Property.of(p -> p.long_(l -> l));
        } else if (fieldType == Double.class || fieldType == double.class) {
            return Property.of(p -> p.double_(d -> d));
        } else if (fieldType == Float.class || fieldType == float.class) {
            return Property.of(p -> p.float_(f -> f));
        } else if (fieldType == Boolean.class || fieldType == boolean.class) {
            return Property.of(p -> p.boolean_(b -> b));
        } else if (fieldType == java.time.LocalDateTime.class || 
                   fieldType == java.util.Date.class) {
            return Property.of(p -> p.date(d -> d));
        } else if (java.util.List.class.isAssignableFrom(fieldType)) {
            // 对于List类型，默认使用keyword
            return Property.of(p -> p.keyword(k -> k));
        } else {
            // 默认使用keyword
            return Property.of(p -> p.keyword(k -> k));
        }
    }

    /**
     * 检查分析器是否可用
     * 简单实现：检查常见的不可用分析器
     */
    private boolean isAnalyzerAvailable(String analyzer) {
        // IK分词器相关的分析器
        if ("ik_smart".equals(analyzer) || "ik_max_word".equals(analyzer)) {
            // 可以通过调用Elasticsearch API检查，这里简化处理
            return false; // 假设IK分词器未安装
        }

        // 其他常见的内置分析器
        return "standard".equals(analyzer) ||
               "simple".equals(analyzer) ||
               "keyword".equals(analyzer) ||
               "whitespace".equals(analyzer) ||
               "stop".equals(analyzer) ||
               "pattern".equals(analyzer);
    }
}
