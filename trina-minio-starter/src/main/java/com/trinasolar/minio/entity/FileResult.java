package com.trinasolar.minio.entity;

import io.minio.StatObjectResponse;
import lombok.Data;

/**
 * 文件拉平返回对象
 */
@Data
public class FileResult {
    /**
     * 文件id
     */
    Long id;
    /**
     * 路径
     */
    String path;
    /**
     * 存储系统的唯一键，方便做印射关系
     */
    String uniqueKey;
    /**
     * 名称
     */
    String name;
    /**
     * 访问路径
     */
    String link;

    public FileResult(){

    }

    public FileResult(String bucketName,String objetName,String uniqueKey,String link){
        this.path=bucketName;
        this.uniqueKey=uniqueKey;
        this.name=objetName;
        this.link=link;
    }
}
