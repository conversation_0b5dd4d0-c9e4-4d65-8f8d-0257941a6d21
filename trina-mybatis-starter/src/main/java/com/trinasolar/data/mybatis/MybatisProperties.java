package com.trinasolar.data.mybatis;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.ArrayList;
import java.util.List;

/**
 * Mybatis 配置
 *
 * <AUTHOR>
 * @date 2021/6/3
 */
@Data
@RefreshScope
@ConfigurationProperties("plus.mybatis")
public class MybatisProperties {

    /**
     * 是否打印可执行 sql
     */
    private boolean showSql = true;

    /**
     * 打印日志跳过表
     */
    private List<String> skipTable = new ArrayList<>();

    /**
     * 维护租户列名称
     */
    private String tenantColumn = "tenant_id";
    /**
     * 多租户的数据表集合
     */
    private List<String> tables = new ArrayList<>();

}
