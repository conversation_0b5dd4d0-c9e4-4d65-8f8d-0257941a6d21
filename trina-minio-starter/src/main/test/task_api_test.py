#!/usr/bin/env python3
import requests
import json
from datetime import datetime
import sys
import logging

# 基础配置
BASE_URL = "http://localhost:8080/task"
HEADERS = {
    "Content-Type": "application/json"
}
LOG_FILE = "task_api_test_python.log"

# 设置日志记录器
logging.basicConfig(filename=LOG_FILE, level=logging.INFO, 
                    format='%(asctime)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
logger = logging.getLogger(__name__)

# 同时输出到控制台的处理器
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
formatter = logging.Formatter('%(message)s')
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

# 清空日志文件 (如果需要每次运行时清空)
with open(LOG_FILE, 'w') as f:
    pass

# 存储任务ID
task_ids = []

def log_and_print(message):
    """记录日志并打印到控制台"""
    logger.info(message)

def print_separator():
    """打印分隔线"""
    log_and_print("\n" + "-" * 40)

def print_request(url, method, body=None):
    """打印请求信息"""
    log_and_print("\n请求信息:")
    log_and_print(f"URL: {url}")
    log_and_print(f"Method: {method}")
    if body:
        log_and_print(f"Body: {json.dumps(body, ensure_ascii=False, indent=4)}")

def print_response(response_data):
    """打印响应信息"""
    log_and_print("\n响应结果:")
    if response_data is None:
        log_and_print("错误: 服务器未返回任何内容")
    else:
        if isinstance(response_data, (dict, list)):
            log_and_print(json.dumps(response_data, ensure_ascii=False, indent=4))
        else:
            log_and_print(str(response_data))

def execute_request(url, method, body=None):
    """执行请求并处理响应"""
    try:
        if method == "GET":
            response = requests.get(url, headers=HEADERS)
        elif method == "POST":
            response = requests.post(url, headers=HEADERS, json=body)
        elif method == "PUT":
            response = requests.put(url, headers=HEADERS, json=body)
        elif method == "PATCH":
            response = requests.patch(url, headers=HEADERS, json=body)
        elif method == "DELETE":
            response = requests.delete(url, headers=HEADERS)
        
        log_and_print(f"\nHTTP状态码: {response.status_code}")
        if response.status_code >= 200 and response.status_code < 300:
            try:
                return response.json()
            except json.JSONDecodeError:
                log_and_print("响应不是有效的JSON格式")
                return response.text
        else:
            try:
                print_response(response.json())
            except json.JSONDecodeError:
                print_response(response.text)
            return None
    except requests.exceptions.RequestException as e:
        log_and_print(f"请求执行失败: {str(e)}")
        return None

def extract_task_id(response):
    """从响应中提取任务ID"""
    if response and isinstance(response, dict) and "data" in response and isinstance(response["data"], dict) and "id" in response["data"]:
        task_id = response["data"]["id"]
        task_ids.append(task_id)
        return task_id
    return None

def cleanup():
    """清理测试数据"""
    log_and_print("\n清理测试数据...")
    for task_id in list(task_ids):
        if task_id:
            delete_url = f"{BASE_URL}/{task_id}"
            log_and_print(f"删除任务: {delete_url}")
            execute_request(delete_url, "DELETE")

def main():
    log_and_print("开始测试任务管理模块API...")
    
    try:
        # 1. 创建单个任务 - 简单模式
        print_separator()
        log_and_print("1. 创建单个任务 - 简单模式")
        request_body = {
            "taskName": "测试任务",
            "assignee": "张三",
            "plannedStartTime": "2024-03-21 10:00:00",
            "plannedEndTime": "2024-03-21 18:00:00",
            "status": "PENDING",
            "type": "DEVELOPMENT"
        }
        print_request(BASE_URL, "POST", request_body)
        response = execute_request(BASE_URL, "POST", request_body)
        task_id = extract_task_id(response)
        
        if not task_id:
            log_and_print("创建任务失败，无法获取任务ID")
            return

        # 2. 批量创建任务
        print_separator()
        log_and_print("2. 批量创建任务")
        request_body = [
            {
                "taskName": "测试任务2",
                "assignee": "李四",
                "plannedStartTime": "2024-03-21 11:00:00",
                "plannedEndTime": "2024-03-22 11:00:00",
                "status": "PENDING",
                "type": "DEVELOPMENT"
            },
            {
                "taskName": "测试任务3",
                "assignee": "王五",
                "plannedStartTime": "2024-03-21 12:00:00",
                "plannedEndTime": "2024-03-22 12:00:00",
                "status": "PENDING",
                "type": "DEVELOPMENT"
            }
        ]
        print_request(f"{BASE_URL}/batch", "POST", request_body)
        batch_create_response = execute_request(f"{BASE_URL}/batch", "POST", request_body)
        if batch_create_response and isinstance(batch_create_response, dict) and batch_create_response.get('code') == 0 and isinstance(batch_create_response.get('data'), list):
            for item in batch_create_response['data']:
                if isinstance(item, dict) and 'id' in item:
                    task_ids.append(item['id'])
                    log_and_print(f"批量创建任务成功, ID: {item['id']}")
        elif batch_create_response:
            log_and_print(f"批量创建任务响应格式不符合预期或操作失败: {batch_create_response}")
        else:
            log_and_print("批量创建任务未收到有效响应")

        # 3. 简单模式查询 - 等值查询
        print_separator()
        log_and_print("3. 简单模式查询 - 等值查询")
        request_body = {
            "entity": {
                "status": "PENDING",
                "type": "DEVELOPMENT"
            }
        }
        print_request(f"{BASE_URL}/query/list", "POST", request_body)
        execute_request(f"{BASE_URL}/query/list", "POST", request_body)

        # 4. 高级模式查询 - 条件组合
        print_separator()
        log_and_print("4. 高级模式查询 - 条件组合")
        request_body = {
            "mode": "ADVANCED",
            "conditionsGroups": [
                {
                    "joinType": "AND",
                    "conditions": [
                        {
                            "field": "status",
                            "type": "IN",
                            "value": ["PENDING", "IN_PROGRESS"]
                        },
                        {
                            "field": "taskName",
                            "type": "LIKE",
                            "value": "测试"
                        }
                    ]
                }
            ],
            "sorts": [
                {
                    "field": "createTime",
                    "direction": "DESC"
                }
            ]
        }
        print_request(f"{BASE_URL}/query/list", "POST", request_body)
        execute_request(f"{BASE_URL}/query/list", "POST", request_body)

        # 5. 高级模式查询 - 复杂条件组
        print_separator()
        log_and_print("5. 高级模式查询 - 复杂条件组")
        request_body = {
            "mode": "ADVANCED",
            "conditionsGroups": [
                {
                    "joinType": "AND",
                    "conditions": [
                        {
                            "field": "status",
                            "type": "EQUAL",
                            "value": "PENDING"
                        }
                    ]
                },
                {
                    "joinType": "OR",
                    "conditions": [
                        {
                            "field": "assignee",
                            "type": "EQUAL",
                            "value": "张三"
                        },
                        {
                            "field": "assignee",
                            "type": "EQUAL",
                            "value": "李四"
                        }
                    ]
                }
            ],
            "joinType": "AND"
        }
        print_request(f"{BASE_URL}/query/list", "POST", request_body)
        execute_request(f"{BASE_URL}/query/list", "POST", request_body)

        # 6. 分页查询
        print_separator()
        log_and_print("6. 分页查询")
        request_body = {
            "mode": "ADVANCED",
            "pageNo": 1,
            "pageSize": 10,
            "conditionsGroups": [
                {
                    "joinType": "AND",
                    "conditions": [
                        {
                            "field": "status",
                            "type": "NOT_EQUAL",
                            "value": "DELETED"
                        }
                    ]
                }
            ],
            "sorts": [
                {
                    "field": "createTime",
                    "direction": "DESC"
                }
            ]
        }
        print_request(f"{BASE_URL}/query/page", "POST", request_body)
        execute_request(f"{BASE_URL}/query/page", "POST", request_body)

        # 7. 日期范围查询
        print_separator()
        log_and_print("7. 日期范围查询")
        request_body = {
            "mode": "ADVANCED",
            "conditionsGroups": [
                {
                    "joinType": "AND",
                    "conditions": [
                        {
                            "field": "plannedStartTime",
                            "type": "BETWEEN",
                            "value": ["2024-03-21 00:00:00", "2024-03-22 23:59:59"]
                        }
                    ]
                }
            ],
            "sorts": [
                {
                    "field": "plannedStartTime",
                    "direction": "ASC"
                }
            ]
        }
        print_request(f"{BASE_URL}/query/list", "POST", request_body)
        execute_request(f"{BASE_URL}/query/list", "POST", request_body)

        # 8. 计数查询
        print_separator()
        log_and_print("8. 计数查询")
        request_body = {
            "mode": "ADVANCED",
            "conditionsGroups": [
                {
                    "joinType": "AND",
                    "conditions": [
                        {
                            "field": "status",
                            "type": "EQUAL",
                            "value": "PENDING"
                        }
                    ]
                }
            ]
        }
        print_request(f"{BASE_URL}/query/count", "POST", request_body)
        execute_request(f"{BASE_URL}/query/count", "POST", request_body)

        # 9. 更新任务
        print_separator()
        log_and_print("9. 更新任务")
        request_body = {
            "id": task_id,
            "taskName": "更新后的测试任务",
            "assignee": "李四",
            "plannedStartTime": "2024-03-22 10:00:00",
            "plannedEndTime": "2024-03-22 18:00:00",
            "status": "IN_PROGRESS",
            "type": "DEVELOPMENT"
        }
        print_request(f"{BASE_URL}/{task_id}", "PUT", request_body)
        execute_request(f"{BASE_URL}/{task_id}", "PUT", request_body)

        # 10. 部分更新任务
        print_separator()
        log_and_print("10. 部分更新任务")
        request_body = {
            "id": task_id,
            "status": "COMPLETED"
        }
        print_request(f"{BASE_URL}/{task_id}", "PATCH", request_body)
        execute_request(f"{BASE_URL}/{task_id}", "PATCH", request_body)

        # 11. 批量更新任务
        print_separator()
        log_and_print("11. 批量更新任务")
        request_body = [
            {
                "id": task_id,
                "status": "COMPLETED",
                "taskName": "批量更新任务1"
            }
        ]
        print_request(f"{BASE_URL}/batch", "PUT", request_body)
        execute_request(f"{BASE_URL}/batch", "PUT", request_body)

        # 12. 删除单个任务
        print_separator()
        log_and_print("12. 删除单个任务")
        print_request(f"{BASE_URL}/{task_id}", "DELETE")
        execute_request(f"{BASE_URL}/{task_id}", "DELETE")
        if task_id in task_ids:
            task_ids.remove(task_id)

        # 13. 批量删除任务
        print_separator()
        log_and_print("13. 批量删除任务")
        
        ids_to_delete = [tid for tid in task_ids if tid != task_id]

        if ids_to_delete:
            request_body = ids_to_delete
            print_request(f"{BASE_URL}/batch", "DELETE", request_body)
            execute_request(f"{BASE_URL}/batch", "DELETE", request_body)
            for tid_deleted in ids_to_delete:
                if tid_deleted in task_ids:
                    task_ids.remove(tid_deleted)
        else:
            log_and_print("没有其他任务可以删除，跳过批量删除测试")

        log_and_print("\n测试完成！")

    except Exception as e:
        log_and_print(f"测试过程中发生错误: {str(e)}")
    finally:
        cleanup()

if __name__ == "__main__":
    main() 