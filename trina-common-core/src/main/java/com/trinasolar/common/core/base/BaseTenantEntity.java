package com.trinasolar.common.core.base;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 租户基础实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseTenantEntity extends BaseSysEntity {

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    @Schema(description = "租户ID")
    private Long tenantId;

}
