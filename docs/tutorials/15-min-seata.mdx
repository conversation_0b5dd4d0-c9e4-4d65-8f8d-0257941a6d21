---
title: "15分钟了解Seata分布式事务"
description: "快速掌握Seata的基本原理、核心模式及应用场景"
date: "2024-05-28"
tags: ["Seata", "分布式事务", "微服务", "Java", "Spring Cloud"]
---

# 15分钟了解Seata分布式事务

## 🔍 技术简介 (3分钟)

### 什么是Seata？

Seata（Simple Extensible Autonomous Transaction Architecture）是一款开源的分布式事务解决方案，致力于提供高性能和简单易用的分布式事务服务。它最初由阿里巴巴开源，现已成为Apache孵化项目，拥有超过23,000颗Star。

在微服务架构下，一个业务操作通常需要调用多个服务，涉及多个数据源。传统的本地事务已无法满足分布式场景下的数据一致性需求，而Seata提供了一站式的分布式事务解决方案，支持多种事务模式，能够满足不同业务场景的需求。

### 基本原理

Seata采用了分布式事务协调的模型，将一个分布式事务拆分为多个分支事务，由一个全局事务协调器统一管理。它定义了三个核心角色：

1. **TC (Transaction Coordinator)** - 事务协调者：维护全局和分支事务的状态，驱动全局事务的提交或回滚。
2. **TM (Transaction Manager)** - 事务管理器：定义全局事务的范围，开始全局事务、提交或回滚全局事务。
3. **RM (Resource Manager)** - 资源管理器：管理分支事务处理的资源，向TC注册分支事务，上报分支事务的状态，并驱动分支事务的提交或回滚。

Seata的事务处理流程如下：

1. TM向TC申请开启一个全局事务，TC返回一个全局唯一的XID
2. XID在微服务调用链路中传播
3. RM向TC注册分支事务，并将其纳入XID对应的全局事务范畴
4. TM向TC发起针对XID的全局提交或回滚
5. TC调度XID下管辖的全部分支事务完成提交或回滚

### 技术特点

- **多种事务模式**：支持AT、TCC、SAGA和XA四种事务模式，适应不同业务场景
- **高可用**：支持TC集群部署，保证服务的高可用性
- **高性能**：精心设计的协议和算法，确保分布式事务的高性能
- **易扩展**：模块化设计，易于扩展功能
- **生态丰富**：支持主流微服务框架，如Spring Cloud、Dubbo等

## 💡 核心功能 (4分钟)

### 1. AT模式

AT（Automatic Transaction）模式是Seata的默认模式，也是最常用的模式。它基于两阶段提交协议，对业务无侵入，适用于绝大部分CRUD场景。

**工作原理**：

- **一阶段**：业务SQL执行，自动记录undo_log
  1. 拦截业务SQL
  2. 解析SQL，提取表元数据
  3. 查询前镜像（before image）
  4. 执行业务SQL
  5. 查询后镜像（after image）
  6. 生成行锁，插入undo_log
  7. 注册分支事务

- **二阶段**：根据TC指令完成提交或回滚
  - 提交：删除undo_log记录
  - 回滚：根据undo_log记录恢复数据

```java
@GlobalTransactional
public void createOrder(String userId, String commodityCode, int orderCount) {
    // 创建订单
    orderService.create(userId, commodityCode, orderCount);
    // 扣减库存
    stockService.deduct(commodityCode, orderCount);
    // 扣减账户余额
    accountService.debit(userId, orderPrice);
}
```

### 2. TCC模式

TCC（Try-Confirm-Cancel）模式是一种侵入性较强但灵活性更高的分布式事务解决方案，需要业务自行实现Try、Confirm和Cancel三个操作。

**工作原理**：

- **Try**：资源的检查和预留
- **Confirm**：对预留资源的确认
- **Cancel**：对预留资源的释放

```java
@TwoPhaseBusinessAction(name = "deductAccountTCC", commitMethod = "confirm", rollbackMethod = "cancel")
public boolean try(String userId, int money);

public boolean confirm(String userId, int money);

public boolean cancel(String userId, int money);
```

### 3. SAGA模式

SAGA模式是一种长事务解决方案，适用于业务流程长、业务流程多的场景。它通过编排一系列的本地事务及其补偿操作来实现分布式事务。

**工作原理**：

- 正向操作链：T1 -> T2 -> ... -> Tn
- 补偿操作链：Cn -> ... -> C2 -> C1

Seata提供了基于状态机的SAGA模式实现，支持图形化配置。

### 4. XA模式

XA模式是基于数据库XA协议实现的分布式事务模式，完全遵循ACID特性，对应用几乎无侵入。

**工作原理**：

- **一阶段**：执行XA Start、执行业务SQL、执行XA End、执行XA Prepare
- **二阶段**：根据TC指令执行XA Commit或XA Rollback

```java
@GlobalTransactional
public void transfer(String fromAccount, String toAccount, int money) {
    accountService.debit(fromAccount, money);
    accountService.credit(toAccount, money);
}
```

### 5. 全局锁

为了保证分布式事务的隔离性，Seata实现了全局锁机制：

- 在一阶段本地事务提交前，需要先获取该记录的全局锁
- 全局锁保证了全局事务间的写隔离
- 在AT模式下，全局锁与本地锁协同工作，避免脏写

## 📊 应用场景 (3分钟)

### 1. 电商订单处理

**场景**：用户下单时需要创建订单、扣减库存、扣减账户余额。

**挑战**：这三个操作分别在不同的微服务和数据库中，需要保证一致性。

**解决方案**：使用Seata的AT模式，对原有代码几乎无侵入地实现分布式事务。

```java
@GlobalTransactional
public void createOrder(OrderDTO order) {
    // 创建订单
    orderService.create(order);
    // 扣减库存
    inventoryService.deduct(order.getProductId(), order.getCount());
    // 扣减余额
    accountService.deduct(order.getUserId(), order.getAmount());
}
```

**优势**：
- 保证三个操作要么全部成功，要么全部失败
- 对原有业务代码影响小，只需添加注解
- 性能影响小，适合高并发场景

### 2. 金融账户转账

**场景**：银行账户A向账户B转账，涉及A扣款和B收款两个操作。

**挑战**：两个操作可能在不同的服务或数据库中，需要保证转账的原子性。

**解决方案**：使用Seata的TCC模式，实现精确的资源控制。

**优势**：
- 严格的隔离性，避免资金损失
- 灵活的资源控制，满足复杂业务规则
- 不依赖于底层数据库的事务支持

### 3. 复杂业务流程

**场景**：保险理赔流程，包含多个审批步骤和系统交互。

**挑战**：流程长、环节多，可能持续数小时或数天，传统事务难以应对。

**解决方案**：使用Seata的SAGA模式，通过状态机定义正向和补偿流程。

**优势**：
- 支持长事务场景
- 可视化流程定义，易于理解和维护
- 补偿机制灵活，满足复杂业务需求

## 🚀 快速入门 (5分钟)

### 环境准备

1. **JDK**: 1.8+
2. **Maven**: 3.2.x+
3. **数据库**: MySQL 5.7+
4. **Seata Server**: 最新版本

### 部署Seata Server

**步骤1: 下载Seata Server**

```bash
wget https://github.com/apache/incubator-seata/releases/download/v1.6.1/seata-server-1.6.1.tar.gz
tar -xzvf seata-server-1.6.1.tar.gz
cd seata-server-1.6.1
```

**步骤2: 配置Seata Server**

修改`conf/application.yml`配置文件，设置存储模式和注册中心。

```yaml
seata:
  store:
    mode: db  # 存储模式，支持file、db、redis
    db:
      datasource: druid
      db-type: mysql
      driver-class-name: com.mysql.jdbc.Driver
      url: *************************************************
      user: username
      password: password
```

**步骤3: 初始化数据库**

执行`script/server/db/mysql.sql`脚本，初始化Seata Server所需的数据表。

**步骤4: 启动Seata Server**

```bash
sh bin/seata-server.sh -p 8091 -h 127.0.0.1
```

### 客户端集成

**步骤1: 添加依赖**

```xml
<!-- Spring Boot项目 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter</artifactId>
</dependency>

<!-- Seata依赖 -->
<dependency>
    <groupId>io.seata</groupId>
    <artifactId>seata-spring-boot-starter</artifactId>
    <version>1.6.1</version>
</dependency>
```

**步骤2: 配置应用**

在`application.yml`中添加Seata配置：

```yaml
seata:
  enabled: true
  application-id: ${spring.application.name}
  tx-service-group: my_test_tx_group
  service:
    vgroup-mapping:
      my_test_tx_group: default
  registry:
    type: nacos
    nacos:
      server-addr: 127.0.0.1:8848
```

**步骤3: 数据源代理**

为AT模式，需要配置数据源代理：

```java
@Configuration
public class DataSourceConfiguration {
    @Bean
    @ConfigurationProperties(prefix = "spring.datasource")
    public DruidDataSource druidDataSource() {
        return new DruidDataSource();
    }
    
    @Primary
    @Bean
    public DataSourceProxy dataSource(DruidDataSource druidDataSource) {
        return new DataSourceProxy(druidDataSource);
    }
}
```

**步骤4: 创建undo_log表**

在业务数据库中执行以下SQL创建undo_log表：

```sql
CREATE TABLE `undo_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `branch_id` bigint(20) NOT NULL,
  `xid` varchar(100) NOT NULL,
  `context` varchar(128) NOT NULL,
  `rollback_info` longblob NOT NULL,
  `log_status` int(11) NOT NULL,
  `log_created` datetime NOT NULL,
  `log_modified` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_undo_log` (`xid`,`branch_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;
```

**步骤5: 使用全局事务注解**

在需要开启分布式事务的方法上添加`@GlobalTransactional`注解：

```java
@Service
public class OrderServiceImpl implements OrderService {
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private StockClient stockClient;
    @Autowired
    private AccountClient accountClient;
    
    @GlobalTransactional(name = "create-order", timeoutMills = 60000)
    @Override
    public void createOrder(String userId, String commodityCode, int count) {
        // 创建订单
        Order order = new Order();
        order.setUserId(userId);
        order.setCommodityCode(commodityCode);
        order.setCount(count);
        order.setMoney(count * 100);
        orderMapper.insert(order);
        
        // 远程调用库存服务，扣减库存
        stockClient.deduct(commodityCode, count);
        
        // 远程调用账户服务，扣减余额
        accountClient.debit(userId, order.getMoney());
    }
}
```

### 验证分布式事务

1. 启动Order、Stock、Account三个微服务
2. 调用Order服务的createOrder接口
3. 模拟异常情况（例如Account服务抛出异常）
4. 观察三个服务的数据库，验证事务是否回滚

## 总结

Seata作为一款成熟的分布式事务解决方案，提供了AT、TCC、SAGA和XA四种事务模式，能够满足不同业务场景的需求。通过简单的配置和少量代码修改，即可为微服务架构中的分布式事务问题提供可靠解决方案。

Seata的AT模式以其对业务无侵入的特性，成为最受欢迎的分布式事务模式之一，而TCC、SAGA和XA模式则为特定场景提供了更专业的解决方案。随着微服务架构的普及，Seata已成为处理分布式事务的标准工具之一。

## 相关资源

- 官方文档：[Seata Documentation](https://seata.apache.org/zh-cn/docs/overview/what-is-seata)
- GitHub仓库：[Apache Seata](https://github.com/apache/incubator-seata)
- 示例项目：[Seata Samples](https://github.com/apache/incubator-seata-samples)
- 社区：[Seata Community](https://seata.apache.org/community) 