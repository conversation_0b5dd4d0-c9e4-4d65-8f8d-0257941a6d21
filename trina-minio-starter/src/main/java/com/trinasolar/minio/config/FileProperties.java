package com.trinasolar.minio.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 文件存储 配置信息
 */
@Data
@ConfigurationProperties(prefix = "file.server")
public class FileProperties {
    /**
     * 文件存储类型：minio、nas
     */
    private String type;
    /**
     * 应用服务器存储被挂载路径
     */
    private String clientPath ="/app/file-data";
    /**
     * nas服务器地址+挂载路径
     */
    private String nasUrl="http://10.1.11.63:9999/file-data";

}
