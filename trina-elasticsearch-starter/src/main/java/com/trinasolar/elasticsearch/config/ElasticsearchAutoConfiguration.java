package com.trinasolar.elasticsearch.config;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.trinasolar.elasticsearch.properties.ElasticsearchProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.beans.factory.annotation.Qualifier;

/**
 * Elasticsearch自动配置类
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@AutoConfiguration
@ConditionalOnClass(ElasticsearchClient.class)
// 不再自动注册ElasticsearchProperties，改为手动创建
@ComponentScan(basePackages = "com.trinasolar.elasticsearch")
public class ElasticsearchAutoConfiguration {

    /**
     * 创建Elasticsearch专用的ObjectMapper
     */
    @Bean("elasticsearchObjectMapper")
    @ConditionalOnMissingBean(name = "elasticsearchObjectMapper")
    public ObjectMapper elasticsearchObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        // 注册Java 8时间模块
        objectMapper.registerModule(new JavaTimeModule());

        // 禁用将日期写为时间戳
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        // 自动发现并注册其他模块
        objectMapper.findAndRegisterModules();

        return objectMapper;
    }

    /**
     * 创建ElasticsearchProperties Bean
     * 使用配置解析器来支持原生配置优先
     */
    @Bean
    @ConditionalOnMissingBean
    public ElasticsearchProperties elasticsearchProperties(ElasticsearchConfigurationResolver resolver) {
        return resolver.resolveConfiguration();
    }

    /**
     * 创建Elasticsearch客户端
     */
    @Bean
    @ConditionalOnMissingBean
    public ElasticsearchClient elasticsearchClient(ElasticsearchProperties properties,
                                                   @Qualifier("elasticsearchObjectMapper") ObjectMapper objectMapper) {
        log.info("初始化Elasticsearch客户端，连接地址: {}", properties.getHosts());

        // 解析主机地址
        HttpHost[] hosts = properties.getHosts().stream()
            .map(host -> {
                String[] parts = host.split(":");
                String hostname = parts[0];
                int port = parts.length > 1 ? Integer.parseInt(parts[1]) : 9200;
                String scheme = properties.isSslEnabled() ? "https" : "http";
                return new HttpHost(hostname, port, scheme);
            })
            .toArray(HttpHost[]::new);

        // 创建RestClient构建器
        RestClientBuilder builder = RestClient.builder(hosts);

        // 配置认证
        if (properties.getUsername() != null && properties.getPassword() != null) {
            CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(
                AuthScope.ANY,
                new UsernamePasswordCredentials(properties.getUsername(), properties.getPassword())
            );

            builder.setHttpClientConfigCallback(httpClientBuilder ->
                httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider)
            );
        }

        // 配置连接超时
        builder.setRequestConfigCallback(requestConfigBuilder ->
            requestConfigBuilder
                .setConnectTimeout(properties.getConnectionTimeout())
                .setSocketTimeout(properties.getSocketTimeout())
        );

        // 创建RestClient
        RestClient restClient = builder.build();

        // 使用我们配置的ObjectMapper创建传输层
        ElasticsearchTransport transport = new RestClientTransport(
            restClient, new JacksonJsonpMapper(objectMapper)
        );

        // 创建Elasticsearch客户端
        ElasticsearchClient client = new ElasticsearchClient(transport);

        log.info("Elasticsearch客户端初始化完成");
        return client;
    }

    /**
     * 创建RestClient Bean（用于低级别操作）
     */
    @Bean
    @ConditionalOnMissingBean
    public RestClient restClient(ElasticsearchClient elasticsearchClient) {
        // 从ElasticsearchClient中提取RestClient
        // 这是一个简化的实现，实际可能需要更复杂的逻辑
        return RestClient.builder(new HttpHost("localhost", 9200, "http")).build();
    }

}
