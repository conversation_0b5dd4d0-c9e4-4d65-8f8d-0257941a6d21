package com.trinasolar.data.base;

/**
 * 查询模式枚举
 * 用于控制BaseQuery的查询行为
 */
public enum QueryMode {
    /**
     * 简单模式：仅支持等值条件和排序
     * 更高效，适合基础查询场景
     */
    SIMPLE,

    /**
     * 高级模式：支持复杂条件编排
     * - 条件分组
     * - AND/OR逻辑组合
     * - 各种比较操作符（大于、小于、等于等）
     * - BETWEEN、LIKE、IN等高级条件
     */
    ADVANCED,

    /**
     * 自动模式：根据实际使用的查询功能自动选择适合的模式
     * - 仅使用简单功能时，自动使用简单模式
     * - 使用了高级功能时，自动切换到高级模式
     */
    AUTO
} 