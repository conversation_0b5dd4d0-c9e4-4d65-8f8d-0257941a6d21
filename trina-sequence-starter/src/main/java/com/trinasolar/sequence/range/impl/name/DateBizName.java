package com.trinasolar.sequence.range.impl.name;

import com.trinasolar.sequence.range.BizName;
import cn.hutool.core.date.DateUtil;

/**
 * 根据时间重置bizname
 */
public class DateBizName implements BizName {
    private String bizName;

    public DateBizName() {
    }

    public DateBizName(String bizName) {
        this.bizName = bizName;
    }

    /**
     * 生成空间名称
     */
    @Override
    public String create() {
        return bizName + DateUtil.today();
    }
}
