package com.trinasolar.web;

import com.trinasolar.web.properties.CorsProperties;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;

import java.io.IOException;
import java.util.List;

/**
 * 全局跨域配置
 *
 * <AUTHOR>
 * @date 2024-06-11 10:48:58
 */
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(CorsProperties.class)
public class CorsFilter implements Filter, Ordered {

    private final CorsProperties corsProperties;

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
        // 如果 CORS 过滤器被禁用，直接通过
        if (!corsProperties.isEnabled()) {
            chain.doFilter(servletRequest, servletResponse);
        }
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        String origin = request.getHeader(HttpHeaders.ORIGIN);

        // 清除所有可能存在的CORS相关头部（新增Vary头处理）
        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "");
        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "");
        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "");
        response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "");
        response.setHeader(HttpHeaders.ACCESS_CONTROL_MAX_AGE, "");
        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS, "");
        response.setHeader(HttpHeaders.VARY, ""); // 新增Vary头处理

        if (isAllowedOrigin(origin)) {
            response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, origin);
            if (corsProperties.isAllowSubdomains()) {
                response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS, "true");
            }
        } else if (origin == null) {
            // 对于非浏览器请求（如 Postman、服务间调用），设置为通配符
            response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
        }else {
            // 对于不在白名单中的域名，根据策略处理
            // 这里选择拒绝跨域请求，可以根据需要调整
            // 如果需要更宽松的策略，可以设置为 "*"
            handleForbiddenOrigin(response);
            return;
        }

        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, corsProperties.getAllowedHeaders());
        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, corsProperties.getAllowedMethods());
        response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, corsProperties.getExposedHeaders());
        response.setHeader(HttpHeaders.ACCESS_CONTROL_MAX_AGE, String.valueOf(corsProperties.getMaxAge()));

        // 处理预检请求
        if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
            response.setStatus(HttpServletResponse.SC_OK);
        } else {
            chain.doFilter(request, response);
        }
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }

    /**
     * 检查是否为允许的域名
     *
     * @param origin 请求的 origin
     * @return 是否允许
     */
    private boolean isAllowedOrigin(String origin) {
        if (origin == null || origin.isEmpty()) {
            return false;
        }

        List<String> allowedOrigins = corsProperties.getAllowedOrigins();

        // 精确匹配
        if (allowedOrigins.contains(origin)) {
            return true;
        }

        // 支持本地开发环境
        if (isLocalDevelopment(origin)) {
            return true;
        }

        // 支持子域名匹配
        if (corsProperties.isAllowSubdomains()) {
            return isAllowedSubdomain(origin);
        }

        return false;
    }

    /**
     * 检查是否为本地开发环境
     */
    private boolean isLocalDevelopment(String origin) {
        return origin.startsWith("http://localhost:") ||
                origin.startsWith("http://127.0.0.1:") ||
                origin.startsWith("https://localhost:") ||
                origin.startsWith("https://127.0.0.1:");
    }

    /**
     * 检查是否为允许的子域名
     */
    private boolean isAllowedSubdomain(String origin) {
        for (String domain : corsProperties.getAllowedDomains()) {
            if (origin.endsWith("." + domain)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 处理不允许的域名请求
     */
    private void handleForbiddenOrigin(HttpServletResponse response) throws IOException {
        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        response.setContentType("application/json;charset=UTF-8");
        String jsonResponse = "{\"code\":1,\"msg\":\"CORS policy: Origin not allowed\"}";
        response.getWriter().write(jsonResponse);
        response.flushBuffer();
    }
}
