package com.trinasolar.i18n.web;

import com.trinasolar.i18n.config.I18nProperties;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 国际化Web配置
 *
 * <AUTHOR>
 */
public class I18nWebConfig implements WebMvcConfigurer {

    private final I18nInterceptor i18nInterceptor;
    private final I18nProperties properties;

    public I18nWebConfig(I18nInterceptor i18nInterceptor, I18nProperties properties) {
        this.i18nInterceptor = i18nInterceptor;
        this.properties = properties;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 构建拦截器注册
        InterceptorRegistration registration = registry.addInterceptor(i18nInterceptor);
        
        // 添加排除路径
        if (properties.getInterceptor().getExcludePaths() != null && !properties.getInterceptor().getExcludePaths().isEmpty()) {
            registration.excludePathPatterns(properties.getInterceptor().getExcludePaths().toArray(new String[0]));
        }
        
        // 默认排除的静态资源路径
        registration.excludePathPatterns(
            "/favicon.ico",
            "/static/**",
            "/webjars/**",
            "/**/*.js",
            "/**/*.css",
            "/**/*.html",
            "/**/*.jpg",
            "/**/*.jpeg",
            "/**/*.png",
            "/**/*.gif",
            "/**/*.svg",
            "/**/*.ico"
        );
    }
}