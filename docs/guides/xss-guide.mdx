---
title: XSS 防护模块使用指南
description: Trina XSS 防护模块使用指南
---

# XSS 防护模块

## 概述

`trina-xss-starter` 模块为 Trina Common 框架提供了全面的 XSS（跨站脚本）防护能力。它通过输入过滤、HTML 清理和输出编码等多种防护机制，有效防止恶意脚本注入。

## 主要特性

### 🛡️ 核心防护能力

- **输入过滤**：自动过滤用户输入中的恶意脚本
- **HTML 清理**：清理 HTML 内容中的危险标签和属性
- **输出编码**：安全地编码输出内容
- **白名单机制**：支持配置安全的 HTML 标签和属性
- **自定义规则**：支持自定义过滤规则和清理策略
- **多种模式**：清除模式（默认）和转义模式
- **Form Protection**: Automatic form parameter cleaning
- **JSON Protection**: Jackson integration for JSON deserialization protection

## Quick Start

### Maven Dependency

```xml
<dependency>
    <groupId>com.trinasoalr</groupId>
    <artifactId>trina-xss-starter</artifactId>
</dependency>
```

注意：依赖版本由 `trina-parent` 管理，不需要指定版本号。

### Basic Configuration

Add XSS protection configuration in `application.yml`:

```yaml
security:
  xss:
    enabled: true              # Enable XSS protection (default: false)
    trim-text: true           # Trim leading/trailing whitespace (default: true)
    mode: clear               # Mode: clear (default) or escape
    pretty-print: false       # Preserve line breaks (default: false)
    enable-escape: false      # Enable escape in clear mode (default: false)
    path-patterns:            # Intercept paths (default: empty)
      - "/api/**"
      - "/admin/**"
    exclude-patterns:         # Exclude paths
      - "/api/upload/**"
    exclude-fields:           # Exclude fields
      - "password"
      - "token"
```

### Automatic Protection

Once XSS protection is enabled, the framework automatically protects the following content:

1. **HTTP Request Parameters**: Automatically filters form submissions and URL parameters
2. **JSON Request Body**: Automatically filters string fields in JSON data through Jackson integration
3. **File Uploads**: Filters file names and description information

```java
@RestController
@RequestMapping("/api/user")
public class UserController {
    
    @PostMapping("/create")
    public Result<User> createUser(@RequestBody User user) {
        // String fields in user object are automatically XSS filtered
        return Result.success(userService.create(user));
    }
    
    @GetMapping("/search")
    public Result<List<User>> searchUsers(@RequestParam String keyword) {
        // keyword parameter is automatically XSS filtered
        return Result.success(userService.search(keyword));
    }
}
```

### Manual Filtering

For special scenarios, you can manually call XSS filtering methods:

```java
@Service
public class ContentService {
    
    @Autowired
    private XssCleaner xssCleaner;
    
    public void processContent(String content) {
        // Manual XSS filtering
        String cleanContent = xssCleaner.clean(content);
        
        // Process cleaned content
        // ...
    }
    
    public String cleanHtml(String html) {
        // Clean HTML content while preserving safe tags
        return xssCleaner.clean(html);
    }
}
```

### Annotation-based Protection

Use annotations to control XSS protection behavior:

```java
@RestController
public class ContentController {
    
    @PostMapping("/content")
    public Result<String> createContent(
        @RequestBody @XssCleanIgnore ContentRequest request) {
        // This request body will skip XSS cleaning
        return Result.success("Content created");
    }
    
    @PostMapping("/comment")
    public Result<String> createComment(@RequestBody CommentRequest request) {
        // This request body will be automatically cleaned
        return Result.success("Comment created");
    }
}

@Data
public class ContentRequest {
    private String title;
    
    @XssCleanIgnore
    private String rawContent;  // This field will skip XSS cleaning
    
    private String description; // This field will be cleaned
}
```

## Advanced Configuration

### Protection Modes

The module supports two main protection modes:

```yaml
security:
  xss:
    mode: clear  # Available values: clear, escape
```

- **clear**: Clean mode (default) - removes dangerous content
- **escape**: Escape mode - escapes special characters

### Path Configuration

```yaml
security:
  xss:
    # Paths to intercept (default: empty means all paths)
    path-patterns:
      - "/api/**"
      - "/admin/**"
    
    # Paths to exclude from XSS protection
    exclude-patterns:
      - "/api/upload/**"
      - "/api/file/**"
      - "/actuator/**"
```

### Field Exclusion

```yaml
security:
  xss:
    # Fields to exclude from XSS cleaning
    exclude-fields:
      - "password"
      - "token"
      - "signature"
      - "rawContent"
```

## 高级用法

### 自定义过滤器

您可以实现自定义的XSS过滤策略来满足特定的业务需求：

```java
@Component
public class CustomXssFilter implements XssFilterStrategy {
    
    @Override
    public String filter(String input) {
        if (StringUtils.isEmpty(input)) {
            return input;
        }
        
        // 自定义过滤逻辑
        String result = input;
        
        // 移除 script 标签
        result = result.replaceAll("(?i)<script[^>]*>.*?</script>", "");
        
        // 移除 javascript: 协议
        result = result.replaceAll("(?i)javascript:", "");
        
        // 移除 on 事件
        result = result.replaceAll("(?i)\\s*on\\w+\\s*=", "");
        
        return result;
    }
}
```

### 注解式防护

通过注解可以更精细地控制XSS防护行为：

```java
@RestController
public class ApiController {
    
    @PostMapping("/content")
    @XssProtection(mode = XssMode.STRICT)
    public R<String> createContent(@RequestBody @XssClean ContentRequest request) {
        // 使用注解进行严格的XSS防护
        return R.ok("处理成功");
    }
    
    @PostMapping("/html")
    @XssProtection(allowedTags = {"p", "br", "strong"})
    public R<String> saveHtml(@RequestBody HtmlRequest request) {
        // 允许特定的安全HTML标签
        return R.ok("保存成功");
    }
}```

### 条件化配置

根据配置条件动态启用XSS防护功能：

```java
@Configuration
@ConditionalOnProperty(name = "trina.xss.enabled", havingValue = "true")
public class XssConfiguration {
    
    @Bean
    @ConditionalOnMissingBean
    public XssFilter xssFilter() {
        return new DefaultXssFilter();
    }
    
    @Bean
    public FilterRegistrationBean<XssHttpServletRequestFilter> xssFilterRegistration() {
        FilterRegistrationBean<XssHttpServletRequestFilter> registration = 
            new FilterRegistrationBean<>();
        registration.setFilter(new XssHttpServletRequestFilter());
        registration.addUrlPatterns("/*");
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE+1);
        return registration;
    }
}```

## 最佳实践

### 1. 分层防护

建议采用多层防护策略，确保安全性：

```java
// 前端验证 - 第一道防线
// 在前端进行基础的输入验证和格式检查

// 后端过滤 - 核心防护
@PostMapping("/api/content")
public R<String> createContent(@RequestBody @Valid @XssClean ContentRequest request) {
    // 后端进行严格的XSS过滤
    return contentService.create(request);
}

// 数据库存储 - 持久化前验证
// 存储时再次确认数据的安全性和完整性

// 输出编码 - 最后防线
// 输出到页面时进行HTML编码
```

### 2. 白名单策略

推荐使用白名单方式，只允许安全的标签和属性：

```yaml
trina:
  xss:
    # 采用白名单策略，只允许安全的标签和属性
    allowed-tags:
      - p          # 段落
      - br         # 换行
      - strong     # 加粗
      - em         # 斜体
    allowed-attributes:
      - class      # 样式类
    # 严格禁止所有脚本相关内容
    blocked-tags:
      - script     # 脚本标签
      - iframe     # 内嵌框架
      - object     # 对象标签
      - embed      # 嵌入标签
```

### 3. 内容分类处理

根据不同的内容类型采用相应的处理策略：

```java
@Service
public class ContentSecurityService {
    
    public String processUserContent(String content, ContentType type) {
        switch (type) {
            case PLAIN_TEXT:
                // 纯文本内容，采用最严格的过滤策略
                return xssFilter.filterStrict(content);
            case RICH_TEXT:
                // 富文本内容，允许部分安全的HTML标签
                return htmlSanitizer.sanitize(content);
            case CODE:
                // 代码内容，使用专门的代码过滤器
                return codeFilter.filter(content);
            default:
                // 默认使用标准过滤策略
                return xssFilter.filter(content);
        }
    }
}```

## 性能优化

### 1. 缓存策略

通过缓存机制提高过滤性能，避免重复计算：

```java
@Component
public class CachedXssFilter {
    
    private final Cache<String, String> filterCache = 
        Caffeine.newBuilder()
            .maximumSize(10000)              // 最大缓存条目数
            .expireAfterWrite(1, TimeUnit.HOURS)  // 1小时后过期
            .build();
    
    public String filter(String input) {
        return filterCache.get(input, this::doFilter);
    }
    
    private String doFilter(String input) {
        // 实际的XSS过滤逻辑
        return xssFilter.filter(input);
    }
}
```

### 2. 异步处理

对于大量数据的处理，可以采用异步方式：

```java
@Service
public class AsyncXssService {
    
    @Async
    public CompletableFuture<String> filterAsync(String content) {
        String filtered = xssFilter.filter(content);
        return CompletableFuture.completedFuture(filtered);
    }
}```

## 监控和日志

### 安全事件记录

记录XSS攻击尝试，便于安全分析：

```java
@Component
public class XssSecurityLogger {
    
    private static final Logger securityLogger = 
        LoggerFactory.getLogger("SECURITY");
    
    public void logXssAttempt(String originalContent, String clientIp, String userAgent) {
        securityLogger.warn("检测到XSS攻击尝试 - IP: {}, UserAgent: {}, Content: {}", 
            clientIp, userAgent, originalContent);
    }
}
```

### 指标监控

通过Micrometer收集XSS防护相关指标：

```java
@Component
public class XssMetrics {
    
    private final Counter xssAttempts = Counter.builder("xss.attempts")
        .description("检测到的XSS攻击尝试次数")
        .register(Metrics.globalRegistry);
    
    private final Timer filterTime = Timer.builder("xss.filter.time")
        .description("XSS过滤处理时间")
        .register(Metrics.globalRegistry);
    
    public void recordXssAttempt() {
        xssAttempts.increment();
    }
    
    public void recordFilterTime(Duration duration) {
        filterTime.record(duration);
    }
}```

## 常见问题

### Q: 如何处理富文本编辑器的内容？

**A:** 对于富文本内容，建议使用白名单策略，只允许安全的HTML标签：

```yaml
trina:
  xss:
    mode: filter
    allowed-tags:
      - p          # 段落
      - br         # 换行
      - strong     # 加粗
      - em         # 斜体
      - ul         # 无序列表
      - ol         # 有序列表
      - li         # 列表项
      - h1         # 一级标题
      - h2         # 二级标题
      - h3         # 三级标题
    allowed-attributes:
      - class      # CSS类名
      - style      # 内联样式（谨慎使用）
```

### Q: 如何排除特定接口的XSS检查？

**A:** 使用exclude-patterns配置排除不需要XSS检查的接口：

```yaml
security:
  xss:
    exclude-patterns:
      - "/api/upload/**"     # 文件上传接口
      - "/api/webhook/**"    # Webhook回调接口
      - "/actuator/**"       # 监控端点
```

### Q: 过滤后的内容如何恢复？

**A:** XSS过滤是不可逆的安全措施，为了保证数据完整性，建议在设计时考虑：

- **内容预览**: 提供过滤前后的内容对比预览功能
- **原始备份**: 在安全的地方保存原始内容的备份
- **版本控制**: 实现内容的版本管理机制
- **用户确认**: 在过滤前向用户展示将要进行的修改

### Q: 如何自定义XSS过滤规则？

**A:** 可以通过实现自定义过滤器来扩展过滤规则：

```java
@Component
public class CustomXssFilter implements XssFilterStrategy {
    @Override
    public String filter(String input) {
        // 实现自定义的过滤逻辑
        return customFilterLogic(input);
    }
}
```

## 注意事项

### 1. 性能考虑
- **处理时间**: XSS过滤会增加请求处理时间，建议合理配置缓存策略
- **内存使用**: 大量文本过滤可能消耗较多内存，注意监控资源使用情况
- **并发处理**: 高并发场景下建议使用异步处理或线程池

### 2. 业务兼容性
- **特殊字符**: 某些业务必需的特殊字符可能被过滤，需要充分测试
- **格式保持**: 确保过滤后的内容仍能满足业务格式要求
- **用户体验**: 避免过度过滤影响用户正常使用

### 3. 安全维护
- **规则更新**: 定期更新过滤规则，应对新出现的攻击手段
- **日志监控**: 建立完善的安全日志和监控机制
- **应急响应**: 制定XSS攻击的应急响应预案

### 4. 测试验证
- **安全测试**: 定期进行XSS安全测试，验证防护效果
- **回归测试**: 确保XSS防护不会影响现有功能
- **边界测试**: 测试各种边界情况和异常输入

## 更多资源

### 官方文档
- [Trina Common 架构文档](../architecture/overview.mdx)
- [Web模块使用指南](./web-usage-guide.mdx)


### 外部参考
- [OWASP XSS防护指南](https://owasp.org/www-community/attacks/xss/)
- [Spring Security XSS防护](https://docs.spring.io/spring-security/reference/features/exploits/headers.html#headers-xss-protection)
- [JSoup HTML清理库](https://jsoup.org/cookbook/cleaning-html/safelist-sanitizer)

### 相关教程
- 相关教程文档正在完善中