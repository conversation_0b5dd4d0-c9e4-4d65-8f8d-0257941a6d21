package com.trinasolar.gateway.security.util;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.reactive.function.client.WebClientRequestException;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.netty.http.client.PrematureCloseException;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.function.Predicate;

/**
 * WebClient 重试工具类
 * <p>
 * 提供统一的重试策略，避免 PrematureCloseException 等连接问题
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@UtilityClass
public class WebClientRetryUtils {

    /**
     * 判断是否为可重试的异常
     */
    private static final Predicate<Throwable> RETRYABLE_EXCEPTION_FILTER = throwable -> {
        // 连接过早关闭异常
        if (throwable instanceof PrematureCloseException) {
            return true;
        }
        // 网络请求异常（连接超时、DNS解析失败等）
        if (throwable instanceof WebClientRequestException) {
            return true;
        }
        // 5xx 服务器错误
        if (throwable instanceof WebClientResponseException) {
            WebClientResponseException responseException = (WebClientResponseException) throwable;
            return responseException.getStatusCode().is5xxServerError();
        }
        return false;
    };

    /**
     * 创建 Token 验证重试策略
     *
     * @param maxAttempts  最大重试次数
     * @param initialDelay 初始延迟时间
     * @param url          请求URL（用于日志）
     * @return 重试策略
     */
    public static Retry createTokenVerifyRetry(int maxAttempts, Duration initialDelay, String url) {
        return Retry.backoff(maxAttempts, initialDelay)
                .filter(RETRYABLE_EXCEPTION_FILTER)
                .onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> {
                    log.warn("Token verification retry exhausted for URL: {}, attempts: {}", 
                            url, retrySignal.totalRetries());
                    return retrySignal.failure();
                })
                .doBeforeRetry(retrySignal -> {
                    Throwable failure = retrySignal.failure();
                    if (failure instanceof PrematureCloseException) {
                        log.debug("Retrying token verification due to premature connection close, attempt: {}, URL: {}", 
                                retrySignal.totalRetries() + 1, url);
                    } else {
                        log.debug("Retrying token verification due to: {}, attempt: {}, URL: {}", 
                                failure.getClass().getSimpleName(), retrySignal.totalRetries() + 1, url);
                    }
                });
    }

    /**
     * 创建权限加载重试策略
     *
     * @param maxAttempts  最大重试次数
     * @param initialDelay 初始延迟时间
     * @param url          请求URL（用于日志）
     * @param userId       用户ID（用于日志）
     * @return 重试策略
     */
    public static Retry createPermissionLoadRetry(int maxAttempts, Duration initialDelay, String url, String userId) {
        return Retry.backoff(maxAttempts, initialDelay)
                .filter(RETRYABLE_EXCEPTION_FILTER)
                .onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> {
                    log.warn("Permission loading retry exhausted for user: {}, URL: {}, attempts: {}", 
                            userId, url, retrySignal.totalRetries());
                    return retrySignal.failure();
                })
                .doBeforeRetry(retrySignal -> {
                    Throwable failure = retrySignal.failure();
                    if (failure instanceof PrematureCloseException) {
                        log.debug("Retrying permission loading due to premature connection close, user: {}, attempt: {}, URL: {}", 
                                userId, retrySignal.totalRetries() + 1, url);
                    } else {
                        log.debug("Retrying permission loading due to: {}, user: {}, attempt: {}, URL: {}", 
                                failure.getClass().getSimpleName(), userId, retrySignal.totalRetries() + 1, url);
                    }
                });
    }

    /**
     * 创建 Token 请求重试策略
     *
     * @param maxAttempts  最大重试次数
     * @param initialDelay 初始延迟时间
     * @param url          请求URL（用于日志）
     * @return 重试策略
     */
    public static Retry createTokenRequestRetry(int maxAttempts, Duration initialDelay, String url) {
        return Retry.backoff(maxAttempts, initialDelay)
                .filter(RETRYABLE_EXCEPTION_FILTER)
                .onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> {
                    log.warn("Token request retry exhausted for URL: {}, attempts: {}", 
                            url, retrySignal.totalRetries());
                    return retrySignal.failure();
                })
                .doBeforeRetry(retrySignal -> {
                    Throwable failure = retrySignal.failure();
                    if (failure instanceof PrematureCloseException) {
                        log.debug("Retrying token request due to premature connection close, attempt: {}, URL: {}", 
                                retrySignal.totalRetries() + 1, url);
                    } else {
                        log.debug("Retrying token request due to: {}, attempt: {}, URL: {}", 
                                failure.getClass().getSimpleName(), retrySignal.totalRetries() + 1, url);
                    }
                });
    }

    /**
     * 创建通用重试策略
     *
     * @param maxAttempts  最大重试次数
     * @param initialDelay 初始延迟时间
     * @param url          请求URL（用于日志）
     * @param operation    操作名称（用于日志）
     * @return 重试策略
     */
    public static Retry createGenericRetry(int maxAttempts, Duration initialDelay, String url, String operation) {
        return Retry.backoff(maxAttempts, initialDelay)
                .filter(RETRYABLE_EXCEPTION_FILTER)
                .onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> {
                    log.warn("{} retry exhausted for URL: {}, attempts: {}", 
                            operation, url, retrySignal.totalRetries());
                    return retrySignal.failure();
                })
                .doBeforeRetry(retrySignal -> {
                    Throwable failure = retrySignal.failure();
                    if (failure instanceof PrematureCloseException) {
                        log.debug("Retrying {} due to premature connection close, attempt: {}, URL: {}", 
                                operation, retrySignal.totalRetries() + 1, url);
                    } else {
                        log.debug("Retrying {} due to: {}, attempt: {}, URL: {}", 
                                operation, failure.getClass().getSimpleName(), retrySignal.totalRetries() + 1, url);
                    }
                });
    }

    /**
     * 记录连接相关错误
     *
     * @param throwable 异常
     * @param url       请求URL
     * @param operation 操作名称
     */
    public static void logConnectionError(Throwable throwable, String url, String operation) {
        if (throwable instanceof PrematureCloseException) {
            log.warn("Connection prematurely closed during {} for URL: {}", operation, url);
        } else if (throwable instanceof WebClientRequestException) {
            log.warn("Network error during {} for URL: {}, error: {}", operation, url, throwable.getMessage());
        } else {
            log.error("Error during {} for URL: {}", operation, url, throwable);
        }
    }

    /**
     * 记录连接相关错误（带用户信息）
     *
     * @param throwable 异常
     * @param url       请求URL
     * @param operation 操作名称
     * @param userId    用户ID
     */
    public static void logConnectionError(Throwable throwable, String url, String operation, String userId) {
        if (throwable instanceof PrematureCloseException) {
            log.warn("Connection prematurely closed during {} for user: {}, URL: {}", operation, userId, url);
        } else if (throwable instanceof WebClientRequestException) {
            log.warn("Network error during {} for user: {}, URL: {}, error: {}", 
                    operation, userId, url, throwable.getMessage());
        } else {
            log.error("Error during {} for user: {}, URL: {}", operation, userId, url, throwable);
        }
    }
}
