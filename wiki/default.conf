server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # 安全相关头信息
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # 缓存静态资源
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }

    # HTML文件不缓存或短期缓存
    location ~* \.html$ {
        expires 1h;
        add_header Cache-Control "public, no-transform";
    }

    # 处理搜索索引文件
    location ~* /search-index\.json$ {
        expires 1h;
        add_header Cache-Control "public, no-transform";
    }

    # 处理wiki路径
    location /wiki/ {
        alias /usr/share/nginx/html/;
        try_files $uri $uri/ /wiki/index.html;
        index index.html;
    }

    # 重定向根路径到wiki
    location = / {
        return 301 /wiki/;
    }

    # 处理SPA路由 - 保留原有配置作为备用
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}