package com.trinasolar.elasticsearch.exception;

/**
 * 索引操作异常类
 * 用于处理索引创建、删除、更新等操作过程中的异常
 */
public class IndexOperationException extends ElasticsearchException {

    /**
     * 索引名称
     */
    private final String indexName;
    
    /**
     * 操作类型
     */
    private final OperationType operationType;

    /**
     * 索引操作类型枚举
     */
    public enum OperationType {
        /**
         * 创建索引
         */
        CREATE("创建"),
        
        /**
         * 删除索引
         */
        DELETE("删除"),
        
        /**
         * 更新索引
         */
        UPDATE("更新"),
        
        /**
         * 查询索引
         */
        QUERY("查询"),
        
        /**
         * 刷新索引
         */
        REFRESH("刷新"),
        
        /**
         * 打开索引
         */
        OPEN("打开"),
        
        /**
         * 关闭索引
         */
        CLOSE("关闭"),
        
        /**
         * 其他操作
         */
        OTHER("其他操作");
        
        private final String description;
        
        OperationType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    /**
     * 构造函数
     *
     * @param indexName 索引名称
     * @param operationType 操作类型
     * @param message 错误消息
     */
    public IndexOperationException(String indexName, OperationType operationType, String message) {
        super(message, "ES-INDEX-" + operationType.name());
        this.indexName = indexName;
        this.operationType = operationType;
    }

    /**
     * 构造函数
     *
     * @param indexName 索引名称
     * @param operationType 操作类型
     * @param message 错误消息
     * @param cause 原始异常
     */
    public IndexOperationException(String indexName, OperationType operationType, String message, Throwable cause) {
        super(message, cause, "ES-INDEX-" + operationType.name());
        this.indexName = indexName;
        this.operationType = operationType;
    }

    /**
     * 获取索引名称
     *
     * @return 索引名称
     */
    public String getIndexName() {
        return indexName;
    }

    /**
     * 获取操作类型
     *
     * @return 操作类型
     */
    public OperationType getOperationType() {
        return operationType;
    }

    @Override
    public String getMessage() {
        return String.format("索引[%s]%s操作失败: %s", 
                indexName, operationType.getDescription(), super.getMessage());
    }
}
