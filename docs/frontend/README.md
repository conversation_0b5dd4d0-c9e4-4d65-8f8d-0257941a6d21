# Trina 前端脚手架

基于 Vue 3 + TypeScript + Vite + Element Plus 的前端项目脚手架，采用模块化设计，支持国际化、状态管理和路由配置。

## 脚手架菜单

### 🎯 快速开始
- [项目初始化](#开发要求) - 环境配置和项目启动
- [目录结构](#目录结构) - 了解项目组织结构
- [开发规范](#开发规范) - 编码规范和最佳实践

### 🏗️ 核心功能
- [模块化开发](#2-模块化开发) - 功能模块组织方式
- [组件开发](#3-组件开发) - Vue 3 组件开发规范
- [状态管理](#4-状态管理) - Pinia 状态管理
- [路由配置](#5-路由配置) - Vue Router 路由管理
- [API 请求](#6-api-请求) - HTTP 请求封装
- [国际化](#7-国际化) - 多语言支持

### 🔧 开发工具
- [环境配置](#8-环境配置) - 开发环境设置
- [代码风格](#9-代码风格) - ESLint 和 Prettier 配置
- [常见问题](#常见问题) - 开发中的常见问题解决

### 📚 学习资源
- [参考文档](#参考文档) - 相关技术文档链接
- [开发流程](#开发要求) - 完整的开发工作流程

## 目录结构

```
src/
├── api/              # API请求
│   ├── index.ts      # 请求工具
│   └── [模块名].ts    # 各模块API
├── assets/           # 静态资源
├── components/       # 公共组件
│   ├── ui/           # 原子组件
│   └── page/         # 页面组件
├── constants/        # 常量定义
├── hooks/            # 自定义hooks
├── i18n/             # 国际化
│   ├── index.ts      # 国际化配置
│   ├── locales/      # 公共语言包
│   └── [模块名].ts    # 各模块国际化
├── layouts/          # 布局组件
├── router/           # 路由配置
│   ├── index.ts      # 路由入口
│   └── [模块名].ts    # 各模块路由
├── stores/           # 状态管理
│   ├── index.ts      # 状态管理入口
│   └── [模块名].ts    # 各模块状态
├── styles/           # 全局样式
│   └── theme/        # 主题配置
├── types/            # 类型定义
│   ├── index.ts      # 全局类型
│   └── [模块名].ts    # 各模块类型
├── utils/            # 工具函数
├── views/            # 页面视图
│   ├── dashboard/    # 首页模块
│   ├── system/       # 系统模块
│   ├── table/        # 表格模块
│   └── user/         # 用户模块
├── App.vue           # 根组件
└── main.ts           # 应用入口
```

## 开发规范

### 1. 命名规范

- 文件夹和文件命名：
  - 组件文件使用 PascalCase（如 `TablePage.vue`）
  - 非组件文件使用 kebab-case（如 `api-service.ts`）
  - 目录名使用 kebab-case（如 `user-management`）

- 变量命名：
  - 变量和函数使用 camelCase（如 `getUserInfo`）
  - 常量使用 UPPER_SNAKE_CASE（如 `MAX_COUNT`）
  - 类和接口使用 PascalCase（如 `UserService`）

- CSS 命名：
  - 使用 BEM 命名规范
  - 避免使用内联样式

### 2. 模块化开发

- 每个功能模块应包含：
  - 路由配置（`router/[模块名].ts`）
  - 状态管理（`stores/[模块名].ts`）
  - 国际化配置（`i18n/[模块名].ts`）
  - API 请求（`api/[模块名].ts`）
  - 类型定义（`types/[模块名].ts`）
  - 视图组件（`views/[模块名]/`）

- 添加新模块时，需要：
  1. 创建上述相关文件
  2. 在路由入口（`router/index.ts`）中引入并注册路由
  3. 在国际化配置（`i18n/index.ts`）中引入并注册语言包

### 3. 组件开发

- 使用 Vue 3 组合式 API（`<script setup>`）
- 组件属性使用 TypeScript 类型定义
- 复杂组件应拆分为子组件
- 使用 `defineProps` 和 `defineEmits` 定义属性和事件

```vue
<script setup lang="ts">
import { ref } from 'vue'

// 定义属性
const props = defineProps<{
  title: string
  loading?: boolean
}>()

// 定义事件
const emit = defineEmits<{
  (e: 'update', value: string): void
  (e: 'delete'): void
}>()
</script>
```

### 4. 状态管理

- 使用 Pinia 进行状态管理
- 每个模块创建独立的 Store
- 复杂状态应拆分为多个 Store

```ts
// stores/user.ts
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref('')
  
  // 方法
  const login = async () => {
    // ...
  }
  
  return { token, login }
})
```

### 5. 路由配置

- 使用懒加载提高性能
- 使用元信息（meta）配置路由属性
- 需要权限的路由添加 `requiresAuth: true`

```ts
// router/user.ts
import { RouteRecordRaw } from 'vue-router'

const userRoutes: RouteRecordRaw[] = [
  {
    path: '/user',
    component: () => import('@/layouts/MainLayout.vue'),
    children: [
      {
        path: 'profile',
        component: () => import('@/views/user/UserProfile.vue'),
        meta: { 
          title: '用户信息',
          requiresAuth: true
        }
      }
    ]
  }
]

export default userRoutes
```

### 6. API 请求

- 使用 Axios 进行 HTTP 请求
- 封装请求方法，统一处理错误
- 按模块组织 API 请求

```ts
// api/user.ts
import request from './index'

export const login = (data: LoginParams) => {
  return request({
    url: '/auth/login',
    method: 'post',
    data
  })
}
```

### 7. 国际化

- 使用 vue-i18n 进行国际化
- 按模块组织语言包
- 文本使用 `$t('key')` 方式引用

```ts
// i18n/user.ts
export const zhCN = {
  user: {
    login: {
      title: '用户登录',
      username: '用户名'
    }
  }
}
```

### 8. 环境配置

- 使用 `.env.*` 文件配置不同环境
- 环境变量以 `VITE_APP_` 开头
- 通过 `import.meta.env` 访问环境变量

### 9. 代码风格

- 使用 ESLint 和 Prettier 保持代码风格一致
- 提交前运行 lint 检查
- 遵循项目的 `.eslintrc.js` 和 `.prettierrc` 配置

## 开发要求

1. **Node.js 版本**：使用 Node.js >= 16.0.0
2. **包管理工具**：必须使用 Yarn 作为包管理工具
3. **编辑器配置**：推荐使用 VSCode，并安装以下插件：
   - ESLint
   - Prettier
   - Volar
   - TypeScript Vue Plugin

4. **开发流程**：
   - 拉取最新代码：`git pull`
   - 安装依赖：`yarn install`
   - 本地开发：`yarn dev`
   - 构建生产版本：`yarn build`
   - 预览生产版本：`yarn preview`

5. **提交规范**：
   - feat: 新功能
   - fix: 修复 bug
   - docs: 文档更新
   - style: 代码风格修改
   - refactor: 代码重构
   - perf: 性能优化
   - test: 测试相关
   - chore: 构建过程或辅助工具的变动

## 常见问题

1. **依赖安装失败**
   - 尝试清除缓存：`yarn cache clean`
   - 使用淘宝镜像：`yarn config set registry https://registry.npmmirror.com`

2. **启动开发服务器失败**
   - 检查端口是否被占用
   - 检查 Node.js 版本是否符合要求

3. **TypeScript 类型错误**
   - 运行 `yarn type-check` 检查类型错误
   - 确保所有导入的类型定义正确

4. **国际化文本不显示**
   - 检查语言包是否正确导入
   - 检查 i18n 配置是否正确

## 参考文档

- [Vue 3 文档](https://v3.cn.vuejs.org/)
- [TypeScript 文档](https://www.typescriptlang.org/docs/)
- [Vite 文档](https://cn.vitejs.dev/)
- [Element Plus 文档](https://element-plus.org/zh-CN/)
- [Pinia 文档](https://pinia.vuejs.org/zh/)
- [Vue Router 文档](https://router.vuejs.org/zh/)
- [Vue I18n 文档](https://vue-i18n.intlify.dev/)
