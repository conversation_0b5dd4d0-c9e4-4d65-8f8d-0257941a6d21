package com.trinasolar.common.security.aspect;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trinasolar.common.security.annotation.ColPermission;
import com.trinasolar.common.security.context.SecurityContextHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;

import java.lang.reflect.Method;
import java.util.*;

/**
 * 列级权限AOP拦截器
 * <p>
 * 拦截@ColPermission注解，根据用户角色过滤返回数据中的敏感字段
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Aspect
@RequiredArgsConstructor
public class ColumnPermissionAspect {

    private final ObjectMapper objectMapper;

    /**
     * 拦截@ColPermission注解
     */
    @Around("@annotation(com.trinasolar.common.security.annotation.ColPermission)")
    public Object filterColumns(ProceedingJoinPoint joinPoint) throws Throwable {
        // 执行原方法
        Object result = joinPoint.proceed();

        if (result == null) {
            return null;
        }

        if (!SecurityContextHolder.isAuthenticated()) {
            log.warn("用户未登录，跳过列级权限过滤");
            return result;
        }

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        ColPermission colPermission = method.getAnnotation(ColPermission.class);

        if (colPermission == null) {
            return result;
        }

        // 检查用户是否匹配指定角色
        if (!isUserMatchRole(colPermission)) {
            return result;
        }

        // 过滤字段
        return filterResultFields(result, colPermission);
    }

    /**
     * 检查用户是否匹配指定角色
     */
    private boolean isUserMatchRole(ColPermission colPermission) {
        List<String> userRoles = SecurityContextHolder.getCurrentUserRoleCodes();
        if (userRoles == null || userRoles.isEmpty()) {
            return false;
        }

        // 获取注解中指定的角色
        Set<String> targetRoles = new HashSet<>();
        if (!colPermission.role().isEmpty()) {
            targetRoles.add(colPermission.role());
        }
        if (colPermission.roles().length > 0) {
            targetRoles.addAll(Arrays.asList(colPermission.roles()));
        }

        // 检查用户是否拥有任意一个指定角色
        for (String role : targetRoles) {
            if (userRoles.contains(role)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 过滤结果字段
     */
    private Object filterResultFields(Object result, ColPermission colPermission) {
        try {
            // 将结果转换为JSON节点
            JsonNode jsonNode = objectMapper.valueToTree(result);
            // 创建可修改的副本
            ObjectNode resultNode = jsonNode.deepCopy();

            // 仅处理data字段
            JsonNode dataJsonNode = jsonNode.get("data");
            if (dataJsonNode != null) {
                JsonNode filteredData = filterJsonNode(dataJsonNode, colPermission);
                resultNode.set("data", filteredData);
            }

            // 转换回原始类型
            return objectMapper.treeToValue(resultNode, result.getClass());
        } catch (Exception e) {
            log.error("列级权限过滤失败", e);
            return result;
        }
    }

    /**
     * 过滤JSON节点
     */
    private JsonNode filterJsonNode(JsonNode node, ColPermission colPermission) {
        if (node == null) {
            return null;
        }

        if (node.isArray()) {
            // 处理数组
            ArrayNode arrayNode = (ArrayNode) node;
            ArrayNode filteredArray = objectMapper.createArrayNode();
            for (JsonNode item : arrayNode) {
                filteredArray.add(filterJsonNode(item, colPermission));
            }
            return filteredArray;
        } else if (node.isObject()) {
            ObjectNode objectNode = node.deepCopy();

            if (colPermission.mode() == ColPermission.Mode.EXCLUDE) {
                // 排除模式：移除指定字段
                Set<String> excludeFields = new HashSet<>(Arrays.asList(colPermission.exc()));
                for (String field : excludeFields) {
                    objectNode.remove(field);
                }
            } else {
                // 包含模式：只保留指定字段
                Set<String> includeFields = new HashSet<>(Arrays.asList(colPermission.inc()));
                Iterator<String> fieldNames = objectNode.fieldNames();
                Set<String> fieldsToRemove = new HashSet<>();

                while (fieldNames.hasNext()) {
                    String fieldName = fieldNames.next();
                    if (!includeFields.contains(fieldName)) {
                        fieldsToRemove.add(fieldName);
                    }
                }

                for (String field : fieldsToRemove) {
                    objectNode.remove(field);
                }
            }

            return objectNode;
        }

        return node;
    }
}
