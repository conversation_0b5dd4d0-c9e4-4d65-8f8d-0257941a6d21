package com.trinasolar.mongodb.base;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * MongoDB基础服务实现类
 *
 * @param <T> 实体类型
 * @param <R> 仓库类型
 */
@Transactional(readOnly = true)
public abstract class BaseMongoServiceImpl<T extends BaseMongoEntity, R extends BaseMongoRepository<T>> implements BaseMongoService<T> {

    protected final R repository;
    protected final MongoTemplate mongoTemplate;

    /**
     * 构造函数
     *
     * @param repository   仓库实例
     * @param mongoTemplate MongoTemplate实例
     */
    protected BaseMongoServiceImpl(R repository, MongoTemplate mongoTemplate) {
        this.repository = repository;
        this.mongoTemplate = mongoTemplate;
    }

    /**
     * 获取实体类型
     *
     * @return 实体类型
     */
    protected abstract Class<T> getEntityClass();

    @Override
    @Transactional
    public T save(T entity) {
        Assert.notNull(entity, "Entity must not be null");
        
        // 实体保存前处理
        entity.prePersist();
        
        return repository.save(entity);
    }

    @Override
    @Transactional
    public List<T> saveAll(List<T> entities) {
        Assert.notNull(entities, "Entities must not be null");
        
        // 批量保存前处理
        entities.forEach(BaseMongoEntity::prePersist);
        
        return repository.saveAll(entities);
    }

    @Override
    public Optional<T> findById(String id) {
        Assert.notNull(id, "Id must not be null");
        return repository.findById(id);
    }

    @Override
    public List<T> findAll() {
        return repository.findAll();
    }

    @Override
    public List<T> findAllById(List<String> ids) {
        Assert.notNull(ids, "Ids must not be null");
        return repository.findAllById(ids);
    }

    @Override
    public boolean existsById(String id) {
        Assert.notNull(id, "Id must not be null");
        return repository.existsById(id);
    }

    @Override
    public long count() {
        return repository.count();
    }

    @Override
    @Transactional
    public void deleteById(String id) {
        Assert.notNull(id, "Id must not be null");
        repository.deleteById(id);
    }

    @Override
    @Transactional
    public void delete(T entity) {
        Assert.notNull(entity, "Entity must not be null");
        repository.delete(entity);
    }

    @Override
    @Transactional
    public void deleteAll(List<T> entities) {
        Assert.notNull(entities, "Entities must not be null");
        repository.deleteAll(entities);
    }

    @Override
    @Transactional
    public void deleteAll() {
        repository.deleteAll();
    }

    @Override
    @Transactional
    public T partialUpdate(String id, Map<String, Object> updateFields) {
        Assert.notNull(id, "Id must not be null");
        Assert.notNull(updateFields, "Update fields must not be null");
        
        Optional<T> optional = repository.findById(id);
        if (optional.isPresent()) {
            T entity = optional.get();
            
            // 设置更新时间
            entity.setUpdateTime(LocalDateTime.now());
            
            // 应用更新字段（通过MongoTemplate实现部分更新）
            Update update = new Update();
            update.set("updateTime", LocalDateTime.now());
            
            updateFields.forEach(update::set);
            
            mongoTemplate.updateFirst(
                    Query.query(Criteria.where("id").is(id)),
                    update,
                    getEntityClass()
            );
            
            // 重新查询获取更新后的实体
            return repository.findById(id).orElse(null);
        }
        
        return null;
    }

    @Override
    public List<T> findByQuery(BaseMongoQuery<T> query) {
        Assert.notNull(query, "Query must not be null");
        return repository.findByQuery(query);
    }

    @Override
    public PageResult<T> findPage(BaseMongoQuery<T> query) {
        Assert.notNull(query, "Query must not be null");
        return repository.findPage(query, getEntityClass());
    }

    @Override
    public long countByQuery(BaseMongoQuery<T> query) {
        Assert.notNull(query, "Query must not be null");
        return repository.countByQuery(query);
    }

    @Override
    @Transactional
    public long deleteByQuery(BaseMongoQuery<T> query) {
        Assert.notNull(query, "Query must not be null");
        return repository.deleteByQuery(query);
    }

    @Override
    @Transactional
    public boolean softDelete(String id) {
        Assert.notNull(id, "Id must not be null");
        return repository.softDelete(id);
    }

    @Override
    @Transactional
    public long softDeleteBatch(List<String> ids) {
        Assert.notNull(ids, "Ids must not be null");
        return repository.softDeleteBatch(ids);
    }

    @Override
    @Transactional
    public long softDeleteByQuery(BaseMongoQuery<T> query) {
        Assert.notNull(query, "Query must not be null");
        return repository.softDeleteByQuery(query);
    }
} 