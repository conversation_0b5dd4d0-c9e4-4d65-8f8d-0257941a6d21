package com.trinasolar.data.base;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.common.core.utils.string.StringUtils;
import com.trinasolar.data.utils.SecureQueryWrapperBuilder;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.io.Serializable;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 业务封装基础类
 * 常用的方法封装在本类，如需高级mybatis-plus用法使用repository变量操作
 * 建议service依赖repository
 *
 * @param <M> repository
 * @param <T> model
 */
@Validated
public class BaseDataServiceImpl<M extends BaseDataMapper<T>, T> extends ServiceImpl<M, T> implements BaseDataService<T> {

    @Resource
    private M mapper;

    @Override
    public T getByExample(T entity) {
        Wrapper<T> queryWrapper = new QueryWrapper<>(entity);
        return mapper.selectOne(queryWrapper);
    }

    /**
     * 动态查询list
     *
     * @param query 查询条件
     * @return 实体列表
     */
    @Override
    public List<T> getList(BaseQuery<T> query) {
        QueryWrapper<T> queryWrapper = SecureQueryWrapperBuilder.build(query, getEntityClass());
        return mapper.selectList(queryWrapper);
    }

    /**
     * 分页查询
     *
     * @param query 查询条件（包含分页参数）
     * @return 分页结果
     */
    @Override
    public IPage<T> getPage(BaseQuery<T> query) {
        QueryWrapper<T> queryWrapper = SecureQueryWrapperBuilder.build(query, getEntityClass());
        return mapper.selectPage(query.toPage(), queryWrapper);
    }

    /**
     * 高级条件查询列表
     *
     * @param queryWrapper 自定义查询条件
     * @return 实体列表
     */
    @Override
    public List<T> listByWrapper(Wrapper<T> queryWrapper) {
        return mapper.selectList(queryWrapper);
    }

    /**
     * 高级条件分页查询
     *
     * @param query        分页参数
     * @param queryWrapper 自定义查询条件
     * @return 分页查询结果
     */
    @Override
    public IPage<T> pageByWrapper(BaseQuery<T> query, Wrapper<T> queryWrapper) {
        return mapper.selectPage(query.toPage(), queryWrapper);
    }

    /**
     * 根据条件计数
     *
     * @param query 查询条件
     * @return 记录数
     */
    @Override
    public long count(BaseQuery<T> query) {
        QueryWrapper<T> queryWrapper = SecureQueryWrapperBuilder.build(query, getEntityClass());
        return mapper.selectCount(queryWrapper);
    }

    /**
     * 根据查询条件获取单个实体
     *
     * @param queryWrapper 查询条件
     * @return 单个实体
     */
    @Override
    public T getOne(Wrapper<T> queryWrapper) {
        return mapper.selectOne(queryWrapper);
    }

    /**
     * 根据实体对象获取单个实体
     *
     * @param entity 实体对象（非空属性为查询条件）
     * @return 单个实体
     */
    @Override
    public T getOne(T entity) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>(entity);
        return getOne(queryWrapper);
    }

    /**
     * 根据实体对象获取单个实体，指定是否抛出异常
     *
     * @param entity  实体对象（非空属性为查询条件）
     * @param throwEx 查询到多条记录时是否抛出异常
     * @return 单个实体
     */
    @Override
    public T getOne(T entity, boolean throwEx) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>(entity);
        return getOne(queryWrapper, throwEx);
    }

    /**
     * 根据查询条件获取单个实体，指定是否抛出异常
     *
     * @param queryWrapper 查询条件
     * @param throwEx      查询到多条记录时是否抛出异常
     * @return 单个实体
     */
    @Override
    public T getOne(Wrapper<T> queryWrapper, boolean throwEx) {
        return super.getOne(queryWrapper, throwEx);
    }

    /**
     * 根据ID集合批量查询
     *
     * @param idList ID集合
     * @return 实体列表
     */
    @Override
    public List<T> listByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return List.of();
        }
        return mapper.selectByIds(idList);
    }

    /**
     * 根据Map条件查询
     *
     * @param columnMap 列名和值的映射
     * @return 实体列表
     */
    @Override
    public List<T> listByMap(Map<String, Object> columnMap) {
        return super.listByMap(columnMap);
    }

    /**
     * 根据字段名和字段值集合查询
     *
     * @param column 字段名
     * @param values 字段值集合
     * @return 匹配的实体列表
     */
    @Override
    public List<T> listByColumnValues(String column, Collection<?> values) {
        if (CollectionUtils.isEmpty(values)) {
            return List.of();
        }
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(StringUtils.underscoreName(column), values);
        return mapper.selectList(queryWrapper);
    }

    /**
     * 将查询结果转换为Map
     *
     * @param queryWrapper 查询条件
     * @param mapper       转换函数
     * @param <K>          Map的键类型
     * @param <V>          Map的值类型
     * @return 转换后的Map
     */
    @Override
    public <K, V> Map<K, V> listToMap(Wrapper<T> queryWrapper, Function<? super T, K> mapper, Function<? super T, V> valueMapper) {
        List<T> list = this.listByWrapper(queryWrapper);
        Map<K, V> map = new HashMap<>(list.size());
        for (T item : list) {
            K key = mapper.apply(item);
            V value = valueMapper.apply(item);
            map.put(key, value);
        }
        return map;
    }

    /**
     * 根据过滤条件和高级查询条件获取实体列表
     *
     * @param filters 过滤条件
     * @param query   查询条件
     * @return 实体列表
     */
    @Override
    public List<T> getListWithFilters(Map<String, Object> filters, BaseQuery<T> query) {
        QueryWrapper<T> queryWrapper = SecureQueryWrapperBuilder.buildAdvanced(query, filters, getEntityClass());
        return mapper.selectList(queryWrapper);
    }

    /**
     * 根据过滤条件和高级查询条件分页查询
     *
     * @param filters 过滤条件
     * @param query   包含分页和查询条件的请求
     * @return 分页查询结果
     */
    @Override
    public IPage<T> getPageWithFilters(Map<String, Object> filters, BaseQuery<T> query) {
        QueryWrapper<T> queryWrapper = SecureQueryWrapperBuilder.buildAdvanced(query, filters, getEntityClass());
        return mapper.selectPage(query.toPage(), queryWrapper);
    }
}
