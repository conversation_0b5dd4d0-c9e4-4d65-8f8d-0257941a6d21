---
title: Trina Gateway Starter 使用指南
description: Trina Gateway Starter 组件的快速开始、配置方法和使用说明
---

# Trina Gateway Starter 使用指南

## 快速集成

### 1. 添加依赖

```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-gateway-starter</artifactId>
    <version>4.0.0</version>
</dependency>
```

### 2. 启用网关安全

```java
@SpringBootApplication
@EnableGatewaySecurity
public class GatewayApplication {
    public static void main(String[] args) {
        SpringApplication.run(GatewayApplication.class, args);
    }
}
```

### 3. 配置文件

#### 3.1 无nacos配置

```yaml
server:
  port: 8088
spring:
  application:
    name: trina-backend-gateway
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: ${ACTIVE_ENV:dev}
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss.SSS
    time-zone: Asia/Shanghai
    serialization:
      write-dates-as-timestamps: false
  cloud:
    discovery:
      enabled: false
    nacos:
      config.enabled: false
      discovery.enabled: false
    sentinel.enabled: false
    openfeign.okhttp.enabled: true
    gateway:
      server:
        webflux:
          routes:
            # 示例路由配置
            - id: baidu-service
              uri: https://www.baidu.com
              predicates:
                - Path=/api/bdu/**
logging:
  level:
    com.trinasolar.gateway: debug
    org.springframework.cloud.gateway: debug
trina:
  iam:
    # 对应iam环境地址，如测试环境：https://pdweb1.trinasolar.com
    env-url: xxx
    # 客户端ID，需要先到iam申请
    client-id: xxx
    # 客户端密钥，需要先到iam申请
    client-secret: xxx
    # 重定向url，注册在iam的应用重定向地址，可以携带path路径和参数
    redirect-url: xxx
    # 鉴权白名单路径list集合，如：/api/test/*
    white-uris:
      - xxx
```

#### 3.2 有nacos配置

本地 application.yaml 配置

```yaml
server:
  port: 8088
spring:
  application:
    name: trina-backend-gateway
    # 应用分组，用于nacos服务发现和配置
    group: ${NACOS_GROUP:DEFAULT_GROUP}
    # 应用命名空间，用于nacos服务发现和配置
    # namespace: ${spring.application.name}-${spring.profiles.active}
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: ${ACTIVE_ENV:dev}
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss.SSS
    time-zone: Asia/Shanghai
    serialization:
      write-dates-as-timestamps: false
  # nacos 基础配置
  config:
    import:
      - optional:nacos:application-${spring.profiles.active:dev}.yaml
      - optional:nacos:${spring.application.name:application}-${spring.profiles.active:dev}.yaml
  cloud:
    nacos:
      # 认证信息
      username: ${NACOS_USERNAME:nacos}
      password: ${NACOS_PASSWORD:nacos}
      # 服务发现配置
      discovery:
        server-addr: ${NACOS_HOST:127.0.0.1}:${NACOS_PORT:8848}
        # namespace会在NacosEnvironmentProcessor中自动设置
        namespace: ${spring.application.namespace:public}
        group: ${spring.application.group:DEFAULT_GROUP}
        enabled: true
        register-enabled: true
        # 心跳间隔
        heart-beat-interval: 5000
        # 心跳超时时间
        heart-beat-timeout: 15000
        # IP删除超时时间
        ip-delete-timeout: 30000
      # 配置中心配置
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        # namespace会在NacosEnvironmentProcessor中自动设置
        namespace: ${spring.cloud.nacos.discovery.namespace}
        group: ${spring.cloud.nacos.discovery.group}
        file-extension: ${NACOS_FILE_EXTENSION:yaml}
        refresh-enabled: true
        # 配置长轮询超时时间
        timeout: 3000
        # 最大重试次数
        max-retry: 10
        # 配置监听器长轮询超时时间
        config-long-poll-timeout: 46000
        # 配置重试时间
        config-retry-time: 2333
        # 启用远程同步配置
        enable-remote-sync-config: false
        # 扩展配置（应用特定配置）
        extension-configs:
          - data-id: ${spring.application.name:application}-${spring.profiles.active:dev}.${spring.cloud.nacos.config.file-extension:yaml}
            group: ${spring.application.group:DEFAULT_GROUP}
            refresh: true
        # 共享配置（通用配置）
        shared-configs:
          - data-id: common-config-${spring.profiles.active:dev}.${spring.cloud.nacos.config.file-extension:yaml}
            group: ${spring.application.group:DEFAULT_GROUP}
            refresh: true
    sentinel:
      transport:
        dashboard: localhost:8858
      eager: true
feign:
  sentinel:
    enabled: true
logging:
  level:
    com.trinasolar.gateway: debug
    org.springframework.cloud.gateway: debug
```

nacos application-dev.yaml 配置

```yaml
spring:
  cloud:
    gateway:
      server:
        webflux:
          routes:
            # 示例路由配置
            - id: baidu-service
              uri: https://www.baidu.com
              predicates:
                - Path=/api/bdu/**
trina:
  iam:
    # 对应iam环境地址，如测试环境：https://pdweb1.trinasolar.com
    env-url: xxx
    # 客户端ID，需要先到iam申请
    client-id: xxx
    # 客户端密钥，需要先到iam申请
    client-secret: xxx
    # 重定向url，注册在iam的应用重定向地址，可以携带path路径和参数
    redirect-url: xxx
    # 鉴权白名单路径list集合，如：/api/test/*
    white-uris:
      - xxx
```

## 4. 使用说明
1. 访问网关地址，会跳转到 IAM 登录页面。
2. 登录成功后，会返回到网关地址，并携带 token。
3. 网关会验证 token，并返回给客户端。
4. 客户端可以通过 token 访问受保护的资源。

