package com.trinasolar.stream.message;

import com.trinasolar.stream.enums.MessagePriority;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 统一消息实体
 *
 * <AUTHOR>
 * @since 4.0.0
 */
@Data
public class StreamMessage<T> implements Serializable {
    @Serial
    private static final long serialVersionUID = -5862462652870100912L;


    // Getters and Setters
    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 消息主题/队列名称
     */
    private String destination;

    /**
     * 消息内容
     */
    private T payload;

    /**
     * 消息头信息
     */
    private Map<String, Object> headers;

    /**
     * 消息优先级
     */
    private MessagePriority priority;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 重试次数
     */
    private int retryCount;

    /**
     * 最大重试次数
     */
    private int maxRetryCount;

    /**
     * 消息标签
     */
    private String tag;

    /**
     * 消息分组
     */
    private String group;

    /**
     * 链路追踪ID
     */
    private String traceId;

    public StreamMessage() {
        this.messageId = UUID.randomUUID().toString();
        this.headers = new HashMap<>();
        this.priority = MessagePriority.NORMAL;
        this.createTime = LocalDateTime.now();
        this.retryCount = 0;
        this.maxRetryCount = 3;
    }

    public StreamMessage(T payload) {
        this();
        this.payload = payload;
    }

    public StreamMessage(String destination, T payload) {
        this(payload);
        this.destination = destination;
    }

    /**
     * 添加消息头
     *
     * @param key   键
     * @param value 值
     * @return 当前消息实例
     */
    public StreamMessage<T> addHeader(String key, Object value) {
        this.headers.put(key, value);
        return this;
    }

    /**
     * 设置消息优先级
     *
     * @param priority 优先级
     * @return 当前消息实例
     */
    public StreamMessage<T> withPriority(MessagePriority priority) {
        this.priority = priority;
        return this;
    }

    /**
     * 设置消息标签
     *
     * @param tag 标签
     * @return 当前消息实例
     */
    public StreamMessage<T> withTag(String tag) {
        this.tag = tag;
        return this;
    }

    /**
     * 设置消息分组
     *
     * @param group 分组
     * @return 当前消息实例
     */
    public StreamMessage<T> withGroup(String group) {
        this.group = group;
        return this;
    }

    /**
     * 设置过期时间
     *
     * @param expireTime 过期时间
     * @return 当前消息实例
     */
    public StreamMessage<T> withExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
        return this;
    }

    /**
     * 设置最大重试次数
     *
     * @param maxRetryCount 最大重试次数
     * @return 当前消息实例
     */
    public StreamMessage<T> withMaxRetryCount(int maxRetryCount) {
        this.maxRetryCount = maxRetryCount;
        return this;
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount++;
    }

    /**
     * 是否可以重试
     *
     * @return true if can retry
     */
    public boolean canRetry() {
        return this.retryCount < this.maxRetryCount;
    }

    /**
     * 是否已过期
     *
     * @return true if expired
     */
    public boolean isExpired() {
        return this.expireTime != null && LocalDateTime.now().isAfter(this.expireTime);
    }
}
