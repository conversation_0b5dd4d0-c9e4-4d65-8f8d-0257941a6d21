<svg width="800" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; fill: #34495e; }
      .box { fill: #ecf0f1; stroke: #3498db; stroke-width: 2; rx: 8; }
      .arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; text-anchor: middle; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="600" fill="#ffffff" stroke="#bdc3c7" stroke-width="1"/>
  
  <!-- Title -->
  <text x="400" y="40" class="title" text-anchor="middle">Remote模块架构图</text>
  <text x="400" y="65" class="subtitle" text-anchor="middle">基于Spring 6 HTTP Interface的微服务远程调用框架</text>
  
  <!-- 注解驱动层 -->
  <rect x="50" y="100" width="200" height="80" class="box"/>
  <text x="150" y="125" class="text">注解驱动层</text>
  <text x="150" y="145" class="text">@RemoteService</text>
  <text x="150" y="165" class="text">@EnableRemoteServices</text>
  
  <!-- 服务注册与发现 -->
  <rect x="300" y="100" width="200" height="80" class="box"/>
  <text x="400" y="125" class="text">服务注册与发现</text>
  <text x="400" y="145" class="text">RemoteServiceRegistrar</text>
  <text x="400" y="165" class="text">自动扫描和注册</text>
  
  <!-- 代理生成层 -->
  <rect x="550" y="100" width="200" height="80" class="box"/>
  <text x="650" y="125" class="text">代理生成层</text>
  <text x="650" y="145" class="text">RemoteServiceFactory</text>
  <text x="650" y="165" class="text">HTTP Interface代理</text>
  
  <!-- HTTP客户端适配层 -->
  <rect x="175" y="250" width="200" height="80" class="box"/>
  <text x="275" y="275" class="text">HTTP客户端适配层</text>
  <text x="275" y="295" class="text">WebClient</text>
  <text x="275" y="315" class="text">RestClient/RestTemplate</text>
  
  <!-- 拦截器链 -->
  <rect x="425" y="250" width="200" height="80" class="box"/>
  <text x="525" y="275" class="text">拦截器链</text>
  <text x="525" y="295" class="text">HeaderPropagationInterceptor</text>
  <text x="525" y="315" class="text">LoggingInterceptor</text>
  
  <!-- 配置管理 -->
  <rect x="300" y="400" width="200" height="80" class="box"/>
  <text x="400" y="425" class="text">配置管理</text>
  <text x="400" y="445" class="text">RemoteProperties</text>
  <text x="400" y="465" class="text">RemoteAutoConfiguration</text>
  
  <!-- 远程服务 -->
  <rect x="300" y="520" width="200" height="60" class="box"/>
  <text x="400" y="545" class="text">远程微服务</text>
  <text x="400" y="565" class="text">HTTP API</text>
  
  <!-- Arrows -->
  <line x1="250" y1="140" x2="300" y2="140" class="arrow"/>
  <line x1="500" y1="140" x2="550" y2="140" class="arrow"/>
  <line x1="400" y1="180" x2="400" y2="250" class="arrow"/>
  <line x1="650" y1="180" x2="525" y2="250" class="arrow"/>
  <line x1="400" y1="330" x2="400" y2="400" class="arrow"/>
  <line x1="400" y1="480" x2="400" y2="520" class="arrow"/>
  
  <!-- Flow labels -->
  <text x="275" y="135" class="text" style="font-size: 10px;">扫描</text>
  <text x="525" y="135" class="text" style="font-size: 10px;">生成代理</text>
  <text x="415" y="215" class="text" style="font-size: 10px;">调用</text>
  <text x="415" y="365" class="text" style="font-size: 10px;">配置</text>
  <text x="415" y="505" class="text" style="font-size: 10px;">HTTP请求</text>
</svg>