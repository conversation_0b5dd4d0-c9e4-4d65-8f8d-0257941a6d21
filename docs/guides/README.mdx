---
title: 使用指南
description: Trina Common 框架各模块使用指南索引
---

# 📖 使用指南

本目录包含 Trina Common 框架各模块的详细使用指南，提供完整的配置说明、代码示例和最佳实践。

## 🚀 快速开始

### 新项目必读（推荐阅读顺序）
1. **[父模块配置](./parent-module-guide.mdx)** - 项目基础配置和依赖管理
2. **[核心功能使用](./common-core-usage-guide.mdx)** - 核心工具类和通用功能
3. **[Web开发指南](./web-usage-guide.mdx)** - Web应用开发基础

## 📊 数据访问

### 关系型数据库
- **[MyBatis使用指南](./mybatis-usage-guide.mdx)** - ORM框架使用详解
- **[多数据源配置](./datasource-usage-guide.mdx)** - 多数据源管理和切换

### NoSQL数据库
- **[Redis缓存](./redis-usage-guide.mdx)** - Redis缓存集成和使用
- **[Elasticsearch搜索](./elasticsearch-usage-guide.mdx)** - 全文搜索引擎集成

## 🌐 分布式服务

### 服务治理
- **[分布式ID](./sequence-usage-guide.mdx)** - 分布式ID生成策略
- **[分布式事务](./seata-usage-guide.mdx)** - Seata分布式事务管理

## 🔒 安全与网关

### 安全防护
- **[XSS防护](./xss-guide.mdx)** - 跨站脚本攻击防护
- **[API网关](./gateway-usage-guide.mdx)** - 网关配置和使用

### 文档与监控
- **[OpenAPI文档](./openapi-usage-guide.mdx)** - API文档自动生成

## ☁️ 云服务集成

### 存储服务
- **[对象存储](./minio-usage-guide.mdx)** - MinIO文件存储服务
- **[文件传输](./sftp-usage-guide.mdx)** - SFTP文件传输集成

### 任务调度
- **[定时任务](./xxl-job-usage-guide.mdx)** - XXL-JOB分布式任务调度

## 使用指南规范

所有使用指南遵循统一的文档结构：

1. **简介** - 模块概述和核心特性
2. **快速开始** - 基础配置和简单示例
3. **核心 API** - 主要接口和使用方法
4. **高级特性** - 进阶功能和使用场景
5. **最佳实践** - 推荐的使用方式和注意事项
6. **常见问题** - 常见问题及解决方案

## 待完成指南

1. Neo4j 使用指南
2. Security 使用指南
3. 微服务框架使用指南