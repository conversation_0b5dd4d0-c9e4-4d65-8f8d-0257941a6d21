package com.trinasolar.common.security.service;

import com.trinasolar.common.security.domain.SecurityContext;

/**
 * 权限服务接口
 * <p>
 * 提供用户权限查询和验证功能，支持角色权限和功能权限
 * <p>
 * 实现类需要根据实际的权限管理系统来获取用户的角色和权限信息
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface PermissionService {

    /**
     * 获取用户权限
     *
     * @param userId 用户ID
     * @param token  token
     * @return 角色列表
     */
    SecurityContext.Authorization getUserPermissions(String token, String userId);

    /**
     * 刷新用户权限缓存
     *
     * @param userId 用户ID
     */
    void refreshUserPermissions(String userId);
}
