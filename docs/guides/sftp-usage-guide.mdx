---
title: SFTP 模块使用指南
description: Trina SFTP 模块的使用方法与最佳实践
---

# SFTP文件传输模块

`trina-sftp-starter` 提供了基于SFTP协议的文件传输功能，支持文件上传、下载、目录操作等功能，为应用程序提供安全可靠的文件传输服务。

## 功能概述

SFTP模块封装了SFTP协议的常用操作，提供了简单易用的API接口，支持多服务器配置和连接池管理。

### 主要功能

- **文件上传**：支持单文件和批量文件上传
- **文件下载**：支持文件下载和流式下载
- **目录操作**：创建、删除、列表目录
- **文件管理**：重命名、删除、移动文件
- **权限控制**：文件权限设置和检查
- **连接池**：SFTP连接池管理
- **多服务器**：支持多SFTP服务器配置
- **断点续传**：支持大文件断点续传

## 使用方法

### Maven 依赖

```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-sftp-starter</artifactId>
    <version>${trina.version}</version>
</dependency>
```

### 基本配置

```yaml
# application.yml
trina:
  sftp:
    enabled: true
    # 默认服务器配置
    default:
      host: *************
      port: 22
      username: sftpuser
      password: password
      # 或使用私钥认证
      # private-key-path: /path/to/private/key
      # passphrase: key_passphrase
      timeout: 30000
      charset: UTF-8
      # 连接池配置
      pool:
        max-total: 10
        max-idle: 5
        min-idle: 1
        max-wait-millis: 10000
    # 多服务器配置
    servers:
      backup:
        host: *************
        port: 22
        username: backup
        password: backup123
        timeout: 30000
      archive:
        host: *************
        port: 22
        username: archive
        private-key-path: /etc/ssh/archive_key
        timeout: 30000
```

### 基本使用

```java
@Service
public class FileTransferService {
    
    @Autowired
    private SftpTemplate sftpTemplate;
    
    /**
     * 上传文件
     */
    public boolean uploadFile(String localFilePath, String remoteFilePath) {
        try {
            return sftpTemplate.upload(localFilePath, remoteFilePath);
        } catch (Exception e) {
            log.error("文件上传失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 下载文件
     */
    public boolean downloadFile(String remoteFilePath, String localFilePath) {
        try {
            return sftpTemplate.download(remoteFilePath, localFilePath);
        } catch (Exception e) {
            log.error("文件下载失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 列出目录文件
     */
    public List<SftpFileInfo> listFiles(String remotePath) {
        try {
            return sftpTemplate.listFiles(remotePath);
        } catch (Exception e) {
            log.error("列出文件失败: {}", e.getMessage());
            return Collections.emptyList();
        }
    }
    
    /**
     * 删除文件
     */
    public boolean deleteFile(String remoteFilePath) {
        try {
            return sftpTemplate.delete(remoteFilePath);
        } catch (Exception e) {
            log.error("删除文件失败: {}", e.getMessage());
            return false;
        }
    }
} 