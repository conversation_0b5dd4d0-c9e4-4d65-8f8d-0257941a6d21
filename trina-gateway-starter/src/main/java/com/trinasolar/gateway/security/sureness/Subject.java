package com.trinasolar.gateway.security.sureness;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025-07-08 17:55
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Subject {

    /**
     * remote ip
     **/
    private String remoteHost;

    /**
     * remote device
     **/
    private String userAgent;

    /**
     * the uri resource which this user want access
     * <p>
     * for example: /api/v2/book===get
     **/
    private String targetUri;

    /**
     * token
     **/
    private String token;

    /**
     * app id
     **/
    private String appId;

    /**
     * the roles which this user owned
     **/
    private List<String> roles;

    /**
     * the Roles which can access this resource above-targetUri
     **/
    private List<String> supportRoles;

    /**
     * user principal, such as userId, username, etc.
     */
    private String principal;

    /**
     * the map for principal, add your custom principal
     **/
    private Map<String, Object> principalMap;
}
