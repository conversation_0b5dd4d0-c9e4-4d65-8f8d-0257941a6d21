# S3Template 模块说明文档

## 1. 模块概述

S3Template 是 trina-minio-starter 模块的一部分，提供了基于 AWS SDK v2 的 S3 协议对象存储操作功能。它实现了与 MinioTemplate 相同的 FileTemplate 接口，使应用程序可以无缝切换使用 Minio 或标准 S3 协议的对象存储服务。

## 2. 功能特性

S3Template 提供以下主要功能：

### 2.1 存储桶操作
- 创建存储桶
- 获取所有存储桶列表
- 获取指定名称的存储桶
- 删除存储桶（包括递归删除桶内所有对象）

### 2.2 文件操作
- 根据前缀列出对象（支持递归和非递归模式）
- 获取对象内容（以 InputStream 形式）
- 上传对象（支持普通上传和强制覆盖）
- 获取对象元数据信息
- 删除对象
- 生成对象 URL 和预签名 URL

## 3. 配置说明

使用 `file.storage` 前缀配置存储服务，支持多种模式（s3、minio等）：

```yaml
file:
  storage:
    mode: s3 # 或 minio
    endpoint: http://localhost:9000
    region: us-east-1
    accessKey: test
    secretKey: Ab123456
    bucketName: test-bucket
    pathStyleAccess: true
    connectionTimeout: 10000
```

## 4. 使用方法

### 4.1 配置依赖

确保项目中已添加以下依赖：

```xml
<dependency>
    <groupId>software.amazon.awssdk</groupId>
    <artifactId>s3</artifactId>
</dependency>
<dependency>
    <groupId>software.amazon.awssdk</groupId>
    <artifactId>s3-presigner</artifactId>
</dependency>
```

### 4.2 启用 S3 模式

在 application.yml 中配置：

```yaml
file:
  server:
    type: s3  # 使用 S3 模式，可选值：minio, s3
```

### 4.3 代码示例

#### 注入 FileTemplate 接口

```java
@Autowired
private FileTemplate fileTemplate;  // 根据配置自动注入 MinioTemplate 或 S3Template
```

#### 存储桶操作

```java
// 创建存储桶
fileTemplate.createBucket("my-bucket");

// 获取所有存储桶
List<Bucket> buckets = fileTemplate.getAllBuckets();

// 获取指定存储桶
Optional<Bucket> bucket = fileTemplate.getBucket("my-bucket");

// 删除存储桶
fileTemplate.removeBucket("my-bucket");
```

#### 文件操作

```java
// 列出对象
List<MinioItem> items = fileTemplate.getAllObjectsByPrefix("my-bucket", "prefix/", true);

// 获取对象
InputStream is = fileTemplate.getObject("my-bucket", "path/to/object.txt");

// 上传对象
FileResult result = fileTemplate.putObject("my-bucket", "path/to/object.txt", inputStream);

// 获取对象信息
StatObjectResponse info = fileTemplate.getObjectInfo("my-bucket", "path/to/object.txt");

// 删除对象
fileTemplate.removeObject("my-bucket", "path/to/object.txt");

// 获取对象URL
String url = fileTemplate.getObjectURL("my-bucket", "path/to/object.txt", 3600); // 有效期1小时
```

## 5. 实现原理

S3Template 基于 AWS SDK v2 实现，主要使用以下组件：

- `S3Client`: 用于执行基本的 S3 操作（创建/删除桶、上传/下载/删除对象等）
- `S3Presigner`: 用于生成预签名 URL

为了与 MinioTemplate 保持接口兼容，S3Template 使用了反射机制将 AWS SDK 的对象适配为 Minio SDK 的对象：

- `S3BucketAdapter`: 将 AWS SDK 的 `Bucket` 对象适配为 Minio 的 `Bucket` 对象
- 使用反射创建 Minio 的 `StatObjectResponse` 对象

## 6. 注意事项

1. S3Template 和 MinioTemplate 实现了相同的 FileTemplate 接口，但底层使用不同的 SDK
2. S3Template 使用 AWS SDK v2，而 MinioTemplate 使用 Minio SDK
3. 两者的配置参数略有不同，请参考各自的配置说明
4. 切换存储实现时，只需修改配置文件中的 `file.server.type` 值即可

## 7. 常见问题

### 7.1 路径样式访问与虚拟主机样式访问

S3 支持两种访问方式：
- 路径样式：`http://s3.amazonaws.com/bucket-name/key-name`
- 虚拟主机样式：`http://bucket-name.s3.amazonaws.com/key-name`

通过 `path-style-access` 配置项控制，默认为 `true`（路径样式）。

### 7.2 兼容性说明

S3Template 设计为与任何兼容 S3 协议的对象存储服务兼容，包括：
- Amazon S3
- MinIO
- Ceph
- 阿里云 OSS
- 腾讯云 COS
- 华为云 OBS
- 等其他兼容 S3 协议的存储服务

## 8. 测试

S3Template 提供了完整的单元测试，覆盖了所有主要功能，包括：
- 存储桶操作测试
- 对象操作测试
- URL 生成测试

可以通过运行 `S3TemplateTest` 类来验证功能的正确性。
