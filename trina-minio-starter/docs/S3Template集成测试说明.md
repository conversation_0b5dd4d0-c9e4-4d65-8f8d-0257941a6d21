# S3Template 集成测试说明文档

## 概述

本文档描述了 `trina-minio-starter` 模块中 `S3Template` 类的集成测试实现。与之前使用 Mockito 模拟的单元测试不同，现在的测试使用真实的 S3 兼容服务（如 MinIO 或 Amazon S3）进行测试，确保所有 S3 操作在实际环境中能够正确工作。

## 测试环境配置

### 配置文件

集成测试使用 `application-test.yml` 配置文件，该文件位于 `src/test/resources` 目录下。配置文件包含连接真实 S3 兼容服务所需的参数：

```yaml
spring:
  application:
    name: trina-minio-starter-test

s3:
  endpoint: https://shenben.club  # 替换为您的S3兼容服务地址
  region: us-east-1              # 替换为您的区域
  accessKey: test                # 替换为您的访问密钥
  secretKey: Ab123456           # 替换为您的秘密密钥
  bucketName: test-bucket       # 默认存储桶名称
  pathStyleAccess: true         # 使用路径样式访问
  connectionTimeout: 10000      # 连接超时时间(毫秒)
```

### 本地测试环境搭建

如果您没有可用的S3兼容服务，可以使用Docker运行MinIO作为本地测试环境：

```bash
# 创建docker-compose.yml文件
cat > docker-compose.yml << EOF
version: '3'
services:
  minio:
    image: minio/minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: test
      MINIO_ROOT_PASSWORD: Ab123456
    command: server /data --console-address ":9001"
    volumes:
      - ./data:/data
EOF

# 启动MinIO服务
docker-compose up -d
```

然后修改`application-test.yml`中的endpoint为：

```yaml
s3:
  endpoint: http://localhost:9000
  # 其他配置保持不变
```

## 测试内容

集成测试覆盖了以下功能：

1. **存储桶操作**
   - 创建存储桶 (`testCreateBucket`)
   - 获取所有存储桶 (`testGetAllBuckets`)
   - 获取指定存储桶 (`testGetBucket`)
   - 删除桶 (`testRemoveBucket`)

2. **对象操作**
   - 上传对象 (`testPutObject`)
   - 获取对象 (`testGetObject`)
   - 获取对象信息 (`testGetObjectInfo`)
   - 删除对象 (`testRemoveObject`)
   - 批量删除对象 (`testRemoveObjects`)
   - 上传对象（含元数据）
   - 下载对象
   - 删除单个对象
   - 批量删除对象
   - 生成对象 URL
   - 生成预签名 URL

3. **异常处理**
   - 获取不存在的对象
   - 获取不存在的对象信息

## 测试设计特点

1. **真实环境测试**：所有测试都针对真实的 S3 兼容服务执行，确保功能在实际环境中正常工作。

2. **自动化设置与清理**：测试使用 `@BeforeEach` 和 `@AfterEach` 注解自动创建和清理测试资源。

3. **唯一对象名**：使用 UUID 生成唯一的对象名，避免测试之间的冲突。

4. **完整断言**：每个测试都包含完整的结果验证，确保操作成功且结果符合预期。

5. **异常测试**：包含对异常情况的测试，验证错误处理逻辑。

## 运行测试

要运行集成测试，可以使用以下命令：

```bash
# 运行所有测试
mvn test

# 只运行 S3TemplateTest
mvn -Dtest=S3TemplateTest test
```

## 注意事项

1. 运行测试前，确保 `application-test.yml` 中配置的 S3 兼容服务可用。

2. 测试会创建和删除对象及桶，请不要在生产环境的重要桶上运行测试。

3. 如果测试失败，可能需要手动清理创建的测试资源。

4. 测试使用的默认桶是 `test-bucket`，请确保该桶存在或测试有权限创建该桶。

## 未来改进

1. 添加更多边缘情况的测试，如大文件上传、并发操作等。

2. 实现测试隔离，为每次测试运行创建唯一的测试桶。

3. 添加性能测试，验证在不同负载下的表现。

4. 集成到 CI/CD 流程，确保每次代码变更都通过集成测试。
