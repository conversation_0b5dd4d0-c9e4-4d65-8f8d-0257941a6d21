# 简化的Dockerfile - 使用预构建的静态文件
FROM nginx:alpine

# 安装必要的工具
RUN apk add --no-cache curl

# 复制自定义 nginx 配置
COPY nginx.conf /etc/nginx/nginx.conf
COPY default.conf /etc/nginx/conf.d/default.conf

# 复制本地预构建的静态文件到 nginx 目录
COPY build /usr/share/nginx/html

# 创建日志目录
RUN mkdir -p /var/log/nginx

# 设置权限
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/log/nginx

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/wiki/ || exit 1

# 启动 nginx
CMD ["nginx", "-g", "daemon off;"]