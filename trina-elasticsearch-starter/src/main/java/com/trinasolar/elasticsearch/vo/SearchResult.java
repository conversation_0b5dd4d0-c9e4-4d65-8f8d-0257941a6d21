package com.trinasolar.elasticsearch.vo;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

/**
 * 搜索结果值对象
 * 用于封装Elasticsearch搜索结果
 *
 * @param <T> 文档类型
 */
public class SearchResult<T> {

    /**
     * 总命中数
     */
    private long totalHits;

    /**
     * 总命中数关系（等于、大于等于）
     */
    private String totalHitsRelation;

    /**
     * 最大分数
     */
    private Float maxScore;

    /**
     * 命中列表
     */
    private List<SearchHit<T>> hits;

    /**
     * 聚合结果
     */
    private Map<String, Object> aggregations;

    /**
     * 执行时间（毫秒）
     */
    private long took;

    /**
     * 分片信息
     */
    private ShardInfo shardInfo;

    /**
     * 文档列表（简化版本，直接包含源数据）
     */
    private List<T> documents;

    /**
     * 高亮结果（简化版本）
     */
    private Map<String, List<String>> highlights;

    /**
     * 记录列表（兼容BaseEsServiceImpl）
     */
    private List<T> records;

    /**
     * 总记录数（兼容BaseEsServiceImpl）
     */
    private Long total;

    /**
     * 当前页记录数
     */
    private Integer size;

    /**
     * 起始位置
     */
    private Integer from;

    /**
     * 查询耗时(毫秒)
     */
    private Long tookInMillis;

    /**
     * 是否超时
     */
    private Boolean timedOut;

    /**
     * 滚动ID(用于滚动查询)
     */
    private String scrollId;

    /**
     * 建议结果
     */
    private Map<String, Object> suggest;

    /**
     * 获取总命中数
     *
     * @return 总命中数
     */
    public long getTotalHits() {
        return totalHits;
    }

    /**
     * 设置总命中数
     *
     * @param totalHits 总命中数
     */
    public void setTotalHits(long totalHits) {
        this.totalHits = totalHits;
    }

    /**
     * 获取总命中数关系
     *
     * @return 总命中数关系
     */
    public String getTotalHitsRelation() {
        return totalHitsRelation;
    }

    /**
     * 设置总命中数关系
     *
     * @param totalHitsRelation 总命中数关系
     */
    public void setTotalHitsRelation(String totalHitsRelation) {
        this.totalHitsRelation = totalHitsRelation;
    }

    /**
     * 获取最大分数
     *
     * @return 最大分数
     */
    public Float getMaxScore() {
        return maxScore;
    }

    /**
     * 设置最大分数
     *
     * @param maxScore 最大分数
     */
    public void setMaxScore(Float maxScore) {
        this.maxScore = maxScore;
    }

    /**
     * 获取命中列表
     *
     * @return 命中列表
     */
    public List<SearchHit<T>> getHits() {
        return hits;
    }

    /**
     * 设置命中列表
     *
     * @param hits 命中列表
     */
    public void setHits(List<SearchHit<T>> hits) {
        this.hits = hits;
    }

    /**
     * 获取聚合结果
     *
     * @return 聚合结果
     */
    public Map<String, Object> getAggregations() {
        return aggregations;
    }

    /**
     * 设置聚合结果
     *
     * @param aggregations 聚合结果
     */
    public void setAggregations(Map<String, Object> aggregations) {
        this.aggregations = aggregations;
    }

    /**
     * 获取执行时间
     *
     * @return 执行时间
     */
    public long getTook() {
        return took;
    }

    /**
     * 设置执行时间
     *
     * @param took 执行时间
     */
    public void setTook(long took) {
        this.took = took;
    }

    /**
     * 获取分片信息
     *
     * @return 分片信息
     */
    public ShardInfo getShardInfo() {
        return shardInfo;
    }

    /**
     * 设置分片信息
     *
     * @param shardInfo 分片信息
     */
    public void setShardInfo(ShardInfo shardInfo) {
        this.shardInfo = shardInfo;
    }

    /**
     * 获取文档列表
     *
     * @return 文档列表
     */
    public List<T> getDocuments() {
        return documents;
    }

    /**
     * 设置文档列表
     *
     * @param documents 文档列表
     */
    public void setDocuments(List<T> documents) {
        this.documents = documents;
    }

    /**
     * 获取高亮结果
     *
     * @return 高亮结果
     */
    public Map<String, List<String>> getHighlights() {
        return highlights;
    }

    /**
     * 设置高亮结果
     *
     * @param highlights 高亮结果
     */
    public void setHighlights(Map<String, List<String>> highlights) {
        this.highlights = highlights;
    }

    /**
     * 搜索命中项
     *
     * @param <T> 文档类型
     */
    public static class SearchHit<T> {
        /**
         * 索引名称
         */
        @JsonProperty("_index")
        private String index;

        /**
         * 文档ID
         */
        @JsonProperty("_id")
        private String id;

        /**
         * 文档分数
         */
        @JsonProperty("_score")
        private Float score;

        /**
         * 文档源数据
         */
        @JsonProperty("_source")
        private T source;

        /**
         * 高亮结果
         */
        private Map<String, List<String>> highlight;

        /**
         * 获取索引名称
         *
         * @return 索引名称
         */
        public String getIndex() {
            return index;
        }

        /**
         * 设置索引名称
         *
         * @param index 索引名称
         */
        public void setIndex(String index) {
            this.index = index;
        }

        /**
         * 获取文档ID
         *
         * @return 文档ID
         */
        public String getId() {
            return id;
        }

        /**
         * 设置文档ID
         *
         * @param id 文档ID
         */
        public void setId(String id) {
            this.id = id;
        }

        /**
         * 获取文档分数
         *
         * @return 文档分数
         */
        public Float getScore() {
            return score;
        }

        /**
         * 设置文档分数
         *
         * @param score 文档分数
         */
        public void setScore(Float score) {
            this.score = score;
        }

        /**
         * 获取文档源数据
         *
         * @return 文档源数据
         */
        public T getSource() {
            return source;
        }

        /**
         * 设置文档源数据
         *
         * @param source 文档源数据
         */
        public void setSource(T source) {
            this.source = source;
        }

        /**
         * 获取高亮结果
         *
         * @return 高亮结果
         */
        public Map<String, List<String>> getHighlight() {
            return highlight;
        }

        /**
         * 设置高亮结果
         *
         * @param highlight 高亮结果
         */
        public void setHighlight(Map<String, List<String>> highlight) {
            this.highlight = highlight;
        }
    }

    /**
     * 分片信息
     */
    public static class ShardInfo {
        /**
         * 总分片数
         */
        private int total;

        /**
         * 成功分片数
         */
        private int successful;

        /**
         * 跳过分片数
         */
        private int skipped;

        /**
         * 失败分片数
         */
        private int failed;

        /**
         * 获取总分片数
         *
         * @return 总分片数
         */
        public int getTotal() {
            return total;
        }

        /**
         * 设置总分片数
         *
         * @param total 总分片数
         */
        public void setTotal(int total) {
            this.total = total;
        }

        /**
         * 获取成功分片数
         *
         * @return 成功分片数
         */
        public int getSuccessful() {
            return successful;
        }

        /**
         * 设置成功分片数
         *
         * @param successful 成功分片数
         */
        public void setSuccessful(int successful) {
            this.successful = successful;
        }

        /**
         * 获取跳过分片数
         *
         * @return 跳过分片数
         */
        public int getSkipped() {
            return skipped;
        }

        /**
         * 设置跳过分片数
         *
         * @param skipped 跳过分片数
         */
        public void setSkipped(int skipped) {
            this.skipped = skipped;
        }

        /**
         * 获取失败分片数
         *
         * @return 失败分片数
         */
        public int getFailed() {
            return failed;
        }

        /**
         * 设置失败分片数
         *
         * @param failed 失败分片数
         */
        public void setFailed(int failed) {
            this.failed = failed;
        }
    }

    // ==================== 新增字段的 getter/setter 方法 ====================

    /**
     * 获取记录列表
     *
     * @return 记录列表
     */
    public List<T> getRecords() {
        return records;
    }

    /**
     * 设置记录列表
     *
     * @param records 记录列表
     */
    public void setRecords(List<T> records) {
        this.records = records;
    }

    /**
     * 获取总记录数
     *
     * @return 总记录数
     */
    public Long getTotal() {
        return total;
    }

    /**
     * 设置总记录数
     *
     * @param total 总记录数
     */
    public void setTotal(Long total) {
        this.total = total;
    }

    /**
     * 获取当前页记录数
     *
     * @return 当前页记录数
     */
    public Integer getSize() {
        return size;
    }

    /**
     * 设置当前页记录数
     *
     * @param size 当前页记录数
     */
    public void setSize(Integer size) {
        this.size = size;
    }

    /**
     * 获取起始位置
     *
     * @return 起始位置
     */
    public Integer getFrom() {
        return from;
    }

    /**
     * 设置起始位置
     *
     * @param from 起始位置
     */
    public void setFrom(Integer from) {
        this.from = from;
    }

    /**
     * 获取查询耗时
     *
     * @return 查询耗时(毫秒)
     */
    public Long getTookInMillis() {
        return tookInMillis;
    }

    /**
     * 设置查询耗时
     *
     * @param tookInMillis 查询耗时(毫秒)
     */
    public void setTookInMillis(Long tookInMillis) {
        this.tookInMillis = tookInMillis;
    }

    /**
     * 获取是否超时
     *
     * @return 是否超时
     */
    public Boolean getTimedOut() {
        return timedOut;
    }

    /**
     * 设置是否超时
     *
     * @param timedOut 是否超时
     */
    public void setTimedOut(Boolean timedOut) {
        this.timedOut = timedOut;
    }

    /**
     * 获取滚动ID
     *
     * @return 滚动ID
     */
    public String getScrollId() {
        return scrollId;
    }

    /**
     * 设置滚动ID
     *
     * @param scrollId 滚动ID
     */
    public void setScrollId(String scrollId) {
        this.scrollId = scrollId;
    }

    /**
     * 获取建议结果
     *
     * @return 建议结果
     */
    public Map<String, Object> getSuggest() {
        return suggest;
    }

    /**
     * 设置建议结果
     *
     * @param suggest 建议结果
     */
    public void setSuggest(Map<String, Object> suggest) {
        this.suggest = suggest;
    }

    // ==================== 工具方法 ====================

    /**
     * 检查是否有结果
     *
     * @return 是否有结果
     */
    public boolean hasResults() {
        return records != null && !records.isEmpty();
    }

    /**
     * 检查是否有聚合结果
     *
     * @return 是否有聚合结果
     */
    public boolean hasAggregations() {
        return aggregations != null && !aggregations.isEmpty();
    }

    /**
     * 检查是否有建议结果
     *
     * @return 是否有建议结果
     */
    public boolean hasSuggest() {
        return suggest != null && !suggest.isEmpty();
    }

    /**
     * 检查是否支持滚动查询
     *
     * @return 是否支持滚动查询
     */
    public boolean hasScrollId() {
        return scrollId != null && !scrollId.trim().isEmpty();
    }

    /**
     * 创建空结果
     *
     * @param <T> 文档类型
     * @return 空结果
     */
    public static <T> SearchResult<T> empty() {
        SearchResult<T> result = new SearchResult<>();
        result.setRecords(List.of());
        result.setTotal(0L);
        result.setSize(0);
        result.setFrom(0);
        result.setMaxScore(0.0f);
        result.setTookInMillis(0L);
        result.setTimedOut(false);
        return result;
    }

    /**
     * 创建简单结果
     *
     * @param <T> 文档类型
     * @param records 记录列表
     * @param total 总记录数
     * @return 搜索结果
     */
    public static <T> SearchResult<T> of(List<T> records, Long total) {
        SearchResult<T> result = new SearchResult<>();
        result.setRecords(records);
        result.setTotal(total);
        result.setSize(records != null ? records.size() : 0);
        result.setFrom(0);
        return result;
    }

    /**
     * 创建分页结果
     *
     * @param <T> 文档类型
     * @param records 记录列表
     * @param total 总记录数
     * @param from 起始位置
     * @param size 页面大小
     * @return 搜索结果
     */
    public static <T> SearchResult<T> of(List<T> records, Long total, Integer from, Integer size) {
        SearchResult<T> result = new SearchResult<>();
        result.setRecords(records);
        result.setTotal(total);
        result.setSize(records != null ? records.size() : 0);
        result.setFrom(from);
        return result;
    }
}
