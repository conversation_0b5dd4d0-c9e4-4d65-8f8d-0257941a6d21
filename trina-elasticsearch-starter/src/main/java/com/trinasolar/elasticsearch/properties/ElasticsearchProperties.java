package com.trinasolar.elasticsearch.properties;

import lombok.Data;

import java.util.List;

/**
 * Elasticsearch配置属性
 * 不再使用@ConfigurationProperties自动绑定，改为手动创建Bean
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
public class ElasticsearchProperties {

    /**
     * Elasticsearch主机地址列表
     * 格式: ["localhost:9200", "host2:9200"]
     */
    private List<String> hosts = List.of("localhost:9200");

    /**
     * 用户名
     */
    private String username = "elastic";

    /**
     * 密码
     */
    private String password = "elastic";

    /**
     * 连接超时时间（毫秒）
     */
    private int connectionTimeout = 5000;

    /**
     * Socket超时时间（毫秒）
     */
    private int socketTimeout = 60000;

    /**
     * 是否启用SSL
     */
    private boolean sslEnabled = false;

    /**
     * SSL证书路径
     */
    private String sslCertificatePath;

    /**
     * 向量化配置
     */
    private VectorConfig vector = new VectorConfig();

    @Data
    public static class VectorConfig {
        /**
         * Ollama API地址
         */
        private String ollamaUrl = "http://localhost:11434";

        /**
         * 向量化模型名称
         */
        private String model = "bge-m3";

        /**
         * 向量维度
         */
        private int dimensions = 1024;

        /**
         * 请求超时时间（毫秒）
         */
        private int timeout = 30000;
    }
}
