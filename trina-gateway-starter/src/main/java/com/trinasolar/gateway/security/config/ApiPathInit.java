package com.trinasolar.gateway.security.config;


import com.github.benmanes.caffeine.cache.Cache;
import com.trinasolar.common.security.properties.SecurityProperties;
import com.trinasolar.gateway.security.sureness.matcher.DefaultPathRoleMatcher;
import com.trinasolar.gateway.security.sureness.provider.RemotePathTreeProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.web.reactive.function.client.WebClient;

/**
 *  网关启动初始化API PATH 权限
 *
 * <AUTHOR>
 * @since 2025-07-28 18:28
 **/
@Slf4j
@RequiredArgsConstructor
public class ApiPathInit implements CommandLineRunner {

    private final SecurityProperties securityProperties;
    private final WebClient webClient;
    private final Cache<String, Object> iamCache;

    @Override
    public void run(String... args) {
        this.init();
    }

    /**
     * 初始化
     *
     */
    public void init() {
        // pathRoleMatcher init
        // pathTree resource provider
        RemotePathTreeProvider pathTreeProvider = new RemotePathTreeProvider(securityProperties, webClient, iamCache);
        DefaultPathRoleMatcher pathRoleMatcher = getInstance();
        pathRoleMatcher.setPathTreeProvider(pathTreeProvider);
        pathRoleMatcher.buildTree();
        if (log.isDebugEnabled()) {
            log.debug("DefaultPathRoleMatcher init");
        }
    }

    /**
     * singleton
     * <AUTHOR>
     * @date 15:30 2019-03-10
     */
    private static class SingleDefaultSecurityManager {
        private static final DefaultPathRoleMatcher INSTANCE = new DefaultPathRoleMatcher();
    }

    public static DefaultPathRoleMatcher getInstance() {
        return SingleDefaultSecurityManager.INSTANCE;
    }
}
