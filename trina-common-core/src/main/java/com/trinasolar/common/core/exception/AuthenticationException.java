package com.trinasolar.common.core.exception;

import java.io.Serial;

/**
 * 认证异常
 * <p>
 * 用于token验证失败时抛出的异常
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
public class AuthenticationException extends RuntimeException {


    @Serial
    private static final long serialVersionUID = 5841660990211935104L;

    public AuthenticationException() {
        super();
    }

    public AuthenticationException(String message) {
        super(message);
    }

    public AuthenticationException(String message, Throwable cause) {
        super(message, cause);
    }

    public AuthenticationException(Throwable cause) {
        super(cause);
    }
}
