package com.trinasolar.elasticsearch.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.ArrayList;
import java.util.HashMap;

/**
 * 文档实体类
 * 用于表示索引中的文档结构，支持复杂的文档内容解析和存储
 * 重构后支持更丰富的文档元数据和内容结构
 * 
 * <AUTHOR>
 * @since 2024-03-21
 * @version 2.0.0
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Document {

    /**
     * 文档ID
     */
    @JsonProperty("id")
    private String id;

    /**
     * 文档标题
     */
    @JsonProperty("title")
    private String title;

    /**
     * 文档作者
     */
    @JsonProperty("author")
    private String author;

    /**
     * 文档标签列表
     */
    @JsonProperty("tag")
    private List<String> tag;

    /**
     * 文档描述/摘要
     */
    @JsonProperty("description")
    private String description;

    /**
     * 文档完整内容
     */
    @JsonProperty("contents")
    private String contents;

    /**
     * 文档类型（如：markdown, text, html等）
     */
    @JsonProperty("document_type")
    private String documentType;

    /**
     * 文档分类（如：技术文档、需求文档、设计文档等）
     */
    @JsonProperty("category")
    private String category;

    /**
     * 文档状态（如：草稿、审核中、已发布等）
     */
    @JsonProperty("status")
    private String status;

    /**
     * 文档版本号
     */
    @JsonProperty("version")
    private String version;

    /**
     * 文档语言
     */
    @JsonProperty("language")
    private String language;

    /**
     * 文档字数统计
     */
    @JsonProperty("word_count")
    private Integer wordCount;

    /**
     * 文档章节结构（标题层级）
     */
    @JsonProperty("sections")
    private List<String> sections;

    /**
     * 文档关键词
     */
    @JsonProperty("keywords")
    private List<String> keywords;

    /**
     * 文档元数据（扩展字段）
     */
    @JsonProperty("metadata")
    private Map<String, Object> metadata;

    /**
     * 创建时间
     */
    @JsonProperty("created_time")
    private String createdTime;

    /**
     * 更新时间
     */
    @JsonProperty("updated_time")
    private String updatedTime;

    /**
     * 默认构造函数
     */
    public Document() {
        this.createdTime = LocalDateTime.now().toString();
        this.updatedTime = LocalDateTime.now().toString();
        this.documentType = "markdown";
        this.status = "draft";
        this.language = "zh-CN";
        this.version = "1.0.0";
    }

    /**
     * 基础构造函数
     * 
     * @param title 标题
     * @param author 作者
     * @param tag 标签
     * @param description 描述
     * @param contents 内容
     */
    public Document(String title, String author, List<String> tag, String description, String contents) {
        this();
        this.title = title;
        this.author = author;
        this.tag = tag;
        this.description = description;
        this.contents = contents;
        this.wordCount = contents != null ? contents.length() : 0;
    }

    /**
     * 完整构造函数
     * 
     * @param title 标题
     * @param author 作者
     * @param tag 标签
     * @param description 描述
     * @param contents 内容
     * @param documentType 文档类型
     * @param category 文档分类
     * @param status 文档状态
     */
    public Document(String title, String author, List<String> tag, String description, 
                   String contents, String documentType, String category, String status) {
        this(title, author, tag, description, contents);
        this.documentType = documentType;
        this.category = category;
        this.status = status;
    }

    // Getter和Setter方法
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public List<String> getTag() {
        return tag;
    }

    public void setTag(List<String> tag) {
        this.tag = tag;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getContents() {
        return contents;
    }

    public void setContents(String contents) {
        this.contents = contents;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getDocumentType() {
        return documentType;
    }

    public void setDocumentType(String documentType) {
        this.documentType = documentType;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public Integer getWordCount() {
        return wordCount;
    }

    public void setWordCount(Integer wordCount) {
        this.wordCount = wordCount;
    }

    public List<String> getSections() {
        return sections;
    }

    public void setSections(List<String> sections) {
        this.sections = sections;
    }

    public List<String> getKeywords() {
        return keywords;
    }

    public void setKeywords(List<String> keywords) {
        this.keywords = keywords;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    /**
     * 更新文档内容并重新计算字数
     * 
     * @param contents 新的文档内容
     */
    public void updateContents(String contents) {
        this.contents = contents;
        this.wordCount = contents != null ? contents.length() : 0;
        this.updatedTime = LocalDateTime.now().toString();
    }

    /**
     * 添加标签
     * 
     * @param newTag 新标签
     */
    public void addTag(String newTag) {
        if (this.tag != null && !this.tag.contains(newTag)) {
            this.tag.add(newTag);
        }
    }

    /**
     * 添加关键词
     * 
     * @param keyword 新关键词
     */
    public void addKeyword(String keyword) {
        if (this.keywords != null && !this.keywords.contains(keyword)) {
            this.keywords.add(keyword);
        }
    }

    /**
     * 检查文档是否为空
     * 
     * @return 如果文档内容为空返回true
     */
    public boolean isEmpty() {
        return contents == null || contents.trim().isEmpty();
    }

    /**
     * 获取文档摘要（前200个字符）
     * 
     * @return 文档摘要
     */
    public String getSummary() {
        if (contents == null || contents.isEmpty()) {
            return description != null ? description : "";
        }
        return contents.length() > 200 ? contents.substring(0, 200) + "..." : contents;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Document document = (Document) o;
        return Objects.equals(id, document.id) &&
               Objects.equals(title, document.title) &&
               Objects.equals(author, document.author);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, title, author);
    }

    @Override
    public String toString() {
        return "Document{" +
                "id='" + id + '\'' +
                ", title='" + title + '\'' +
                ", author='" + author + '\'' +
                ", tag=" + tag +
                ", description='" + description + '\'' +
                ", documentType='" + documentType + '\'' +
                ", category='" + category + '\'' +
                ", status='" + status + '\'' +
                ", version='" + version + '\'' +
                ", language='" + language + '\'' +
                ", wordCount=" + wordCount +
                ", sections=" + sections +
                ", keywords=" + keywords +
                ", createdTime='" + createdTime + '\'' +
                ", updatedTime='" + updatedTime + '\'' +
                '}';
    }
}