package com.trinasolar.sentinel;

import com.alibaba.cloud.sentinel.annotation.SentinelRestTemplate;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * Sentinel自动配置类
 * 用于配置Sentinel的基本设置，如开启注解支持等
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@ConditionalOnProperty(prefix = "spring.cloud.sentinel", name = "enabled", havingValue = "true", matchIfMissing = true)
public class SentinelAutoConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(SentinelAutoConfiguration.class);

    public SentinelAutoConfiguration() {
        logger.info("🛡️ Sentinel自动配置已启用，使用Spring Cloud Alibaba官方配置");
    }

    /**
     * 创建一个支持Sentinel的RestTemplate
     * 使用@SentinelRestTemplate注解，可以对RestTemplate的调用进行限流和熔断
     */
    @Bean
    @SentinelRestTemplate(
            blockHandler = "handleBlock",
            blockHandlerClass = SentinelAutoConfiguration.class,
            fallback = "handleFallback",
            fallbackClass = SentinelAutoConfiguration.class
    )
    public RestTemplate sentinelRestTemplate() {
        logger.info("✅ 已创建支持Sentinel的RestTemplate");
        return new RestTemplate();
    }

    /**
     * RestTemplate限流处理方法
     * 当RestTemplate调用被限流时，会调用此方法
     */
    public static ClientHttpResponse handleBlock(HttpRequest request, byte[] body,
                                                 ClientHttpRequestExecution execution, BlockException ex) {
        logger.warn("⚠️ RestTemplate调用被限流: {}", request.getURI());
        return new SentinelClientHttpResponse("{\"code\":429,\"message\":\"请求过于频繁，请稍后再试\"}");
    }

    /**
     * RestTemplate降级处理方法
     * 当RestTemplate调用出现异常时，会调用此方法
     */
    public static ClientHttpResponse handleFallback(HttpRequest request, byte[] body,
                                                    ClientHttpRequestExecution execution, BlockException ex) {
        logger.error("❌ RestTemplate调用异常，触发降级: {}", request.getURI(), ex);
        return new SentinelClientHttpResponse("{\"code\":500,\"message\":\"服务暂时不可用，请稍后再试\"}");
    }

    /**
     * 自定义Sentinel响应类
     * 用于返回自定义的响应内容
     */
    private static class SentinelClientHttpResponse implements ClientHttpResponse {
        private final String body;

        public SentinelClientHttpResponse(String body) {
            this.body = body;
        }

        @Override
        public HttpStatus getStatusCode() throws IOException {
            return HttpStatus.TOO_MANY_REQUESTS;
        }

        @Override
        public String getStatusText() throws IOException {
            return HttpStatus.TOO_MANY_REQUESTS.getReasonPhrase();
        }

        @Override
        public void close() {
        }

        @Override
        public InputStream getBody() throws IOException {
            return new ByteArrayInputStream(body.getBytes());
        }

        @Override
        public HttpHeaders getHeaders() {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            return headers;
        }
    }
}
