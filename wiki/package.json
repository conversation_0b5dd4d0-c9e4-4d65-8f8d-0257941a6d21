{"name": "wiki", "version": "0.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start --host 0.0.0.0", "build": "docusaurus build --out-dir dist", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve --host 0.0.0.0", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "typecheck": "tsc"}, "dependencies": {"@docusaurus/core": "^3.8.0", "@docusaurus/plugin-client-redirects": "^3.8.0", "@docusaurus/preset-classic": "^3.8.0", "@docusaurus/theme-live-codeblock": "^3.8.0", "@docusaurus/theme-mermaid": "^3.8.0", "@easyops-cn/docusaurus-search-local": "^0.49.2", "@mdx-js/react": "^3.0.0", "@types/d3": "^7.4.3", "clsx": "^2.0.0", "d3": "^7.9.0", "prism-react-renderer": "^2.3.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@docusaurus/module-type-aliases": "^3.8.0", "@docusaurus/tsconfig": "^3.8.0", "@docusaurus/types": "^3.8.0", "typescript": "~5.6.2"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 3 chrome version", "last 3 firefox version", "last 5 safari version"]}, "engines": {"node": ">=18.0"}}