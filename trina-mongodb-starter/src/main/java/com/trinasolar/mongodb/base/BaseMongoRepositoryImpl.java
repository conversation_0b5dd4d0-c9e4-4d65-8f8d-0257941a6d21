package com.trinasolar.mongodb.base;

import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.repository.query.MongoEntityInformation;
import org.springframework.data.mongodb.repository.support.SimpleMongoRepository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * MongoDB基础仓库实现类
 *
 * @param <T> 实体类型
 */
public class BaseMongoRepositoryImpl<T extends BaseMongoEntity> extends SimpleMongoRepository<T, String> implements BaseMongoRepository<T> {

    private final MongoTemplate mongoTemplate;
    private final MongoEntityInformation<T, String> entityInformation;

    /**
     * 构造函数
     *
     * @param entityInformation 实体信息
     * @param mongoTemplate     MongoTemplate实例
     */
    public BaseMongoRepositoryImpl(MongoEntityInformation<T, String> entityInformation, MongoTemplate mongoTemplate) {
        super(entityInformation, mongoTemplate);
        this.mongoTemplate = mongoTemplate;
        this.entityInformation = entityInformation;
    }

    @Override
    public List<T> findByQuery(BaseMongoQuery<T> query) {
        return mongoTemplate.find(query.buildQuery(), entityInformation.getJavaType());
    }

    @Override
    public long countByQuery(BaseMongoQuery<T> query) {
        return mongoTemplate.count(query.buildQuery(), entityInformation.getJavaType());
    }

    @Override
    public long deleteByQuery(BaseMongoQuery<T> query) {
        DeleteResult result = mongoTemplate.remove(query.buildQuery(), entityInformation.getJavaType());
        return result.getDeletedCount();
    }

    @Override
    public PageResult<T> findPage(BaseMongoQuery<T> query, Class<T> clazz) {
        // 构建查询条件
        Query mongoQuery = query.buildQuery();
        
        // 查询总数
        long total = mongoTemplate.count(mongoQuery, clazz);
        
        // 设置分页
        mongoQuery.skip((long) query.getPageNo() * query.getPageSize());
        mongoQuery.limit(query.getPageSize());
        
        // 执行查询
        List<T> records = mongoTemplate.find(mongoQuery, clazz);
        
        // 返回分页结果
        return PageResult.of(records, total, query.getPageNo(), query.getPageSize());
    }

    @Override
    public boolean softDelete(String id) {
        Query query = new Query(Criteria.where("id").is(id).and("deleted").is(0));
        Update update = new Update()
                .set("deleted", 1)
                .set("updateTime", LocalDateTime.now());
        
        UpdateResult result = mongoTemplate.updateFirst(query, update, entityInformation.getJavaType());
        return result.getModifiedCount() > 0;
    }

    @Override
    public long softDeleteBatch(List<String> ids) {
        Query query = new Query(Criteria.where("id").in(ids).and("deleted").is(0));
        Update update = new Update()
                .set("deleted", 1)
                .set("updateTime", LocalDateTime.now());
        
        UpdateResult result = mongoTemplate.updateMulti(query, update, entityInformation.getJavaType());
        return result.getModifiedCount();
    }

    @Override
    public long softDeleteByQuery(BaseMongoQuery<T> query) {
        Query mongoQuery = query.buildQuery();
        Update update = new Update()
                .set("deleted", 1)
                .set("updateTime", LocalDateTime.now());
        
        UpdateResult result = mongoTemplate.updateMulti(mongoQuery, update, entityInformation.getJavaType());
        return result.getModifiedCount();
    }
} 