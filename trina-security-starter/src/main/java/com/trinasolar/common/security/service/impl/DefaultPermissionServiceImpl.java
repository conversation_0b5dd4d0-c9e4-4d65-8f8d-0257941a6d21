package com.trinasolar.common.security.service.impl;

import com.trinasolar.common.security.domain.SecurityContext;
import com.trinasolar.common.security.service.PermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;

import java.util.ArrayList;
import java.util.List;

/**
 * 默认权限服务实现
 * <p>
 * 提供基础的权限服务实现，支持缓存机制
 * 实际项目中应该根据具体的权限管理系统来实现
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@RequiredArgsConstructor
@ConditionalOnMissingBean(PermissionService.class)
public class DefaultPermissionServiceImpl implements PermissionService {


    @Override
    public SecurityContext.Authorization getUserPermissions(String token, String userId) {
        return loadUserPermissionsFromSystem(token, userId);
    }

    @Override
    public void refreshUserPermissions(String userId) {
        log.info("User permissions cache refreshed for user: {}", userId);
    }

    /**
     * 从权限管理系统加载用户角色
     * 实际项目中需要根据具体的权限管理系统实现
     */
    private SecurityContext.Authorization loadUserPermissionsFromSystem(String token, String userId) {
        // TODO: 实际项目中应该调用权限管理系统的API获取用户角色
        log.debug("Loading user roles from system for user: {}", userId);

        SecurityContext.Authorization authorization = new SecurityContext.Authorization();

        // 默认实现：根据用户ID模拟角色分配
        List<String> roles = new ArrayList<>();

        // 这里可以根据实际需求实现角色获取逻辑
        // 例如：调用权限管理系统的REST API
        // 或者：查询数据库获取用户角色关系

        // 示例：为演示目的提供默认角色
        if ("itoutsource.cz1510".equals(userId) || userId.contains("itoutsource.cz1510")) {
            roles.add("general");
            roles.add("user");
        } else {
            roles.add("user");
        }

        authorization.setRoleCodes(roles);

        List<String> permissions = new ArrayList<>();
        if (roles.contains("itoutsource.cz1510")) {
            permissions.add("user:add");
            permissions.add("user:edit");
            permissions.add("user:delete");
            permissions.add("user:view");
            permissions.add("system:config");
        }

        if (roles.contains("user")) {
            permissions.add("user:view");
            permissions.add("profile:edit");
        }
        authorization.setPermissions(permissions);

        SecurityContext.Role role = new SecurityContext.Role();
        role.setId("1");
        role.setCode("admin");
        role.setName("管理员");

        // 0表示全部可见
        //role.setDsType(0);
        //role.setDsScope(List.of());

        // 1表示自选部门范围内可见
        role.setDsType(1);
        role.setDsScope(List.of("1", "2"));

        // 2表示本部门及下级可见

        // 3表示本部门可见

        // 4表示仅本人的
        authorization.setRoles(List.of(role));
        return authorization;
    }
}
