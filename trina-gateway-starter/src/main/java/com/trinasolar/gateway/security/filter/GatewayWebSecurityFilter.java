package com.trinasolar.gateway.security.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.common.security.context.SecurityContextHolder;
import com.trinasolar.common.security.properties.SecurityProperties;
import com.trinasolar.gateway.security.util.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 网关Web安全过滤器 - WebFilter实现
 * <p>
 * 基于 WebFlux 的响应式安全过滤器，实现 IAM 登录对接功能
 * 只处理认证，不处理API权限
 * <p>
 * 此过滤器能够拦截所有WebFlux请求，包括网关本身的RestController接口
 * 与 GatewaySecurityFilter 配合使用，确保完整的安全覆盖
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
public class GatewayWebSecurityFilter implements WebFilter, Ordered {

    private final ObjectMapper objectMapper;
    private final SecurityProperties securityProperties;
    private final WebClient webClient;
    private final Cache<String, Object> iamCache;

    public GatewayWebSecurityFilter(ObjectMapper objectMapper, SecurityProperties securityProperties,
                                    WebClient webClient, Cache<String, Object> iamCache) {
        this.objectMapper = objectMapper;
        this.securityProperties = securityProperties;
        this.webClient = webClient;
        this.iamCache = iamCache;
    }

    /**
     * 默认白名单，注意为包含contextPath的路径
     */
    public static final List<String> DEFAULT_WHITE_URLS = List.of(
            "/api/scf/token",
            "/swagger-ui/**", "/swagger-resources/**", "/webjars/**", "/v3/api-docs/**", "/v2/api-docs/**",
            "favicon.ico", "/error", "/actuator/**", "health"
    );

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();
        String path = request.getURI().getPath();
        String authorization = request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION);

        log.debug("Gateway web security filter processing request: {} with authorization: {}", path,
                authorization != null ? "Bearer ***" : "null");

        // 检查是否已经通过WebFilter处理过认证
        // 如果SecurityContext已经设置，说明WebFilter已经处理过认证，直接放行
        //if (SecurityContextHolder.getContext() != null &&
        //        SecurityContextHolder.getContext().getAuthentication() != null &&
        //        SecurityContextHolder.getContext().getAuthentication().isAuthenticated()) {
        //    log.debug("Request already authenticated by WebFilter, skipping GlobalFilter authentication");
        //    return chain.filter(exchange);
        //}

        // 1. 检查是否启用安全功能
        if (!securityProperties.isEnabled()) {
            log.debug("Gateway security is disabled, skipping authentication");
            return chain.filter(exchange);
        }

        // 2. 默认白名单处理
        if (SecurityUtils.matchWhiteUrl(DEFAULT_WHITE_URLS, path)) {
            log.debug("Request path {} matches default whitelist, skipping authentication", path);
            return chain.filter(exchange);
        }

        // 3. 配置白名单处理
        List<String> whiteUrls = securityProperties.getWhiteUris();
        if (SecurityUtils.matchWhiteUrl(whiteUrls, path)) {
            log.debug("Request path {} matches configured whitelist, skipping authentication", path);
            return chain.filter(exchange);
        }

        // 3.1 添加内部系统签名验证
        String tsltoken = SecurityUtils.getValueFromRequest(request, "tsltoken");
        if (StringUtils.hasText(tsltoken) && "hF7jR6sK6zE9yP3bI3iN3fQ9wU1vI5cR".equals(tsltoken)) {
            log.debug("Request path {} matches internal system signature, skipping authentication", path);
            return chain.filter(exchange);
        }

        // 4. 检查Authorization头
        if (!StringUtils.hasText(authorization) || !authorization.startsWith(SecurityConstant.TOKEN_PREFIX)) {
            log.warn("Missing or invalid authorization header for path: {}", path);
            return SecurityUtils.handleUnauthorized(securityProperties, objectMapper, response, "token 为空");
        }

        // 5. 验证token并设置安全上下文
        return verifyTokenAndSetContext(authorization)
                .flatMap(isValid -> {
                    if (isValid) {
                        log.debug("Token validation successful for path: {}", path);
                        return chain.filter(exchange);
                    } else {
                        log.warn("Token validation failed for path: {}", path);
                        return SecurityUtils.handleUnauthorized(securityProperties, objectMapper, response, "token 无效，验证失败");
                    }
                })
                .onErrorResume(throwable -> {
                    log.error("Error during token validation for path: {}", path, throwable);
                    return chain.filter(exchange);
                });
    }

    /**
     * 验证token并设置安全上下文
     */
    private Mono<Boolean> verifyTokenAndSetContext(String authorization) {
        return SecurityUtils.verifyTokenAndSetContext(
                authorization,
                iamCache,
                webClient,
                objectMapper,
                securityProperties
        );
    }

    @Override
    public int getOrder() {
        // 设置比GlobalFilter更高的优先级，确保先执行
        return Ordered.HIGHEST_PRECEDENCE + 1;
    }
}
