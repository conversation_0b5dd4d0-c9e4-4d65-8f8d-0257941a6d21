package com.trinasolar.stream;

import com.trinasolar.common.core.factory.YamlPropertySourceFactory;
import com.trinasolar.stream.template.StreamTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * Trina Stream 自动配置类
 *
 * <AUTHOR>
 * @since 4.0.0
 */
@Configuration
@ConditionalOnClass(StreamBridge.class)
@PropertySource(value = "classpath:stream-config.yml", factory = YamlPropertySourceFactory.class)
public class TrinaStreamAutoConfiguration {

    /**
     * 创建StreamTemplate Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public StreamTemplate streamTemplate(StreamBridge streamBridge) {
        return new StreamTemplate(streamBridge);
    }
}
