package com.trinasolar.data.base;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.github.yulichang.base.MPJBaseMapper;
import com.trinasolar.data.datascope.DataScope;
import jakarta.validation.constraints.NotEmpty;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.List;

/**
 * 数据操作接口
 */
public interface BaseDataMapper<T> extends MPJBaseMapper<T> {

    /**
     * 根据 entity 条件，查询全部记录
     *
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     * @param scope        数据权限范围
     * @return List<T>
     */
    List<T> selectListByScope(@Param(Constants.WRAPPER) Wrapper<T> queryWrapper, DataScope scope);

    /**
     * 根据 entity 条件，查询全部记录（并翻页）
     *
     * @param page         分页查询条件（可以为 RowBounds.DEFAULT）
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     * @param scope        数据权限范围
     * @return Page
     */
    <E extends IPage<T>> E selectPageByScope(E page, @Param(Constants.WRAPPER) Wrapper<T> queryWrapper,
                                             DataScope scope);

    /**
     * 根据 Wrapper 条件，查询总记录数
     *
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     * @param scope        数据权限范围
     * @return Integer
     */
    Long selectCountByScope(@Param(Constants.WRAPPER) Wrapper<T> queryWrapper, DataScope scope);


    /**
     * 根据对象每个属性搜索
     *
     * @param entity 实体
     * @return {@link T }
     */
    T getByExample(T entity);

    /**
     * 动态查询list
     *
     * @param query 查询
     * @return {@link List }<{@link T }>
     */
    List<T> getList(BaseQuery<T> query);


    /**
     * 批量逻辑删除
     *
     * @param ids ids¬
     * @return boolean
     */
    boolean batchDeleteLogic(@NotEmpty List<Serializable> ids);
}
