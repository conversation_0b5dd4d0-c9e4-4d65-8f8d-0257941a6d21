package com.trinasolar.common.security.aspect;

import com.trinasolar.common.core.exception.AuthorizationException;
import com.trinasolar.common.security.annotation.HasPermission;
import com.trinasolar.common.security.context.SecurityContextHolder;
import com.trinasolar.common.core.exception.AuthenticationException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;

/**
 * 功能权限AOP拦截器
 * <p>
 * 拦截@HasRole和@HasPermission注解，验证用户是否拥有相应的角色或权限
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Aspect
public class PermissionAspect {

    /**
     * 拦截@HasPermission注解
     */
    @Before("@annotation(com.trinasolar.common.security.annotation.HasPermission) || @within(com.trinasolar.common.security.annotation.HasPermission)")
    public void checkPermission(JoinPoint joinPoint) {
        if (!SecurityContextHolder.isAuthenticated()) {
            throw new AuthenticationException("用户未登录");
        }

        HasPermission hasPermission = getAnnotation(joinPoint, HasPermission.class);
        if (hasPermission == null) {
            return;
        }

        String[] requiredPermissions = hasPermission.value();
        if (requiredPermissions.length == 0) {
            return;
        }

        String userId = SecurityContextHolder.getCurrentUserId();
        boolean hasAccess = false;

        if (hasPermission.logical() == HasPermission.Logical.AND) {
            // 需要拥有所有权限
            hasAccess = SecurityContextHolder.hasAllPermissions(requiredPermissions);
        } else {
            // 拥有任意一个权限即可
            hasAccess = SecurityContextHolder.hasAnyPermission(requiredPermissions);
        }

        if (!hasAccess) {
            log.warn("用户 {} 访问被拒绝，缺少功能权限: {}", userId, String.join(",", requiredPermissions));
            throw new AuthorizationException("访问被拒绝，缺少功能权限: " + String.join(",", requiredPermissions));
        }

        log.debug("用户 {} 功能权限验证通过: {}", userId, String.join(",", requiredPermissions));
    }

    /**
     * 获取方法或类上的注解
     */
    private <T extends Annotation> T getAnnotation(JoinPoint joinPoint, Class<T> annotationClass) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 先检查方法上的注解
        T annotation = method.getAnnotation(annotationClass);
        if (annotation != null) {
            return annotation;
        }

        // 再检查类上的注解
        return method.getDeclaringClass().getAnnotation(annotationClass);
    }
}
