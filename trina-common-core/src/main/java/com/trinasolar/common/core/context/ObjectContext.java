package com.trinasolar.common.core.context;

import cn.hutool.core.thread.threadlocal.NamedThreadLocal;

/**
 * <AUTHOR>
 * @usage Object数据结构的Context实现，Context指能够线程内共享的变量
 * @date 2021/10/5 11:48 下午
 */
public class ObjectContext {
    private static final ThreadLocal<Object> context = new NamedThreadLocal<>("ObjectContext");

    public static Object getContext() {
        Object map = context.get();
        return map;
    }

    public static void setContext(Object contextObject) {
        context.set(contextObject);
    }

    public static void clear() {
        context.remove();
    }
}
