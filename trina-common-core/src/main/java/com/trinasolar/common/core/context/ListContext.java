package com.trinasolar.common.core.context;

import cn.hutool.core.thread.threadlocal.NamedThreadLocal;

import java.util.ArrayList;
import java.util.List;

/**
 * List数据结构的ThreadLocal上下文实现
 * <AUTHOR>
 * @usage 提供线程安全的List共享变量存储，生命周期与线程绑定
 * @date 2021/10/5 11:42 下午
 */
public class ListContext {
    // 使用NamedThreadLocal便于调试识别（JDK扩展的ThreadLocal）
    private static final ThreadLocal<List> context = new NamedThreadLocal<>("ListContext");

    /**
     * 获取当前线程的List上下文
     * @return 如果不存在则创建新的ArrayList并绑定到当前线程
     */
    public static List getContext() {
        List list = context.get();
        if (list == null) {
            list = new ArrayList();  // 默认初始容量10
            context.set(list);       // 绑定到当前线程
        }
        return list;
    }

    /**
     * 设置当前线程的List上下文
     * @param list 要绑定的List实例（允许替换现有实例）
     */
    public static void setContext(List list) {
        context.set(list);  // ThreadLocal的写操作是线程隔离的
    }

    /**
     * 清除当前线程的List上下文
     */
    public static void clear() {
        context.remove();  // 防止内存泄漏的关键操作
    }

    /**
     * 向上下文List添加元素
     * @param value 要添加的元素
     * @return 添加结果（遵循List.add规范）
     */
    public static boolean add(Object value) {
        return getContext().add(value);  // 自动初始化保证NPE安全
    }

    /**
     * 在指定位置插入元素
     * @param i 插入位置索引
     * @param value 要插入的元素
     * @throws IndexOutOfBoundsException 如果索引越界
     */
    public static void add(int i, Object value) {
        getContext().add(i, value);  // 支持有序插入
    }

    /**
     * 获取指定位置的元素
     * @param i 元素索引
     * @return 对应位置的元素
     * @throws IndexOutOfBoundsException 如果索引越界
     */
    public Object get(int i) {
        return getContext().get(i);  // 注意：非静态方法设计可能是个问题
    }
}
