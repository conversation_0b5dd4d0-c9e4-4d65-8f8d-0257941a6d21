package com.trinasolar.gateway.security.sureness.util;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * common util
 *
 * <AUTHOR>
 * @since 2025-07-08 18:39
 */
public class SurenessCommonUtil {

    private static final String PATH_SPLIT = "/";
    private static final String UNKNOWN = "Unknown";
    private static final String LINUX = "Linux";
    private static final String WINDOWS = "Windows";
    private static final String ANDROID = "Android";
    private static final String IPHONE = "iPhone";
    private static final String IPOD = "Ipod";
    private static final String IPAD = "ipad";
    private static final String CHROME = "Chrome";
    private static final String SAFARI = "safari";

    /**
     * match the userAgent
     *
     * @param userAgent string from request
     * @return userAgent
     */
    public static String findUserAgent(String userAgent) {
        if (userAgent == null || userAgent.isEmpty()) {
            userAgent = UNKNOWN;
        } else if (userAgent.contains(ANDROID)) {
            userAgent = ANDROID;
        } else if (userAgent.contains(LINUX)) {
            userAgent = LINUX;
        } else if (userAgent.contains(IPHONE)) {
            userAgent = IPHONE;
        } else if (userAgent.contains(WINDOWS)) {
            userAgent = WINDOWS;
        } else if (userAgent.contains(CHROME)) {
            userAgent = CHROME;
        } else if (userAgent.contains(IPOD)) {
            userAgent = IPOD;
        } else if (userAgent.contains(IPAD)) {
            userAgent = IPAD;
        } else if (userAgent.contains(SAFARI)) {
            userAgent = SAFARI;
        } else {
            userAgent = UNKNOWN;
        }
        return userAgent;
    }

    /**
     * Splice the contextPath to the head of the each uriResource
     *
     * @param contextPath context path eg: "/v2"
     * @param uriResource set of uriResource eg: "/school/book===get===[role1]"
     * @return set of uriResource eg: "/v2/school/book===get===[role1]"
     */
    public static Set<String> attachContextPath(String contextPath, Set<String> uriResource) {
        if (contextPath == null || contextPath.isEmpty() || uriResource == null || uriResource.isEmpty()) {
            return uriResource;
        }
        // format context path
        contextPath = contextPath.toLowerCase().trim();
        contextPath = contextPath.replace("//", "/");
        if (!contextPath.startsWith(PATH_SPLIT)) {
            contextPath = PATH_SPLIT.concat(contextPath);
        }
        if (contextPath.endsWith(PATH_SPLIT)) {
            contextPath = contextPath.substring(0, contextPath.length() - 1);
        }
        final String finalContextPath = contextPath;
        return uriResource.stream().map(resource ->
                resource.startsWith(PATH_SPLIT)
                        ? finalContextPath.concat(resource)
                        : finalContextPath.concat(PATH_SPLIT).concat(resource)
        ).collect(Collectors.toSet());
    }
}