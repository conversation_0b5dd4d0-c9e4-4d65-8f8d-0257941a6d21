package com.trinasolar.nacos.utils;

import com.alibaba.cloud.nacos.NacosConfigProperties;
import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * Nacos连接测试工具
 * 当配置 spring.cloud.nacos.test-connection=true 时，
 * 在应用启动时会执行连接测试并输出详细的诊断信息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@ConditionalOnProperty(prefix = "spring.cloud.nacos", name = "test-connection", havingValue = "true")
public class NacosConnectionTester implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(NacosConnectionTester.class);
    
    private final NacosDiscoveryProperties discoveryProperties;
    private final NacosConfigProperties configProperties;
    
    public NacosConnectionTester(
            NacosDiscoveryProperties discoveryProperties,
            NacosConfigProperties configProperties) {
        this.discoveryProperties = discoveryProperties;
        this.configProperties = configProperties;
    }

    @Override
    public void run(String... args) throws Exception {
        logger.info("=====================================================");
        logger.info("           Nacos 连接测试工具启动                    ");
        logger.info("=====================================================");
        
        // 测试服务发现连接
        logger.info("测试 Nacos 服务发现连接...");
        NacosConnectionChecker.ConnectionCheckResult discoveryResult = NacosConnectionChecker.checkDiscoveryConnection(discoveryProperties);
        if (discoveryResult.isSuccess()) {
            logger.info("✓ 服务发现连接成功: {}", discoveryResult.getMessage());
        } else {
            logger.error("✗ 服务发现连接失败: {}", discoveryResult.getMessage());
            printConnectionDetails(discoveryProperties.getServerAddr(), 
                    discoveryProperties.getUsername(), 
                    discoveryProperties.getNamespace(), 
                    discoveryProperties.getGroup());
        }
        
        // 测试配置中心连接
        logger.info("测试 Nacos 配置中心连接...");
        NacosConnectionChecker.ConnectionCheckResult configResult = NacosConnectionChecker.checkConfigConnection(configProperties);
        if (configResult.isSuccess()) {
            logger.info("✓ 配置中心连接成功: {}", configResult.getMessage());
        } else {
            logger.error("✗ 配置中心连接失败: {}", configResult.getMessage());
            printConnectionDetails(configProperties.getServerAddr(), 
                    configProperties.getUsername(), 
                    configProperties.getNamespace(), 
                    configProperties.getGroup());
        }
        
        // 输出总结
        if (discoveryResult.isSuccess() && configResult.isSuccess()) {
            logger.info("=====================================================");
            logger.info("           Nacos 连接测试全部通过!                   ");
            logger.info("=====================================================");
        } else {
            logger.error("=====================================================");
            logger.error("           Nacos 连接测试存在失败项!                 ");
            logger.error("=====================================================");
            
            logger.error("常见问题排查:");
            logger.error("1. Nacos服务器是否已启动? 尝试访问: http://{}/nacos/", 
                    discoveryProperties.getServerAddr());
            logger.error("2. 网络连接是否正常? 尝试: ping {}", 
                    discoveryProperties.getServerAddr().split(":")[0]);
            logger.error("3. 用户名密码是否正确?");
            logger.error("4. 命名空间是否存在且有权限访问?");
            logger.error("5. 如果使用Docker或Kubernetes，检查网络配置");
            logger.error("6. 检查防火墙设置，确保Nacos端口已开放");
            logger.error("7. 如果使用HTTPS，确保证书配置正确");
        }
    }
    
    private void printConnectionDetails(String serverAddr, String username, String namespace, String group) {
        logger.error("连接详情:");
        logger.error("- 服务器地址: {}", serverAddr);
        logger.error("- 用户名: {}", username);
        logger.error("- 命名空间: {}", namespace);
        logger.error("- 分组: {}", group);
    }
}
