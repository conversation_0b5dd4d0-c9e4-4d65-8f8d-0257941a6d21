package com.trinasolar.elasticsearch.exception;

/**
 * Elasticsearch自定义异常类
 * 用于封装和统一处理Elasticsearch相关的异常
 */
public class ElasticsearchException extends RuntimeException {

    /**
     * 错误代码
     */
    private final String errorCode;

    /**
     * 构造函数
     *
     * @param message 错误消息
     */
    public ElasticsearchException(String message) {
        this(message, null, "ES-UNKNOWN");
    }

    /**
     * 构造函数
     *
     * @param message 错误消息
     * @param errorCode 错误代码
     */
    public ElasticsearchException(String message, String errorCode) {
        this(message, null, errorCode);
    }

    /**
     * 构造函数
     *
     * @param message 错误消息
     * @param cause 原始异常
     */
    public ElasticsearchException(String message, Throwable cause) {
        this(message, cause, "ES-UNKNOWN");
    }

    /**
     * 构造函数
     *
     * @param message 错误消息
     * @param cause 原始异常
     * @param errorCode 错误代码
     */
    public ElasticsearchException(String message, Throwable cause, String errorCode) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    /**
     * 获取错误代码
     *
     * @return 错误代码
     */
    public String getErrorCode() {
        return errorCode;
    }
}
