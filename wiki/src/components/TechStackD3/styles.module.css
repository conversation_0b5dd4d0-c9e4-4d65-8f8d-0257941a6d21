/* D3 技术栈可视化样式 */
.techStackContainer {
  margin: 4rem 0;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  border-radius: 30px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.techStackContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(74, 158, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 107, 107, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(168, 230, 207, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
}

.title {
  font-size: 3rem;
  font-weight: 800;
  color: #ffffff;
  margin-bottom: 0.5rem;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  background: linear-gradient(135deg, #4A9EFF, #FF6B6B, #4ECDC4);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
  margin-bottom: 0;
}

.legend {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
}

.legendItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.legendItem:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.legendColor {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.svgContainer {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
  position: relative;
  z-index: 2;
}

.svg {
  max-width: 100%;
  height: auto;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-top: 3rem;
  position: relative;
  z-index: 2;
}

.stat {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem 2.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s;
}

.stat:hover::before {
  left: 100%;
}

.stat:hover {
  transform: translateY(-5px) scale(1.05);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.statNumber {
  font-size: 3rem;
  font-weight: 800;
  color: #4A9EFF;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 10px rgba(74, 158, 255, 0.5);
  position: relative;
  z-index: 1;
}

.statLabel {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  position: relative;
  z-index: 1;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .techStackContainer {
    padding: 2rem 1rem;
  }
  
  .title {
    font-size: 2.5rem;
  }
  
  .legend {
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .stats {
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .techStackContainer {
    margin: 2rem 0;
    padding: 2rem 1rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
  
  .legend {
    flex-direction: column;
    align-items: center;
    gap: 0.8rem;
  }
  
  .stats {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .stat {
    padding: 1.5rem 2rem;
  }
  
  .statNumber {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.8rem;
  }
  
  .legendItem {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }
  
  .stat {
    padding: 1rem 1.5rem;
  }
  
  .statNumber {
    font-size: 2rem;
  }
  
  .statLabel {
    font-size: 0.9rem;
  }
}

/* D3 节点动画增强 */
.tech-node {
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.tech-icon {
  user-select: none;
  -webkit-user-select: none;
}

.tech-name {
  user-select: none;
  -webkit-user-select: none;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}