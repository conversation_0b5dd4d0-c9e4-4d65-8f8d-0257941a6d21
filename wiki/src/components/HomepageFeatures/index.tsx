import React from 'react';
import type { ReactNode } from 'react';
import clsx from 'clsx';
import Heading from '@theme/Heading';
import TechStackD3 from '../TechStackD3';
import styles from './styles.module.css';

type FeatureItem = {
  title: string;
  Svg: React.ComponentType<React.ComponentProps<'svg'>>;
  description: ReactNode;
};

const FeatureList: FeatureItem[] = [
  {
    title: '🚀 开箱即用',
    Svg: require('@site/static/img/undraw_docusaurus_mountain.svg').default,
    description: (
      <>
        基于 Spring Boot 3.4 + Java 17，提供 20+ 核心模块，
        包含微服务开发所需的全部组件，让您快速构建企业级应用。
      </>
    ),
  },
  {
    title: '🏗️ 模块化架构',
    Svg: require('@site/static/img/undraw_docusaurus_tree.svg').default,
    description: (
      <>
        采用模块化设计，支持按需引入。包含数据访问、缓存、消息队列、
        服务治理、安全认证等完整的微服务解决方案。
      </>
    ),
  },
  {
    title: '📈 生产就绪',
    Svg: require('@site/static/img/undraw_docusaurus_react.svg').default,
    description: (
      <>
        内置监控、链路追踪、限流熔断、分布式事务等生产级特性，
        支持多数据源、读写分离，满足高并发场景需求。
      </>
    ),
  },
];

function Feature({title, Svg, description}: FeatureItem) {
  return (
    <div className={clsx('col col--4')}>
      <div className="text--center">
        <Svg className={styles.featureSvg} role="img" />
      </div>
      <div className="text--center padding-horiz--md">
        <Heading as="h3">{title}</Heading>
        <p>{description}</p>
      </div>
    </div>
  );
}

export default function HomepageFeatures(): ReactNode {
  return (
    <section className={styles.features}>
      <div className="container">
        <div className="row">
          {FeatureList.map((props, idx) => (
            <Feature key={idx} {...props} />
          ))}
        </div>
        
        {/* D3.js 技术栈可视化 */}
        <TechStackD3 />
        
        {/* 核心模块展示 */}
        <div className={styles.modules}>
          <Heading as="h2" className={styles.modulesTitle}>
            📦 核心模块
          </Heading>
          <div className={styles.moduleGrid}>
            <div className={styles.moduleCard}>
              <h4>🏗️ Common Core</h4>
              <p>基础工具类、异常处理、通用接口等核心功能</p>
            </div>
            <div className={styles.moduleCard}>
              <h4>🔍 Base Query</h4>
              <p>统一查询框架，支持条件构建、排序、分页</p>
            </div>
            <div className={styles.moduleCard}>
              <h4>💾 MyBatis Starter</h4>
              <p>基于 MyBatis-Plus 的数据访问层增强</p>
            </div>
            <div className={styles.moduleCard}>
              <h4>⚡ Redis Starter</h4>
              <p>Redis 缓存操作、分布式锁、限流等功能</p>
            </div>
            <div className={styles.moduleCard}>
              <h4>🔐 Security Starter</h4>
              <p>XSS 防护、权限控制、安全认证等安全模块</p>
            </div>
            <div className={styles.moduleCard}>
              <h4>🌐 Web Starter</h4>
              <p>Web 应用开发、RESTful API、异常处理</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
