---
title: Sentinel Starter 使用指南
description: Trina Sentinel Starter 的快速开始、配置方法与常见问题
---

# 1. 快速开始

## 1.1 引入依赖
在 `pom.xml` 中添加如下依赖：
```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-sentinel-starter</artifactId>
    <version>4.0.0</version>
</dependency>
```

## 1.2 基础配置
在 `application.yml` 或 `sentinel.yaml` 中配置：
```yaml
spring:
  cloud:
    sentinel:
      transport:
        dashboard: localhost:8858
      eager: true
feign:
  sentinel:
    enabled: true
```

# 2. 核心功能
- 自动装配 Sentinel 及其依赖，开箱即用
- 支持 Nacos 动态规则管理（流控/熔断）
- RestTemplate/Feign 限流与熔断处理
- 配置增强器自动补全常用配置

# 3. 高级特性
- 规则优先级：Nacos > 本地配置 > 默认规则
- 支持通过环境变量灵活配置 dashboard、规则数据源等
- 启动日志详细，便于排查

# 4. 常见问题
- 规则未生效：检查 Nacos 配置、group/dataId 是否一致
- dashboard 无法连接：确认端口和地址配置正确
- RestTemplate/Feign 未限流：确认注解和依赖已生效
- 本地规则被覆盖：Nacos 规则优先生效

# 5. 最佳实践
- 推荐通过 Nacos 管理所有规则，便于动态调整
- 项目 application.yml 建议配置 dashboard 地址和端口
- 关注启动日志，及时发现规则加载和配置问题
- 规则优先级：Nacos > 本地配置 > 默认规则

# 6. 参考资料
- [Spring Cloud Alibaba Sentinel 官方文档](https://github.com/alibaba/Sentinel/wiki)
- [Sentinel Nacos 数据源文档](https://github.com/alibaba/Sentinel/wiki/%E4%B8%AD%E6%96%87%E6%96%87%E6%A1%A3#nacos-%E6%95%B0%E6%8D%AE%E6%BA%90) 