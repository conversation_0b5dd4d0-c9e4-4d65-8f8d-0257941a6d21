package com.trinasolar.stream.enums;

import lombok.Getter;

/**
 * 消息优先级枚举
 *
 * <AUTHOR>
 * @since 4.0.0
 */
@Getter
public enum MessagePriority {
    
    /**
     * 低优先级
     */
    LOW(1, "低优先级"),
    
    /**
     * 普通优先级
     */
    NORMAL(5, "普通优先级"),
    
    /**
     * 高优先级
     */
    HIGH(10, "高优先级");
    
    private final int level;
    private final String description;
    
    MessagePriority(int level, String description) {
        this.level = level;
        this.description = description;
    }

}
