package com.trinasolar.sentinel;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.Ordered;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;

import java.util.HashMap;
import java.util.Map;

/**
 * Sentinel配置增强器
 * 确保Spring Cloud Sentinel配置正确加载
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class SentinelConfigurationEnhancer implements EnvironmentPostProcessor, Ordered {

    private static final Logger logger = LoggerFactory.getLogger(SentinelConfigurationEnhancer.class);
    private static final String PROPERTY_SOURCE_NAME = "defaultSentinelConfig";

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        try {
            // 设置默认的Sentinel配置
            setDefaultSentinelConfig(environment);
            
            logger.info("✅ Sentinel配置增强处理完成");
        } catch (Exception e) {
            logger.warn("⚠️ Sentinel配置增强处理失败: {}", e.getMessage());
        }
    }

    /**
     * 设置默认的Sentinel配置
     */
    private void setDefaultSentinelConfig(ConfigurableEnvironment environment) {
        // 只有在用户未明确禁用Sentinel时才设置默认配置
        if (!environment.containsProperty("spring.cloud.sentinel.enabled") || 
            environment.getProperty("spring.cloud.sentinel.enabled", Boolean.class, true)) {
            
            Map<String, Object> defaultConfig = new HashMap<>();
            
            // 设置默认的Sentinel配置
            if (!environment.containsProperty("spring.cloud.sentinel.transport.dashboard")) {
                defaultConfig.put("spring.cloud.sentinel.transport.dashboard", "localhost:8858");
            }
            
            if (!environment.containsProperty("spring.cloud.sentinel.transport.port")) {
                defaultConfig.put("spring.cloud.sentinel.transport.port", 8719);
            }

            if (!environment.containsProperty("feign.sentinel.enabled")) {
                defaultConfig.put("feign.sentinel.enabled", true);
            }

            if (!environment.containsProperty("spring.cloud.sentinel.eager")) {
                defaultConfig.put("spring.cloud.sentinel.eager", true);
            }
            
            // 添加默认的Nacos数据源配置（如果启用了Nacos）
            if (environment.containsProperty("spring.cloud.nacos.config.server-addr") && 
                !environment.containsProperty("spring.cloud.sentinel.datasource.flow.nacos.server-addr")) {
                
                String serverAddr = environment.getProperty("spring.cloud.nacos.config.server-addr");
                String namespace = environment.getProperty("spring.cloud.nacos.config.namespace", 
                                                          environment.getProperty("spring.profiles.active", "public"));
                
                // 流控规则数据源
                defaultConfig.put("spring.cloud.sentinel.datasource.flow.nacos.server-addr", serverAddr);
                defaultConfig.put("spring.cloud.sentinel.datasource.flow.nacos.namespace", namespace);
                defaultConfig.put("spring.cloud.sentinel.datasource.flow.nacos.data-id", "sentinel-flow-rules");
                defaultConfig.put("spring.cloud.sentinel.datasource.flow.nacos.group-id", "DEFAULT_GROUP");
                defaultConfig.put("spring.cloud.sentinel.datasource.flow.nacos.data-type", "json");
                defaultConfig.put("spring.cloud.sentinel.datasource.flow.nacos.rule-type", "flow");
                
                // 熔断规则数据源
                defaultConfig.put("spring.cloud.sentinel.datasource.degrade.nacos.server-addr", serverAddr);
                defaultConfig.put("spring.cloud.sentinel.datasource.degrade.nacos.namespace", namespace);
                defaultConfig.put("spring.cloud.sentinel.datasource.degrade.nacos.data-id", "sentinel-degrade-rules");
                defaultConfig.put("spring.cloud.sentinel.datasource.degrade.nacos.group-id", "DEFAULT_GROUP");
                defaultConfig.put("spring.cloud.sentinel.datasource.degrade.nacos.data-type", "json");
                defaultConfig.put("spring.cloud.sentinel.datasource.degrade.nacos.rule-type", "degrade");
            }
            
            // 只有在有配置需要添加时才创建属性源
            if (!defaultConfig.isEmpty()) {
                MapPropertySource propertySource = new MapPropertySource(PROPERTY_SOURCE_NAME, defaultConfig);
                environment.getPropertySources().addLast(propertySource);
                logger.info("📋 已添加默认Sentinel配置");
            }
        }
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}
