package com.trinasolar.elasticsearch.base.annotation;

/**
 * Elasticsearch字段类型枚举
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
public enum FieldType {
    
    /**
     * 自动推断类型
     */
    AUTO("auto"),
    
    /**
     * 文本类型，支持全文搜索
     */
    TEXT("text"),
    
    /**
     * 关键词类型，不分词
     */
    KEYWORD("keyword"),
    
    /**
     * 长整型
     */
    LONG("long"),
    
    /**
     * 整型
     */
    INTEGER("integer"),
    
    /**
     * 短整型
     */
    SHORT("short"),
    
    /**
     * 字节型
     */
    BYTE("byte"),
    
    /**
     * 双精度浮点型
     */
    DOUBLE("double"),
    
    /**
     * 单精度浮点型
     */
    FLOAT("float"),
    
    /**
     * 半精度浮点型
     */
    HALF_FLOAT("half_float"),
    
    /**
     * 缩放浮点型
     */
    SCALED_FLOAT("scaled_float"),
    
    /**
     * 日期类型
     */
    DATE("date"),
    
    /**
     * 日期纳秒类型
     */
    DATE_NANOS("date_nanos"),
    
    /**
     * 布尔类型
     */
    BOOLEAN("boolean"),
    
    /**
     * 二进制类型
     */
    BINARY("binary"),
    
    /**
     * 整数范围类型
     */
    INTEGER_RANGE("integer_range"),
    
    /**
     * 浮点范围类型
     */
    FLOAT_RANGE("float_range"),
    
    /**
     * 长整型范围类型
     */
    LONG_RANGE("long_range"),
    
    /**
     * 双精度范围类型
     */
    DOUBLE_RANGE("double_range"),
    
    /**
     * 日期范围类型
     */
    DATE_RANGE("date_range"),
    
    /**
     * IP地址类型
     */
    IP("ip"),
    
    /**
     * 对象类型
     */
    OBJECT("object"),
    
    /**
     * 嵌套类型
     */
    NESTED("nested"),
    
    /**
     * 地理点类型
     */
    GEO_POINT("geo_point"),
    
    /**
     * 地理形状类型
     */
    GEO_SHAPE("geo_shape"),
    
    /**
     * 完成建议类型
     */
    COMPLETION("completion"),
    
    /**
     * 令牌计数类型
     */
    TOKEN_COUNT("token_count"),
    
    /**
     * 附件类型
     */
    ATTACHMENT("attachment"),
    
    /**
     * 感知器类型
     */
    PERCOLATOR("percolator"),
    
    /**
     * 连接类型
     */
    JOIN("join"),
    
    /**
     * 排名特征类型
     */
    RANK_FEATURE("rank_feature"),
    
    /**
     * 排名特征集合类型
     */
    RANK_FEATURES("rank_features"),
    
    /**
     * 密集向量类型
     */
    DENSE_VECTOR("dense_vector"),
    
    /**
     * 稀疏向量类型
     */
    SPARSE_VECTOR("sparse_vector"),
    
    /**
     * 搜索即类型
     */
    SEARCH_AS_YOU_TYPE("search_as_you_type"),
    
    /**
     * 别名类型
     */
    ALIAS("alias"),
    
    /**
     * 扁平化类型
     */
    FLATTENED("flattened"),
    
    /**
     * 形状类型
     */
    SHAPE("shape"),
    
    /**
     * 直方图类型
     */
    HISTOGRAM("histogram"),
    
    /**
     * 常量关键词类型
     */
    CONSTANT_KEYWORD("constant_keyword"),
    
    /**
     * 通配符类型
     */
    WILDCARD("wildcard"),
    
    /**
     * 点类型
     */
    POINT("point"),
    
    /**
     * 版本类型
     */
    VERSION("version");
    
    private final String value;
    
    FieldType(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
}