//package com.trinasolar.common.core.utils.mock;
//
//import cn.hutool.core.io.resource.ClassPathResource;
//import com.alibaba.fastjson2.JSONArray;
//import com.alibaba.fastjson2.JSONObject;
//import com.alibaba.fastjson2.TypeReference;
//
//import java.nio.charset.StandardCharsets;
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 测试数据转换类，放在resources/testData下面，命名***.json
// */
//public class MockTestDataHelper {
//
//
//    /**
//     * 测试数据转数组
//     *
//     * @param dataFile
//     * @param clazz
//     * @param <T>
//     * @return
//     */
//    public static <T> List<T> testDataToArray(String dataFile, Class<T> clazz) {
//        // 读取json文件
//        ClassPathResource resource = new ClassPathResource("testData/" + dataFile + ".json");
//        String result = resource.readStr(StandardCharsets.UTF_8);
//        ArrayList<T> data = JSONArray.parseArray(result, ArrayList.class);
//        return data;
//    }
//
//    public static <T> List<T> testDataToArray(String dataFile, TypeReference<T> type) {
//        // 读取json文件
//        ClassPathResource resource = new ClassPathResource("testData/" + dataFile + ".json");
//        String result = resource.readStr(StandardCharsets.UTF_8);
//        ArrayList<T> data = JSONArray.parseObject(result, type.getType());
//        return data;
//    }
//
//
//    /**
//     * 测试数据转对象
//     *
//     * @param dataFile
//     * @param clazz
//     * @param <T>
//     * @return
//     */
//    public static <T> T testDataToObject(String dataFile, Class<T> clazz) {
//        // 读取json文件
//        ClassPathResource resource = new ClassPathResource("testData/" + dataFile + ".json");
//        String result = resource.readStr(StandardCharsets.UTF_8);
//        T data = JSONObject.parseObject(result, clazz);
//        return data;
//    }
//
//    public static <T> T testDataToObject(String dataFile, TypeReference<T> type) {
//        // 读取json文件
//        ClassPathResource resource = new ClassPathResource("testData/" + dataFile + ".json");
//        String result = resource.readStr(StandardCharsets.UTF_8);
//        T data = JSONObject.parseObject(result, type);
//        return data;
//    }
//}
