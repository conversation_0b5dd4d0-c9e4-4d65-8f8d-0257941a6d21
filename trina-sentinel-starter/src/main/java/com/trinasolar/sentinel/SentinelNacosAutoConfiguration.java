package com.trinasolar.sentinel;

import com.alibaba.csp.sentinel.datasource.ReadableDataSource;
import com.alibaba.csp.sentinel.datasource.nacos.NacosDataSource;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRule;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRuleManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import jakarta.annotation.PostConstruct;
import com.alibaba.nacos.api.PropertyKeyConst;

import java.util.List;
import java.util.Properties;

/**
 * Sentinel与Nacos集成自动配置类
 * 用于从Nacos动态获取Sentinel规则
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@ConditionalOnProperty(name = {"spring.cloud.sentinel.enabled", "spring.cloud.nacos.config.enabled"}, havingValue = "true", matchIfMissing = true)
public class SentinelNacosAutoConfiguration {
    
    private static final Logger logger = LoggerFactory.getLogger(SentinelNacosAutoConfiguration.class);
    
    @Value("${spring.cloud.nacos.config.server-addr:localhost:8848}")
    private String serverAddr;
    
    @Value("${spring.cloud.nacos.config.namespace:${spring.profiles.active:public}}")
    private String namespace;
    
    @Value("${spring.cloud.sentinel.datasource.flow.nacos.data-id:sentinel-flow-rules}")
    private String flowDataId;
    
    @Value("${spring.cloud.sentinel.datasource.flow.nacos.group-id:DEFAULT_GROUP}")
    private String flowGroupId;
    
    @Value("${spring.cloud.sentinel.datasource.degrade.nacos.data-id:sentinel-degrade-rules}")
    private String degradeDataId;
    
    @Value("${spring.cloud.sentinel.datasource.degrade.nacos.group-id:DEFAULT_GROUP}")
    private String degradeGroupId;

    public SentinelNacosAutoConfiguration() {
        // 使用默认构造函数
    }

    @PostConstruct
    public void init() {
        try {
            logger.info("🔄 初始化Sentinel Nacos数据源");
            logger.info("📋 配置信息:");
            logger.info("  - Nacos服务器: {}", serverAddr);
            logger.info("  - 命名空间: {}", namespace);
            logger.info("  - 流控规则数据ID: {}", flowDataId);
            logger.info("  - 流控规则分组: {}", flowGroupId);
            logger.info("  - 熔断规则数据ID: {}", degradeDataId);
            logger.info("  - 熔断规则分组: {}", degradeGroupId);
            
            // 初始化流控规则数据源
            initFlowRuleDataSource();
            
            // 初始化熔断规则数据源
            initDegradeRuleDataSource();
            
            logger.info("✅ Sentinel Nacos数据源初始化完成");
        } catch (Exception e) {
            logger.error("❌ Sentinel Nacos数据源初始化失败", e);
        }
    }
    
    /**
     * 初始化流控规则数据源
     */
    private void initFlowRuleDataSource() {
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.SERVER_ADDR, serverAddr);
        if (namespace != null && !namespace.equals("public")) {
            properties.setProperty(PropertyKeyConst.NAMESPACE, namespace);
        }
        
        ReadableDataSource<String, List<FlowRule>> flowRuleDataSource = new NacosDataSource<>(
                properties,
                flowGroupId,
                flowDataId,
                source -> JSON.parseObject(source, new TypeReference<List<FlowRule>>() {})
        );
        FlowRuleManager.register2Property(flowRuleDataSource.getProperty());
        logger.info("✅ 已注册流控规则Nacos数据源: {}/{}", flowGroupId, flowDataId);
    }
    
    /**
     * 初始化熔断规则数据源
     */
    private void initDegradeRuleDataSource() {
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.SERVER_ADDR, serverAddr);
        if (namespace != null && !namespace.equals("public")) {
            properties.setProperty(PropertyKeyConst.NAMESPACE, namespace);
        }
        
        ReadableDataSource<String, List<DegradeRule>> degradeRuleDataSource = new NacosDataSource<>(
                properties,
                degradeGroupId,
                degradeDataId,
                source -> JSON.parseObject(source, new TypeReference<List<DegradeRule>>() {})
        );
        DegradeRuleManager.register2Property(degradeRuleDataSource.getProperty());
        logger.info("✅ 已注册熔断规则Nacos数据源: {}/{}", degradeGroupId, degradeDataId);
    }
}
