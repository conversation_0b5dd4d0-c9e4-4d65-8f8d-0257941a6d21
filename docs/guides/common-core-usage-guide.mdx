---
title: Trina Common Core 使用指南
description: Trina Common Core 核心模块的快速开始、配置方法和使用说明
---

# Trina Common Core 使用指南

## 1. 快速开始

### 1.1 引入依赖

在项目的 `pom.xml` 中添加依赖：

```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-common-core</artifactId>
    <version>4.0.0</version>
</dependency>
```

### 1.2 基础配置

在 `application.yml` 中添加基础配置：

```yaml
# 核心模块配置
trina:
  core:
    # 线程池配置
    thread-pool:
      core-size: 10
      max-size: 50
      queue-capacity: 1000
      keep-alive-seconds: 300
    
    # 租户配置
    tenant:
      enabled: true
      header-name: "Tenant"
    
    # 响应格式配置
    response:
      success-code: 0
      fail-code: 1
```

### 1.3 基本使用

创建一个简单的实体类：

```java
import com.trinasolar.common.core.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户实体")
public class User extends BaseEntity {
    
    @Schema(description = "用户名")
    private String username;
    
    @Schema(description = "邮箱")
    private String email;
    
    @Schema(description = "手机号")
    private String phone;
}
```

创建控制器：

```java
import com.trinasolar.common.core.base.R;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/users")
public class UserController {
    
    @GetMapping("/{id}")
    public R<User> getUser(@PathVariable Long id) {
        // 业务逻辑
        User user = userService.findById(id);
        return R.ok(user);
    }
    
    @PostMapping
    public R<User> createUser(@RequestBody User user) {
        User savedUser = userService.save(user);
        return R.ok(savedUser, "用户创建成功");
    }
}
```

## 2. 核心功能

### 2.1 统一响应格式

使用 `R<T>` 类统一API响应格式：

```java
// 成功响应
public R<User> successExample() {
    User user = new User();
    user.setUsername("张三");
    return R.ok(user);
}

// 失败响应
public R<Void> errorExample() {
    return R.failed("操作失败");
}

// 带消息的成功响应
public R<List<User>> successWithMessage() {
    List<User> users = userService.findAll();
    return R.ok(users, "查询成功");
}

// 链式调用
public R<User> chainExample() {
    User user = new User();
    return R.ok(user)
            .setMsg("操作成功")
            .setCode(200);
}
```

### 2.2 基础实体类

#### BaseEntity 使用

```java
import com.trinasolar.common.core.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class Product extends BaseEntity {
    
    private String name;
    private BigDecimal price;
    private String description;
    
    // 自动继承的字段：
    // - id: 主键ID
    // - sort: 排序值
    // - createBy: 创建者
    // - updateBy: 更新者
    // - createAt: 创建时间
    // - updateAt: 修改时间
}
```

#### BaseSysEntity 使用

```java
import com.trinasolar.common.core.base.BaseSysEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class Order extends BaseSysEntity {
    
    private String orderNo;
    private BigDecimal amount;
    private String status;
    
    // 额外继承的字段：
    // - version: 版本号（乐观锁）
    // - status: 当前状态（0正常，1停用）
    // - deleted: 逻辑删除标识
    // - reserved: 保留数据标识
    // - description: 描述
}
```

#### BaseTenantEntity 使用

```java
import com.trinasolar.common.core.base.BaseTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class TenantUser extends BaseTenantEntity {
    
    private String username;
    private String email;
    
    // 额外继承的字段：
    // - tenantId: 租户ID
}
```

### 2.3 上下文管理

#### 租户上下文使用

```java
import com.trinasolar.common.core.context.TenantContextHolder;

@Service
public class UserService {
    
    public User findById(Long id) {
        // 获取当前租户ID
        Long tenantId = TenantContextHolder.getTenantId();
        
        // 设置租户ID（谨慎使用）
        TenantContextHolder.setTenantId(1L);
        
        // 跳过租户过滤
        TenantContextHolder.setTenantSkip();
        
        // 执行业务逻辑
        User user = userMapper.selectById(id);
        
        // 清理上下文
        TenantContextHolder.clear();
        
        return user;
    }
}
```

#### Map上下文使用

```java
import com.trinasolar.common.core.context.MapContext;

@Service
public class OrderService {
    
    public void processOrder() {
        // 设置上下文数据
        MapContext.put("orderId", "ORDER001");
        MapContext.put("userId", 123L);
        
        // 获取上下文数据
        String orderId = (String) MapContext.get("orderId");
        Long userId = (Long) MapContext.get("userId");
        
        // 清理上下文
        MapContext.clear();
    }
}
```

#### List上下文使用

```java
import com.trinasolar.common.core.context.ListContext;

@Service
public class LogService {
    
    public void logOperation() {
        // 添加日志条目
        ListContext.add("用户登录");
        ListContext.add("查询订单");
        
        // 获取所有日志
        List<String> logs = ListContext.getContext();
        
        // 清理上下文
        ListContext.clear();
    }
}
```

### 2.4 异常处理

#### 基础异常使用

```java
import com.trinasolar.common.core.exception.BaseException;
import com.trinasolar.common.core.exception.BusinessException;

@Service
public class ProductService {
    
    public Product findById(Long id) {
        Product product = productMapper.selectById(id);
        if (product == null) {
            // 使用基础异常
            throw new BaseException("product", "PRODUCT_NOT_FOUND", 
                new Object[]{id}, "产品不存在");
        }
        return product;
    }
    
    public void updateStock(Long id, Integer quantity) {
        if (quantity < 0) {
            // 使用业务异常
            throw new BusinessException("库存数量不能为负数");
        }
        
        // 业务逻辑
        productMapper.updateStock(id, quantity);
    }
}
```

#### 全局异常处理

```java
import com.trinasolar.common.core.base.R;
import com.trinasolar.common.core.exception.BaseException;
import com.trinasolar.common.core.exception.BusinessException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(BusinessException.class)
    public R<Void> handleBusinessException(BusinessException e) {
        return R.failed(e.getMessage());
    }
    
    @ExceptionHandler(BaseException.class)
    public R<Void> handleBaseException(BaseException e) {
        return R.failed(e.getMessage());
    }
    
    @ExceptionHandler(Exception.class)
    public R<Void> handleException(Exception e) {
        log.error("系统异常", e);
        return R.failed("系统异常，请联系管理员");
    }
}
```

### 2.5 工具类使用

#### 字符串工具

```java
import com.trinasolar.common.core.utils.string.StringUtils;

public class StringExample {
    
    public void stringOperations() {
        // 空值判断
        boolean isEmpty = StringUtils.isEmpty("");
        boolean isNotEmpty = StringUtils.isNotEmpty("hello");
        
        // 集合判断
        List<String> list = Arrays.asList("a", "b", "c");
        boolean listEmpty = StringUtils.isEmpty(list);
        
        // 数组判断
        String[] array = {"a", "b"};
        boolean arrayEmpty = StringUtils.isEmpty(array);
        
        // 默认值处理
        String result = StringUtils.nvl(null, "default");
    }
}
```

#### 日期时间工具

```java
import com.trinasolar.common.core.utils.DateUtils;
import java.time.LocalDateTime;
import java.time.LocalDate;

public class DateExample {
    
    public void dateOperations() {
        // 格式化日期
        LocalDateTime now = LocalDateTime.now();
        String formatted = DateUtils.format(now, "yyyy-MM-dd HH:mm:ss");
        
        // 解析日期
        LocalDateTime parsed = DateUtils.parse("2024-01-01 12:00:00");
        
        // 计算时间差
        LocalDateTime start = LocalDateTime.now();
        LocalDateTime end = start.plusHours(2);
        long minutes = DateUtils.betweenMinutes(start, end);
        
        // 获取当前时间戳
        long timestamp = DateUtils.getCurrentTimestamp();
    }
}
```

#### 类型转换工具

```java
import com.trinasolar.common.core.utils.ConvertUtils;

public class ConvertExample {
    
    public void convertOperations() {
        // 字符串转换
        String str = ConvertUtils.toStr(123);
        String defaultStr = ConvertUtils.toStr(null, "default");
        
        // 数字转换
        Integer num = ConvertUtils.toInt("123");
        Long longNum = ConvertUtils.toLong("123");
        
        // 布尔转换
        Boolean bool = ConvertUtils.toBool("true");
        
        // 字符转换
        Character ch = ConvertUtils.toChar("a");
    }
}
```

#### 随机数工具

```java
import com.trinasolar.common.core.utils.RandomUtil;

public class RandomExample {
    
    public void randomOperations() {
        // 生成随机字符串（包含数字和字母）
        String randomStr = RandomUtil.randomStr(8);
        
        // 生成随机数字字符串
        String randomNum = RandomUtil.randomInt(6);
    }
}
```

#### 线程工具

```java
import com.trinasolar.common.core.utils.ThreadUtils;
import java.util.concurrent.ExecutorService;

public class ThreadExample {
    
    public void threadOperations() {
        // 创建线程池
        ExecutorService executor = ThreadUtils.newThreadPoolExecutor(
            10, 20, 60L, TimeUnit.SECONDS, 
            new LinkedBlockingQueue<>(100), 
            "my-pool", false
        );
        
        // 创建单线程执行器
        ExecutorService singleExecutor = ThreadUtils.newSingleThreadExecutor(
            "single-pool", false
        );
        
        // 创建定时任务执行器
        ScheduledExecutorService scheduledExecutor = ThreadUtils.newSingleThreadScheduledExecutor(
            "scheduled-pool", false
        );
    }
}
```

### 2.6 配置管理

#### 线程池配置

```java
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Service
public class AsyncService {
    
    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    
    public void asyncOperation() {
        threadPoolTaskExecutor.submit(() -> {
            // 异步执行的任务
            System.out.println("异步任务执行中...");
        });
    }
}
```

#### YAML配置处理

```java
import com.trinasolar.common.core.utils.YamlUtil;
import java.util.Map;

public class ConfigExample {
    
    public void yamlOperations() {
        try {
            // 加载YAML文件
            Map<?, ?> config = YamlUtil.loadYaml("application.yml");
            
            // 获取配置属性
            Object value = YamlUtil.getProperty(config, "server.port");
            
            // 保存配置
            Map<String, Object> newConfig = new HashMap<>();
            newConfig.put("key", "value");
            YamlUtil.dumpYaml("new-config.yml", newConfig);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
```

## 3. 高级特性

### 3.1 多租户支持

#### 租户过滤器

```java
import com.trinasolar.common.core.context.TenantContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Component
public class TenantFilter extends OncePerRequestFilter {
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain filterChain) 
            throws ServletException, IOException {
        
        try {
            // 从请求头获取租户ID
            String tenantId = request.getHeader("Tenant");
            if (StringUtils.isNotEmpty(tenantId)) {
                TenantContextHolder.setTenantId(Long.valueOf(tenantId));
            }
            
            filterChain.doFilter(request, response);
            
        } finally {
            // 清理租户上下文
            TenantContextHolder.clear();
        }
    }
}
```

#### 租户数据源切换

```java
import com.trinasolar.common.core.context.TenantContextHolder;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

public class TenantDataSource extends AbstractRoutingDataSource {
    
    @Override
    protected Object determineCurrentLookupKey() {
        return TenantContextHolder.getTenantId();
    }
}
```

### 3.2 自定义工具类

#### 扩展StringUtils

```java
import com.trinasolar.common.core.utils.string.StringUtils;

public class CustomStringUtils extends StringUtils {
    
    /**
     * 生成UUID（去除横线）
     */
    public static String generateUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 驼峰转下划线
     */
    public static String camelToUnderscore(String str) {
        if (StringUtils.isEmpty(str)) {
            return str;
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (Character.isUpperCase(c)) {
                if (i > 0) {
                    sb.append('_');
                }
                sb.append(Character.toLowerCase(c));
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }
}
```

#### 自定义异常

```java
import com.trinasolar.common.core.exception.BaseException;

public class CustomException extends BaseException {
    
    public CustomException(String message) {
        super("custom", "CUSTOM_ERROR", null, message);
    }
    
    public CustomException(String code, Object[] args) {
        super("custom", code, args, null);
    }
}
```

### 3.3 性能优化

#### 线程池优化

```java
import com.trinasolar.common.core.config.thread.ThreadPoolConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CustomThreadPoolConfig {
    
    @Bean("customThreadPool")
    public ThreadPoolTaskExecutor customThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(500);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("custom-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}
```

#### 缓存优化

```java
import com.trinasolar.common.core.context.MapContext;
import org.springframework.stereotype.Service;

@Service
public class CacheService {
    
    public Object getCachedData(String key) {
        // 从上下文缓存获取
        Object cached = MapContext.get(key);
        if (cached != null) {
            return cached;
        }
        
        // 从数据库获取
        Object data = loadFromDatabase(key);
        
        // 缓存到上下文
        MapContext.put(key, data);
        
        return data;
    }
}
```

## 4. 常见问题

### 4.1 配置问题

**问题**：ThreadLocal内存泄漏

**解决方案**：
```java
// 确保在使用完毕后清理ThreadLocal
try {
    TenantContextHolder.setTenantId(tenantId);
    // 执行业务逻辑
} finally {
    TenantContextHolder.clear();
}
```

**问题**：线程池配置不生效

**解决方案**：
```yaml
# 确保配置正确
trina:
  core:
    thread-pool:
      core-size: 10
      max-size: 50
      queue-capacity: 1000
      keep-alive-seconds: 300
```

### 4.2 使用问题

**问题**：BaseEntity字段自动填充不生效

**解决方案**：
```java
// 确保实体类正确继承
@Data
@EqualsAndHashCode(callSuper = true)
public class MyEntity extends BaseEntity {
    // 实体字段
}

// 配置MyBatis-Plus自动填充
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {
    
    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, "createAt", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "updateAt", LocalDateTime.class, LocalDateTime.now());
    }
    
    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, "updateAt", LocalDateTime.class, LocalDateTime.now());
    }
}
```

**问题**：异常处理不生效

**解决方案**：
```java
// 确保全局异常处理器正确配置
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(BusinessException.class)
    public R<Void> handleBusinessException(BusinessException e) {
        return R.failed(e.getMessage());
    }
}
```

### 4.3 故障排查

**问题**：租户上下文丢失

**排查步骤**：
1. 检查是否使用了线程池
2. 确认是否使用了TransmittableThreadLocal
3. 验证线程池配置是否正确

**解决方案**：
```java
// 使用TransmittableThreadLocal
private final ThreadLocal<Long> THREAD_LOCAL_TENANT = new TransmittableThreadLocal<>();

// 或者使用TenantBroker
TenantBroker.runAs(tenantId, () -> {
    // 业务逻辑
});
```

**问题**：工具类方法找不到

**排查步骤**：
1. 检查依赖是否正确引入
2. 确认包名是否正确
3. 验证方法签名是否匹配

**解决方案**：
```java
// 确保正确导入
import com.trinasolar.common.core.utils.string.StringUtils;
import com.trinasolar.common.core.utils.DateUtils;
```

## 5. 最佳实践

### 5.1 实体类设计

```java
// 推荐：使用BaseEntity作为基础
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
public class User extends BaseEntity {
    
    @TableField("username")
    private String username;
    
    @TableField("email")
    private String email;
    
    // 避免重复定义基础字段
    // 不要重复定义id、createAt等字段
}
```

### 5.2 异常处理

```java
// 推荐：使用统一的异常处理
@Service
public class UserService {
    
    public User findById(Long id) {
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        return user;
    }
    
    public void createUser(User user) {
        // 参数验证
        if (StringUtils.isEmpty(user.getUsername())) {
            throw new BusinessException("用户名不能为空");
        }
        
        // 业务逻辑
        userMapper.insert(user);
    }
}
```

### 5.3 上下文管理

```java
// 推荐：正确使用上下文
@Service
public class OrderService {
    
    public void processOrder(Order order) {
        try {
            // 设置上下文
            TenantContextHolder.setTenantId(order.getTenantId());
            
            // 执行业务逻辑
            validateOrder(order);
            saveOrder(order);
            sendNotification(order);
            
        } finally {
            // 清理上下文
            TenantContextHolder.clear();
        }
    }
}
```

### 5.4 工具类使用

```java
// 推荐：合理使用工具类
public class DataProcessor {
    
    public void processData(List<String> dataList) {
        // 使用工具类进行空值检查
        if (StringUtils.isEmpty(dataList)) {
            return;
        }
        
        // 使用工具类进行类型转换
        for (String data : dataList) {
            Integer value = ConvertUtils.toInt(data, 0);
            processValue(value);
        }
    }
}
```

## 6. 参考资料

- [Trina Common Core 架构文档](../architecture/common-core-architecture.mdx)
- [Spring Boot 官方文档](https://spring.io/projects/spring-boot)
- [MyBatis-Plus 官方文档](https://baomidou.com/)
- [Hutool 官方文档](https://hutool.cn/)
- [Apache Commons Lang3 文档](https://commons.apache.org/proper/commons-lang/)

## 7. 更新日志

### v4.0.0
- 新增BaseSysEntity支持系统级实体
- 新增BaseTenantEntity支持多租户
- 优化ThreadLocal使用，支持线程池环境
- 新增更多工具类方法
- 完善异常处理机制

### v3.0.0
- 重构响应格式，使用R统一返回
- 新增上下文管理功能
- 优化工具类结构
- 增强配置管理能力

### v2.0.0
- 基础功能实现
- 核心工具类开发
- 异常处理框架搭建