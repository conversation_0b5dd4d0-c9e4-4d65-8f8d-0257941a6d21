package com.trinasolar.stream.exception;

import java.io.Serial;

/**
 * 消息发送异常
 *
 * <AUTHOR>
 * @since 4.0.0
 */
public class MessageSendException extends StreamException {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    public MessageSendException(String message) {
        super("消息发送失败: " + message);
    }
    
    public MessageSendException(String message, Throwable cause) {
        super("消息发送失败: " + message, cause);
    }
}
