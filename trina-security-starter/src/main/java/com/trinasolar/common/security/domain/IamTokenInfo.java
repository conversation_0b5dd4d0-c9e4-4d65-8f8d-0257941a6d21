package com.trinasolar.common.security.domain;


import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * IAM token 信息
 *
 * <AUTHOR>
 * @date 2025-06-20 14:01
 */
@Schema(description = "OAuth2令牌信息响应实体")
@Data
public class IamTokenInfo {

    @Schema(description = "访问令牌", example = "access_token_123")
    private String access_token;

    @Schema(description = "刷新令牌", example = "refresh_token_456")
    private String refresh_token;

    @Schema(description = "授权范围", example = "read write")
    private String scope;

    @Schema(description = "令牌类型", example = "Bearer")
    private String token_type;

    @Schema(description = "过期时间（秒）", example = "3600")
    private Long expires_in;
}
