package com.trinasolar.elasticsearch.base.annotation;

/**
 * 动态映射策略枚举
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
public enum DynamicMapping {
    
    /**
     * 禁用动态映射
     */
    FALSE("false"),
    
    /**
     * 启用动态映射
     */
    TRUE("true"),
    
    /**
     * 严格模式，遇到未知字段抛出异常
     */
    STRICT("strict");
    
    private final String value;
    
    DynamicMapping(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
}