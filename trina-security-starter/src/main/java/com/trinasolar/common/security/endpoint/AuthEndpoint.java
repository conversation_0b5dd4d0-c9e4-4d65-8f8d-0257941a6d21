package com.trinasolar.common.security.endpoint;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.github.benmanes.caffeine.cache.Cache;
import com.trinasolar.common.core.base.R;
import com.trinasolar.common.core.constant.CacheConstants;
import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.common.security.context.SecurityContextHolder;
import com.trinasolar.common.security.domain.IamTokenInfo;
import com.trinasolar.common.security.domain.IamUserInfo;
import com.trinasolar.common.security.domain.SecurityContext;
import com.trinasolar.common.security.properties.SecurityProperties;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;

/**
 * auth 端点
 *
 * <AUTHOR>
 * @date 2025-06-20 14:02
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/scf")
@Tag(name = "认证管理", description = "IAM认证相关接口")
public class AuthEndpoint {

    private final SecurityProperties securityProperties;
    private final RestTemplate restTemplate;
    private final Cache<String, Object> iamCache;

    /**
     * 通过授权码从 TAM 远程获取令牌
     *
     * @param code 授权码
     * @return {@link R }<{@link IamTokenInfo }>
     */
    @Operation(
            summary = "获取访问令牌",
            description = "通过授权码模式获取IAM访问令牌",
            responses = {
                    @ApiResponse(responseCode = "200", description = "令牌获取成功"),
                    @ApiResponse(responseCode = "400", description = "无效的授权码")
            }
    )
    @GetMapping("/token")
    @Parameter(description = "授权码", required = true, in = ParameterIn.QUERY)
    public R<IamTokenInfo> getToken(@RequestParam String code) {
        IamTokenInfo tokenInfo = remoteGetTokenInfo(code);
        if (ObjUtil.isNull(tokenInfo)) {
            return R.failed("获取token失败");
        }
        String tokenCacheKey = CacheConstants.buildTokenInfoKey(tokenInfo.getAccess_token());
        iamCache.put(tokenCacheKey, tokenInfo.getAccess_token());


        return R.ok(tokenInfo);
    }

    /**
     * 获取用户信息
     *
     * @return {@link R }<{@link IamUserInfo }>
     */
    @Operation(
            summary = "获取用户信息",
            description = "获取当前认证用户的详细信息",
            security = @SecurityRequirement(name = "BearerAuth")
    )
    @ApiResponse(responseCode = "200", description = "用户信息获取成功")
    @GetMapping("/userinfo")
    public R<SecurityContext.UserPrincipal> getUserInfo() {
        return R.ok(SecurityContextHolder.getContext().getPrincipal());
    }

    /**
     * 通过token从 TAM 远程注销
     *
     * @param authorization 授权
     * @return {@link R }<{@link Boolean }>
     */
    @Operation(
            summary = "注销登录",
            description = "注销当前登录状态及上下文权限，使当前访问令牌失效",
            security = @SecurityRequirement(name = "BearerAuth")
    )
    @ApiResponse(responseCode = "200", description = "注销成功")
    @Parameter(description = "访问令牌", required = true, in = ParameterIn.HEADER, example = "Bearer access_token")
    @GetMapping("/logout")
    public R<Boolean> logout(@RequestHeader String authorization) {
        return R.ok(remoteLogout(authorization));
    }

    /**
     * 刷新 SecurityContextHolder及缓存中的token信息
     * <p>
     * 注意：这里只做清除，清除后下一次请求接口时会走过滤器重新缓存token和加载上下文权限信息
     *
     * @param authorization 授权
     * @return {@link R }<{@link Boolean }>
     * @see com.trinasolar.common.security.interceptor.SecurityInterceptor
     * #verifyTokenRemotelyAndSetContext(String)
     */
    @Operation(
            summary = "刷新权限上下文信息",
            description = "重新加载权限数据",
            security = @SecurityRequirement(name = "BearerAuth")
    )
    @ApiResponse(responseCode = "200", description = "刷新成功")
    @Parameter(description = "访问令牌", required = true, in = ParameterIn.HEADER, example = "Bearer access_token")
    @GetMapping("/refresh")
    public R<Boolean> refresh(@RequestHeader String authorization) {
        SecurityContextHolder.clearContext();
        String token = authorization.substring(SecurityConstant.TOKEN_PREFIX.length());
        iamCache.asMap().remove(CacheConstants.buildTokenInfoKey(token));
        iamCache.asMap().remove(CacheConstants.buildMenuInfoKey(token));
        iamCache.asMap().remove(CacheConstants.buildAuthInfoKey(token));
        return R.ok(true);
    }

    @Operation(
            summary = "获取用户应用菜单信息",
            description = "获取当前认证用户的已有应用菜单树信息",
            security = @SecurityRequirement(name = "BearerAuth")
    )
    @ApiResponse(responseCode = "200", description = "用户应用菜单信息获取成功")
    @GetMapping("/menu")
    public R<JSONArray> getMenu(@RequestHeader String authorization) {
        return R.ok(remoteMenu(authorization));
    }

    @Operation(
            summary = "获取用户应用按钮权限信息",
            description = "获取当前认证用户的已有应用按钮信息",
            security = @SecurityRequirement(name = "BearerAuth")
    )
    @ApiResponse(responseCode = "200", description = "用户应用按钮权限信息获取成功")
    @GetMapping("/auth")
    public R<JSONObject> getBtnAuth(@RequestHeader String authorization) {
        return R.ok(remoteBtnAuth(authorization));
    }

    /**
     * 远程获取token信息
     *
     * @param code 授权码
     * @return {@link IamTokenInfo }
     */
    private IamTokenInfo remoteGetTokenInfo(String code) {
        try {
            String url = securityProperties.getEnvUrl() + SecurityConstant.TOKEN_PATH;

            MultiValueMap<String, Object> formBody = new LinkedMultiValueMap<>();
            formBody.add("grant_type", "authorization_code");
            formBody.add("client_id", securityProperties.getClientId());
            formBody.add("client_secret", securityProperties.getClientSecret());
            formBody.add("redirect_uri", securityProperties.getRedirectUrl());
            formBody.add("code", code);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(formBody, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
            String body = response.getBody();
            if (response.getStatusCode().is2xxSuccessful() && body != null) {
                IamTokenInfo tokenInfo = JSONUtil.toBean(body, IamTokenInfo.class);
                log.debug("remote get tokenInfo success: {}", tokenInfo);
                return tokenInfo;
            } else {
                JSONObject parseObj = JSONUtil.parseObj(body);
                if (ObjUtil.isNotNull(body)) {
                    log.error("remote get tokenInfo error [{}],error_description[{}]", parseObj.getStr("error"), parseObj.getStr("error_description"));
                }
                log.error("remote get tokenInfo error with status [{}], body [{}]", response.getStatusCode(), response.getBody());
                return null;
            }
        } catch (Exception e) {
            log.error("remote get tokenInfo exception", e);
            return null;
        }
    }

    /**
     * 远程注销
     *
     * @param authorization 授权token
     * @return {@link Boolean }
     */
    private Boolean remoteLogout(String authorization) {
        try {
            String token = authorization.substring(SecurityConstant.TOKEN_PREFIX.length());
            String url = securityProperties.getEnvUrl() + SecurityConstant.LOGOUT_PATH + securityProperties.getClientId() + SecurityConstant.REDIRECT_URI + securityProperties.getRedirectUrl();

            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.set(HttpHeaders.AUTHORIZATION, authorization);

            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    url, HttpMethod.GET, requestEntity, String.class
            );
            if (response.getStatusCode().is2xxSuccessful()) {
                log.debug("remote logout success");
                SecurityContextHolder.clearContext();
                iamCache.asMap().remove(CacheConstants.buildTokenInfoKey(token));
                iamCache.asMap().remove(CacheConstants.buildMenuInfoKey(token));
                iamCache.asMap().remove(CacheConstants.buildAuthInfoKey(token));
                return true;
            } else {
                log.error("remote get logout error with status [{}], body [{}]", response.getStatusCode(), response.getBody());
                SecurityContextHolder.clearContext();
                iamCache.asMap().remove(CacheConstants.buildTokenInfoKey(token));
                iamCache.asMap().remove(CacheConstants.buildMenuInfoKey(token));
                iamCache.asMap().remove(CacheConstants.buildAuthInfoKey(token));
                return false;
            }
        } catch (Exception e) {
            log.error("remote get logout exception", e);
            return false;
        }
    }


    /**
     * 远程获取菜单
     *
     * @param authorization 授权
     * @return {@link JSONArray }
     */
    private JSONArray remoteMenu(String authorization) {
        String token = authorization.substring(SecurityConstant.TOKEN_PREFIX.length());
        Object cacheMenu = iamCache.getIfPresent(CacheConstants.buildMenuInfoKey(token));
        if (!ObjectUtils.isEmpty(cacheMenu)) {
            return (JSONArray) cacheMenu;
        }
        try {
            String url = securityProperties.getTaspUrl() + SecurityConstant.TASP_MENU_PATH + StrPool.SLASH + securityProperties.getAppId();

            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.set(HttpHeaders.AUTHORIZATION, authorization);

            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    url, HttpMethod.GET, requestEntity, String.class
            );
            if (response.getStatusCode().is2xxSuccessful()) {
                log.debug("remote get menus success");
                String body = response.getBody();
                JSONObject bodyObj = JSONUtil.parseObj(body);
                JSONArray data = bodyObj.getJSONArray("data");
                iamCache.put(CacheConstants.buildMenuInfoKey(token), data);
                return data;
            } else {
                log.error("remote get menus error with status [{}], body [{}]", response.getStatusCode(), response.getBody());
                return null;
            }
        } catch (Exception e) {
            log.error("remote get menus exception", e);
            return null;
        }
    }

    /**
     * 远程获取按钮权限信息
     *
     * @param authorization 授权
     * @return {@link JSONObject }
     */
    private JSONObject remoteBtnAuth(String authorization) {
        String token = authorization.substring(SecurityConstant.TOKEN_PREFIX.length());
        Object cacheAuth = iamCache.getIfPresent(CacheConstants.buildAuthInfoKey(token));
        if (!ObjectUtils.isEmpty(cacheAuth)) {
            return (JSONObject) cacheAuth;
        }
        try {
            String url = securityProperties.getTaspUrl() + SecurityConstant.TASP_AUTH_PATH + StrPool.SLASH + securityProperties.getAppId();

            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.set(HttpHeaders.AUTHORIZATION, authorization);

            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    url, HttpMethod.GET, requestEntity, String.class
            );
            if (response.getStatusCode().is2xxSuccessful()) {
                log.debug("remote get btn auth success");
                String body = response.getBody();
                JSONObject bodyObj = JSONUtil.parseObj(body);
                JSONObject data = bodyObj.getJSONObject("data");
                iamCache.put(CacheConstants.buildAuthInfoKey(token), data);
                return data;
            } else {
                log.error("remote get btn auth error with status [{}], body [{}]", response.getStatusCode(), response.getBody());
                return null;
            }
        } catch (Exception e) {
            log.error("remote get btn auth exception", e);
            return null;
        }
    }
}
