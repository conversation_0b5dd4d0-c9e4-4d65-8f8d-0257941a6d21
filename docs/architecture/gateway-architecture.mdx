---
title: Gateway Starter 架构文档
description: Trina Gateway Starter 的架构设计、核心实现与扩展机制
---

# Trina Gateway Starter

Trina Gateway Starter 是一个基于 Spring Cloud Gateway 和 WebFlux 的响应式网关安全模块，提供了完整的 IAM 登录对接功能。

## 功能特性

### 1. 响应式认证功能
- 基于 WebFlux 的响应式认证
- IAM 系统集成，支持 OAuth2 认证
- Token 缓存机制，优化性能
- 响应式安全上下文管理

### 2. 网关安全过滤器
- 双重过滤器架构，确保完整安全覆盖
  - **WebFilter**: 拦截所有WebFlux请求，包括网关本身的RestController
  - **GlobalFilter**: 拦截通过路由配置的请求
- 支持白名单配置，灵活控制访问
- 高性能 Caffeine 缓存
- 完善的错误处理和日志记录

### 3. 安全工具复用
- 直接使用 trina-security-starter 中的 SecurityContextHolder
- 直接使用 trina-security-starter 中的 SecurityUtils
- 保持与 WebMVC 应用的一致性

### 4. 核心组件

- **GatewaySecurityFilter**：网关安全过滤器，处理所有请求的认证逻辑
- **SecurityContextHolder**：复用 security-starter 的安全上下文管理
- **GatewaySecurityProperties**：网关安全配置属性
- **SecurityUtils**：复用 security-starter 的安全工具类

### 5. 架构特点

- **组件复用**：直接复用 security-starter 中的安全组件，保持一致性
- **高性能缓存**：使用 Caffeine 缓存，减少远程调用
- **灵活配置**：支持多种配置选项，适应不同环境
- **易于集成**：简单的注解启用，无侵入式设计

## 认证流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as 网关
    participant Filter as GatewaySecurityFilter
    participant Cache as Caffeine缓存
    participant IAM as IAM服务器
    participant Context as ReactiveSecurityContextHolder
    participant Service as 后端服务

    Client->>Gateway: 1. 请求携带Token
    Gateway->>Filter: 2. 全局过滤器拦截
    Filter->>Cache: 3. 检查Token缓存

    alt Token缓存命中
        Cache-->>Filter: 4. 返回缓存结果
        Filter->>Context: 5. 设置安全上下文(ThreadLocal)
    else Token缓存未命中
        Filter->>IAM: 6. 远程验证Token
        IAM-->>Filter: 7. 返回用户信息
        Filter->>Context: 8. 构建并设置安全上下文(ThreadLocal)
        Filter->>Cache: 9. 缓存Token验证结果
    end

    Filter->>Service: 10. 转发请求到后端服务
    Service-->>Gateway: 11. 返回响应
    Gateway-->>Client: 12. 返回最终响应
```

## 快速开始

### 1. 添加依赖

在你的网关项目中添加 trina-gateway-starter 依赖：

```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-gateway-starter</artifactId>
    <version>4.0.0</version>
</dependency>
```

### 2. 启用网关安全功能

在主类上添加 `@EnableGatewaySecurity` 注解：

```java
@SpringBootApplication
@EnableGatewaySecurity
public class GatewayApplication {
    public static void main(String[] args) {
        SpringApplication.run(GatewayApplication.class, args);
    }
}
```

### 3. 配置文件

```yaml
trina:
  gateway:
    iam:
      enabled: true
      env-url: https://your-iam-server.com
      client-id: your-client-id
      client-secret: your-client-secret
      redirect-url: http://your-gateway.com/callback
      permission-url: http://trina-test.trinasolar.com/app/perms
      white-uris:
        - /api/public/**
        - /health
        - /actuator/**
      connect-timeout: 5000
      read-timeout: 10000
      write-timeout: 10000
      cache:
        enabled: true
        initial-capacity: 512
        maximum-size: 10000
        expire-after-write: 5m
        expire-after-access: 10m
        weak-keys: true
        record-stats: true
```

### 4. 使用安全工具

```java
@RestController
@RequestMapping("/api/gateway")
public class GatewayController {

    @GetMapping("/current-user")
    public Mono<String> getCurrentUser() {
        return Mono.fromCallable(() -> {
            String userId = SecurityUtils.getCurrentUserId();
            return "Current user ID: " + userId;
        });
    }

    @GetMapping("/check-auth")
    public Mono<Boolean> checkAuth() {
        return Mono.fromCallable(() -> SecurityUtils.isAuthenticated());
    }

    @GetMapping("/user-info")
    public Mono<UserInfo> getUserInfo() {
        return Mono.fromCallable(() -> {
            SecurityContext context = SecurityUtils.getCurrentContext();
            UserInfo userInfo = new UserInfo();
            userInfo.setUserId(context.getUserId());
            userInfo.setUsername(context.getUsername());
            userInfo.setDisplayName(context.getDisplayName());
            userInfo.setAuthenticated(context.isAuthenticated());
            return userInfo;
        });
    }
}
```

## 配置说明

### 基础配置

| 配置项 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| `trina.gateway.iam.enabled` | 是否启用网关安全功能 | `true` | 否 |
| `trina.gateway.iam.env-url` | IAM 服务器地址 | - | 是 |
| `trina.gateway.iam.client-id` | IAM 客户端ID | - | 是 |
| `trina.gateway.iam.client-secret` | IAM 客户端密钥 | - | 是 |
| `trina.gateway.iam.redirect-url` | 重定向地址 | - | 是 |

### 网络配置

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `trina.gateway.iam.connect-timeout` | 连接超时时间(ms) | `5000` |
| `trina.gateway.iam.read-timeout` | 读取超时时间(ms) | `10000` |
| `trina.gateway.iam.write-timeout` | 写入超时时间(ms) | `10000` |

### 缓存配置

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `trina.gateway.iam.cache.enabled` | 是否启用缓存 | `true` |
| `trina.gateway.iam.cache.initial-capacity` | 初始容量 | `512` |
| `trina.gateway.iam.cache.maximum-size` | 最大缓存条目数 | `10000` |
| `trina.gateway.iam.cache.expire-after-write` | 写入后过期时间 | `5m` |
| `trina.gateway.iam.cache.expire-after-access` | 访问后过期时间 | `10m` |

## 与 trina-security-starter 的区别

| 特性 | trina-security-starter | trina-gateway-starter |
|------|----------------------|---------------------|
| 适用场景 | WebMVC 应用 | WebFlux 网关 |
| 编程模型 | 同步阻塞 | 网关过滤器 + 同步工具类 |
| HTTP 客户端 | RestTemplate | WebClient |
| 安全上下文 | ThreadLocal | ThreadLocal (复用) |
| 过滤器类型 | Servlet Filter | Global Filter |
| 权限处理 | 支持 API 权限和列级权限 | 仅支持认证，不处理权限 |
| 工具类 | SecurityUtils | SecurityUtils (复用) |

## 最佳实践

### 1. 缓存配置优化

```yaml
trina:
  gateway:
    iam:
      cache:
        # 根据预估用户数设置初始容量
        initial-capacity: 1024
        # 根据内存大小设置最大容量
        maximum-size: 50000
        # 认证信息建议5-10分钟过期
        expire-after-write: 8m
        # 清理长时间未访问的数据
        expire-after-access: 15m
```

### 2. 白名单配置

```yaml
trina:
  gateway:
    iam:
      white-uris:
        # 健康检查
        - /actuator/**
        - /health
        # 公开API
        - /api/public/**
        # 静态资源
        - /static/**
        - /favicon.ico
        # API文档
        - /swagger-ui/**
        - /v3/api-docs/**
```

### 3. 错误处理

网关会自动处理认证失败的情况，返回 401 状态码和 JSON 格式的错误信息：

```json
{
  "code": 401,
  "message": "Unauthorized",
  "data": null
}
```

## 注意事项

1. **工具类复用**：直接使用 security-starter 中的 SecurityUtils 和 SecurityContextHolder，保持 API 一致性
2. **上下文传递**：安全上下文基于 ThreadLocal，在网关的每个请求线程中独立存储
3. **缓存策略**：只缓存 token 验证结果，不缓存完整用户信息，确保数据一致性
4. **性能优化**：合理配置缓存参数和网络超时时间，平衡性能和安全性
5. **监控告警**：建议配置相关监控指标，及时发现认证异常

## 故障排查

### 常见问题

1. **认证失败**：检查 IAM 服务器地址和客户端配置
2. **缓存问题**：检查缓存配置和 JVM 内存设置
3. **网络超时**：调整连接和读取超时时间
4. **白名单不生效**：检查路径匹配规则和配置格式

### 日志配置

```yaml
logging:
  level:
    com.trinasolar.gateway.security: DEBUG
```

## 版本兼容性

- Spring Boot 3.x
- Spring Cloud Gateway 4.x
- Spring WebFlux 6.x
- Java 17+