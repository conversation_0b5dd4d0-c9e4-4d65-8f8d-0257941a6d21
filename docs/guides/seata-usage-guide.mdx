---
title: "Seata分布式事务使用指南"
description: "trina-seata-starter模块的详细使用说明和最佳实践"
---

# Seata分布式事务使用指南

## 1. 快速开始

### 1.1 引入依赖

在需要使用分布式事务的微服务项目中添加依赖：

```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-seata-starter</artifactId>
    <version>4.0.0</version>
</dependency>
```

### 1.2 基础配置

trina-seata-starter提供了开箱即用的配置，默认情况下无需额外配置即可使用。如需自定义配置，可在`application.yml`中覆盖：

```yaml
# 基本配置（可选，有默认值）
seata:
  enabled: true  # 是否启用Seata，默认true
  application-id: \${spring.application.name}  # 应用ID
  tx-service-group: default_tx_group  # 事务群组（可以每个应用独立取名，也可以使用相同的名字）

# 服务发现配置（根据实际环境调整）
seata:
  service:
    vgroup-mapping:
      default_tx_group: default  # 事务组映射到TC集群
    grouplist:
      default: 127.0.0.1:8091  # TC服务器地址
```

### 1.3 数据库准备

在每个业务数据库中创建undo_log表：

```sql
CREATE TABLE `undo_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `branch_id` bigint(20) NOT NULL,
  `xid` varchar(100) NOT NULL,
  `context` varchar(128) NOT NULL,
  `rollback_info` longblob NOT NULL,
  `log_status` int(11) NOT NULL,
  `log_created` datetime NOT NULL,
  `log_modified` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_undo_log` (`xid`,`branch_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;
```

### 1.4 基本使用

在需要分布式事务的方法上添加`@GlobalTransactional`注解：

```java
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class OrderService {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private StockService stockService;

    @Autowired
    private AccountService accountService;

    @GlobalTransactional(name = "create-order", timeoutMills = 60000)
    public void createOrder(String userId, String productId, int count) {
        // 1. 创建订单
        Order order = new Order();
        order.setUserId(userId);
        order.setProductId(productId);
        order.setCount(count);
        order.setAmount(count * 100);
        orderMapper.insert(order);

        // 2. 扣减库存（远程调用）
        stockService.deduct(productId, count);

        // 3. 扣减账户余额（远程调用）
        accountService.debit(userId, order.getAmount());
    }
}

## 2. 核心功能

### 2.1 全局事务注解

#### @GlobalTransactional

**基本用法**：
```java
@GlobalTransactional
public void businessMethod() {
    // 业务逻辑
}
```

**完整配置**：
```java
@GlobalTransactional(
    name = "business-transaction",           // 事务名称
    timeoutMills = 60000,                   // 超时时间（毫秒）
    propagation = Propagation.REQUIRED,     // 事务传播行为
    lockRetryInterval = 10,                 // 锁重试间隔
    lockRetryTimes = 30                     // 锁重试次数
)
public void businessMethod() {
    // 业务逻辑
}
```

**参数说明**：

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| name | String | "" | 事务名称，用于监控和日志 |
| timeoutMills | int | 60000 | 事务超时时间（毫秒） |
| propagation | Propagation | REQUIRED | 事务传播行为 |
| lockRetryInterval | int | 10 | 全局锁重试间隔（毫秒） |
| lockRetryTimes | int | 30 | 全局锁重试次数 |

### 2.2 事务传播行为

```java
// REQUIRED：如果当前存在事务，则加入该事务；如果不存在，则创建新事务
@GlobalTransactional(propagation = Propagation.REQUIRED)
public void method1() { }

// REQUIRES_NEW：总是创建新事务，如果当前存在事务，则挂起当前事务
@GlobalTransactional(propagation = Propagation.REQUIRES_NEW)
public void method2() { }

// NEVER：以非事务方式执行，如果当前存在事务，则抛出异常
@GlobalTransactional(propagation = Propagation.NEVER)
public void method3() { }
```

### 2.3 异常处理

**自动回滚**：
```java
@GlobalTransactional
public void businessMethod() throws Exception {
    // 业务操作1
    service1.operation();

    // 业务操作2
    service2.operation();

    // 如果抛出RuntimeException或Error，事务自动回滚
    if (someCondition) {
        throw new RuntimeException("业务异常，触发回滚");
    }
}
```

**手动回滚**：
```java
import io.seata.core.context.RootContext;

@GlobalTransactional
public void businessMethod() {
    try {
        // 业务操作
        service1.operation();
        service2.operation();

        // 根据业务逻辑决定是否回滚
        if (needRollback) {
            GlobalTransactionContext.reload(RootContext.getXID()).rollback();
        }
    } catch (Exception e) {
        // 异常处理
        log.error("业务执行失败", e);
        throw e;  // 重新抛出异常，触发自动回滚
    }
}
```

## 3. 高级特性

### 3.1 多数据源支持

**配置多数据源**：
```yaml
spring:
  datasource:
    primary:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *******************************
      username: root
      password: password
    secondary:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *******************************
      username: root
      password: password

# Seata自动代理所有数据源
seata:
  enable-auto-data-source-proxy: true
```

**使用多数据源**：
```java
@Service
public class MultiDataSourceService {

    @Autowired
    @Qualifier("primaryJdbcTemplate")
    private JdbcTemplate primaryJdbcTemplate;

    @Autowired
    @Qualifier("secondaryJdbcTemplate")
    private JdbcTemplate secondaryJdbcTemplate;

    @GlobalTransactional
    public void crossDatabaseTransaction() {
        // 操作数据库1
        primaryJdbcTemplate.update("INSERT INTO table1 VALUES (?, ?)", 1, "data1");

        // 操作数据库2
        secondaryJdbcTemplate.update("INSERT INTO table2 VALUES (?, ?)", 2, "data2");
    }
}
```

### 3.2 远程服务调用

**Feign客户端集成**：
```java
@FeignClient(name = "stock-service")
public interface StockClient {

    @PostMapping("/stock/deduct")
    Result deduct(@RequestParam("productId") String productId,
                  @RequestParam("count") int count);
}

@Service
public class OrderService {

    @Autowired
    private StockClient stockClient;

    @GlobalTransactional
    public void createOrder(String productId, int count) {
        // 本地数据库操作
        orderMapper.insert(order);

        // 远程服务调用，自动传播事务上下文
        Result result = stockClient.deduct(productId, count);
        if (!result.isSuccess()) {
            throw new RuntimeException("库存扣减失败");
        }
    }
}
```

## 4. 配置详解

### 4.1 完整配置示例

```yaml
seata:
  # 基础配置
  enabled: true
  application-id: \${spring.application.name}
  tx-service-group: my_tx_group
  enable-auto-data-source-proxy: true
  use-jdk-proxy: true

  # 客户端配置
  client:
    # 资源管理器配置
    rm:
      report-success-enable: true
      table-meta-check-enable: false
      report-retry-count: 5
      async-commit-buffer-limit: 10000
      lock:
        retry-interval: 10
        retry-times: 30
        retry-policy-branch-rollback-on-conflict: true

    # 事务管理器配置
    tm:
      commit-retry-count: 5
      rollback-retry-count: 5
      degrade-check: false
      degrade-check-period: 2000
      degrade-check-allow-times: 10

    # undo日志配置
    undo:
      data-validation: true
      log-serialization: jackson
      log-table: undo_log
      only-care-update-columns: true

  # 服务配置
  service:
    vgroup-mapping:
      my_tx_group: default
    grouplist:
      default: 127.0.0.1:8091
    disable-global-transaction: false

  # 传输配置
  transport:
    type: TCP
    server: NIO
    heartbeat: true
    serialization: seata
    compressor: none
    enable-client-batch-send-request: true
```

### 4.2 环境配置

**开发环境**：
```yaml
seata:
  service:
    grouplist:
      default: dev-seata:8091
  client:
    rm:
      report-success-enable: false  # 减少日志输出
```

**测试环境**：
```yaml
seata:
  service:
    grouplist:
      default: test-seata:8091
  client:
    tm:
      commit-retry-count: 3
      rollback-retry-count: 3
```

**生产环境**：
```yaml
seata:
  service:
    grouplist:
      default: prod-seata-cluster:8091
  client:
    rm:
      async-commit-buffer-limit: 50000  # 增大缓存
    tm:
      commit-retry-count: 10
      rollback-retry-count: 10
```

## 5. 最佳实践

### 5.1 事务设计原则

**1. 最小化事务范围**：
```java
// ❌ 错误：事务范围过大
@GlobalTransactional
public void processLargeTransaction() {
    // 大量业务逻辑
    // 多个远程调用
    // 长时间运行的操作
}

// ✅ 正确：合理的事务范围
@GlobalTransactional
public void processOrder(OrderDTO order) {
    // 只包含必要的原子操作
    createOrder(order);
    deductStock(order.getProductId(), order.getCount());
    deductAccount(order.getUserId(), order.getAmount());
}
```

**2. 合理设置超时时间**：
```java
// 根据业务复杂度设置合理的超时时间
@GlobalTransactional(timeoutMills = 30000)  // 30秒
public void simpleTransaction() { }

@GlobalTransactional(timeoutMills = 120000) // 2分钟
public void complexTransaction() { }
```

**3. 异常处理策略**：
```java
@GlobalTransactional
public void businessMethod() {
    try {
        // 业务操作
        doBusinessLogic();
    } catch (BusinessException e) {
        // 业务异常，记录日志但不阻止回滚
        log.warn("业务异常: {}", e.getMessage());
        throw e;
    } catch (Exception e) {
        // 系统异常，记录详细日志
        log.error("系统异常，事务将回滚", e);
        throw new RuntimeException("系统异常", e);
    }
}
```

### 5.2 性能优化

**1. 批量操作优化**：
```java
@GlobalTransactional
public void batchProcess(List<OrderDTO> orders) {
    // 批量插入订单
    orderMapper.batchInsert(orders);
    
    // 批量扣减库存
    Map<String, Integer> stockMap = orders.stream()
        .collect(Collectors.groupingBy(
            OrderDTO::getProductId,
            Collectors.summingInt(OrderDTO::getCount)
        ));
    stockService.batchDeduct(stockMap);
}
```

**2. 异步处理**：
```java
@Service
public class OrderService {
    
    @Async
    @GlobalTransactional
    public CompletableFuture<Void> processOrderAsync(OrderDTO order) {
        // 异步处理订单
        processOrder(order);
        return CompletableFuture.completedFuture(null);
    }
}
```

### 5.3 监控和日志

**1. 事务监控**：
```java
@Component
public class TransactionMonitor {
    
    @EventListener
    public void handleGlobalTransactionEvent(GlobalTransactionEvent event) {
        log.info("全局事务事件: XID={}, 状态={}, 耗时={}ms", 
                event.getXid(), event.getStatus(), event.getDuration());
    }
}
```

**2. 性能日志**：
```java
@GlobalTransactional
public void businessMethod() {
    long startTime = System.currentTimeMillis();
    try {
        // 业务逻辑
        doBusinessLogic();
    } finally {
        long duration = System.currentTimeMillis() - startTime;
        log.info("事务执行完成，耗时: {}ms", duration);
    }
}
```

## 6. 常见问题

### 6.1 配置问题

**Q: 事务不生效，没有回滚？**

A: 检查以下配置：
1. 确认已引入trina-seata-starter依赖
2. 检查`seata.enabled=true`
3. 确认方法上有`@GlobalTransactional`注解
4. 检查是否抛出了RuntimeException或Error

**Q: 连接TC失败？**

A: 检查网络和配置：
```yaml
seata:
  service:
    grouplist:
      default: correct-seata-server:8091  # 确认地址正确
```

### 6.2 使用问题

**Q: 事务超时如何处理？**

A: 调整超时时间或优化业务逻辑：
```java
@GlobalTransactional(timeoutMills = 120000)  // 增加超时时间
public void longRunningTransaction() {
    // 或者优化业务逻辑，减少执行时间
}
```

**Q: 如何处理只读操作？**

A: 只读操作不需要全局事务：
```java
// ❌ 不推荐：只读操作使用全局事务
@GlobalTransactional
public List<Order> queryOrders() {
    return orderMapper.selectAll();
}

// ✅ 推荐：只读操作不使用全局事务
public List<Order> queryOrders() {
    return orderMapper.selectAll();
}
```

### 6.3 故障排查

**1. 开启调试日志**：
```yaml
logging:
  level:
    io.seata: DEBUG
    com.trinasolar.seata: DEBUG
```

**2. 检查undo_log表**：
```sql
-- 查看未清理的undo_log
SELECT * FROM undo_log WHERE log_status = 0;

-- 清理异常的undo_log（谨慎操作）
DELETE FROM undo_log WHERE log_created < DATE_SUB(NOW(), INTERVAL 1 DAY);
```

## 7. 参考资料

- [Seata官方文档](https://seata.io/zh-cn/docs/overview/what-is-seata.html)
- [Spring Cloud Alibaba Seata](https://github.com/alibaba/spring-cloud-alibaba/wiki/Seata)
- [15分钟了解Seata分布式事务](../tutorials/15-min-seata.mdx)
- [Seata架构文档](../architecture/seata-architecture.mdx)
