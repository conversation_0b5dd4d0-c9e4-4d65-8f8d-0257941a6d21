package com.trinasolar.common.core.context;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.io.Serial;
import java.io.Serializable;
import java.util.function.Supplier;

/**
 * 租户运行时代理<br/>
 * 这是一个工具类，用于切换租户运行时，保护租户ID上下文<br/>
 * 下面这段代码演示问题所在 <pre>
 *     void methodA(){
 *         // 因为某些特殊原因，需要手动指定租户
 *         TenantContextHolder.setTenantId(1);
 *         // do something ...
 *     }
 *     void methodB(){
 *         // 因为某些特殊原因，需要手动指定租户
 *         TenantContextHolder.setTenantId(2);
 *         methodA();
 *         // 此时租户ID已经变成 1
 *         // do something ...
 *     }
 * </pre> 嵌套设置租户ID会导致租户上下文难以维护,并且很难察觉，容易导致数据错乱。 推荐的写法： <pre>
 *     void methodA(){
 *         TenantBroker.RunAs(1,() -> {
 *             // do something ...
 *         });
 *     }
 *     void methodB(){
 *         TenantBroker.RunAs(2,() -> {
 *              methodA();
 *             // do something ...
 *         });
 *     }
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-06-05 19:51
 **/
@Slf4j
@UtilityClass
public class TenantBroker {

    @FunctionalInterface
    public interface RunAs<T> {

        /**
         * 执行业务逻辑
         *
         * @param tenantId 租户ID
         * @throws Exception 异常
         */
        void run(T tenantId) throws Exception;

    }

    @FunctionalInterface
    public interface ApplyAs<T, R> {

        /**
         * 执行业务逻辑,返回一个值
         *
         * @param tenantId 租户ID
         * @return 返回一个值
         * @throws Exception 异常
         */
        R apply(T tenantId) throws Exception;

    }

    /**
     * 以某个租户的身份运行
     *
     * @param tenant 租户ID
     * @param func   函数
     */
    public void runAs(Long tenant, RunAs<Long> func) {
        final Long pre = TenantContextHolder.getTenantId();
        try {
            log.trace("TenantBroker switch pre[{}] -> target[{}]", pre, tenant);
            TenantContextHolder.setTenantId(tenant);
            func.run(tenant);
        } catch (Exception e) {
            // 记录异常日志
            log.error("Error occurred while running as tenant {}", tenant, e);
            throw new TenantBrokerExceptionWrapper(e.getMessage(), e);
        } finally {
            log.trace("TenantBroker revert pre[{}] <- target[{}]", pre, tenant);
            TenantContextHolder.setTenantId(pre);
        }
    }

    /**
     * 以某个租户的身份运行
     *
     * @param tenant 租户ID
     * @param func   函数
     * @return 返回一个值
     */
    public <T> T applyAs(Long tenant, ApplyAs<Long, T> func) {
        final Long pre = TenantContextHolder.getTenantId();
        try {
            log.trace("TenantBroker switch pre[{}] -> target[{}]", pre, tenant);
            TenantContextHolder.setTenantId(tenant);
            return func.apply(tenant);
        } catch (Exception e) {
            // 记录异常日志
            log.error("Error occurred while applying as tenant {}", tenant, e);
            throw new TenantBrokerExceptionWrapper(e.getMessage(), e);
        } finally {
            log.trace("TenantBroker revert pre[{}] <- target[{}]", pre, tenant);
            TenantContextHolder.setTenantId(pre);
        }
    }

    /**
     * 以某个租户的身份运行
     *
     * @param supplier 租户提供商
     * @param func     函数
     */
    public void runAs(Supplier<Long> supplier, RunAs<Long> func) {
        runAs(supplier.get(), func);
    }

    /**
     * 以某个租户的身份运行
     *
     * @param supplier 租户提供商
     * @param func     供应商
     * @return 返回一个值
     */
    public <T> T applyAs(Supplier<Long> supplier, ApplyAs<Long, T> func) {
        return applyAs(supplier.get(), func);
    }

    /**
     * 租户代理异常包装器
     *
     * <AUTHOR>
     * @date 2025-06-06 09:38
     */
    public static class TenantBrokerExceptionWrapper extends RuntimeException implements Serializable {

        @Serial
        private static final long serialVersionUID = -1262090672199700597L;

        public TenantBrokerExceptionWrapper(String message, Throwable cause) {
            super(message, cause);
        }
    }

}
