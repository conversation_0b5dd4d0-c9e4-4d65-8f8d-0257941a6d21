package com.trinasolar.elasticsearch.vector.annotation;

import java.lang.annotation.*;

/**
 * 向量字段注解
 * 用于标记实体类中的向量字段，并配置向量搜索相关参数
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface VectorField {
    
    /**
     * 字段名称，默认使用属性名
     * @return 字段名称
     */
    String name() default "";
    
    /**
     * 向量维度
     * @return 向量维度，必须大于0
     */
    int dims();
    
    /**
     * 相似性度量算法
     * 支持: cosine, dot_product, l2_norm, max_inner_product
     * @return 相似性度量算法，默认cosine
     */
    String similarity() default "cosine";
    
    /**
     * 是否启用索引
     * 启用索引后可以进行近似kNN搜索，但会增加存储空间
     * @return 是否启用索引，默认true
     */
    boolean index() default true;
    
    /**
     * HNSW算法参数 - 连接数
     * 控制图中每个节点的最大连接数，影响搜索质量和索引大小
     * 推荐值: 文本向量16，图像向量32
     * @return 连接数，默认16
     */
    int m() default 16;
    
    /**
     * HNSW算法参数 - 构建时候选数
     * 控制索引构建时的候选数量，影响索引质量和构建时间
     * 推荐值: 文本向量100，图像向量200
     * @return 构建时候选数，默认100
     */
    int efConstruction() default 100;
    
    /**
     * 字段描述
     * @return 字段描述
     */
    String description() default "";
    
    /**
     * 是否忽略该字段
     * @return 是否忽略，默认false
     */
    boolean ignore() default false;
}