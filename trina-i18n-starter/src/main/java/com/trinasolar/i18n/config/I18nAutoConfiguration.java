package com.trinasolar.i18n.config;

import com.trinasolar.i18n.api.TranslationApiClient;
import com.trinasolar.i18n.api.TranslationApiClientImpl;
import com.trinasolar.i18n.cache.TranslationCache;
import com.trinasolar.i18n.cache.TranslationCacheImpl;
import com.trinasolar.i18n.core.I18nService;
import com.trinasolar.i18n.core.I18nServiceImpl;
import com.trinasolar.i18n.file.TranslationFileLoader;
import com.trinasolar.i18n.file.TranslationFileLoaderImpl;
import com.trinasolar.i18n.file.TranslationFileSaver;
import com.trinasolar.i18n.file.TranslationFileSaverImpl;
import com.trinasolar.i18n.web.I18nInterceptor;
import com.trinasolar.i18n.web.I18nWebConfig;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.client.RestClient;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * 国际化自动配置类
 *
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties(I18nProperties.class)
@ConditionalOnProperty(prefix = "trina.i18n", name = "enabled", havingValue = "true", matchIfMissing = true)
public class I18nAutoConfiguration {

    /**
     * 配置翻译缓存
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "trina.i18n.cache", name = "enabled", havingValue = "true", matchIfMissing = true)
    public TranslationCache translationCache(I18nProperties properties) {
        return new TranslationCacheImpl(properties);
    }

    /**
     * 配置翻译文件加载器
     */
    @Bean
    @ConditionalOnMissingBean
    public TranslationFileLoader translationFileLoader(I18nProperties properties) {
        return new TranslationFileLoaderImpl(properties);
    }

    /**
     * 配置翻译文件保存器
     */
    @Bean
    @ConditionalOnMissingBean
    public TranslationFileSaver translationFileSaver(I18nProperties properties, AsyncTaskExecutor i18nTaskExecutor) {
        return new TranslationFileSaverImpl(properties, i18nTaskExecutor);
    }

    /**
     * 配置翻译API客户端
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "trina.i18n.api", name = "enabled", havingValue = "true")
    public TranslationApiClient translationApiClient(I18nProperties properties) {
        // 根据环境选择WebClient或RestClient
        try {
            Class.forName("org.springframework.web.reactive.function.client.WebClient");
            WebClient webClient = WebClient.builder()
                    .baseUrl(properties.getApi().getUrl())
                    .build();
            return new TranslationApiClientImpl(properties, webClient);
        } catch (ClassNotFoundException e) {
            RestClient restClient = RestClient.builder()
                    .baseUrl(properties.getApi().getUrl())
                    .build();
            return new TranslationApiClientImpl(properties, restClient);
        }
    }

    /**
     * 配置国际化服务
     */
    @Bean
    @ConditionalOnMissingBean
    public I18nService i18nService(
            I18nProperties properties,
            TranslationCache translationCache,
            TranslationFileLoader translationFileLoader,
            TranslationFileSaver translationFileSaver,
            TranslationApiClient translationApiClient) {
        return new I18nServiceImpl(
                properties,
                translationCache,
                translationFileLoader,
                translationFileSaver,
                translationApiClient);
    }

    /**
     * 配置异步任务执行器
     */
    @Bean
    @ConditionalOnMissingBean(name = "i18nTaskExecutor")
    public AsyncTaskExecutor i18nTaskExecutor(I18nProperties properties) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(properties.getAsync().getCorePoolSize());
        executor.setMaxPoolSize(properties.getAsync().getMaxPoolSize());
        executor.setQueueCapacity(properties.getAsync().getQueueCapacity());
        executor.setThreadNamePrefix(properties.getAsync().getThreadNamePrefix());
        return executor;
    }

    /**
     * 配置Web拦截器
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "trina.i18n.interceptor", name = "enabled", havingValue = "true", matchIfMissing = true)
    public I18nInterceptor i18nInterceptor(I18nProperties properties) {
        return new I18nInterceptor(properties);
    }

    /**
     * 配置Web拦截器注册
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "trina.i18n.interceptor", name = "enabled", havingValue = "true", matchIfMissing = true)
    public I18nWebConfig i18nWebConfig(I18nInterceptor i18nInterceptor, I18nProperties properties) {
        return new I18nWebConfig(i18nInterceptor, properties);
    }
} 