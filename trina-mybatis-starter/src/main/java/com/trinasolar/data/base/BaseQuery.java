package com.trinasolar.data.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * 通用查询对象，支持高级动态查询条件
 *
 * <p>通过组合 query 实体对象和 conditionsGroups 支持从简单到复杂的查询条件构建。</p>
 * <p>支持两种查询模式：</p>
 * <ul>
 *   <li>简单模式：仅使用 query 对象中的非空字段作为等值查询条件</li>
 *   <li>高级模式：使用 conditionsGroups 构建复杂的条件组和条件</li>
 * </ul>
 *
 * <p>示例JSON格式：</p>
 * <pre>
 * // 简单查询示例
 * {
 *   "query": {
 *     "name": "张三",
 *     "status": "ACTIVE"
 *   },
 *   "asc": ["createTime"],
 *   "desc": ["updateTime"],
 *   "pageNo": 1,
 *   "size": 10,
 *   "queryMode": "SIMPLE"
 * }
 *
 * // 高级查询示例
 * {
 *   "query": {
 *     "departmentId": 123
 *   },
 *   "conditionsGroups": [
 *     {
 *       "joinType": "AND",
 *       "conditions": [
 *         {
 *           "field": "status",
 *           "type": "EQUAL",
 *           "value": "ACTIVE"
 *         },
 *         {
 *           "field": "vipLevel",
 *           "type": "EQUAL",
 *           "value": 2
 *         }
 *       ]
 *     },
 *     {
 *       "joinType": "OR",
 *       "conditions": [
 *         {
 *           "field": "type",
 *           "type": "EQUAL",
 *           "value": "VIP"
 *         },
 *         {
 *           "field": "type",
 *           "type": "EQUAL",
 *           "value": "SVIP"
 *         }
 *       ]
 *     }
 *   ],
 *   "asc": ["createTime"],
 *   "desc": ["updateTime"],
 *   "pageNo": 1,
 *   "size": 10,
 *   "queryMode": "ADVANCED"
 * }
 * </pre>
 */
@Data
@Accessors(chain = true)
public class BaseQuery<T> {
    // 基础查询实体对象，用于简单的等值查询，会提取所有非空字段作为查询条件
    private T query;

    // 查询字段，用于指定返回的列
    private String[] column;

    // 排序相关字段
    private String[] asc = new String[]{};   // 升序排序字段
    private String[] desc = new String[]{}; // 降序排序字段

    // 分页参数
    private int size = 20;   // 每页记录数，默认20
    private int pageNo = 0;  // 页码，从0开始

    // 条件组列表，用于构建复杂查询条件，多个条件组之间默认使用AND连接
    private List<ConditionGroup> conditionsGroups = new ArrayList<>();

    // 查询模式，默认为自动模式(根据是否有条件组自动判断)
    private QueryMode queryMode = QueryMode.AUTO;

    public BaseQuery() {
    }

    public BaseQuery(T query) {
        this.query = query;
    }

    /**
     * 获取或创建默认的条件组
     *
     * @return 默认的条件组
     */
    private ConditionGroup getOrCreateDefaultGroup() {
        if (conditionsGroups.isEmpty()) {
            ConditionGroup group = new ConditionGroup(LogicalOperator.AND);
            conditionsGroups.add(group);
        }
        return conditionsGroups.get(0);
    }

    /**
     * 添加一个条件
     *
     * @param field 字段名
     * @param type  条件类型
     * @param value 值
     * @return 当前查询对象
     * @throws IllegalArgumentException if the condition type is null
     */
    public BaseQuery<T> addCondition(String field, QueryConditionType type, Object value) {
        if (type == null) {
            throw new IllegalArgumentException("Query condition type cannot be null");
        }
        QueryCondition condition = new QueryCondition(field, type, value);
        getOrCreateDefaultGroup().getConditions().add(condition);
        return this;
    }

    /**
     * 添加一个等于条件
     *
     * @param field 字段名
     * @param value 值
     * @return 当前查询对象
     */
    public BaseQuery<T> eq(String field, Object value) {
        return addCondition(field, QueryConditionType.EQUAL, value);
    }

    /**
     * 添加一个不等于条件
     *
     * @param field 字段名
     * @param value 值
     * @return 当前查询对象
     */
    public BaseQuery<T> ne(String field, Object value) {
        return addCondition(field, QueryConditionType.NOT_EQUAL, value);
    }

    /**
     * 添加一个模糊查询条件
     *
     * @param field 字段名
     * @param value 值
     * @return 当前查询对象
     */
    public BaseQuery<T> contains(String field, String value) {
        return addCondition(field, QueryConditionType.CONTAINS, value);
    }

    /**
     * 添加一个IN条件
     *
     * @param field  字段名
     * @param values 值集合
     * @return 当前查询对象
     */
    public BaseQuery<T> in(String field, List<?> values) {
        return addCondition(field, QueryConditionType.IN, values);
    }

    /**
     * 添加一个NOT IN条件
     *
     * @param field  字段名
     * @param values 值集合
     * @return 当前查询对象
     */
    public BaseQuery<T> notIn(String field, List<?> values) {
        return addCondition(field, QueryConditionType.NOT_IN, values);
    }

    /**
     * 添加一个条件组
     *
     * @param joinType 连接类型 (AND 或 OR)
     * @return 新建的条件组
     */
    public ConditionGroup createGroup(LogicalOperator joinType) {
        ConditionGroup group = new ConditionGroup(joinType);
        this.conditionsGroups.add(group);
        return group;
    }

    /**
     * 创建一个AND条件组
     *
     * @return 新建的条件组
     */
    public ConditionGroup and() {
        return createGroup(LogicalOperator.AND);
    }

    /**
     * 创建一个OR条件组
     *
     * @return 新建的条件组
     */
    public ConditionGroup or() {
        return createGroup(LogicalOperator.OR);
    }

    /**
     * 转换为分页对象
     *
     * @return 分页对象
     */
    public Page<T> toPage() {
        // MyBatis-Plus uses 0-based pagination but our API is 1-based
        // Convert pageNo from 1-based to 0-based
        long adjustedPageNo = pageNo;
        if (pageNo > 0) {
            adjustedPageNo = pageNo - 1;
        }
        return new Page<>(adjustedPageNo, size);
    }

    /**
     * 添加一个升序排序
     *
     * @param field 字段名
     * @return 当前查询对象
     */
    public BaseQuery<T> orderByAsc(String field) {
        String[] newAsc = new String[this.asc.length + 1];
        System.arraycopy(this.asc, 0, newAsc, 0, this.asc.length);
        newAsc[this.asc.length] = field;
        this.asc = newAsc;
        return this;
    }

    /**
     * 添加一个降序排序
     *
     * @param field 字段名
     * @return 当前查询对象
     */
    public BaseQuery<T> orderByDesc(String field) {
        String[] newDesc = new String[this.desc.length + 1];
        System.arraycopy(this.desc, 0, newDesc, 0, this.desc.length);
        newDesc[this.desc.length] = field;
        this.desc = newDesc;
        return this;
    }
}

