---
title: MongoDB 使用指南
description: Trina Common 框架中 MongoDB 的集成与使用指南
---

# MongoDB 使用指南

> 本文档详细介绍了如何在 Trina Common 框架中集成和使用 MongoDB 数据库。

## 1. 简介

Trina Common 框架通过 `trina-mongodb-starter` 模块提供了对 MongoDB 的自动配置和便捷操作支持。基于 Spring Data MongoDB 构建，提供了完整的 CRUD 功能和事务支持。

### 核心特性

- 自动配置 MongoDB 客户端连接
- 支持连接池配置
- 事务支持
- 简化的 Repository 接口
- 灵活的查询构建

## 2. 快速开始

### 2.1 添加依赖

在项目的 `pom.xml` 中添加以下依赖：

```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-mongodb-starter</artifactId>
    <version>${trina.version}</version>
</dependency>
```

### 2.2 配置 MongoDB 连接

在 `application.yml` 或 `application.properties` 中配置 MongoDB 连接信息：

```yaml
trina:
  mongodb:
    # 方式一：使用 URI 配置（推荐）
    uri: **************************************************************************
    
    # 方式二：使用单独配置项
    # host: localhost
    # port: 27017
    # database: your_database
    # username: your_username
    # password: your_password
    # authentication-database: admin  # 认证数据库，默认为 admin
    
    # 连接池配置
    max-connection-pool-size: 100
    min-connection-pool-size: 10
    connect-timeout: 10s
    socket-timeout: 30s
    
    # 是否启用事务（需要 MongoDB 副本集）
    enable-transaction: false
    
    # 副本集配置（如使用副本集）
    # replica-set: rs0
    
    # SSL 配置
    # ssl: false
```

## 3. 基础使用

### 3.1 定义实体类

```java
import com.trinasolar.mongodb.base.BaseMongoEntity;
import org.springframework.data.mongodb.core.mapping.Document;
import lombok.Data;

@Data
@Document(collection = "users")  // 指定集合名称，默认为类名小写
public class User extends BaseMongoEntity {
    
    private String username;
    private String email;
    private Integer age;
    private List<String> roles;
    private Date createTime = new Date();
    
    // 嵌套文档
    private Address address;
    
    @Data
    public static class Address {
        private String city;
        private String street;
        private String zipCode;
    }
}
```

### 3.2 创建 Repository 接口

```java
import com.trinasolar.mongodb.base.BaseMongoRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface UserRepository extends BaseMongoRepository<User> {
    
    // 自定义查询方法
    List<User> findByUsername(String username);
    
    List<User> findByAgeGreaterThan(int age);
    
    @Query("{ 'email' : { $regex: ?0 } }")
    List<User> findByEmailLike(String emailPattern);
    
    // 使用 @Query 注解定义复杂查询
    @Query("{ 'roles': { $in: ?0 } }")
    List<User> findByRoles(List<String> roles);
    
    // 分页查询
    Page<User> findByAgeGreaterThan(int age, Pageable pageable);
}
```

### 3.3 使用 BaseMongoService

```java
import com.trinasolar.mongodb.base.BaseMongoService;
import org.springframework.stereotype.Service;

@Service
public class UserService extends BaseMongoService<User> {
    
    private final UserRepository userRepository;
    
    public UserService(UserRepository userRepository) {
        super(userRepository);
        this.userRepository = userRepository;
    }
    
    // 自定义业务方法
    public List<User> findActiveUsers() {
        return userRepository.findByStatus("ACTIVE");
    }
    
    // 复杂查询
    public List<User> findUsersByComplexCondition(String keyword, Integer minAge, List<String> roles) {
        BaseMongoQuery<User> query = new BaseMongoQuery<>();
        
        if (StringUtils.hasText(keyword)) {
            query.or(
                q -> q.like("username", keyword),
                q -> q.like("email", keyword)
            );
        }
        
        if (minAge != null) {
            query.gte("age", minAge);
        }
        
        if (roles != null && !roles.isEmpty()) {
            query.in("roles", roles);
        }
        
        return findByQuery(query);
    }
}
```

### 3.4 创建 Controller

```java
import com.trinasolar.common.core.base.Result;
import com.trinasolar.mongodb.base.BaseMongoController;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/users")
public class UserController extends BaseMongoController<User, String> {
    
    private final UserService userService;
    
    public UserController(UserService userService) {
        super(userService);
        this.userService = userService;
    }
    
    // 自定义API端点
    @GetMapping("/active")
    public Result<List<User>> listActiveUsers() {
        return Result.success(userService.findActiveUsers());
    }
    
    @PostMapping("/search")
    public Result<List<User>> searchUsers(@RequestBody UserSearchDTO searchDTO) {
        return Result.success(userService.findUsersByComplexCondition(
            searchDTO.getKeyword(),
            searchDTO.getMinAge(),
            searchDTO.getRoles()
        ));
    }
}
```

## 4. 高级查询

### 4.1 使用 BaseMongoQuery 构建复杂查询

```java
// 创建查询对象
BaseMongoQuery<User> query = new BaseMongoQuery<>();

// 等值查询
query.eq("status", "ACTIVE");

// 模糊查询
query.like("username", "john");

// 范围查询
query.gte("age", 18).lte("age", 30);

// IN 查询
query.in("role", Arrays.asList("ADMIN", "USER"));

// 排序
query.desc("createTime");

// 分页
query.page(1, 10);

// 执行查询
List<User> users = userService.findByQuery(query);
```

### 4.2 聚合查询

```java
import org.springframework.data.mongodb.core.aggregation.*;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;

// 构建聚合管道
Aggregation aggregation = newAggregation(
    match(Criteria.where("status").is("ACTIVE")),  // 匹配条件
    group("department")                             // 按部门分组
        .count().as("count")                        // 计算每组的数量
        .avg("age").as("avgAge")                    // 计算平均年龄
        .push("$$ROOT").as("employees"),            // 将文档推送到数组中
    sort(Sort.Direction.DESC, "count"),             // 按数量降序排序
    limit(10)                                        // 限制结果数量
);

// 执行聚合查询
AggregationResults<Document> results = mongoTemplate.aggregate(
    aggregation, "users", Document.class);

// 处理结果
List<Document> resultList = results.getMappedResults();
```

## 5. 事务管理

### 5.1 配置副本集

要使用 MongoDB 事务，需要配置副本集。以下是配置步骤：

1. 创建副本集配置文件 `mongod.conf`：

```yaml
storage:
  dbPath: /data/db
  journal:
    enabled: true

replication:
  replSetName: rs0

net:
  bindIp: 0.0.0.0
  port: 27017
```

2. 启动 MongoDB 实例：

```bash
mongod --config /etc/mongod.conf
```

3. 初始化副本集：

```javascript
// 连接到 MongoDB
mongo

// 初始化副本集
rs.initiate({
  _id: "rs0",
  members: [
    { _id: 0, host: "localhost:27017" }
  ]
});
```

### 5.2 启用事务支持

在配置文件中启用事务：

```yaml
trina:
  mongodb:
    enable-transaction: true
```

### 5.3 使用事务

```java
import org.springframework.transaction.annotation.Transactional;

@Service
public class OrderService {
    
    private final MongoTemplate mongoTemplate;
    private final UserService userService;
    
    public OrderService(MongoTemplate mongoTemplate, UserService userService) {
        this.mongoTemplate = mongoTemplate;
        this.userService = userService;
    }
    
    @Transactional(rollbackFor = Exception.class)
    public void placeOrder(Order order) {
        // 1. 扣减用户余额
        User user = userService.findById(order.getUserId());
        if (user.getBalance().compareTo(order.getTotalAmount()) < 0) {
            throw new RuntimeException("Insufficient balance");
        }
        user.setBalance(user.getBalance().subtract(order.getTotalAmount()));
        mongoTemplate.save(user);
        
        // 2. 创建订单
        order.setStatus("CREATED");
        order.setCreateTime(new Date());
        mongoTemplate.save(order);
        
        // 3. 扣减库存
        for (OrderItem item : order.getItems()) {
            Query query = new Query(Criteria.where("productId").is(item.getProductId()));
            Update update = new Update().inc("stock", -item.getQuantity());
            mongoTemplate.updateFirst(query, update, Product.class);
        }
    }
}
```

## 6. 索引管理

### 6.1 使用注解定义索引

```java
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;

@Document(collection = "users")
@CompoundIndexes({
    @CompoundIndex(name = "email_1_username_1", def = "{'email': 1, 'username': 1}")
})
public class User extends BaseMongoEntity {
    
    @Indexed(unique = true)
    private String email;
    
    @Indexed
    private String username;
    
    @Indexed(expireAfterSeconds = 3600 * 24 * 30)  // 30天后过期
    private Date lastLoginTime;
    
    // 其他字段...
}
```

### 6.2 编程方式创建索引

```java
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.index.IndexOperations;

@Component
public class MongoIndexConfig implements CommandLineRunner {
    
    private final MongoTemplate mongoTemplate;
    
    public MongoIndexConfig(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }
    
    @Override
    public void run(String... args) {
        // 创建单字段索引
        IndexOperations indexOps = mongoTemplate.indexOps(User.class);
        indexOps.ensureIndex(new Index().on("email", Sort.Direction.ASC).unique());
        
        // 创建复合索引
        indexOps.ensureIndex(
            new Index()
                .on("lastName", Sort.Direction.ASC)
                .on("firstName", Sort.Direction.ASC)
                .named("name_idx")
        );
        
        // 创建TTL索引
        indexOps.ensureIndex(
            new Index()
                .on("expireAt", Sort.Direction.ASC)
                .expire(0)
        );
    }
}
```

## 7. 最佳实践

1. **合理设计文档结构**：根据应用场景选择适当的文档结构（内嵌文档 vs 引用）
2. **使用适当的索引**：为常用查询字段创建索引，但避免过度索引
3. **批量操作**：使用批量操作提高性能
4. **合理分页**：对于大型集合，使用 `skip` 和 `limit` 进行分页
5. **监控性能**：使用 MongoDB 的监控工具（如 mongostat、mongotop）监控性能
6. **备份策略**：配置定期的数据库备份
7. **安全配置**：启用认证和授权，限制网络访问

## 8. 常见问题

### 8.1 如何解决重复键错误？

确保唯一索引字段的值不重复，或者在插入前检查记录是否已存在。

### 8.2 如何优化查询性能？

- 为查询条件创建适当的索引
- 使用 `explain()` 分析查询执行计划
- 限制返回的字段
- 使用 `$project` 阶段减少数据传输

### 8.3 如何处理大文档？

- 考虑将大文档拆分为多个小文档
- 使用 GridFS 存储大文件
- 避免在文档中存储大数组

## 9. 总结

Trina Common 框架提供了强大的 MongoDB 集成支持，通过 `BaseMongoRepository` 和 `BaseMongoService` 可以快速实现常见的 CRUD 操作。结合事务、索引和聚合等高级功能，可以满足各种复杂业务场景的需求。

通过本文档，您应该已经了解了如何在 Trina 项目中使用 MongoDB 进行开发。如需更多帮助，请参考 [MongoDB 官方文档](https://docs.mongodb.com/) 和 [Spring Data MongoDB 参考文档](https://spring.io/projects/spring-data-mongodb)。
