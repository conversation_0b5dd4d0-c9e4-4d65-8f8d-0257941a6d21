package com.trinasolar.common.security.annotation;

import java.lang.annotation.*;

/**
 * 角色权限注解
 * 
 * 用于标识接口需要的角色权限，支持单个角色或多个角色
 * 
 * 使用示例：
 * @HasRole("admin")                    // 需要admin角色
 * @HasRole({"admin", "manager"})       // 需要admin或manager角色之一
 * @HasRole(value = {"admin", "manager"}, logical = Logical.AND) // 需要同时拥有admin和manager角色
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface HasRole {

    /**
     * 需要的角色列表
     */
    String[] value();

    /**
     * 逻辑关系：AND表示需要拥有所有角色，OR表示拥有任意一个角色即可
     */
    Logical logical() default Logical.OR;

    /**
     * 逻辑关系枚举
     */
    enum Logical {
        /**
         * 逻辑与：需要拥有所有指定的角色
         */
        AND,
        
        /**
         * 逻辑或：拥有任意一个指定的角色即可
         */
        OR
    }
}
