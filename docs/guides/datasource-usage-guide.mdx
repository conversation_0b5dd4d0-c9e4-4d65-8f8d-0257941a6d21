---
title: 多数据源模块使用指南
description: Trina 多数据源模块的使用方法与最佳实践
---

# 多数据源模块使用指南

`trina-datasource-starter` 是 Trina Common 框架中的多数据源管理模块，基于 Dynamic-Datasource 实现，提供了动态数据源切换、读写分离、多租户数据源隔离等功能，为应用提供灵活的数据源管理能力。

## 功能概述

多数据源模块解决了在同一应用中需要连接多个数据库或数据源的问题，支持根据不同的业务场景动态切换数据源，支持读写分离和分库分表，提高了应用的扩展性和性能。

### 主要功能

- **动态数据源**: 支持运行时动态切换数据源
- **读写分离**: 内置读写分离功能，提高查询性能
- **多租户支持**: 基于租户 ID 自动路由到不同数据源
- **数据源分组**: 支持数据源分组管理
- **监控集成**: 与监控系统无缝集成
- **事务支持**: 跨库事务支持
- **负载均衡**: 多数据源下的负载均衡策略 