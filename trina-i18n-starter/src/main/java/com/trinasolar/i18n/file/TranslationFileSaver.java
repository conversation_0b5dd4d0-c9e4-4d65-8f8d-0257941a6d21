package com.trinasolar.i18n.file;

import java.util.Map;

/**
 * 翻译文件保存器接口
 *
 * <AUTHOR>
 */
public interface TranslationFileSaver {

    /**
     * 异步保存单个翻译到文件
     *
     * @param key 翻译键
     * @param value 翻译值
     * @param language 语言
     */
    void saveTranslationAsync(String key, String value, String language);

    /**
     * 异步保存多个翻译到文件
     *
     * @param translations 翻译映射
     * @param language 语言
     */
    void saveTranslationsAsync(Map<String, String> translations, String language);

    /**
     * 同步保存单个翻译到文件
     *
     * @param key 翻译键
     * @param value 翻译值
     * @param language 语言
     * @return 是否保存成功
     */
    boolean saveTranslation(String key, String value, String language);

    /**
     * 同步保存多个翻译到文件
     *
     * @param translations 翻译映射
     * @param language 语言
     * @return 是否保存成功
     */
    boolean saveTranslations(Map<String, String> translations, String language);
} 