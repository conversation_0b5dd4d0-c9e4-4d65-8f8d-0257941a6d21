package com.trinasolar.common.security.aspect;

import com.trinasolar.common.core.exception.AuthenticationException;
import com.trinasolar.common.core.exception.AuthorizationException;
import com.trinasolar.common.security.annotation.HasRole;
import com.trinasolar.common.security.context.SecurityContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;

/**
 * 功能权限AOP拦截器
 * <p>
 * 拦截@HasRole和@HasPermission注解，验证用户是否拥有相应的角色或权限
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Aspect
public class RolePermissionAspect {

    /**
     * 拦截@HasRole注解
     */
    @Before("@annotation(com.trinasolar.common.security.annotation.HasRole) || @within(com.trinasolar.common.security.annotation.HasRole)")
    public void checkRole(JoinPoint joinPoint) {
        if (!SecurityContextHolder.isAuthenticated()) {
            throw new AuthenticationException("用户未登录");
        }

        HasRole hasRole = getAnnotation(joinPoint, HasRole.class);
        if (hasRole == null) {
            return;
        }

        String[] requiredRoles = hasRole.value();
        if (requiredRoles.length == 0) {
            return;
        }

        String userId = SecurityContextHolder.getCurrentUserId();
        boolean hasAccess = false;

        if (hasRole.logical() == HasRole.Logical.AND) {
            // 需要拥有所有角色
            hasAccess = SecurityContextHolder.hasAllRoles(requiredRoles);
        } else {
            // 拥有任意一个角色即可
            hasAccess = SecurityContextHolder.hasAnyRole(requiredRoles);
        }

        if (!hasAccess) {
            log.warn("用户 {} 访问被拒绝，缺少角色权限: {}", userId, String.join(",", requiredRoles));
            throw new AuthorizationException("访问被拒绝，缺少角色权限: " + String.join(",", requiredRoles));
        }

        log.debug("用户 {} 角色权限验证通过: {}", userId, String.join(",", requiredRoles));
    }

    /**
     * 获取方法或类上的注解
     */
    private <T extends Annotation> T getAnnotation(JoinPoint joinPoint, Class<T> annotationClass) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 先检查方法上的注解
        T annotation = method.getAnnotation(annotationClass);
        if (annotation != null) {
            return annotation;
        }

        // 再检查类上的注解
        return method.getDeclaringClass().getAnnotation(annotationClass);
    }
}
