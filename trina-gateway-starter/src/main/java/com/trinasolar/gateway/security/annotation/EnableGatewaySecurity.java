package com.trinasolar.gateway.security.annotation;

import com.trinasolar.common.security.cache.IamCaffeineCacheConfiguration;
import com.trinasolar.gateway.security.AutoGatewaySecurityConfiguration;
import com.trinasolar.gateway.security.endpoint.GatewayAuthEndpoint;
import com.trinasolar.gateway.security.filter.SurenessGatewayFilterFactory;
import org.springframework.context.annotation.Import;
import org.springframework.web.reactive.config.EnableWebFlux;

import java.lang.annotation.*;

/**
 * 启用网关安全管控
 * <p>
 * 在网关应用的主类上添加此注解，启用基于 WebFlux 的 IAM 认证功能
 *
 * <pre>
 * &#64;SpringBootApplication
 * &#64;EnableGatewaySecurity
 * public class GatewayApplication {
 *     public static void main(String[] args) {
 *         SpringApplication.run(GatewayApplication.class, args);
 *     }
 * }
 * </pre>
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@EnableWebFlux
@Import({AutoGatewaySecurityConfiguration.class, SurenessGatewayFilterFactory.class, IamCaffeineCacheConfiguration.class, GatewayAuthEndpoint.class})
public @interface EnableGatewaySecurity {
}
