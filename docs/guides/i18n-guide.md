# Trina多语言国际化模块使用指南

## 1. 模块简介

Trina I18n Starter 是一个用于微服务国际化的 Spring Boot Starter，提供了统一的翻译功能，支持从文件、缓存和翻译API获取翻译内容。该模块为Trina框架提供了全面的国际化解决方案。

### 1.1 功能特点

- **多级翻译查找**：按照缓存->原始文件->[language].json->新翻译文件->new-[language].json->翻译API的顺序查找翻译
- **支持多语言切换**：可以通过请求参数、请求头或Locale自动切换语言
- **自定义翻译文件路径**：可以灵活配置翻译文件的存放位置
- **高性能内存缓存**：使用内存缓存提高翻译查找性能
- **异步保存新翻译**：通过API获取的新翻译结果会异步保存到新翻译文件中
- **注解标记不翻译字段**：支持通过`@NoTranslation`注解标记不需要翻译的字段
- **可扩展的翻译API适配**：支持自定义翻译API客户端实现

## 2. 快速开始

### 2.1 添加依赖

在`pom.xml`中添加以下依赖：

```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-i18n-starter</artifactId>
    <version>${trina.version}</version>
</dependency>
```

### 2.2 配置参数

在`application.yml`中添加以下配置：

```yaml
trina:
  i18n:
    enabled: true                         # 是否启用国际化功能
    file-path: i18n                       # 翻译文件目录路径
    default-language: zh-CN               # 默认语言
    supported-languages:                  # 支持的语言列表
      - zh-CN
      - en-US
    api:
      enabled: false                      # 是否启用翻译API
      url: https://api.example.com/translate  # 翻译API地址
      app-key: your-api-key               # API密钥
      app-secret: your-api-secret         # API密钥
      timeout: 5s                         # 超时时间
      retry-count: 3                      # 重试次数
    cache:
      enabled: true                       # 是否启用缓存
      maximum-size: 10000                 # 最大缓存条目数
      expire-after-write: 24h             # 缓存过期时间
    async:
      core-pool-size: 2                   # 线程池核心线程数
      max-pool-size: 5                    # 线程池最大线程数
      queue-capacity: 100                 # 队列容量
      thread-name-prefix: i18n-async-     # 线程名称前缀
    interceptor:
      enabled: true                       # 是否启用拦截器
      language-param: lang                # 语言参数名
      prefer-header: true                 # 是否优先使用Header中的语言
      language-header: Accept-Language    # 语言Header名
      exclude-paths:                      # 排除的路径
        - /swagger-ui/**
        - /v3/api-docs/**
```

### 2.3 创建翻译文件

在资源目录下创建翻译文件，默认路径为`i18n`：

**i18n/zh-CN.json**：

```json
{
  "hello": "你好",
  "world": "世界",
  "welcome": "欢迎使用Trina I18n Starter"
}
```

**i18n/en-US.json**：

```json
{
  "hello": "Hello",
  "world": "World",
  "welcome": "Welcome to Trina I18n Starter"
}
```

## 3. 核心API使用

### 3.1 I18nService接口

`I18nService`是多语言模块的核心接口，提供了以下主要方法：

| 方法 | 描述 |
| --- | --- |
| `String getMessage(String key, String lang)` | 获取指定语言的翻译 |
| `String getMessage(String key, String defaultMessage, String language)` | 获取指定语言的翻译，如果未找到则返回默认消息 |
| `String getMessage(String key)` | 获取当前语言的翻译 |
| `String getMessageWithDefault(String key, String defMsg)` | 获取当前语言的翻译，如果未找到则返回默认消息 |
| `Map<String, String> getAllMessages(String language)` | 获取指定语言的所有翻译 |
| `void reloadTranslations()` | 重新加载翻译文件 |
| `void setCurrentLanguage(String language)` | 设置当前线程的语言 |
| `String getCurrentLanguage()` | 获取当前线程的语言 |
| `void clearCurrentLanguage()` | 清除当前线程的语言 |

### 3.2 使用示例

```java
@Service
public class MyService {
    
    private final I18nService i18nService;
    
    public MyService(I18nService i18nService) {
        this.i18nService = i18nService;
    }
    
    public String getWelcomeMessage() {
        return i18nService.getMessage("welcome");
    }
    
    public String getWelcomeMessageInEnglish() {
        return i18nService.getMessage("welcome", "en-US");
    }
    
    public String getCustomMessage(String key, String defaultMessage) {
        return i18nService.getMessageWithDefault(key, defaultMessage);
    }
}
```

### 3.3 标记不需要翻译的字段

使用`@NoTranslation`注解标记不需要翻译的字段：

```java
public class User {
    
    private String id;
    private String name;
    
    @NoTranslation
    private String email;
    
    // Getters and setters
}
```

## 4. 工作原理

### 4.1 拦截器提取语言信息

`I18nInterceptor`从请求中提取语言信息，按照以下顺序：

1. 从请求头中获取语言（如果`prefer-header=true`）
2. 从请求参数中获取语言
3. 从请求的Locale中获取语言
4. 如果以上都未获取到，则使用配置的默认语言

### 4.2 多级翻译查找

翻译内容查找按照以下顺序进行：

1. 首先从缓存中查找
2. 如果未找到，从原始翻译文件[language].json中查找
3. 如果仍未找到，从新翻译文件new-[language].json中查找
4. 如果依然未找到且启用了翻译API，则调用翻译API获取翻译
5. 如果以上都未找到，则返回原始键或默认消息

### 4.3 异步保存新翻译

通过API获取的新翻译结果会异步保存到`new-[language].json`文件中，减少I/O操作对性能的影响。

### 4.4 高性能缓存策略

使用内存缓存提高翻译查找性能，缓存配置包括：

- `maximum-size`：最大缓存条目数
- `expire-after-write`：缓存过期时间

## 5. 扩展功能

### 5.1 自定义翻译API客户端

如果需要使用自定义的翻译API，可以实现`TranslationApiClient`接口并注册为Bean：

```java
@Component
public class CustomTranslationApiClient implements TranslationApiClient {
    
    @Override
    public String translate(String text, String sourceLang, String targetLang) {
        // 实现翻译逻辑
        return translatedText;
    }
}
```

### 5.2 自定义翻译缓存

如果需要使用自定义的缓存实现，可以实现`TranslationCache`接口并注册为Bean：

```java
@Component
public class CustomTranslationCache implements TranslationCache {
    
    @Override
    public void put(String key, String language, String value) {
        // 实现缓存存储逻辑
    }
    
    @Override
    public String get(String key, String language) {
        // 实现缓存获取逻辑
        return cachedValue;
    }
    
    @Override
    public void clear() {
        // 实现缓存清理逻辑
    }
}
```

## 6. 最佳实践

### 6.1 翻译文件管理

- 为每种语言创建单独的翻译文件
- 使用有意义的键名，推荐使用模块名或功能名作为前缀，如`user.login.success`
- 定期合并新翻译文件到原始翻译文件

### 6.2 性能优化

- 为提高性能，适当增加缓存条目数和过期时间
- 对于不常变化的翻译内容，可以预加载到缓存中
- 在高并发环境下，调整异步任务的线程池参数

### 6.3 安全建议

- API密钥和密钥不要硬编码在配置文件中，建议使用环境变量或配置中心
- 敏感信息不要作为翻译键使用

## 7. 常见问题排查

| 问题 | 可能原因 | 解决方案 |
| --- | --- | --- |
| 翻译未生效 | 1. 翻译文件路径错误<br />2. 键名不匹配<br />3. 语言标识不匹配 | 1. 检查配置的`file-path`是否正确<br />2. 检查翻译文件中的键名<br />3. 检查支持的语言列表 |
| 缓存不生效 | 1. 缓存未启用<br />2. 缓存配置不当 | 1. 确保`cache.enabled=true`<br />2. 检查缓存的大小和过期时间 |
| 翻译API调用失败 | 1. API未启用<br />2. API配置错误<br />3. 网络问题 | 1. 确保`api.enabled=true`<br />2. 检查API地址和密钥<br />3. 检查网络连接和超时设置 |

## 8. 版本历史

| 版本 | 发布日期 | 主要变更 |
| --- | --- | --- |
| 1.0.0 | 2023-01-01 | 初始版本 |
| 1.1.0 | 2023-03-15 | 添加缓存功能 |
| 1.2.0 | 2023-06-20 | 添加异步保存功能 |
| 1.3.0 | 2023-09-10 | 添加注解支持 |