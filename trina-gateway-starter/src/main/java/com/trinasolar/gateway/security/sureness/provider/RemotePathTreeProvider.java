package com.trinasolar.gateway.security.sureness.provider;


import cn.hutool.core.text.StrPool;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.benmanes.caffeine.cache.Cache;
import com.trinasolar.common.core.constant.CacheConstants;
import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.common.security.context.SecurityContextHolder;
import com.trinasolar.common.security.properties.SecurityProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientRequestException;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.PrematureCloseException;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.Set;

/**
 *  权限服务端提供权限数据
 *
 * <AUTHOR>
 * @since 2025-07-28 17:58
 **/
@Slf4j
@RequiredArgsConstructor
public class RemotePathTreeProvider implements PathTreeProvider {

    private final SecurityProperties securityProperties;
    private final WebClient webClient;
    private final Cache<String, Object> iamCache;

    @Override
    public Set<String> providePathData() {
        //String accessToken = SecurityContextHolder.getContext().getAccessToken();
        // 调用远程认证服务并同步等待结果（根据响应式框架特性可能需要调整）
        //return remoteApiPathAuth(SecurityConstant.TOKEN_PREFIX + accessToken)
        //        .onErrorReturn(Set.of())
        //        .block();
        return Set.of("/kepler/upms/u/users/**===put===[2asda,admin,app_admin,app_dvlpr,bbyqgly,cehsi,ceshipath,child1,cjadmin,cjgkyzjs,cjsczhzxddry,clglzxfjgly,cloudaccountadmin,cmpt_admin,cmpt_dvlpr,cpcsadmin,cpcsuser1,cqecfjgly,cqscfjgly,cqshcfjgly,cqycfjgly,cyjdfjgly,dynamic-home-admin,ewewreer,fgsgly,gcjsyjyfjgly,glqcxsjsh,glqsczhzxddry,gxjdfjgly,gyjhfjgly,gyszxfjgly,gzjdfjgly,hnjdfjgly,hxgsfjgly,jcztgly,jdzxfjgly,jhadmin,jyglzxfjgly,ktfgsfjgly,ktkfyjyfjgly,lxgsfjgly,pcs-fjs-test,pcs-zjs,pcsadmin,pxzxfjgly,scsygsfjgly,testcpp,threemenutest,tset,usermanage,wordOrderInspection,wqwqwq,wzgyzxfjgly,xclyfjgly,xmglbfjgly,xngcgsfjgly,YJZHCSRY,ynjdfjgly,ynrqgsfjgly,yqxsfjgly,yw605fjgly,yxdtclyxshy,yxdtxlyxshy,zijuesesasd,zjrqgsfjgly,zsxnhwtest]",
                "/kepler/upms/u/orgs/**===get===[2asda,admin,app_admin,app_dvlpr,bbyqgly,cehsi,ceshipath,child1,cjadmin,cjgkyzjs,cjsczhzxddry,clglzxfjgly,cloudaccountadmin,cmpt_admin,cmpt_dvlpr,cpcsadmin,cpcsuser1,cqecfjgly,cqscfjgly,cqshcfjgly,cqycfjgly,cyjdfjgly,dynamic-home-admin,ewewreer,fgsgly,gcjsyjyfjgly,glqcxsjsh,glqsczhzxddry,gxjdfjgly,gyjhfjgly,gyszxfjgly,gzjdfjgly,hnjdfjgly,hxgsfjgly,jcztgly,jdzxfjgly,jhadmin,jyglzxfjgly,ktfgsfjgly,ktkfyjyfjgly,lxgsfjgly,pcs-fjs-test,pcs-zjs,pcsadmin,pxzxfjgly,qxfu-mmm,scsygsfjgly,testcpp,threemenutest,tset,usermanage,wordOrderInspection,wqwqwq,wzgyzxfjgly,xclyfjgly,xmglbfjgly,xngcgsfjgly,YJZHCSRY,ynjdfjgly,ynrqgsfjgly,yqxsfjgly,yw605fjgly,yxdtclyxshy,yxdtxlyxshy,zijuesesasd,zjrqgsfjgly,zsxnhwtest]",
                "/api/bdu/**===get===[scftest]");
    }

    @Override
    public Set<String> provideExcludedResource() {
        return Set.of();
    }


    /**
     * 远程获取API PATH权限资源
     *
     * @param authorization 授权
     * @return {@link JSONArray }
     */
    private Mono<Set<String>> remoteApiPathAuth(String authorization) {
        String url = securityProperties.getTaspUrl() + SecurityConstant.TASP_API_AUTH_PATH + StrPool.SLASH + securityProperties.getAppId();
        String token = authorization.substring(SecurityConstant.TOKEN_PREFIX.length());
        Object cacheMenu = iamCache.getIfPresent(CacheConstants.buildApiAuthInfoKey(token));
        if (!ObjectUtils.isEmpty(cacheMenu)) {
            @SuppressWarnings("unchecked")
            Set<String> cachedSet = (Set<String>) cacheMenu;
            return Mono.just(cachedSet);
        }

        return webClient.get()
                .uri(url)
                .header(HttpHeaders.AUTHORIZATION, authorization)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofSeconds(10))  // API权限数据加载超时
                .retryWhen(Retry.backoff(2, Duration.ofMillis(500))
                        .filter(throwable -> {
                            // 只对连接相关异常进行重试
                            return throwable instanceof PrematureCloseException ||
                                   throwable instanceof WebClientRequestException ||
                                   (throwable instanceof WebClientResponseException &&
                                    ((WebClientResponseException) throwable).getStatusCode().is5xxServerError());
                        })
                        .onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> {
                            log.warn("API path data loading retry exhausted for URL: {}, attempts: {}",
                                    url, retrySignal.totalRetries());
                            return retrySignal.failure();
                        }))
                .map(body -> {
                    JSONObject bodyObj = JSONObject.parseObject(body);
                    // 使用TypeReference指定泛型类型
                    Set<String> data = bodyObj.getObject("data", new TypeReference<Set<String>>() {
                    });
                    iamCache.put(CacheConstants.buildApiAuthInfoKey(token), data);
                    return data;
                })
                .doOnError(throwable -> {
                    if (throwable instanceof PrematureCloseException) {
                        log.warn("Connection prematurely closed during API path data loading for URL: {}", url);
                    } else if (throwable instanceof WebClientRequestException) {
                        log.warn("Network error during API path data loading for URL: {}, error: {}", url, throwable.getMessage());
                    } else {
                        log.error("Error during API path data loading for URL: {}", url, throwable);
                    }
                })
                .onErrorResume(throwable -> {
                    log.error("Remote API path data loading failed, falling back to empty set", throwable);
                    return Mono.just(Set.of());
                });
    }
}
