package com.trinasolar.elasticsearch.vector.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * 向量字段映射配置
 * 用于定义向量字段的Elasticsearch映射信息
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VectorFieldMapping {
    
    /**
     * 字段名称
     */
    private String fieldName;
    
    /**
     * 向量维度
     */
    private int dims;
    
    /**
     * 相似性度量算法
     * 支持: cosine, dot_product, l2_norm, max_inner_product
     */
    private String similarity;
    
    /**
     * 是否启用索引
     */
    private boolean index;
    
    /**
     * HNSW算法参数 - 连接数
     */
    private Integer m;
    
    /**
     * HNSW算法参数 - 构建时候选数
     */
    private Integer efConstruction;
    
    /**
     * 创建默认的向量字段映射
     * 
     * @param fieldName 字段名称
     * @param dims 向量维度
     * @return 向量字段映射
     */
    public static VectorFieldMapping createDefault(String fieldName, int dims) {
        return VectorFieldMapping.builder()
                .fieldName(fieldName)
                .dims(dims)
                .similarity("cosine")
                .index(true)
                .m(16)
                .efConstruction(100)
                .build();
    }
    
    /**
     * 创建文本向量字段映射
     * 适用于文本嵌入向量，使用余弦相似度
     * 
     * @param fieldName 字段名称
     * @param dims 向量维度
     * @return 向量字段映射
     */
    public static VectorFieldMapping createForText(String fieldName, int dims) {
        return VectorFieldMapping.builder()
                .fieldName(fieldName)
                .dims(dims)
                .similarity("cosine")
                .index(true)
                .m(16)
                .efConstruction(100)
                .build();
    }
    
    /**
     * 创建图像向量字段映射
     * 适用于图像特征向量，使用L2范数
     * 
     * @param fieldName 字段名称
     * @param dims 向量维度
     * @return 向量字段映射
     */
    public static VectorFieldMapping createForImage(String fieldName, int dims) {
        return VectorFieldMapping.builder()
                .fieldName(fieldName)
                .dims(dims)
                .similarity("l2_norm")
                .index(true)
                .m(32)
                .efConstruction(200)
                .build();
    }
    
    /**
     * 创建推荐系统向量字段映射
     * 适用于推荐系统，使用最大内积
     * 
     * @param fieldName 字段名称
     * @param dims 向量维度
     * @return 向量字段映射
     */
    public static VectorFieldMapping createForRecommendation(String fieldName, int dims) {
        return VectorFieldMapping.builder()
                .fieldName(fieldName)
                .dims(dims)
                .similarity("max_inner_product")
                .index(true)
                .m(16)
                .efConstruction(100)
                .build();
    }
    
    /**
     * 转换为Elasticsearch映射配置
     * 
     * @return Elasticsearch映射配置
     */
    public Map<String, Object> toElasticsearchMapping() {
        Map<String, Object> mapping = new HashMap<>();
        mapping.put("type", "dense_vector");
        mapping.put("dims", dims);
        mapping.put("index", index);
        mapping.put("similarity", similarity);
        
        if (index && m != null && efConstruction != null) {
            Map<String, Object> indexOptions = new HashMap<>();
            indexOptions.put("type", "hnsw");
            indexOptions.put("m", m);
            indexOptions.put("ef_construction", efConstruction);
            mapping.put("index_options", indexOptions);
        }
        
        return mapping;
    }
    
    /**
     * 验证配置的有效性
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        if (fieldName == null || fieldName.trim().isEmpty()) {
            return false;
        }
        if (dims <= 0 || dims > 4096) {
            return false;
        }
        if (similarity == null || !isValidSimilarity(similarity)) {
            return false;
        }
        if (index && (m == null || m <= 0 || efConstruction == null || efConstruction <= 0)) {
            return false;
        }
        return true;
    }
    
    /**
     * 检查相似性度量算法是否有效
     * 
     * @param similarity 相似性度量算法
     * @return 是否有效
     */
    private boolean isValidSimilarity(String similarity) {
        return "cosine".equals(similarity) || 
               "dot_product".equals(similarity) || 
               "l2_norm".equals(similarity) || 
               "max_inner_product".equals(similarity);
    }
}