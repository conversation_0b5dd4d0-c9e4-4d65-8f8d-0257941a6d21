package com.trinasolar.nacos.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 配置验证工具类
 * 
 * 用于验证Nacos相关配置是否正确设置，帮助排查配置问题
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class ConfigurationValidator {

    private static final Logger logger = LoggerFactory.getLogger(ConfigurationValidator.class);

    /**
     * 验证配置结果
     */
    public static class ValidationResult {
        private final boolean valid;
        private final List<String> errors;
        private final List<String> warnings;

        public ValidationResult(boolean valid, List<String> errors, List<String> warnings) {
            this.valid = valid;
            this.errors = errors;
            this.warnings = warnings;
        }

        public boolean isValid() {
            return valid;
        }

        public List<String> getErrors() {
            return errors;
        }

        public List<String> getWarnings() {
            return warnings;
        }

        public void logResults() {
            if (valid) {
                logger.info("✅ Nacos配置验证通过");
            } else {
                logger.error("❌ Nacos配置验证失败");
            }

            for (String error : errors) {
                logger.error("❌ 错误: {}", error);
            }

            for (String warning : warnings) {
                logger.warn("⚠️ 警告: {}", warning);
            }
        }
    }

    /**
     * 验证Nacos配置
     */
    public static ValidationResult validateNacosConfiguration(ConfigurableEnvironment environment) {
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        // 1. 验证基础配置
        validateBasicConfiguration(environment, errors, warnings);

        // 2. 验证服务发现配置
        validateDiscoveryConfiguration(environment, errors, warnings);

        // 3. 验证配置中心配置
        validateConfigConfiguration(environment, errors, warnings);

        // 4. 验证占位符解析
        validatePlaceholderResolution(environment, errors, warnings);

        boolean valid = errors.isEmpty();
        return new ValidationResult(valid, errors, warnings);
    }

    /**
     * 验证基础配置
     */
    private static void validateBasicConfiguration(ConfigurableEnvironment environment, 
                                                  List<String> errors, List<String> warnings) {
        // 检查应用名称
        String applicationName = environment.getProperty("spring.application.name");
        if (!StringUtils.hasText(applicationName)) {
            errors.add("spring.application.name 未配置，这是必需的配置项");
        } else if ("unknown".equals(applicationName)) {
            warnings.add("spring.application.name 为默认值 'unknown'，建议设置为实际应用名称");
        }

        // 检查Profile
        String activeProfile = environment.getProperty("spring.profiles.active");
        if (!StringUtils.hasText(activeProfile)) {
            warnings.add("spring.profiles.active 未配置，将使用默认值 'dev'");
        }

        // 检查应用分组
        String applicationGroup = environment.getProperty("spring.application.group");
        if (!StringUtils.hasText(applicationGroup)) {
            warnings.add("spring.application.group 未配置，将使用默认值 'DEFAULT_GROUP'");
        }
    }

    /**
     * 验证服务发现配置
     */
    private static void validateDiscoveryConfiguration(ConfigurableEnvironment environment, 
                                                      List<String> errors, List<String> warnings) {
        // 检查服务器地址
        String discoveryServerAddr = environment.getProperty("spring.cloud.nacos.discovery.server-addr");
        if (!StringUtils.hasText(discoveryServerAddr)) {
            errors.add("spring.cloud.nacos.discovery.server-addr 未配置");
        } else {
            // 验证地址格式
            if (!discoveryServerAddr.contains(":")) {
                warnings.add("Nacos服务器地址格式可能不正确: " + discoveryServerAddr);
            }
        }

        // 检查命名空间
        String discoveryNamespace = environment.getProperty("spring.cloud.nacos.discovery.namespace");
        if (!StringUtils.hasText(discoveryNamespace)) {
            warnings.add("spring.cloud.nacos.discovery.namespace 未配置，将使用public命名空间");
        }

        // 检查分组
        String discoveryGroup = environment.getProperty("spring.cloud.nacos.discovery.group");
        if (!StringUtils.hasText(discoveryGroup)) {
            warnings.add("spring.cloud.nacos.discovery.group 未配置，将使用默认分组");
        }
    }

    /**
     * 验证配置中心配置
     */
    private static void validateConfigConfiguration(ConfigurableEnvironment environment, 
                                                   List<String> errors, List<String> warnings) {
        // 检查配置中心是否启用
        boolean configEnabled = environment.getProperty("spring.cloud.nacos.config.enabled", Boolean.class, true);
        if (!configEnabled) {
            warnings.add("Nacos配置中心已禁用");
            return;
        }

        // 检查配置中心服务器地址
        String configServerAddr = environment.getProperty("spring.cloud.nacos.config.server-addr");
        if (!StringUtils.hasText(configServerAddr)) {
            errors.add("spring.cloud.nacos.config.server-addr 未配置");
        }

        // 检查文件扩展名
        String fileExtension = environment.getProperty("spring.cloud.nacos.config.file-extension");
        if (!StringUtils.hasText(fileExtension)) {
            warnings.add("spring.cloud.nacos.config.file-extension 未配置，将使用默认值 'yaml'");
        }

        // 检查共享配置
        String sharedConfigDataId = environment.getProperty("spring.cloud.nacos.config.shared-configs[0].data-id");
        if (!StringUtils.hasText(sharedConfigDataId)) {
            warnings.add("未配置共享配置，将自动生成");
        }

        // 检查扩展配置
        String extensionConfigDataId = environment.getProperty("spring.cloud.nacos.config.extension-configs[0].data-id");
        if (!StringUtils.hasText(extensionConfigDataId)) {
            warnings.add("未配置扩展配置，将自动生成");
        }
    }

    /**
     * 验证占位符解析
     */
    private static void validatePlaceholderResolution(ConfigurableEnvironment environment, 
                                                     List<String> errors, List<String> warnings) {
        // 检查是否存在未解析的占位符
        String[] propertiesToCheck = {
            "spring.cloud.nacos.config.shared-configs[0].data-id",
            "spring.cloud.nacos.config.extension-configs[0].data-id",
            "spring.application.namespace"
        };

        for (String property : propertiesToCheck) {
            String value = environment.getProperty(property);
            if (StringUtils.hasText(value) && value.contains("${")) {
                warnings.add("属性 " + property + " 包含未解析的占位符: " + value);
            }
        }
    }

    /**
     * 打印配置信息（用于调试）
     */
    public static void printConfigurationInfo(ConfigurableEnvironment environment) {
        logger.info("📋 Nacos配置信息:");
        logger.info("  应用名称: {}", environment.getProperty("spring.application.name"));
        logger.info("  活动Profile: {}", environment.getProperty("spring.profiles.active"));
        logger.info("  应用分组: {}", environment.getProperty("spring.application.group"));
        logger.info("  应用命名空间: {}", environment.getProperty("spring.application.namespace"));
        
        logger.info("📋 Nacos服务发现配置:");
        logger.info("  服务器地址: {}", environment.getProperty("spring.cloud.nacos.discovery.server-addr"));
        logger.info("  命名空间: {}", environment.getProperty("spring.cloud.nacos.discovery.namespace"));
        logger.info("  分组: {}", environment.getProperty("spring.cloud.nacos.discovery.group"));
        
        logger.info("📋 Nacos配置中心配置:");
        logger.info("  服务器地址: {}", environment.getProperty("spring.cloud.nacos.config.server-addr"));
        logger.info("  命名空间: {}", environment.getProperty("spring.cloud.nacos.config.namespace"));
        logger.info("  分组: {}", environment.getProperty("spring.cloud.nacos.config.group"));
        logger.info("  文件扩展名: {}", environment.getProperty("spring.cloud.nacos.config.file-extension"));
        
        logger.info("📋 共享配置:");
        logger.info("  Data ID: {}", environment.getProperty("spring.cloud.nacos.config.shared-configs[0].data-id"));
        logger.info("  分组: {}", environment.getProperty("spring.cloud.nacos.config.shared-configs[0].group"));
        
        logger.info("📋 扩展配置:");
        logger.info("  Data ID: {}", environment.getProperty("spring.cloud.nacos.config.extension-configs[0].data-id"));
        logger.info("  分组: {}", environment.getProperty("spring.cloud.nacos.config.extension-configs[0].group"));
    }
}
