package com.trinasolar.i18n.web;

import com.trinasolar.i18n.config.I18nProperties;
import com.trinasolar.i18n.core.I18nService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.support.RequestContextUtils;

import java.util.Locale;

/**
 * 国际化拦截器
 *
 * <AUTHOR>
 */
@Slf4j
public class I18nInterceptor implements HandlerInterceptor {

    private final I18nProperties properties;
    private final I18nService i18nService;

    public I18nInterceptor(I18nProperties properties) {
        this.properties = properties;
        this.i18nService = null; // 在Spring环境中会自动注入
    }

    public I18nInterceptor(I18nProperties properties, I18nService i18nService) {
        this.properties = properties;
        this.i18nService = i18nService;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String language = determineLanguage(request);
        if (StringUtils.hasText(language)) {
            // 设置到I18nService
            if (i18nService != null) {
                i18nService.setCurrentLanguage(language);
            }
            
            // 设置到LocaleContextHolder
            Locale locale = parseLocale(language);
            LocaleContextHolder.setLocale(locale);
            
            // 设置到LocaleResolver
            LocaleResolver localeResolver = RequestContextUtils.getLocaleResolver(request);
            if (localeResolver != null) {
                localeResolver.setLocale(request, response, locale);
            }
        }
        
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 清除当前线程的语言设置
        if (i18nService != null) {
            i18nService.clearCurrentLanguage();
        }
        
        // 清除LocaleContextHolder
        LocaleContextHolder.resetLocaleContext();
    }

    /**
     * 从请求中确定语言
     */
    private String determineLanguage(HttpServletRequest request) {
        String language = null;
        
        // 从Header中获取
        if (properties.getInterceptor().isPreferHeader()) {
            language = getLanguageFromHeader(request);
        }
        
        // 从参数中获取
        if (!StringUtils.hasText(language)) {
            language = getLanguageFromParameter(request);
        }
        
        // 如果还是未获取到，尝试从请求的Locale中获取
        if (!StringUtils.hasText(language)) {
            language = getLanguageFromLocale(request);
        }
        
        // 如果仍然未获取到，使用默认语言
        if (!StringUtils.hasText(language)) {
            language = properties.getDefaultLanguage();
        }
        
        // 验证语言是否支持
        if (properties.getSupportedLanguages() != null && !properties.getSupportedLanguages().isEmpty()) {
            if (!properties.getSupportedLanguages().contains(language)) {
                language = properties.getDefaultLanguage();
            }
        }
        
        return language;
    }

    /**
     * 从Header中获取语言
     */
    private String getLanguageFromHeader(HttpServletRequest request) {
        String headerName = properties.getInterceptor().getLanguageHeader();
        if (!StringUtils.hasText(headerName)) {
            headerName = "Accept-Language";
        }
        
        String language = request.getHeader(headerName);
        if (StringUtils.hasText(language)) {
            // 解析Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
            if ("Accept-Language".equalsIgnoreCase(headerName)) {
                // 取第一个语言标签
                if (language.contains(",")) {
                    language = language.split(",")[0];
                }
                // 去除权重
                if (language.contains(";")) {
                    language = language.split(";")[0];
                }
            }
            
            return language.trim();
        }
        
        return null;
    }

    /**
     * 从参数中获取语言
     */
    private String getLanguageFromParameter(HttpServletRequest request) {
        String paramName = properties.getInterceptor().getLanguageParam();
        if (!StringUtils.hasText(paramName)) {
            paramName = "lang";
        }
        
        String language = request.getParameter(paramName);
        if (StringUtils.hasText(language)) {
            return language.trim();
        }
        
        return null;
    }

    /**
     * 从Locale中获取语言
     */
    private String getLanguageFromLocale(HttpServletRequest request) {
        Locale locale = RequestContextUtils.getLocale(request);
        if (locale != null) {
            return locale.toLanguageTag();
        }
        
        return null;
    }

    /**
     * 解析语言为Locale
     */
    private Locale parseLocale(String language) {
        if (!StringUtils.hasText(language)) {
            return Locale.getDefault();
        }
        
        // 处理语言-国家格式
        if (language.contains("-")) {
            String[] parts = language.split("-", 2);
            return new Locale(parts[0], parts[1]);
        }
        
        return new Locale(language);
    }
} 