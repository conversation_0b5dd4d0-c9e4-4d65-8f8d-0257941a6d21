package com.trinasolar.common.security.util;

import com.trinasolar.common.security.context.SecurityContextHolder;
import com.trinasolar.common.security.domain.SecurityContext;
import lombok.experimental.UtilityClass;

import java.util.List;

/**
 * 安全工具类
 * <p>
 * 提供便捷的权限检查和用户信息获取方法
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@UtilityClass
public class SecurityUtils {

    /**
     * 获取当前安全上下文
     */
    public SecurityContext getCurrentContext() {
        return SecurityContextHolder.getContext();
    }

    /**
     * 获取当前用户ID
     */
    public String getCurrentUserId() {
        return SecurityContextHolder.getCurrentUserId();
    }

    /**
     * 获取当前用户名
     */
    public String getCurrentUsername() {
        return SecurityContextHolder.getCurrentUsername();
    }

    /**
     * 获取当前用户角色
     */
    public List<String> getCurrentUserRoleCodes() {
        return SecurityContextHolder.getCurrentUserRoleCodes();
    }

    /**
     * 获取当前用户权限
     */
    public List<String> getCurrentUserPermissions() {
        return SecurityContextHolder.getCurrentUserPermissions();
    }

    /**
     * 获取当前用户数据权限范围
     */
    public List<SecurityContext.Role> getCurrentUserRoles() {
        return SecurityContextHolder.getCurrentUserRoles();
    }

    /**
     * 检查当前用户是否拥有指定角色
     */
    public boolean hasRole(String role) {
        return SecurityContextHolder.hasRole(role);
    }

    /**
     * 检查当前用户是否拥有任意一个角色
     */
    public boolean hasAnyRole(String... roles) {
        return SecurityContextHolder.hasAnyRole(roles);
    }

    /**
     * 检查当前用户是否拥有指定权限
     */
    public boolean hasPermission(String permission) {
        return SecurityContextHolder.hasPermission(permission);
    }

    /**
     * 检查当前用户是否拥有任意一个权限
     */
    public boolean hasAnyPermission(String... permissions) {
        return SecurityContextHolder.hasAnyPermission(permissions);
    }

    /**
     * 检查当前用户是否为管理员
     */
    public boolean isAdmin() {
        return hasRole("admin");
    }

    /**
     * 检查当前用户是否已登录
     */
    public boolean isAuthenticated() {
        return SecurityContextHolder.isAuthenticated();
    }

    /**
     * 检查当前用户是否为超级管理员
     */
    public boolean isSuperAdmin() {
        return hasRole("super_admin");
    }

    /**
     * 获取当前用户的部门ID
     */
    public String getCurrentUserDeptId() {
        SecurityContext context = getCurrentContext();
        return context != null && context.getPrincipal() != null
                ? context.getPrincipal().getDeptId() : null;
    }

    /**
     * 获取当前用户的邮箱
     */
    public String getCurrentUserEmail() {
        SecurityContext context = getCurrentContext();
        return context != null && context.getPrincipal() != null
                ? context.getPrincipal().getEmail() : null;
    }

    /**
     * 获取当前用户的显示名称
     */
    public String getCurrentUserDisplayName() {
        return SecurityContextHolder.getCurrentDisplayName();
    }
}
