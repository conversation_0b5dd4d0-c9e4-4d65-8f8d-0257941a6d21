package com.trinasolar.elasticsearch.base.controller;

import com.trinasolar.common.core.base.R;
import com.trinasolar.elasticsearch.base.entity.BaseEsEntity;
import com.trinasolar.elasticsearch.base.service.BaseEsService;
import com.trinasolar.elasticsearch.base.wrapper.EsQueryWrapper;
import com.trinasolar.elasticsearch.vo.SearchResult;
// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.Parameter;
// import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Elasticsearch基础控制器
 * 提供通用的ES操作接口
 * 
 * @param <S> Service类型，必须继承BaseEsService
 * @param <T> 实体类型，必须继承BaseEsEntity
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
//@Tag(name = "Elasticsearch基础操作", description = "提供通用的ES文档操作接口")
public abstract class BaseEsController<S extends BaseEsService<T>, T extends BaseEsEntity> {
    
    @Autowired
    protected S baseEsService;
    
    // ==================== 索引管理 ====================
    
    @PostMapping("/index/create")
    //@Operation(summary = "创建索引", description = "创建Elasticsearch索引")
    public R<String> createIndex() {
        boolean result = baseEsService.createIndex();
        return result ? R.ok("索引创建成功") : R.failed("索引创建失败");
    }
    
    @DeleteMapping("/index/delete")
    //@Operation(summary = "删除索引", description = "删除Elasticsearch索引")
    public R<String> deleteIndex() {
        boolean result = baseEsService.deleteIndex();
        return result ? R.ok("索引删除成功") : R.failed("索引删除失败");
    }
    
    @GetMapping("/index/exists")
    //@Operation(summary = "检查索引是否存在", description = "检查Elasticsearch索引是否存在")
    public R<Boolean> indexExists() {
        boolean exists = baseEsService.indexExists();
        return R.ok(exists, "索引" + (exists ? "存在" : "不存在"));
    }
    
    @PostMapping("/index/refresh")
    //@Operation(summary = "刷新索引", description = "刷新Elasticsearch索引")
    public R<String> refreshIndex() {
        boolean result = baseEsService.refreshIndex();
        return result ? R.ok("索引刷新成功") : R.failed("索引刷新失败");
    }
    
    // ==================== 文档操作 ====================
    
    @PostMapping("/save")
    //@Operation(summary = "保存文档", description = "保存单个文档到Elasticsearch")
    public R<T> save(@RequestBody T entity) {
        T result = baseEsService.save(entity);
        return R.ok(result, "保存成功");
    }
    
    @PostMapping("/saveBatch")
    //@Operation(summary = "批量保存文档", description = "批量保存文档到Elasticsearch")
    public R<Integer> saveBatch(@RequestBody List<T> entityList) {
        int count = baseEsService.saveBatch(entityList);
        return R.ok(count, "批量保存成功，共保存 " + count + " 条记录");
    }
    
    @DeleteMapping("/remove/{id}")
    //@Operation(summary = "根据ID删除文档", description = "根据ID删除Elasticsearch文档")
    public R<String> removeById(
        //@Parameter(description = "文档ID", required = true)
        @PathVariable("id") Serializable id
    ) {
        boolean result = baseEsService.removeById(id);
        return result ? R.ok("删除成功") : R.failed("文档不存在或删除失败");
    }
    
    @DeleteMapping("/removeBatch")
    //@Operation(summary = "批量删除文档", description = "根据ID列表批量删除文档")
    public R<Integer> removeByIds(@RequestBody List<String> idList) {
        int count = baseEsService.removeByIds(idList);
        return R.ok(count, "批量删除成功，共删除 " + count + " 条记录");
    }
    
    @PutMapping("/update")
    //@Operation(summary = "更新文档", description = "根据ID更新文档")
    public R<T> updateById(@RequestBody T entity) {
        T result = baseEsService.updateById(entity);
        return R.ok(result, "更新成功");
    }
    
    @PutMapping("/updateBatch")
    //@Operation(summary = "批量更新文档", description = "批量更新文档")
    public R<Integer> updateBatchById(@RequestBody List<T> entityList) {
        int count = baseEsService.updateBatchById(entityList);
        return R.ok(count, "批量更新成功，共更新 " + count + " 条记录");
    }
    
    @PostMapping("/saveOrUpdate")
    //@Operation(summary = "保存或更新文档", description = "如果文档存在则更新，否则保存")
    public R<T> saveOrUpdate(@RequestBody T entity) {
        T result = baseEsService.saveOrUpdate(entity);
        return R.ok(result, "操作成功");
    }
    
    @PostMapping("/saveOrUpdateBatch")
    //@Operation(summary = "批量保存或更新文档", description = "批量保存或更新文档")
    public R<Integer> saveOrUpdateBatch(@RequestBody List<T> entityList) {
        int count = baseEsService.saveOrUpdateBatch(entityList);
        return R.ok(count, "批量操作成功，共处理 " + count + " 条记录");
    }
    
    // ==================== 查询操作 ====================
    
    @GetMapping("/get/{id}")
    //@Operation(summary = "根据ID查询文档", description = "根据ID查询单个文档")
    public R<T> getById(
        //@Parameter(description = "文档ID", required = true)
        @PathVariable("id") Serializable id
    ) {
        T entity = baseEsService.getById(id);
        return entity != null ? R.ok(entity, "查询成功") : R.failed("文档不存在");
    }
    
    @PostMapping("/listByIds")
    //@Operation(summary = "根据ID列表查询文档", description = "根据ID列表批量查询文档")
    public R<List<T>> listByIds(@RequestBody List<String> idList) {
        List<T> list = baseEsService.listByIds(idList);
        return R.ok(list, "查询成功");
    }
    
    @PostMapping("/listByMap")
    //@Operation(summary = "根据条件查询文档列表", description = "根据字段条件查询文档列表")
    public R<List<T>> listByMap(@RequestBody Map<String, Object> columnMap) {
        List<T> list = baseEsService.listByMap(columnMap);
        return R.ok(list, "查询成功");
    }
    
    @GetMapping("/list")
    //@Operation(summary = "查询所有文档", description = "查询所有文档列表")
    public R<List<T>> list() {
        List<T> list = baseEsService.list();
        return R.ok(list);
    }
    
    @GetMapping("/count")
    //@Operation(summary = "统计文档数量", description = "统计索引中的文档总数")
    public R<Long> count() {
        long count = baseEsService.count();
        return R.ok(count, "统计成功");
    }
    
    // ==================== 搜索操作 ====================
    
    @PostMapping("/search")
    //@Operation(summary = "搜索文档", description = "根据查询条件搜索文档")
    public R<SearchResult<T>> search(@RequestBody SearchRequest searchRequest) {
        EsQueryWrapper<T> queryWrapper = buildQueryWrapper(searchRequest);
        SearchResult<T> result = baseEsService.search(queryWrapper);
        return R.ok(result, "搜索成功");
    }
    
    @PostMapping("/searchWithHighlight")
    //@Operation(summary = "高亮搜索文档", description = "根据查询条件搜索文档并高亮显示")
    public R<SearchResult<T>> searchWithHighlight(@RequestBody HighlightSearchRequest searchRequest) {
        EsQueryWrapper<T> queryWrapper = buildQueryWrapper(searchRequest);
        String[] highlightFields = searchRequest.getHighlightFields();
        SearchResult<T> result = baseEsService.searchWithHighlight(queryWrapper, highlightFields);
        return R.ok(result);
    }
    
    @PostMapping("/scroll")
    //@Operation(summary = "滚动搜索文档", description = "使用滚动方式搜索大量文档")
    public R<SearchResult<T>> scroll(@RequestBody ScrollSearchRequest searchRequest) {
        EsQueryWrapper<T> queryWrapper = buildQueryWrapper(searchRequest);
        String scrollTime = StringUtils.hasText(searchRequest.getScrollTime()) ?
                searchRequest.getScrollTime() : "1m";
        SearchResult<T> result = baseEsService.scroll(queryWrapper, scrollTime);
        return R.ok(result, "滚动搜索成功");
    }
    
    @PostMapping("/scroll/continue")
    //@Operation(summary = "继续滚动搜索", description = "继续滚动搜索下一批文档")
    public R<SearchResult<T>> continueScroll(@RequestBody ContinueScrollRequest request) {
        String scrollTime = StringUtils.hasText(request.getScrollTime()) ?
                request.getScrollTime() : "1m";
        SearchResult<T> result = baseEsService.scroll(request.getScrollId(), scrollTime);
        return R.ok(result, "继续滚动搜索成功");
    }
    
    @DeleteMapping("/scroll/clear/{scrollId}")
    //@Operation(summary = "清除滚动搜索", description = "清除滚动搜索上下文")
    public R<String> clearScroll(
        //@Parameter(description = "滚动ID", required = true)
        @PathVariable("scrollId") String scrollId
    ) {
        boolean result = baseEsService.clearScroll(scrollId);
        return result ? R.ok("清除滚动搜索成功") : R.failed("清除滚动搜索失败");
    }
    
    // ==================== 聚合操作 ====================
    
    @PostMapping("/aggregation")
    //@Operation(summary = "聚合查询", description = "执行聚合查询")
    public R<Map<String, Object>> aggregation(@RequestBody AggregationRequest request) {
        EsQueryWrapper<T> queryWrapper = buildQueryWrapper(request);
        Map<String, Object> result = baseEsService.aggregations(queryWrapper, request.getAggregations());
        return R.ok(result, "聚合查询成功");
    }
    
    // ==================== 建议操作 ====================
    
    @GetMapping("/suggest")
    //@Operation(summary = "自动补全建议", description = "获取自动补全建议")
    public R<List<String>> suggest(
        //@Parameter(description = "字段名", required = true)
        @RequestParam("field") String field,
        //@Parameter(description = "输入文本", required = true)
        @RequestParam("text") String text,
        //@Parameter(description = "返回数量", required = false)
        @RequestParam(value = "size", defaultValue = "10") int size
    ) {
        List<String> suggestions = baseEsService.suggest(field, text, size);
        return R.ok(suggestions, "获取建议成功");
    }
    
    @GetMapping("/suggest/phrase")
    //@Operation(summary = "短语建议", description = "获取短语建议")
    public R<List<String>> phraseSuggest(
        //@Parameter(description = "字段名", required = true)
        @RequestParam("field") String field,
        //@Parameter(description = "输入文本", required = true)
        @RequestParam("text") String text,
        //@Parameter(description = "返回数量", required = false)
        @RequestParam(value = "size", defaultValue = "10") int size
    ) {
        List<String> suggestions = baseEsService.phraseSuggest(field, text, size);
        return R.ok(suggestions, "获取短语建议成功");
    }
    
    @GetMapping("/suggest/term")
    //@Operation(summary = "词项建议", description = "获取词项建议")
    public R<List<String>> termSuggest(
        //@Parameter(description = "字段名", required = true)
        @RequestParam("field") String field,
        //@Parameter(description = "输入文本", required = true)
        @RequestParam("text") String text,
        //@Parameter(description = "返回数量", required = false)
        @RequestParam(value = "size", defaultValue = "10") int size
    ) {
        List<String> suggestions = baseEsService.termSuggest(field, text, size);
        return R.ok(suggestions, "获取词项建议成功");
    }

    // ==================== 向量搜索操作 ====================

    @PostMapping("/vector/save")
    //@Operation(summary = "保存文档并生成向量", description = "保存文档到Elasticsearch并自动生成向量")
    public R<T> saveWithVector(
        @RequestBody T entity,
        //@Parameter(description = "标题字段名", required = false)
        @RequestParam(value = "titleField", defaultValue = "title") String titleField,
        //@Parameter(description = "内容字段名", required = false)
        @RequestParam(value = "contentField", defaultValue = "content") String contentField
    ) {
        try {
            T result = baseEsService.saveWithVector(entity, titleField, contentField);
            return R.ok(result, "保存并生成向量成功");
        } catch (UnsupportedOperationException e) {
            return R.failed("当前Elasticsearch版本不支持向量功能，请升级到7.3+版本");
        } catch (Exception e) {
            log.error("保存文档并生成向量失败", e);
            return R.failed("保存失败: " + e.getMessage());
        }
    }

    @PostMapping("/vector/saveBatch")
    //@Operation(summary = "批量保存文档并生成向量", description = "批量保存文档到Elasticsearch并自动生成向量")
    public R<List<T>> saveBatchWithVector(
        @RequestBody List<T> entityList,
        //@Parameter(description = "标题字段名", required = false)
        @RequestParam(value = "titleField", defaultValue = "title") String titleField,
        //@Parameter(description = "内容字段名", required = false)
        @RequestParam(value = "contentField", defaultValue = "content") String contentField
    ) {
        try {
            List<T> result = baseEsService.saveBatchWithVector(entityList, titleField, contentField);
            return R.ok(result, "批量保存并生成向量成功，共处理 " + result.size() + " 条记录");
        } catch (UnsupportedOperationException e) {
            return R.failed("当前Elasticsearch版本不支持向量功能，请升级到7.3+版本");
        } catch (Exception e) {
            log.error("批量保存文档并生成向量失败", e);
            return R.failed("批量保存失败: " + e.getMessage());
        }
    }

    @GetMapping("/vector/search")
    //@Operation(summary = "向量搜索", description = "基于向量相似度搜索文档")
    public R<List<T>> vectorSearch(
        //@Parameter(description = "查询文本", required = true)
        @RequestParam("queryText") String queryText,
        //@Parameter(description = "向量字段名", required = false)
        @RequestParam(value = "vectorField", defaultValue = "contentVector") String vectorField,
        //@Parameter(description = "相似度阈值", required = false)
        @RequestParam(value = "threshold", defaultValue = "0.7") Float threshold,
        //@Parameter(description = "返回数量", required = false)
        @RequestParam(value = "size", defaultValue = "10") Integer size
    ) {
        try {
            // 处理URL编码问题
            String decodedQueryText = decodeUrlParameter(queryText);
            log.debug("向量搜索文本: 原始={}, 解码后={}", queryText, decodedQueryText);

            List<T> result = baseEsService.vectorSearch(decodedQueryText, vectorField, threshold, size);
            return R.ok(result, "向量搜索成功，找到 " + result.size() + " 条相似文档");
        } catch (UnsupportedOperationException e) {
            return R.failed("当前Elasticsearch版本不支持向量搜索功能，请升级到7.3+版本");
        } catch (Exception e) {
            log.error("向量搜索失败", e);
            return R.failed("向量搜索失败: " + e.getMessage());
        }
    }

    @GetMapping("/vector/similar/{id}")
    //@Operation(summary = "查找相似文档", description = "根据文档ID查找相似文档")
    public R<List<T>> findSimilarEntities(
        //@Parameter(description = "文档ID", required = true)
        @PathVariable("id") String entityId,
        //@Parameter(description = "向量字段名", required = false)
        @RequestParam(value = "vectorField", defaultValue = "contentVector") String vectorField,
        //@Parameter(description = "相似度阈值", required = false)
        @RequestParam(value = "threshold", defaultValue = "0.7") Float threshold,
        //@Parameter(description = "返回数量", required = false)
        @RequestParam(value = "size", defaultValue = "10") Integer size
    ) {
        try {
            List<T> result = baseEsService.findSimilarEntities(entityId, vectorField, threshold, size);
            return R.ok(result, "查找相似文档成功，找到 " + result.size() + " 条相似文档");
        } catch (UnsupportedOperationException e) {
            return R.failed("当前Elasticsearch版本不支持向量搜索功能，请升级到7.3+版本");
        } catch (Exception e) {
            log.error("查找相似文档失败", e);
            return R.failed("查找相似文档失败: " + e.getMessage());
        }
    }

    @GetMapping("/vector/hybrid")
    //@Operation(summary = "混合搜索", description = "结合关键词搜索和向量搜索")
    public R<List<T>> hybridSearch(
        //@Parameter(description = "查询关键词", required = true)
        @RequestParam("keyword") String keyword,
        //@Parameter(description = "搜索字段", required = false)
        @RequestParam(value = "keywordFields", required = false) String[] keywordFields,
        //@Parameter(description = "向量字段名", required = false)
        @RequestParam(value = "vectorField", defaultValue = "contentVector") String vectorField,
        //@Parameter(description = "关键词权重", required = false)
        @RequestParam(value = "keywordWeight", defaultValue = "0.6") Float keywordWeight,
        //@Parameter(description = "向量权重", required = false)
        @RequestParam(value = "vectorWeight", defaultValue = "0.4") Float vectorWeight,
        //@Parameter(description = "向量相似度阈值", required = false)
        @RequestParam(value = "threshold", defaultValue = "0.7") Float threshold,
        //@Parameter(description = "返回数量", required = false)
        @RequestParam(value = "size", defaultValue = "10") Integer size
    ) {
        try {
            // 处理URL编码问题
            String decodedKeyword = decodeUrlParameter(keyword);
            log.debug("混合搜索关键词: 原始={}, 解码后={}", keyword, decodedKeyword);

            List<T> result = baseEsService.hybridSearch(decodedKeyword, keywordFields, vectorField,
                keywordWeight, vectorWeight, threshold, size);
            return R.ok(result, "混合搜索成功，找到 " + result.size() + " 条文档");
        } catch (UnsupportedOperationException e) {
            return R.failed("当前Elasticsearch版本不支持向量搜索功能，请升级到7.3+版本");
        } catch (Exception e) {
            log.error("混合搜索失败", e);
            return R.failed("混合搜索失败: " + e.getMessage());
        }
    }

    // ==================== 文档导入操作 ====================

    @PostMapping("/import/file")
    //@Operation(summary = "导入文件", description = "从文件内容创建文档")
    public R<T> importFile(
        //@Parameter(description = "文件名", required = true)
        @RequestParam("fileName") String fileName,
        //@Parameter(description = "文件内容", required = true)
        @RequestBody String fileContent,
        //@Parameter(description = "作者", required = false)
        @RequestParam(value = "author", defaultValue = "系统导入") String author
    ) {
        try {
            // 这里需要子类实现具体的文件导入逻辑
            throw new UnsupportedOperationException("请在子类中实现文件导入逻辑");
        } catch (Exception e) {
            log.error("导入文件失败", e);
            return R.failed("导入文件失败: " + e.getMessage());
        }
    }

    // ==================== 系统信息操作 ====================

    @GetMapping("/system/info")
    //@Operation(summary = "获取系统信息", description = "获取Elasticsearch系统信息和版本支持情况")
    public R<Map<String, Object>> getSystemInfo() {
        try {
            Map<String, Object> systemInfo = baseEsService.getSystemInfo();
            return R.ok(systemInfo, "获取系统信息成功");
        } catch (Exception e) {
            log.error("获取系统信息失败", e);
            return R.failed("获取系统信息失败: " + e.getMessage());
        }
    }

    @GetMapping("/system/version")
    //@Operation(summary = "检查版本支持", description = "检查当前Elasticsearch版本的功能支持情况")
    public R<Map<String, Object>> checkVersionSupport() {
        try {
            Map<String, Object> versionInfo = baseEsService.checkVersionSupport();
            return R.ok(versionInfo, "版本检查完成");
        } catch (Exception e) {
            log.error("版本检查失败", e);
            return R.failed("版本检查失败: " + e.getMessage());
        }
    }

    @GetMapping("/system/test-encoding")
    //@Operation(summary = "测试字符编码", description = "测试URL参数的字符编码处理")
    public R<Map<String, Object>> testEncoding(
        @RequestParam(value = "text", defaultValue = "测试中文") String text
    ) {
        Map<String, Object> result = new HashMap<>();
        result.put("originalText", text);
        result.put("decodedText", decodeUrlParameter(text));
        result.put("sanitizedText", sanitizeKeyword(text));
        result.put("textLength", text.length());
        result.put("textBytes", text.getBytes(StandardCharsets.UTF_8).length);
        result.put("isValidUtf8", isValidUtf8String(text));
        result.put("containsPercent", text.contains("%"));
        result.put("timestamp", getCurrentTimeString());

        // 详细的编码信息
        Map<String, Object> encodingDetails = new HashMap<>();
        try {
            byte[] utf8Bytes = text.getBytes(StandardCharsets.UTF_8);
            StringBuilder hexString = new StringBuilder();
            for (byte b : utf8Bytes) {
                hexString.append(String.format("%02x ", b));
            }
            encodingDetails.put("utf8Hex", hexString.toString().trim());
            encodingDetails.put("utf8ByteArray", utf8Bytes);
        } catch (Exception e) {
            encodingDetails.put("error", e.getMessage());
        }
        result.put("encodingDetails", encodingDetails);

        log.info("字符编码测试: 原始={}, 解码={}, 清理={}, 有效UTF8={}",
            text, decodeUrlParameter(text), sanitizeKeyword(text), isValidUtf8String(text));

        return R.ok(result, "字符编码测试完成");
    }

    @PostMapping("/system/test-encoding-post")
    //@Operation(summary = "POST方式测试字符编码", description = "通过POST请求体测试字符编码")
    public R<Map<String, Object>> testEncodingPost(@RequestBody Map<String, String> request) {
        String text = request.getOrDefault("text", "测试中文");

        Map<String, Object> result = new HashMap<>();
        result.put("originalText", text);
        result.put("textLength", text.length());
        result.put("textBytes", text.getBytes(StandardCharsets.UTF_8).length);
        result.put("timestamp", getCurrentTimeString());
        result.put("method", "POST");

        log.info("POST字符编码测试: 文本={}, 长度={}", text, text.length());

        return R.ok(result, "POST字符编码测试完成");
    }

    // ==================== 工具方法 ====================
    
    /**
     * 构建查询包装器
     * 子类可以重写此方法来自定义查询逻辑
     */
    protected EsQueryWrapper<T> buildQueryWrapper(SearchRequest searchRequest) {
        EsQueryWrapper<T> queryWrapper = baseEsService.queryWrapper();
        
        // 设置分页
        if (searchRequest.getFrom() != null) {
            queryWrapper.from(searchRequest.getFrom());
        }
        if (searchRequest.getSize() != null) {
            queryWrapper.size(searchRequest.getSize());
        }
        
        // 设置查询条件
        if (StringUtils.hasText(searchRequest.getKeyword())) {
            if (searchRequest.getSearchFields() != null && searchRequest.getSearchFields().length > 0) {
                queryWrapper.multiMatch(searchRequest.getKeyword(), searchRequest.getSearchFields());
            } else {
                queryWrapper.queryString(searchRequest.getKeyword());
            }
        }
        
        // 设置过滤条件
        if (searchRequest.getFilters() != null && !searchRequest.getFilters().isEmpty()) {
            searchRequest.getFilters().forEach(queryWrapper::eq);
        }
        
        // 设置排序
        if (searchRequest.getSorts() != null && !searchRequest.getSorts().isEmpty()) {
            searchRequest.getSorts().forEach((field, order) -> {
                if ("asc".equalsIgnoreCase(order)) {
                    queryWrapper.orderByAsc(field);
                } else {
                    queryWrapper.orderByDesc(field);
                }
            });
        }
        
        return queryWrapper;
    }
    
    // ==================== 请求对象 ====================
    
    /**
     * 基础搜索请求
     */
    public static class SearchRequest {
        private String keyword;
        private String[] searchFields;
        private Map<String, Object> filters;
        private Map<String, String> sorts;
        private Integer from;
        private Integer size;
        
        // getters and setters
        public String getKeyword() { return keyword; }
        public void setKeyword(String keyword) { this.keyword = keyword; }
        
        public String[] getSearchFields() { return searchFields; }
        public void setSearchFields(String[] searchFields) { this.searchFields = searchFields; }
        
        public Map<String, Object> getFilters() { return filters; }
        public void setFilters(Map<String, Object> filters) { this.filters = filters; }
        
        public Map<String, String> getSorts() { return sorts; }
        public void setSorts(Map<String, String> sorts) { this.sorts = sorts; }
        
        public Integer getFrom() { return from; }
        public void setFrom(Integer from) { this.from = from; }
        
        public Integer getSize() { return size; }
        public void setSize(Integer size) { this.size = size; }
    }
    
    /**
     * 高亮搜索请求
     */
    public static class HighlightSearchRequest extends SearchRequest {
        private String[] highlightFields;
        
        public String[] getHighlightFields() { return highlightFields; }
        public void setHighlightFields(String[] highlightFields) { this.highlightFields = highlightFields; }
    }
    
    /**
     * 滚动搜索请求
     */
    public static class ScrollSearchRequest extends SearchRequest {
        private String scrollTime;
        
        public String getScrollTime() { return scrollTime; }
        public void setScrollTime(String scrollTime) { this.scrollTime = scrollTime; }
    }
    
    /**
     * 继续滚动请求
     */
    public static class ContinueScrollRequest {
        private String scrollId;
        private String scrollTime;
        
        public String getScrollId() { return scrollId; }
        public void setScrollId(String scrollId) { this.scrollId = scrollId; }
        
        public String getScrollTime() { return scrollTime; }
        public void setScrollTime(String scrollTime) { this.scrollTime = scrollTime; }
    }
    
    /**
     * 聚合请求
     */
    public static class AggregationRequest extends SearchRequest {
        private Map<String, String> aggregations;
        
        public Map<String, String> getAggregations() { return aggregations; }
        public void setAggregations(Map<String, String> aggregations) { this.aggregations = aggregations; }
    }

    // ==================== 工具方法 ====================

    /**
     * 获取当前时间字符串
     */
    protected String getCurrentTimeString() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 解码URL参数，处理中文字符编码问题
     */
    protected String decodeUrlParameter(String parameter) {
        if (parameter == null) {
            return null;
        }

        try {
            // 检查是否已经是正确的UTF-8字符串
            if (isValidUtf8String(parameter)) {
                log.debug("参数已经是有效的UTF-8字符串: {}", parameter);
                return parameter;
            }

            // 尝试URL解码
            String decoded = java.net.URLDecoder.decode(parameter, "UTF-8");
            log.debug("URL参数解码: {} -> {}", parameter, decoded);
            return decoded;
        } catch (Exception e) {
            log.warn("URL参数解码失败，使用原始参数: {}", parameter, e);
            return parameter;
        }
    }

    /**
     * 检查字符串是否为有效的UTF-8字符串
     */
    private boolean isValidUtf8String(String str) {
        try {
            // 如果字符串包含URL编码的百分号，说明需要解码
            if (str.contains("%")) {
                return false;
            }

            // 检查是否包含非ASCII字符但能正确显示
            byte[] bytes = str.getBytes(StandardCharsets.UTF_8);
            String reconstructed = new String(bytes, StandardCharsets.UTF_8);
            return str.equals(reconstructed);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 验证和清理搜索关键词
     */
    protected String sanitizeKeyword(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return "";
        }

        // 移除特殊字符，保留中文、英文、数字和常用符号
        String sanitized = keyword.replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9\\s\\-_.]", "");
        log.debug("关键词清理: {} -> {}", keyword, sanitized);
        return sanitized.trim();
    }
}