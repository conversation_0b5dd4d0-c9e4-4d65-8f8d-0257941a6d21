package com.trinasolar.openfeign.exception;


import java.io.Serial;

/**
 * Feign 解码 IO 错误
 *
 * <AUTHOR>
 * @since 2025-06-11 16:26
 **/
public class FeignDecodeIOException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = 8256878533915791230L;

    public FeignDecodeIOException() {
        super();
    }

    public FeignDecodeIOException(String message) {
        super(message);
    }

    public FeignDecodeIOException(String message, Throwable cause) {
        super(message, cause);
    }

    public FeignDecodeIOException(Throwable cause) {
        super(cause);
    }

    protected FeignDecodeIOException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
