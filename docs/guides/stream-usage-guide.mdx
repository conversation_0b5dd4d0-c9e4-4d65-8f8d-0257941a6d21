---
title: Trina Stream Starter 使用指南
description: Trina Stream Starter 组件的快速开始、配置方法和使用说明
---

# Trina Stream Starter 使用指南

Trina Stream Starter 是 Trina 团队提供的快速规范的集成分布式消息中间件组件，基于Spring Cloud Stream的统一分布式消息队列组件，支持Kafka、RabbitMQ、RocketMQ的自动切换和统一API。

## 1. 快速开始

### 1.1 引入依赖

在 `pom.xml` 中添加如下依赖：

```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-stream-starter</artifactId>
    <version>4.0.0</version>
</dependency>
```

### 1.2 基础配置

在 `application.yml` 中添加基本配置：

```yaml
spring:
  cloud:
    stream:
      bindings:
        # 新版本固定格式  函数名-{out/in}-{index}
        userEvent-out-0:
          destination: user-events
          content-type: application/json
        # 新版本固定格式  函数名-{out/in}-{index}
        userEvent-in-0:
          destination: user-events
          group: user-service
          content-type: application/json
      # 启用Binder，支持kafka、rabbit、rocketmq
      default-binder: rabbit
      # kafka 配置示例
      kafka:
        binder:
          brokers: localhost:9092
      # rabbit 配置示例
      rabbit:
        binder:
          nodes:  localhost:5672
      # rocketmq 配置示例
      rocketmq:
        binder:
          name-server: localhost:9876
```

## 2. 核心功能

### ✅ 核心功能
- **多MQ支持**: 支持Kafka、RabbitMQ、RocketMQ自动切换
- **统一API**: 提供StreamTemplate统一消息发送接口
- **消费模式**: 支持Push和Pull两种消费模式
- **消息管理**: 支持消息优先级、标签、分组、延时等特性
- **重试机制**: 内置消息重试和死信队列支持
- **链路追踪**: 支持消息链路追踪
- **详细日志**: 提供完整的消息发送和消费日志

### 🛠 高级特性
- **消息优先级**: 支持LOW、NORMAL、HIGH三种优先级
- **延迟消息**: 支持延迟消息发送
- **异步处理**: 支持同步和异步消息发送
- **异常处理**: 完善的异常处理和重试机制
- **监控集成**: 支持链路追踪和监控指标
- **配置灵活**: 丰富的配置选项，支持不同场景需求

### 2.1 定义事件或消息对象

```java
/**
 * 用户事件示例
 * <p>
 * 演示如何定义领域事件对象
 * </p>
 *
 * <AUTHOR>
 */
@Data
public class UserEvent {

    private String userId;
    private String eventType;
    private String data;
    private LocalDateTime timestamp;

    public UserEvent() {
        this.timestamp = LocalDateTime.now();
    }

    public UserEvent(String userId, String eventType, String data) {
        this();
        this.userId = userId;
        this.eventType = eventType;
        this.data = data;
    }
}
```

### 2.2 消息生产者

```java
/**
 * 用户事件生产者示例
 * <p>
 * 演示如何使用TrinaStreamTemplate和DomainEventPublisher发送消息
 * </p>
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class UserEventProducer {

    private static final Logger log = LoggerFactory.getLogger(UserEventProducer.class);

    private final StreamTemplate streamTemplate;

    /**
     * 使用StreamTemplate发送普通消息
     */
    public boolean sendUserMessage(String topic, UserEvent userEvent) {
        log.info("发送用户消息: topic={}, event={}", topic, userEvent);
        return streamTemplate.send(topic, userEvent);
    }
}
```

```java
@RestController
@RequiredArgsConstructor
public class UserStreamController {

    private final UserEventProducer userEventProducer;

    @GetMapping("/stream/user")
    public R<Boolean> userStream() {
        UserEvent userEvent = new UserEvent("user1", "USER_CREATED", "用户创建");
        return R.ok(userEventProducer.sendUserMessage("user-events", userEvent));
    }
}
```

### 2.3 消息消费者

```java
/**
 * 用户事件消费者示例 - Push模式
 * <p>
 * 演示如何使用AbstractStreamConsumer实现Push模式的消息消费
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class UserEventConsumer {

    // 注意：这里的函数名未userEvent，与yml中bindings下的in前面一段对应
    @Bean
    public Consumer<UserEvent> userEvent() {
        /*return new Consumer<String>() {
            @Override
            public void accept(String message) {
                System.out.println("userEvent接到消息：" + message);
            }
        };*/
        return userEvent -> {
            System.out.println("userEvent接到消息：" + userEvent);
        };
    }
}
```

## 3 使用说明

### 3.1 默认依赖

Trina Stream Starter 默认依赖已包含kafka、rabbit、rocketmq三种MQ，如果需要使用其他MQ，请自行添加依赖：

```xml
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-stream</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-stream-kafka</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-stream-rabbit</artifactId>
</dependency>
<dependency>
    <groupId>com.alibaba.cloud</groupId>
    <artifactId>spring-cloud-starter-stream-rocketmq</artifactId>
</dependency>
```

### 3.2 默认配置

默认情况下，Trina Stream Starter 使用kafka作为默认的MQ，并使用localhost的默认配置。

```yaml
# Spring Cloud Stream 配置
spring:
  cloud:
    stream:
      default-binder: kafka
      # Kafka特定配置
      kafka:
        binder:
          brokers: localhost:9092
          auto-create-topics: true
          auto-add-partitions: true
      rabbit:
        binder:
          nodes: localhost:5672
      rocketmq:
        binder:
          name-server: localhost:9876
  rabbitmq:
    addresses: ${spring.cloud.stream.rabbit.binder.nodes}
```

### 3.3 注意事项

由于Trina Stream Starter 是基于Spring Cloud Stream（最新版本）灵活的编程模型来构建与共享消息传递系统连接的高度可伸缩的事件驱动微服务框架，所以在使用时需要注意以下事项：

- **bindings通道配置**: 新版本固定格式 函数名-out/in-index，函数名与消费者驼峰函数名一致
- **default-binder启用配置**: 启用Binder，支持kafka、rabbit、rocketmq，与下面配置的binder相关
- **StreamTemplate使用**: 统一使用StreamTemplate发送消息，底层使用StreamBridge发送消息
- **StreamMessage T**: 统一消息包装，封装了消息ID、消息主题/队列名称、消息内容、消息头、消息优先级、创建时间、过期时间等信息

