package com.trinasolar.common.core.utils;

import com.trinasolar.common.core.exception.BusinessException;
import com.trinasolar.common.core.helper.LoggerHelper;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Marshaller;
import jakarta.xml.bind.Unmarshaller;
import org.w3c.dom.Document;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

import javax.xml.XMLConstants;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.*;
import java.nio.charset.StandardCharsets;

public class XmlConvertUtil {

    /**
     * xml to java bean
     *
     * @param clazz
     * @param xmlStr
     * @param <T>
     * @return
     */
    public static <T> T xmlToJava(Class<T> clazz, String xmlStr) {
        try {
            JAXBContext context = JAXBContext.newInstance(clazz);
            Unmarshaller unmarshaller = context.createUnmarshaller();
            return clazz.cast(unmarshaller.unmarshal(new StringReader(xmlStr)));
        } catch (JAXBException e) {
            throw new BusinessException("xml convert to javaBean error：" + e.getMessage());
        }
    }

    /**
     * java bean to xml
     *
     * @param obj
     * @param <T>
     * @return
     */
    public static <T> String javaToxml(T obj) {
        StringWriter sw = new StringWriter();
        try {
            JAXBContext context = JAXBContext.newInstance(obj.getClass());
            Marshaller marshaller = context.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE);
            marshaller.marshal(obj, sw);
        } catch (JAXBException e) {
            throw new BusinessException("javaBean convert to xml error：" + e.getMessage());
        }
        return sw.toString();
    }

    public static InputStream stringToInputStream(String string) {
        return new ByteArrayInputStream(string.getBytes(StandardCharsets.UTF_8));
    }

    public static String stream2str(InputStream inputStream) {
        try (BufferedReader in = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            StringBuilder buffer = new StringBuilder();
            String line;
            while ((line = in.readLine()) != null) {
                buffer.append(line);
            }
            return buffer.toString();
        } catch (IOException e) {
            LoggerHelper.errorWithDebugger("IOException error", e);
            throw new BusinessException("Stream to string conversion error: " + e.getMessage());
        }
    }

    public static Document stringToDoc(String xmlStr) {
        //字符串转XML
        Document doc = null;
        try {
            xmlStr = new String(xmlStr.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
            StringReader sr = new StringReader(xmlStr);
            InputSource is = new InputSource(sr);
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            String feature = "http://apache.org/xml/features/disallow-doctype-decl";
            factory.setFeature(feature, true);
            // 添加额外的安全配置
            factory.setFeature("http://xml.org/sax/features/external-general-entities", false);
            factory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            factory.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
            factory.setXIncludeAware(false);
            factory.setExpandEntityReferences(false);

            DocumentBuilder builder = factory.newDocumentBuilder();
            doc = builder.parse(is);
        } catch (ParserConfigurationException | SAXException | IOException e) {
            LoggerHelper.errorWithDebugger("XML parsing error ", e);
            throw new BusinessException("XML parsing error: " + e.getMessage());
        }
        return doc;
    }

    public static String docToString(Document doc) {
        // XML转字符串
        try {
            TransformerFactory tf = TransformerFactory.newInstance();
            tf.setFeature(XMLConstants.FEATURE_SECURE_PROCESSING, true);
            Transformer t = tf.newTransformer();
            t.setOutputProperty("encoding", "UTF-8");
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            t.transform(new DOMSource(doc), new StreamResult(bos));
            return bos.toString(StandardCharsets.UTF_8);
        } catch (TransformerException e) {
            LoggerHelper.errorWithDebugger("TransformerException error ", e);
            throw new BusinessException("Document to string conversion error: " + e.getMessage());
        }
    }

    public static class MyByteArrayOutputStream extends ByteArrayOutputStream {
        public ByteArrayInputStream getByteArrayInputStream() {
            return new ByteArrayInputStream(this.toByteArray());
        }
    }
}