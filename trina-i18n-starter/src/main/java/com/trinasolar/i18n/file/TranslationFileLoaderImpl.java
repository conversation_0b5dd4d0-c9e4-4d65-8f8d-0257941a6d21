package com.trinasolar.i18n.file;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trinasolar.i18n.config.I18nProperties;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 翻译文件加载器实现
 *
 * <AUTHOR>
 */
public class TranslationFileLoaderImpl implements TranslationFileLoader {

    private static final Logger log = LoggerFactory.getLogger(TranslationFileLoaderImpl.class);
    
    private final I18nProperties properties;
    private final ObjectMapper objectMapper;
    private final Map<String, Map<String, String>> cachedTranslations;
    private final Map<String, Map<String, String>> cachedNewTranslations;
    private final ResourceLoader resourceLoader;

    public TranslationFileLoaderImpl(I18nProperties properties) {
        this.properties = properties;
        this.objectMapper = new ObjectMapper();
        this.cachedTranslations = new ConcurrentHashMap<>();
        this.cachedNewTranslations = new ConcurrentHashMap<>();
        this.resourceLoader = null; // 在Spring环境中会自动注入
    }

    public TranslationFileLoaderImpl(I18nProperties properties, ResourceLoader resourceLoader) {
        this.properties = properties;
        this.objectMapper = new ObjectMapper();
        this.cachedTranslations = new ConcurrentHashMap<>();
        this.cachedNewTranslations = new ConcurrentHashMap<>();
        this.resourceLoader = resourceLoader;
    }

    @Override
    public Optional<String> loadTranslation(String key, String language) {
        if (key == null || language == null) {
            return Optional.empty();
        }

        // 按照顺序尝试查找翻译
        // 1. 从原始翻译文件查找
        Map<String, String> translations = getOrLoadTranslations(language);
        if (translations.containsKey(key)) {
            return Optional.ofNullable(translations.get(key));
        }

        // 2. 从新翻译文件查找
        Map<String, String> newTranslations = getOrLoadNewTranslations(language);
        return Optional.ofNullable(newTranslations.get(key));
    }

    @Override
    public Map<String, String> loadAllTranslations(String language) {
        if (language == null) {
            return Collections.emptyMap();
        }

        // 合并原始翻译和新翻译
        Map<String, String> result = new HashMap<>();
        result.putAll(getOrLoadTranslations(language));
        result.putAll(getOrLoadNewTranslations(language));
        return result;
    }

    @Override
    public void reloadAllFiles() {
        // 清除所有缓存的翻译
        cachedTranslations.clear();
        cachedNewTranslations.clear();

        // 重新加载支持的语言翻译
        if (properties.getSupportedLanguages() != null) {
            properties.getSupportedLanguages().forEach(this::reloadFiles);
        }

        // 确保默认语言被加载
        if (StringUtils.hasText(properties.getDefaultLanguage())) {
            reloadFiles(properties.getDefaultLanguage());
        }
    }

    @Override
    public void reloadFiles(String language) {
        if (language == null) {
            return;
        }

        // 移除缓存中的翻译
        cachedTranslations.remove(language);
        cachedNewTranslations.remove(language);

        // 重新加载翻译
        getOrLoadTranslations(language);
        getOrLoadNewTranslations(language);
    }

    @Override
    public String getFilePath(String language, boolean isNewTranslation) {
        if (language == null) {
            return null;
        }

        String baseDir = properties.getFilePath();
        if (!StringUtils.hasText(baseDir)) {
            baseDir = "i18n";
        }

        String fileName = isNewTranslation ? "new-" + language + ".json" : language + ".json";
        return baseDir + File.separator + fileName;
    }

    /**
     * 获取或加载原始翻译
     */
    private Map<String, String> getOrLoadTranslations(String language) {
        return cachedTranslations.computeIfAbsent(language, this::loadTranslationsFromFile);
    }

    /**
     * 获取或加载新翻译
     */
    private Map<String, String> getOrLoadNewTranslations(String language) {
        return cachedNewTranslations.computeIfAbsent(language, this::loadNewTranslationsFromFile);
    }

    /**
     * 从文件加载原始翻译
     */
    private Map<String, String> loadTranslationsFromFile(String language) {
        return loadFromFile(getFilePath(language, false));
    }

    /**
     * 从文件加载新翻译
     */
    private Map<String, String> loadNewTranslationsFromFile(String language) {
        return loadFromFile(getFilePath(language, true));
    }

    /**
     * 从文件加载翻译映射
     */
    private Map<String, String> loadFromFile(String filePath) {
        if (!StringUtils.hasText(filePath)) {
            return Collections.emptyMap();
        }

        try {
            // 尝试从Spring资源加载
            if (resourceLoader != null) {
                Resource resource = resourceLoader.getResource("classpath:" + filePath);
                if (resource.exists()) {
                    try (Reader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8)) {
                        String json = FileCopyUtils.copyToString(reader);
                        return objectMapper.readValue(json, new TypeReference<Map<String, String>>() {});
                    }
                }
            }

            // 尝试从文件系统加载
            Path path = Paths.get(filePath);
            if (Files.exists(path)) {
                String json = Files.readString(path, StandardCharsets.UTF_8);
                return objectMapper.readValue(json, new TypeReference<Map<String, String>>() {});
            }
        } catch (IOException e) {
            log.error("Failed to load translations from file: {}", filePath, e);
        }

        return Collections.emptyMap();
    }
}