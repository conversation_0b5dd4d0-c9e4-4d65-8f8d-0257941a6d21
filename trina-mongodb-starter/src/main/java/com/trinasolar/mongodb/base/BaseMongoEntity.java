package com.trinasolar.mongodb.base;

import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * MongoDB基础实体类
 * 包含所有MongoDB文档共有的基础字段
 */
public class BaseMongoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文档ID
     */
    @Id
    private String id;

    /**
     * 创建时间
     */
    @CreatedDate
    @Field("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Field("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    @CreatedBy
    @Field("create_by")
    private String createBy;

    /**
     * 更新人ID
     */
    @LastModifiedBy
    @Field("update_by")
    private String updateBy;

    /**
     * 是否删除(0-未删除，1-已删除)
     */
    @Field("deleted")
    private Integer deleted = 0;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    /**
     * 实体保存前的处理
     * 如果是新实体，设置创建时间
     * 更新实体时，设置更新时间
     */
    public void prePersist() {
        LocalDateTime now = LocalDateTime.now();
        if (this.createTime == null) {
            this.createTime = now;
        }
        this.updateTime = now;
        
        if (this.deleted == null) {
            this.deleted = 0;
        }
    }
} 