package com.trinasolar.minio.service;

import java.io.InputStream;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import com.trinasolar.minio.entity.FileResult;
import com.trinasolar.minio.enums.FileServiceType;
import com.trinasolar.minio.utils.SpringUtils;
import com.trinasolar.minio.vo.MinioItem;
import io.minio.StatObjectResponse;
import io.minio.messages.Bucket;

/**
 * @ClassName FileTemplate
 * @Description 文件服务接口层
 * <AUTHOR>
 * @Date 2021/12/23 11:55 上午
 **/
public interface FileTemplate {

    /**
     * 根据userType返回实现类
     *
     * @return
     */
    static FileTemplate getInstance(String type){

        String serviceName = FileServiceType.getServiceNameByType(type);
        return (FileTemplate)SpringUtils.getContext().getBean(serviceName);
    }

    /**
     * 创建bucket
     *
     * @param bucketName bucket名称
     */
    void createBucket(String bucketName);

    /**
     * 获取全部bucket
     */
    List<Bucket> getAllBuckets();

    /**
     * @param bucketName bucket名称
     */
    Optional<Bucket> getBucket(String bucketName);

    /**
     * @param bucketName bucket名称
     */
    void removeBucket(String bucketName);

    /**
     * 根据文件前置查询文件
     *
     * @param bucketName bucket名称
     * @param prefix     前缀
     * @param recursive  是否递归查询
     * @return MinioItem 列表
     */
    List<MinioItem> getAllObjectsByPrefix(String bucketName, String prefix, boolean recursive);

    /**
     * 获取文件外链
     *
     * @param bucketName bucket名称
     * @param objectName 文件名称
     * @param expires    过期时间 <=7
     * @return url
     */
    String getObjectURL(String bucketName, String objectName, Integer expires);

    /**
     * 获取文件
     *
     * @param bucketName bucket名称
     * @param objectName 文件名称
     * @return 二进制流
     */
    InputStream getObject(String bucketName, String objectName);

    /**
     * 上传文件
     *
     * @param bucketName bucket名称
     * @param objectName 文件名称
     * @param stream     文件流
     */
    FileResult putObject(String bucketName, String objectName, InputStream stream) throws Exception;

    /**
     * 使用objectName作为文件名，如果存在则覆盖
     * @param bucketName
     * @param objectName
     * @param stream
     * @return
     * @throws Exception
     */
    FileResult putObjectForce(String bucketName, String objectName, InputStream stream) throws Exception;

    /**
     * 上传文件
     *
     * @param bucketName  bucket名称
     * @param objectName  文件名称
     * @param stream      文件流
     * @param size        大小
     * @param contextType 类型
     */
    FileResult putObject(String bucketName, String objectName, InputStream stream, long size, String contextType)
        throws Exception;

    /**
     * 获取文件信息
     *
     * @param bucketName bucket名称
     * @param objectName 文件名称
     */
    StatObjectResponse getObjectInfo(String bucketName, String objectName) throws Exception;

    String getPresignedObjectUrl(String bucket, String object, Integer expireTime, TimeUnit timeUnit) throws Exception;

    /**
     * 删除文件
     *
     * @param bucketName bucket名称
     * @param objectName 文件名称
     */
    void removeObject(String bucketName, String objectName) throws Exception;



}
