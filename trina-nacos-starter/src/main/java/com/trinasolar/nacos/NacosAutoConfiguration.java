package com.trinasolar.nacos;

import com.alibaba.cloud.nacos.NacosConfigProperties;
import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.trinasolar.nacos.utils.NacosConnectionChecker;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.util.StringUtils;

/**
 * Nacos自动配置类
 * <p>
 * 优化后的配置类，提供更好的配置管理和连接检查
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
@ConditionalOnClass({NacosDiscoveryProperties.class, NacosConfigProperties.class})
public class NacosAutoConfiguration {

    private final ConfigurableEnvironment environment;

    public NacosAutoConfiguration(ConfigurableEnvironment environment) {
        this.environment = environment;

        log.info("🚀 Nacos自动配置已启用, 服务器地址: {}, 命名空间: {}, 分组: {}",
                environment.getProperty("spring.cloud.nacos.discovery.server-addr"),
                environment.getProperty("spring.cloud.nacos.discovery.namespace"),
                environment.getProperty("spring.cloud.nacos.discovery.group")
        );
    }

    /**
     * 测试Nacos连接
     */
    @PostConstruct
    public void testNacosConnection() {
        // 检查是否启用了Nacos
        boolean nacosEnabled = environment.getProperty("spring.cloud.nacos.discovery.enabled", Boolean.class, true);
        if (!nacosEnabled) {
            log.info("📋 Nacos未启用，跳过配置导入处理");
            return;
        }

        String serverAddr = environment.getProperty("spring.cloud.nacos.discovery.server-addr");
        if (!StringUtils.hasText(serverAddr)) {
            log.warn("⚠️ Nacos服务器地址未配置，跳过连接检查");
            return;
        }

        log.info("🔍 开始检查Nacos连接...");

        // 创建临时的NacosDiscoveryProperties用于测试连接
        NacosDiscoveryProperties properties = new NacosDiscoveryProperties();
        properties.setServerAddr(serverAddr);
        properties.setNamespace(environment.getProperty("spring.cloud.nacos.discovery.namespace"));
        properties.setUsername(environment.getProperty("spring.cloud.nacos.discovery.username"));
        properties.setPassword(environment.getProperty("spring.cloud.nacos.discovery.password"));

        NacosConnectionChecker.ConnectionCheckResult result = NacosConnectionChecker.checkDiscoveryConnection(properties);

        if (result.isSuccess()) {
            log.info("✅ Nacos连接成功: {}", serverAddr);
        } else {
            log.warn("❌ Nacos连接失败: {}, 错误: {}", serverAddr, result.getMessage());
        }
    }
}
