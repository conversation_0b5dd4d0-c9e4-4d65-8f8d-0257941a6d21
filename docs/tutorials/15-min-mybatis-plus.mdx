---
title: "15分钟了解MyBatis-Plus框架"
description: "快速掌握MyBatis-Plus的核心概念、主要特性、应用场景及入门示例"
date: "2024-03-21"
tags: ["MyBatis-Plus", "ORM", "数据库", "持久层"]
---

# 15分钟了解MyBatis-Plus持久层框架

## 🔍 技术简介 (3分钟)

### 什么是MyBatis-Plus？

MyBatis-Plus（简称MP）是一个基于MyBatis的增强工具，它在MyBatis的基础上只做增强不做改变，为简化开发、提高效率而生。MyBatis-Plus由国内开发者苞米豆（baomidou）团队开源，专注于简化MyBatis操作，让开发人员无需编写大量的SQL语句，即可实现单表的CRUD操作。

### 与MyBatis的关系

MyBatis-Plus完全兼容MyBatis，它仅仅是在MyBatis的基础上进行增强，并不替换或破坏MyBatis的原有功能，可以理解为MyBatis的"升级版"或"增强版"。开发者可以无缝从MyBatis迁移到MyBatis-Plus。

### 核心原理

MyBatis-Plus通过以下几个核心设计实现对MyBatis的增强：

1. **内置通用Mapper**：预先定义大量通用方法，无需手动编写XML
2. **内置通用Service**：提供更多批量操作方法
3. **强大的条件构造器**：通过链式调用构建复杂查询条件
4. **自动分页**：简化分页查询操作
5. **代码生成器**：快速生成实体类、Mapper、Service等代码
6. **插件机制**：提供多种插件扩展功能，如分页、乐观锁等

### 技术架构

MyBatis-Plus的核心架构如下：

![MyBatis-Plus架构图](https://baomidou.com/img/mybatis-plus-framework.jpg)

## 💡 核心功能 (4分钟)

### 1. 基础CRUD操作

MyBatis-Plus内置了丰富的CRUD方法，几乎涵盖了所有的单表操作场景：

- **插入操作**：`insert`
- **更新操作**：`updateById`、`update`
- **删除操作**：`deleteById`、`deleteBatchIds`、`delete`
- **查询操作**：`selectById`、`selectBatchIds`、`selectList`、`selectCount`等
- **分页查询**：`selectPage`、`selectMapsPage`

通过继承`BaseMapper<T>`接口，无需编写任何SQL语句即可获得上述能力。

### 2. 条件构造器

MP提供了强大的条件构造器（Wrapper），用于构建复杂的查询条件：

- **QueryWrapper**：查询条件构造器，用于构建WHERE条件
- **UpdateWrapper**：更新条件构造器，用于构建SET语句及WHERE条件
- **LambdaQueryWrapper**：支持Lambda表达式的查询条件构造器，更类型安全
- **LambdaUpdateWrapper**：支持Lambda表达式的更新条件构造器

条件构造器支持各种SQL比较操作符，如等于、不等于、大于、小于、LIKE、IN、EXISTS等。

### 3. 自动填充功能

MP支持通过注解自动填充实体字段，常用于创建时间、更新时间等通用字段的自动维护：

```java
@TableField(fill = FieldFill.INSERT)
private LocalDateTime createTime;

@TableField(fill = FieldFill.INSERT_UPDATE)
private LocalDateTime updateTime;
```

只需实现`MetaObjectHandler`接口，即可自动填充指定字段。

### 4. 逻辑删除

MP支持通过简单配置实现逻辑删除（软删除），无需修改代码：

```java
@TableLogic
private Integer deleted;
```

配置后，调用删除方法将自动转换为更新操作，将删除标识设置为已删除状态。

### 5. 乐观锁插件

MP提供乐观锁插件，通过`@Version`注解和拦截器机制，实现数据更新时的乐观锁控制：

```java
@Version
private Integer version;
```

### 6. 代码生成器

MP提供了强大的代码生成器，可快速生成实体类、Mapper接口、Service、Controller等代码，支持自定义模板，大幅提高开发效率。

### 7. 多租户支持

MP支持多租户模式，可以通过配置自动为所有SQL添加租户条件，实现数据隔离。

### 8. 动态表名

MP支持动态表名功能，允许在运行时根据不同条件切换表名，适用于分表场景。

## 🚀 应用场景 (3分钟)

### 1. 标准化CRUD应用

对于大多数业务系统，80%以上的功能是标准化的CRUD操作。MyBatis-Plus提供的通用方法可以大幅减少这类代码的编写量，提高开发效率。

### 2. 管理系统开发

后台管理系统通常涉及大量的数据维护和查询操作，MyBatis-Plus的条件构造器和分页功能能够轻松实现复杂查询和数据展示需求。

### 3. 多租户系统

对于SaaS应用或需要数据隔离的系统，MyBatis-Plus的多租户插件可以自动为每个SQL添加租户条件，确保数据安全隔离。

### 4. 大型系统的数据访问层

在大型系统中，MyBatis-Plus可以作为统一的数据访问层框架，提供标准化的接口和实现，减少团队成员学习成本，提高代码质量和可维护性。

### 5. 需要快速开发的项目

MyBatis-Plus的代码生成器可以一键生成各层代码，大幅缩短项目开发周期，适合需要快速交付的项目。

### 6. 敏捷开发和原型验证

在敏捷开发过程中，MyBatis-Plus可以快速搭建数据访问层，方便进行功能验证和迭代开发。

## 🛠️ 快速入门 (5分钟)

### 1. 环境准备

- JDK 1.8+
- Maven 3.0+
- Spring Boot 2.x+
- MySQL 5.7+

### 2. 项目依赖

在Spring Boot项目的`pom.xml`中添加MyBatis-Plus依赖：

```xml
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
    <version>3.5.3</version>
</dependency>

<dependency>
    <groupId>mysql</groupId>
    <artifactId>mysql-connector-java</artifactId>
    <scope>runtime</scope>
</dependency>
```

### 3. 配置数据源

在`application.yml`中配置数据源：

```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************
    username: root
    password: root

mybatis-plus:
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 打印SQL日志
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # 全局配置
  global-config:
    db-config:
      # ID生成策略
      id-type: auto
      # 表名前缀
      table-prefix: t_
```

### 4. 创建实体类

```java
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("t_user")
public class User {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String username;
    
    private String password;
    
    private String email;
    
    private Integer age;
    
    private LocalDateTime createTime;
    
    private LocalDateTime updateTime;
}
```

### 5. 创建Mapper接口

```java
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UserMapper extends BaseMapper<User> {
    // 无需编写任何方法，继承BaseMapper即可获得基础CRUD能力
}
```

### 6. 创建Service层

```java
import com.baomidou.mybatisplus.extension.service.IService;

public interface UserService extends IService<User> {
    // 可以定义自己的业务方法
}
```

```java
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
    // 实现自定义业务方法
}
```

### 7. 使用示例

```java
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/users")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    // 添加用户
    @PostMapping
    public boolean save(@RequestBody User user) {
        return userService.save(user);
    }
    
    // 更新用户
    @PutMapping
    public boolean update(@RequestBody User user) {
        return userService.updateById(user);
    }
    
    // 删除用户
    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable Long id) {
        return userService.removeById(id);
    }
    
    // 根据ID查询
    @GetMapping("/{id}")
    public User getById(@PathVariable Long id) {
        return userService.getById(id);
    }
    
    // 条件查询
    @GetMapping("/list")
    public List<User> list(String username, Integer minAge) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        // 构建查询条件
        if (username != null) {
            wrapper.like(User::getUsername, username);
        }
        if (minAge != null) {
            wrapper.ge(User::getAge, minAge);
        }
        wrapper.orderByDesc(User::getCreateTime);
        
        return userService.list(wrapper);
    }
    
    // 分页查询
    @GetMapping("/page")
    public Page<User> page(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            String username) {
        
        Page<User> page = new Page<>(current, size);
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        
        if (username != null) {
            wrapper.like(User::getUsername, username);
        }
        
        return userService.page(page, wrapper);
    }
}
```

### 8. 启用分页插件

```java
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MybatisPlusConfig {
    
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 添加分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }
}
```

## 📚 延伸学习

- [MyBatis-Plus官方文档](https://baomidou.com/)
- [MyBatis-Plus GitHub仓库](https://github.com/baomidou/mybatis-plus)
- [MyBatis-Plus示例项目](https://github.com/baomidou/mybatis-plus-samples)
- [MyBatis-Plus视频教程](https://www.bilibili.com/video/BV1Ds411E76Y)
- [SpringBoot整合MyBatis-Plus实战](https://juejin.cn/post/6844903997522493448) 