package com.trinasolar.data.utils;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/**
 * 实体字段映射器 - 用于安全地将前端字段映射到数据库列名
 *
 * <p>此工具类用于防止SQL注入，通过验证字段名是否存在于实体类中</p>
 * <p>并将实体类属性正确映射为数据库列名，支持驼峰转下划线</p>
 */
public class EntityFieldMapper<T> {
    private static final Map<Class<?>, EntityFieldMapper<?>> FIELD_MAPPERS = new ConcurrentHashMap<>();
    private static final Set<String> COMMON_FIELDS = new HashSet<>(Arrays.asList(
        "id", "createTime", "updateTime", "createBy", "updateBy", "deleted", "version",
        "create_time", "update_time", "create_by", "update_by"
    ));
    
    private static final Pattern SQL_INJECTION_PATTERN = Pattern.compile(
        "(?i).*(\\b(select|insert|update|delete|drop|truncate|alter|exec|union|create|where|from)\\b|[;'\"=]|\\s+or\\s+|\\s+and\\s+|--|/\\*|\\*/|\\\\x|\\\\u|\\\\0).*"
    );
    
    private static final Pattern VALID_FIELD_PATTERN = Pattern.compile(
        "^[a-zA-Z][a-zA-Z0-9_]*$"
    );
    
    private final Class<T> entityClass;
    private final Map<String, String> fieldToColumnMap;
    private String tableName;
    
    /**
     * 创建实体映射器
     * 
     * @param entityClass 实体类
     */
    @SuppressWarnings("unchecked")
    public static <T> EntityFieldMapper<T> forClass(Class<T> entityClass) {
        return (EntityFieldMapper<T>) FIELD_MAPPERS.computeIfAbsent(entityClass, 
                                                                   cls -> new EntityFieldMapper<>(entityClass));
    }
    
    public EntityFieldMapper(Class<T> entityClass) {
        this.entityClass = entityClass;
        this.fieldToColumnMap = initializeFieldMappings();
        initializeTableName();
    }
    
    /**
     * 初始化字段映射
     */
    private Map<String, String> initializeFieldMappings() {
        Map<String, String> mappings = new HashMap<>();
        Class<?> currentClass = entityClass;
        
        // 遍历类层次结构
        while (currentClass != null && !currentClass.equals(Object.class)) {
            for (Field field : currentClass.getDeclaredFields()) {
                // 跳过非数据库字段
                if (field.isAnnotationPresent(TableField.class)) {
                    TableField tableField = field.getAnnotation(TableField.class);
                    if (!tableField.exist()) {
                        continue;
                    }
                    String columnName = tableField.value();
                    if (StringUtils.hasText(columnName)) {
                        mappings.put(field.getName(), columnName);
                        mappings.put(columnName, columnName);
                        continue;
                    }
                }
                
                // 处理 @TableId 注解
                if (field.isAnnotationPresent(TableId.class)) {
                    TableId tableId = field.getAnnotation(TableId.class);
                    String columnName = tableId.value();
                    if (StringUtils.hasText(columnName)) {
                        mappings.put(field.getName(), columnName);
                        mappings.put(columnName, columnName);
                        continue;
                    }
                }
                
                // 默认使用驼峰转下划线
            String fieldName = field.getName();
                String columnName = camelToUnderscore(fieldName);
                mappings.put(fieldName, columnName);
                mappings.put(columnName, columnName);
            }
            currentClass = currentClass.getSuperclass();
        }
        
        // 添加通用字段
        for (String commonField : COMMON_FIELDS) {
            if (!mappings.containsKey(commonField)) {
                String columnName = camelToUnderscore(commonField);
                mappings.put(commonField, columnName);
                mappings.put(columnName, columnName);
        }
        }
        
        return Collections.unmodifiableMap(mappings);
    }
    
    /**
     * 初始化表名
     */
    private void initializeTableName() {
        TableName tableNameAnno = entityClass.getAnnotation(TableName.class);
        this.tableName = tableNameAnno != null ? tableNameAnno.value() : camelToUnderscore(entityClass.getSimpleName());
                }
    
    /**
     * Validates and maps a field name to its corresponding column name
     * 
     * @param fieldName the field name to validate and map
     * @return the mapped column name
     * @throws IllegalArgumentException if the field name is invalid or has SQL injection risks
     */
    public String validateAndMapToColumn(String fieldName) {
        if (!StringUtils.hasText(fieldName)) {
            throw new IllegalArgumentException("Field name cannot be empty");
        }
        
        // Check field name format
        if (!VALID_FIELD_PATTERN.matcher(fieldName).matches()) {
            throw new IllegalArgumentException("Invalid field name format: " + fieldName);
        }
        
        // Check for SQL injection risks
        if (containsSqlInjectionRisk(fieldName)) {
            throw new IllegalArgumentException("Field name contains SQL injection risk: " + fieldName);
    }
    
        // Check if field exists in the mapping
        String columnName = fieldToColumnMap.get(fieldName);
        if (columnName == null) {
            throw new IllegalArgumentException("Invalid field name: " + fieldName);
        }
        
        return columnName;
    }
    
    /**
     * Checks if a value has SQL injection risks
     */
    private boolean containsSqlInjectionRisk(String value) {
        if (!StringUtils.hasText(value)) {
            return false;
        }
        
        // Skip SQL injection check for common safe fields
        if (COMMON_FIELDS.contains(value)) {
            return false;
        }
        
        // Check for SQL keywords and special characters
        if (SQL_INJECTION_PATTERN.matcher(value).matches()) {
            return true;
        }
        
        // Check for common SQL injection patterns
        String[] sqlKeywords = {"SELECT", "INSERT", "UPDATE", "DELETE", "DROP", "TRUNCATE", "ALTER", "EXEC", "UNION", "CREATE", "WHERE", "FROM"};
        String upperValue = value.toUpperCase();
        for (String keyword : sqlKeywords) {
            if (upperValue.contains(keyword)) {
                return true;
            }
        }
        
        // Check for special characters that could be used in SQL injection
        String[] specialChars = {"'", "\"", ";", "--", "/*", "*/", "\\x", "\\u", "\\0"};
        for (String specialChar : specialChars) {
            if (value.contains(specialChar)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 驼峰命名转下划线命名
     */
    private String camelToUnderscore(String camel) {
        if (!StringUtils.hasText(camel)) {
            return camel;
        }
        
        StringBuilder result = new StringBuilder();
        result.append(Character.toLowerCase(camel.charAt(0)));
        
        for (int i = 1; i < camel.length(); i++) {
            char ch = camel.charAt(i);
            if (Character.isUpperCase(ch)) {
                result.append('_');
                result.append(Character.toLowerCase(ch));
            } else {
                result.append(ch);
            }
        }
        
        return result.toString();
    }
    
    /**
     * 获取表名
     */
    public String getTableName() {
        return tableName;
    }
    
    /**
     * 验证字段名是否有效
     */
    public boolean isValidFieldName(String fieldName) {
        if (!StringUtils.hasText(fieldName)) {
            return false;
        }
        return fieldToColumnMap.containsKey(fieldName);
    }
    
    /**
     * Validates a list of field names
     * 
     * @param fieldNames the field names to validate
     * @throws IllegalArgumentException if any field name is invalid
     */
    public void validateFieldNames(List<String> fieldNames) {
        if (fieldNames == null || fieldNames.isEmpty()) {
            return;
        }
        
        for (String fieldName : fieldNames) {
            validateAndMapToColumn(fieldName);
        }
    }
} 