package com.trinasolar.elasticsearch.config;

import com.trinasolar.elasticsearch.properties.ElasticsearchProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * Elasticsearch配置解析器
 * 支持原生配置优先，如果没有原生配置则使用自定义配置
 * 
 * 配置优先级：
 * 1. elasticsearch.* (原生配置)
 * 2. trina.elasticsearch.* (自定义配置)
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@Component
public class ElasticsearchConfigurationResolver {

    private final Environment environment;

    public ElasticsearchConfigurationResolver(Environment environment) {
        this.environment = environment;
    }

    /**
     * 解析Elasticsearch配置
     * 优先使用原生配置，如果没有则使用自定义配置
     */
    public ElasticsearchProperties resolveConfiguration() {
        ElasticsearchProperties properties = new ElasticsearchProperties();
        
        // 1. 尝试读取原生配置 elasticsearch.*
        if (hasNativeConfiguration()) {
            log.info("检测到原生Elasticsearch配置，使用 elasticsearch.* 配置");
            loadNativeConfiguration(properties);
        } else {
            log.info("未检测到原生配置，使用自定义 trina.elasticsearch.* 配置");
            loadCustomConfiguration(properties);
        }
        
        // 打印最终配置（隐藏密码）
        logFinalConfiguration(properties);
        
        return properties;
    }

    /**
     * 检查是否存在原生配置
     */
    private boolean hasNativeConfiguration() {
        // 检查关键的原生配置属性
        return environment.containsProperty("elasticsearch.hosts") ||
               environment.containsProperty("elasticsearch.uris") ||
               environment.containsProperty("elasticsearch.host") ||
               environment.containsProperty("spring.elasticsearch.uris") ||
               environment.containsProperty("spring.elasticsearch.hosts");
    }

    /**
     * 加载原生配置
     */
    private void loadNativeConfiguration(ElasticsearchProperties properties) {
        // 支持多种原生配置格式
        
        // 1. elasticsearch.hosts
        String hosts = environment.getProperty("elasticsearch.hosts");
        if (hosts == null) {
            hosts = environment.getProperty("elasticsearch.uris");
        }
        if (hosts == null) {
            hosts = environment.getProperty("elasticsearch.host");
        }
        if (hosts == null) {
            hosts = environment.getProperty("spring.elasticsearch.uris");
        }
        if (hosts == null) {
            hosts = environment.getProperty("spring.elasticsearch.hosts");
        }
        
        if (hosts != null) {
            properties.setHosts(parseHosts(hosts));
        }

        // 2. 认证信息
        String username = environment.getProperty("elasticsearch.username");
        if (username == null) {
            username = environment.getProperty("spring.elasticsearch.username");
        }
        if (username != null) {
            properties.setUsername(username);
        }

        String password = environment.getProperty("elasticsearch.password");
        if (password == null) {
            password = environment.getProperty("spring.elasticsearch.password");
        }
        if (password != null) {
            properties.setPassword(password);
        }

        // 3. 连接配置
        Integer connectionTimeout = environment.getProperty("elasticsearch.connection-timeout", Integer.class);
        if (connectionTimeout == null) {
            connectionTimeout = environment.getProperty("spring.elasticsearch.connection-timeout", Integer.class);
        }
        if (connectionTimeout != null) {
            properties.setConnectionTimeout(connectionTimeout);
        }

        Integer socketTimeout = environment.getProperty("elasticsearch.socket-timeout", Integer.class);
        if (socketTimeout == null) {
            socketTimeout = environment.getProperty("spring.elasticsearch.socket-timeout", Integer.class);
        }
        if (socketTimeout != null) {
            properties.setSocketTimeout(socketTimeout);
        }

        // 4. SSL配置
        Boolean ssl = environment.getProperty("elasticsearch.ssl", Boolean.class);
        if (ssl == null) {
            ssl = environment.getProperty("spring.elasticsearch.ssl", Boolean.class);
        }
        if (ssl != null) {
            properties.setSslEnabled(ssl);
        }

        // 5. 向量配置（只有自定义配置支持）
        loadVectorConfiguration(properties, "elasticsearch.vector");
    }

    /**
     * 加载自定义配置
     */
    private void loadCustomConfiguration(ElasticsearchProperties properties) {
        try {
            // 使用Spring Boot的Binder来绑定配置
            Binder binder = Binder.get(environment);
            ElasticsearchProperties boundProperties = binder
                .bind("trina.elasticsearch", ElasticsearchProperties.class)
                .orElse(new ElasticsearchProperties());
            
            // 复制配置
            properties.setHosts(boundProperties.getHosts());
            properties.setUsername(boundProperties.getUsername());
            properties.setPassword(boundProperties.getPassword());
            properties.setConnectionTimeout(boundProperties.getConnectionTimeout());
            properties.setSocketTimeout(boundProperties.getSocketTimeout());
            properties.setSslEnabled(boundProperties.isSslEnabled());
            properties.setVector(boundProperties.getVector());
            
        } catch (Exception e) {
            log.warn("加载自定义配置失败，使用默认配置: {}", e.getMessage());
            setDefaultConfiguration(properties);
        }
    }

    /**
     * 加载向量配置
     */
    private void loadVectorConfiguration(ElasticsearchProperties properties, String prefix) {
        ElasticsearchProperties.VectorConfig vectorConfig = new ElasticsearchProperties.VectorConfig();
        
        String ollamaUrl = environment.getProperty(prefix + ".ollama-url");
        if (ollamaUrl != null) {
            vectorConfig.setOllamaUrl(ollamaUrl);
        }
        
        String model = environment.getProperty(prefix + ".model");
        if (model != null) {
            vectorConfig.setModel(model);
        }
        
        Integer dimensions = environment.getProperty(prefix + ".dimensions", Integer.class);
        if (dimensions != null) {
            vectorConfig.setDimensions(dimensions);
        }
        
        Integer timeout = environment.getProperty(prefix + ".timeout", Integer.class);
        if (timeout != null) {
            vectorConfig.setTimeout(timeout);
        }
        
        properties.setVector(vectorConfig);
    }

    /**
     * 解析hosts字符串
     */
    private List<String> parseHosts(String hosts) {
        if (hosts == null || hosts.trim().isEmpty()) {
            return List.of("localhost:9200");
        }
        
        // 支持逗号分隔的多个host
        return Arrays.stream(hosts.split(","))
            .map(String::trim)
            .filter(host -> !host.isEmpty())
            .toList();
    }

    /**
     * 设置默认配置
     */
    private void setDefaultConfiguration(ElasticsearchProperties properties) {
        properties.setHosts(List.of("localhost:9200"));
        properties.setUsername("elastic");
        properties.setPassword("elastic");
        properties.setConnectionTimeout(5000);
        properties.setSocketTimeout(60000);
        properties.setSslEnabled(false);
        
        // 默认向量配置
        ElasticsearchProperties.VectorConfig vectorConfig = new ElasticsearchProperties.VectorConfig();
        vectorConfig.setOllamaUrl("http://localhost:11434");
        vectorConfig.setModel("qwen2.5:7b");
        vectorConfig.setDimensions(1024);
        vectorConfig.setTimeout(30000);
        properties.setVector(vectorConfig);
    }

    /**
     * 打印最终配置（隐藏敏感信息）
     */
    private void logFinalConfiguration(ElasticsearchProperties properties) {
        log.info("Elasticsearch配置加载完成:");
        log.info("  hosts: {}", properties.getHosts());
        log.info("  username: {}", properties.getUsername());
        log.info("  password: {}", properties.getPassword() != null ? "xxx" : null);
        log.info("  connectionTimeout: {}", properties.getConnectionTimeout());
        log.info("  socketTimeout: {}", properties.getSocketTimeout());
        log.info("  ssl: {}", properties.isSslEnabled());
        
        if (properties.getVector() != null) {
            log.info("  vector.ollamaUrl: {}", properties.getVector().getOllamaUrl());
            log.info("  vector.model: {}", properties.getVector().getModel());
            log.info("  vector.dimensions: {}", properties.getVector().getDimensions());
            log.info("  vector.timeout: {}", properties.getVector().getTimeout());
        }
    }
}
