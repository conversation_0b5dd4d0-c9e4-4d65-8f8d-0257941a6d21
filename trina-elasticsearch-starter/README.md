# Trina Elasticsearch Starter

基于 `elasticsearch-rest-client` 的 Elasticsearch 集成模块，提供简单易用的 Elasticsearch 操作接口。

## 特性

- 基于官方 `elasticsearch-rest-client` 实现
- 支持多节点集群配置
- 提供完整的 CRUD 操作
- 支持复杂查询构建
- 内置连接池管理
- 支持认证配置（用户名密码、API Key）
- 提供工具类简化查询构建
- 完整的搜索结果解析

## 快速开始

### 1. 添加依赖

在你的项目中添加以下依赖：

```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-elasticsearch-starter</artifactId>
    <version>4.0.0</version>
</dependency>
```

### 2. 配置

在 `application.yml` 中添加 Elasticsearch 配置：

```yaml
elasticsearch:
  hosts:
    - localhost:9200
  connect-timeout: 5000
  socket-timeout: 30000
  connection-request-timeout: 1000
  max-connections: 100
  max-connections-per-route: 10
  # 可选认证配置
  # username: elastic
  # password: changeme
  # api-key: VnVhQ2ZHY0JDZGJrU...
  ssl: false
```

### 3. 使用

注入 `ElasticsearchService` 即可使用：

```java
@Autowired
private ElasticsearchService elasticsearchService;

// 创建索引
boolean created = elasticsearchService.createIndex("products", mapping);

// 索引文档
String result = elasticsearchService.indexDocument("products", "1", document);

// 搜索文档
String query = ElasticsearchUtils.buildMatchQuery("name", "bike");
String searchResult = elasticsearchService.search("products", query);

// 解析搜索结果
SearchResult<Product> result = ElasticsearchUtils.parseSearchResult(searchResult, Product.class);
```

## 核心组件

### ElasticsearchService

核心服务类，提供所有 Elasticsearch 操作：

- `indexExists(String indexName)` - 检查索引是否存在
- `createIndex(String indexName, String mapping)` - 创建索引
- `deleteIndex(String indexName)` - 删除索引
- `indexDocument(String indexName, String id, Object document)` - 索引文档
- `getDocument(String indexName, String id)` - 获取文档
- `updateDocument(String indexName, String id, Object document)` - 更新文档
- `deleteDocument(String indexName, String id)` - 删除文档
- `search(String indexName, String query)` - 搜索文档
- `bulk(String bulkBody)` - 批量操作
- `deleteByQuery(String indexName, String query)` - 按查询删除
- `getClusterHealth()` - 获取集群健康状态

### ElasticsearchUtils

工具类，提供查询构建和结果解析方法：

- `buildMatchAllQuery()` - 构建匹配所有查询
- `buildMatchQuery(String field, Object value)` - 构建匹配查询
- `buildTermQuery(String field, Object value)` - 构建词条查询
- `buildRangeQuery(String field, Object gte, Object lte)` - 构建范围查询
- `buildBoolQuery(...)` - 构建布尔查询
- `buildPageQuery(String baseQuery, int from, int size)` - 构建分页查询
- `buildSortQuery(String baseQuery, String sortField, String sortOrder)` - 构建排序查询
- `parseSearchResult(String responseJson, Class<T> clazz)` - 解析搜索结果

### BaseDocument

基础文档实体类，提供通用字段：

- `id` - 文档ID
- `createTime` - 创建时间
- `updateTime` - 更新时间
- `version` - 版本号
- `deleted` - 是否删除

### SearchResult

搜索结果封装类，包含：

- `total` - 总命中数
- `maxScore` - 最大得分
- `hits` - 搜索结果列表
- `aggregations` - 聚合结果
- `took` - 查询耗时
- `timedOut` - 是否超时

## 示例

完整的使用示例请参考 `ElasticsearchExample` 类，包含：

- 创建索引和映射
- 索引文档
- 搜索和高亮
- 更新和删除文档
- 批量操作
- 集群状态检查

## 配置说明

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| hosts | Elasticsearch服务器地址列表 | localhost:9200 |
| connect-timeout | 连接超时时间(毫秒) | 5000 |
| socket-timeout | Socket超时时间(毫秒) | 30000 |
| connection-request-timeout | 连接请求超时时间(毫秒) | 1000 |
| max-connections | 最大连接数 | 100 |
| max-connections-per-route | 每个路由的最大连接数 | 10 |
| username | 用户名(可选) | - |
| password | 密码(可选) | - |
| api-key | API密钥(可选) | - |
| ssl | 是否启用SSL | false |

## 注意事项

1. 本模块基于 `elasticsearch-rest-client` 9.0.0 版本
2. 支持 Elasticsearch 7.x 和 8.x 版本
3. 索引名称必须符合 Elasticsearch 命名规范（小写字母、数字、下划线、连字符）
4. 建议在生产环境中配置适当的连接池参数
5. 使用认证时，建议使用 API Key 而不是用户名密码

## 版本历史

- 4.0.0: 重构为基于 elasticsearch-rest-client 实现，移除 Spring Data Elasticsearch 依赖