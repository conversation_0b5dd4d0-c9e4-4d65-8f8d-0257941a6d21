seata:
  enabled: true
  application-id: ${spring.application.name}
  tx-service-group: default_tx_group # 事务群组（可以每个应用独立取名，也可以使用相同的名字）
  enable-auto-data-source-proxy: true
  use-jdk-proxy: true
  excludes-for-auto-proxying: firstClassNameForExclude,secondClassNameForExclude
  client:
    rm:
      report-success-enable: true
      table-meta-check-enable: false # 自动刷新缓存中的表结构（默认false）
      report-retry-count: 5 # 一阶段结果上报TC重试次数（默认5）
      async-commit-buffer-limit: 10000 # 异步提交缓存队列长度（默认10000）
      saga-branch-register-enable: false
      lock:
        retry-interval: 10 # 校验或占用全局锁重试间隔（默认10ms）
        retry-times: 30 # 校验或占用全局锁重试次数（默认30）
        retry-policy-branch-rollback-on-conflict: true # 分支事务与其它全局回滚事务冲突时锁策略（优先释放本地锁让回滚成功）
    tm:
      degrade-check: false
      degrade-check-period: 2000
      degrade-check-allow-times: 10
      commit-retry-count: 5 # 一阶段全局提交结果上报TC重试次数（默认1次，建议大于1）
      rollback-retry-count: 5 # 一阶段全局回滚结果上报TC重试次数（默认1次，建议大于1）
    undo:
      data-validation: true # 二阶段回滚镜像校验（默认true开启）
      log-serialization: jackson # undo序列化方式（默认jackson 不支持 LocalDateTime）
      log-table: undo_log # 自定义undo表名（默认undo_log）
      only-care-update-columns: true
  log:
    exception-rate: 100 # 日志异常输出概率（默认100）
  service:
    vgroup-mapping:
      default_tx_group: default # TC 集群（必须与seata-server保持一致）
    grouplist:
      default: 127.0.0.1:8091
    disable-global-transaction: false # 禁用全局事务（默认false）
  transport:
    shutdown:
      wait: 3
    thread-factory:
      boss-thread-prefix: NettyBoss
      worker-thread-prefix: NettyServerNIOWorker
      server-executor-thread-prefix: NettyServerBizHandler
      share-boss-worker: false
      client-selector-thread-prefix: NettyClientSelector
      client-selector-thread-size: 1
      client-worker-thread-prefix: NettyClientWorkerThread
      worker-thread-size: default
      boss-thread-size: 1
    type: TCP
    server: NIO
    heartbeat: true
    serialization: seata
    compressor: none
    enable-client-batch-send-request: true # 客户端事务消息请求是否批量合并发送（默认true）