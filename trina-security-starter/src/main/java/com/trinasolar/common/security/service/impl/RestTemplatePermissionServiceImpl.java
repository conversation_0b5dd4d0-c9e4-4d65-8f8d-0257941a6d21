package com.trinasolar.common.security.service.impl;

import cn.hutool.core.text.StrPool;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.common.security.domain.SecurityContext;
import com.trinasolar.common.security.properties.SecurityProperties;
import com.trinasolar.common.security.service.PermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Collections;
import java.util.List;

/**
 * 基于RestTemplate的权限服务实现
 * <p>
 * 从统一权限接口获取用户权限信息，支持缓存机制
 * 接口地址：/app/perms
 * 响应格式：{"code":0,"message":"",data:{"roles":[],"permissions":[],"dataScops":[]}}
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@RequiredArgsConstructor
public class RestTemplatePermissionServiceImpl implements PermissionService {

    private final RestTemplate restTemplate;
    private final SecurityProperties securityProperties;

    @Override
    public SecurityContext.Authorization getUserPermissions(String token, String userId) {
        return loadUserPermissionsFromSystem(token, userId);
    }

    @Override
    public void refreshUserPermissions(String userId) {
        log.info("User permissions cache refreshed for user: {}", userId);
    }

    /**
     * 从远程权限接口加载用户权限信息
     */
    private SecurityContext.Authorization loadUserPermissionsFromSystem(String token, String userId) {
        try {
            log.debug("Loading user permissions from remote for user: {}", userId);
            String url = UriComponentsBuilder.fromUriString(securityProperties.getTaspUrl() + SecurityConstant.TASP_AUTHORITY_PATH)
                    .queryParam("appId", securityProperties.getAppId())
                    .queryParam("username", userId)
                    .build()
                    .toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.set(HttpHeaders.AUTHORIZATION, token);

            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    requestEntity,
                    String.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                String body = response.getBody();
                log.debug("Successfully loaded permissions for user: {}", userId);
                JSONObject parseObj = JSONUtil.parseObj(body);
                Integer code = parseObj.getInt("code");
                String message = parseObj.getStr("msg");
                Object data = parseObj.getObj("data");
                if (0 == code && data != null) {
                    JSONObject dataObj = JSONUtil.parseObj(data);
                    List<String> permissions = dataObj.getBeanList("permissions", String.class);
                    List<SecurityContext.Role> roles = dataObj.getBeanList("roles", SecurityContext.Role.class);

                    SecurityContext.Authorization authorization = new SecurityContext.Authorization();
                    authorization.setPermissions(permissions);
                    if (CollectionUtils.isEmpty(roles)) {
                        authorization.setRoleCodes(List.of());
                        authorization.setRoles(List.of());
                    } else {
                        authorization.setRoleCodes(roles.stream().map(SecurityContext.Role::getCode).toList());
                        authorization.setRoles(roles);
                    }
                    return authorization;
                } else {
                    log.warn("Failed to load permissions for user: {}, message: {}", userId, message);
                }
            } else {
                log.warn("Failed to load permissions for user: {}, status: {}", userId, response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("Error loading user permissions from remote for user: {}", userId, e);
        }

        return null;
    }

    /**
     * 转换权限数据为DataScope
     */
    //private DataScope convertToDataScope(PermissionData permissionData) {
    //    if (permissionData == null || permissionData.getDataScops() == null || permissionData.getDataScops().isEmpty()) {
    //        // 默认返回仅本人权限
    //        DataScope dataScope = new DataScope();
    //        dataScope.setType(DataScope.DataScopeType.SELF);
    //        return dataScope;
    //    }
    //
    //    // 取第一个数据权限配置
    //    DataScopeInfo dataScopeInfo = permissionData.getDataScops().get(0);
    //
    //    DataScope dataScope = new DataScope();
    //    dataScope.setType(DataScope.DataScopeType.fromCode(dataScopeInfo.getType()));
    //    dataScope.setDeptIds(dataScopeInfo.getDeptIds());
    //    dataScope.setUserIds(dataScopeInfo.getUserIds());
    //    dataScope.setCustomCondition(dataScopeInfo.getCustomCondition());
    //
    //    return dataScope;
    //}
}
