# Spring Cloud Stream 配置
spring:
  cloud:
    stream:
      default-binder: kafka
      # Kafka特定配置
      kafka:
        binder:
          brokers: localhost:9092
          auto-create-topics: true
          auto-add-partitions: true
      rabbit:
        binder:
          nodes: localhost:5672
      rocketmq:
        binder:
          name-server: localhost:9876
  rabbitmq:
    addresses: ${spring.cloud.stream.rabbit.binder.nodes}