# Feign配置
spring:
  cloud:
    openfeign:
      client:
        config:
          default:  # 全局配置
            connectTimeout: 5000
            readTimeout: 10000
            loggerLevel: BASIC
      compression:
        request:
          enabled: true
          mime-types: text/xml,application/xml,application/json
          min-request-size: 2048
        response:
          enabled: true
      httpclient:
        max-connections: 200
        max-connections-per-route: 50
        time-to-live: 900
        connection-timeout: 2000
        follow-redirects: true
    loadbalancer:
      enabled: true