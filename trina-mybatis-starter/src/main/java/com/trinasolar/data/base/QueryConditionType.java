package com.trinasolar.data.base;

import com.baomidou.mybatisplus.core.enums.SqlKeyword;
import lombok.Getter;

/**
 * 查询条件类型枚举
 * 定义了面向前端的几种基础查询条件类型
 */
@Getter
public enum QueryConditionType {
    /**
     * 等于
     */
    EQUAL("EQ", SqlKeyword.EQ),

    /**
     * 不等于
     */
    NOT_EQUAL("NE", SqlKeyword.NE),

    /**
     * 包含（模糊查询）
     */
    CONTAINS("CONTAINS", SqlKeyword.LIKE),

    /**
     * 在集合中
     */
    IN("IN", SqlKeyword.IN),

    /**
     * 不在集合中
     */
    NOT_IN("NOT_IN", SqlKeyword.NOT_IN),

    /**
     * 在范围之间
     */
    BETWEEN("BETWEEN", SqlKeyword.BETWEEN),

    /**
     * 为空
     */
    IS_NULL("IS_NULL", SqlKeyword.IS_NULL),

    /**
     * 不为空
     */
    IS_NOT_NULL("IS_NOT_NULL", SqlKeyword.IS_NOT_NULL),

    /**
     * 模糊查询
     */
    LIKE("LIKE", SqlKeyword.LIKE),

    /**
     * 大于
     */
    GREATER_THAN("GT", SqlKeyword.GT),

    /**
     * 大于等于
     */
    GREATER_THAN_OR_EQUAL("GE", SqlKeyword.GE),

    /**
     * 小于
     */
    LESS_THAN("LT", SqlKeyword.LT),

    /**
     * 小于等于
     */
    LESS_THAN_OR_EQUAL("LE", SqlKeyword.LE),

    /**
     * 不等于
     */
    NOT_LIKE("NOT_LIKE", SqlKeyword.NOT_LIKE);

    /**
     * -- GETTER --
     * 获取操作符代码
     */
    private final String code;
    /**
     * -- GETTER --
     * 获取对应的SqlKeyword
     */
    private final SqlKeyword sqlKeyword;

    QueryConditionType(String code, SqlKeyword sqlKeyword) {
        this.code = code;
        this.sqlKeyword = sqlKeyword;
    }

    /**
     * 将字符串安全转换为查询条件类型
     *
     * @param code 字符串值
     * @return 对应的查询条件类型，如果无法转换则默认返回EQUAL
     */
    public static QueryConditionType fromString(String code) {
        if (code == null) {
            return null;
        }
        for (QueryConditionType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown query condition type: " + code);
    }
} 