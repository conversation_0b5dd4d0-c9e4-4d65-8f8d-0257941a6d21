package com.trinasolar.gateway.security.exception;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.reactive.error.ErrorWebExceptionHandler;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 简化的网关错误处理器
 * <p>
 * 处理 Spring Cloud Gateway 的默认错误，统一返回 R 结构
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Order(-2) // 比全局异常处理器优先级更高
public class GatewayErrorHandler implements ErrorWebExceptionHandler {

    private final ObjectMapper objectMapper;

    @Override
    public Mono<Void> handle(ServerWebExchange exchange, Throwable ex) {
        ServerHttpResponse response = exchange.getResponse();
        
        if (response.isCommitted()) {
            return Mono.error(ex);
        }

        // 获取HTTP状态码
        HttpStatus httpStatus = determineHttpStatus(ex);
        
        // 获取错误信息
        String message = getFriendlyErrorMessage(httpStatus, ex);
        String path = exchange.getRequest().getPath().value();
        String requestId = exchange.getRequest().getHeaders().getFirst("X-Request-ID");

        // 记录日志
        logError(httpStatus, message, path, ex);

        // 构建错误响应
        return writeErrorResponse(response, httpStatus, message, path, requestId);
    }

    /**
     * 确定HTTP状态码
     */
    private HttpStatus determineHttpStatus(Throwable ex) {
        if (ex instanceof ResponseStatusException) {
            ResponseStatusException rse = (ResponseStatusException) ex;
            return HttpStatus.resolve(rse.getStatusCode().value());
        }
        
        // 根据异常类型确定状态码
        String exceptionName = ex.getClass().getSimpleName();
        if (exceptionName.contains("NotFound") || exceptionName.contains("NoHandler")) {
            return HttpStatus.NOT_FOUND;
        } else if (exceptionName.contains("Timeout")) {
            return HttpStatus.GATEWAY_TIMEOUT;
        } else if (exceptionName.contains("Connection") || exceptionName.contains("Network")) {
            return HttpStatus.SERVICE_UNAVAILABLE;
        } else {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }
    }

    /**
     * 获取友好的错误消息
     */
    private String getFriendlyErrorMessage(HttpStatus status, Throwable ex) {
        switch (status) {
            case NOT_FOUND:
                return "请求的资源不存在";
            case BAD_REQUEST:
                return "请求参数错误";
            case UNAUTHORIZED:
                return "未授权访问";
            case FORBIDDEN:
                return "权限不足";
            case SERVICE_UNAVAILABLE:
                return "服务暂时不可用，请稍后重试";
            case GATEWAY_TIMEOUT:
                return "请求超时，请稍后重试";
            case INTERNAL_SERVER_ERROR:
                return "系统内部错误";
            default:
                String message = ex.getMessage();
                return message != null && !message.isEmpty() ? message : "未知错误";
        }
    }

    /**
     * 记录错误日志
     */
    private void logError(HttpStatus status, String message, String path, Throwable ex) {
        String logMessage = String.format("Gateway error - Status: %d, Message: %s, Path: %s", 
                status.value(), message, path);
        
        if (status.is5xxServerError()) {
            log.error(logMessage, ex);
        } else if (status == HttpStatus.NOT_FOUND) {
            log.debug(logMessage); // 404 错误使用 debug 级别
        } else {
            log.warn(logMessage);
        }
    }

    /**
     * 写入错误响应
     */
    private Mono<Void> writeErrorResponse(ServerHttpResponse response, HttpStatus httpStatus, 
                                          String message, String path, String requestId) {
        try {
            // 设置响应状态码和内容类型
            response.setStatusCode(httpStatus);
            response.getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);
            response.getHeaders().add("Cache-Control", "no-cache, no-store, must-revalidate");

            // 构建错误响应体
            Map<String, Object> data = new HashMap<>();
            if (path != null && !path.isEmpty()) {
                data.put("path", path);
            }
            if (requestId != null && !requestId.isEmpty()) {
                data.put("requestId", requestId);
            }
            data.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 1);
            errorResponse.put("msg", message);
            errorResponse.put("data", data);

            // 序列化响应体
            String responseBody = objectMapper.writeValueAsString(errorResponse);
            byte[] bytes = responseBody.getBytes();
            
            return response.writeWith(Mono.just(response.bufferFactory().wrap(bytes)));
        } catch (Exception e) {
            log.error("Error writing error response", e);
            return writeSimpleErrorResponse(response, httpStatus, message);
        }
    }

    /**
     * 写入简单的错误响应（当序列化失败时使用）
     */
    private Mono<Void> writeSimpleErrorResponse(ServerHttpResponse response, HttpStatus httpStatus, String message) {
        try {
            response.setStatusCode(httpStatus);
            response.getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);
            
            String simpleResponse = String.format(
                "{\"code\":1,\"msg\":\"%s\",\"data\":null}", 
                message.replace("\"", "\\\"")
            );
            
            byte[] bytes = simpleResponse.getBytes();
            return response.writeWith(Mono.just(response.bufferFactory().wrap(bytes)));
        } catch (Exception e) {
            log.error("Error writing simple error response", e);
            return Mono.empty();
        }
    }
}
