package com.trinasolar.common.core.base;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.Version;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 系统基础实体类
 *
 * <AUTHOR>
 * @since 2025-06-10 17:21
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public abstract class BaseSysEntity extends BaseEntity {

    @Serial
    private static final long serialVersionUID = -6864615448069259882L;

    @Version
    @Schema(description = "版本号")
    @TableField(value = "version", fill = FieldFill.INSERT)
    private Integer version;

    /**
     * 当前状态：0正常，1停用
     */
    @TableField("status")
    @Schema(description = "当前状态：0正常，1停用 可拓展...")
    private Integer status;

    /**
     * 逻辑删除：0未删除，1已删除
     * tinyint （1） DEFAULT NULL COMMENT '逻辑删除：0未删除，1已删除',
     */
    @TableLogic
    @Schema(description = "逻辑删除：0未删除，1已删除")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    private Boolean deleted;

    /**
     * 保留数据：0不保留，1保留
     */
    @TableField("is_reserved")
    @Schema(description = "保留数据：0不保留，1保留")
    private Boolean reserved;

    /**
     * 描述
     */
    @TableField("description")
    @Schema(description = "描述")
    private String description;
}
