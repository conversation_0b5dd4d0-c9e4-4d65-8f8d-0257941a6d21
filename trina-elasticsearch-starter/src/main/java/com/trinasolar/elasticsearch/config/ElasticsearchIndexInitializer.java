package com.trinasolar.elasticsearch.config;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.indices.CreateIndexRequest;
import co.elastic.clients.elasticsearch.indices.CreateIndexResponse;
import co.elastic.clients.elasticsearch.indices.ExistsRequest;
import co.elastic.clients.elasticsearch._types.mapping.Property;
import co.elastic.clients.elasticsearch._types.mapping.TypeMapping;
// import co.elastic.clients.elasticsearch._types.mapping.DenseVectorSimilarity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * Elasticsearch索引初始化器（已废弃）
 * 用于在应用程序启动时自动检查和创建必要的索引
 *
 * @deprecated 请使用 {@link AnnotationBasedIndexInitializer} 基于注解的自动索引创建
 */
@Deprecated
//@Component  // 已禁用，使用AnnotationBasedIndexInitializer替代
public class ElasticsearchIndexInitializer implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(ElasticsearchIndexInitializer.class);

    @Autowired
    private ElasticsearchClient elasticsearchClient;

    /**
     * 在应用程序启动时运行
     *
     * @param args 命令行参数
     */
    @Override
    public void run(String... args) {
        logger.info("开始初始化Elasticsearch索引...");
        
        // 初始化文章索引
        initArticleIndex();
        
        // 初始化文档索引
        initDocumentIndex();
        
        // 初始化产品索引
        initProductIndex();
        
        logger.info("Elasticsearch索引初始化完成");
    }

    /**
     * 初始化文章索引
     */
    private void initArticleIndex() {
        String indexName = "article";
        try {
            boolean exists = elasticsearchClient.indices().exists(
                ExistsRequest.of(e -> e.index(indexName))
            ).value();
            
            if (!exists) {
                logger.info("创建文章索引: {}", indexName);
                
                // 创建索引请求
                CreateIndexResponse response = elasticsearchClient.indices().create(
                    CreateIndexRequest.of(c -> c
                        .index(indexName)
                        .mappings(m -> m
                            .properties("id", p -> p.keyword(k -> k))
                            .properties("title", p -> p.text(t -> t))
                            .properties("content", p -> p.text(t -> t))
                            .properties("summary", p -> p.text(t -> t))
                            .properties("author", p -> p.keyword(k -> k))
                            .properties("category", p -> p.keyword(k -> k))
                            .properties("tags", p -> p.keyword(k -> k))
                            .properties("status", p -> p.keyword(k -> k))
                            .properties("publishTime", p -> p.date(d -> d))
                            .properties("createTime", p -> p.date(d -> d))
                            .properties("updateTime", p -> p.date(d -> d))
                        )
                    )
                );
                
                logger.info("文章索引创建结果: {}", response.acknowledged());
            } else {
                logger.info("文章索引已存在: {}", indexName);
            }
        } catch (IOException e) {
            logger.error("创建文章索引失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 初始化文档索引
     */
    private void initDocumentIndex() {
        String indexName = "document";
        try {
            boolean exists = elasticsearchClient.indices().exists(
                ExistsRequest.of(e -> e.index(indexName))
            ).value();
            
            if (!exists) {
                logger.info("创建文档索引: {}", indexName);
                
                // 创建索引请求
                CreateIndexResponse response = elasticsearchClient.indices().create(
                    CreateIndexRequest.of(c -> c
                        .index(indexName)
                        .mappings(m -> m
                            .properties("id", p -> p.keyword(k -> k))
                            .properties("title", p -> p.text(t -> t))
                            .properties("content", p -> p.text(t -> t))
                            .properties("fileUrl", p -> p.keyword(k -> k))
                            .properties("fileName", p -> p.keyword(k -> k))
                            .properties("fileType", p -> p.keyword(k -> k))
                            .properties("fileSize", p -> p.long_(l -> l))
                            .properties("category", p -> p.keyword(k -> k))
                            .properties("tags", p -> p.keyword(k -> k))
                            .properties("author", p -> p.keyword(k -> k))
                            .properties("contentVector", p -> p.denseVector(v -> v.dims(1024)))
                            .properties("createTime", p -> p.date(d -> d))
                            .properties("updateTime", p -> p.date(d -> d))
                        )
                    )
                );
                
                logger.info("文档索引创建结果: {}", response.acknowledged());
            } else {
                logger.info("文档索引已存在: {}", indexName);
            }
        } catch (IOException e) {
            logger.error("创建文档索引失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 初始化产品索引
     */
    private void initProductIndex() {
        String indexName = "product";
        try {
            boolean exists = elasticsearchClient.indices().exists(
                ExistsRequest.of(e -> e.index(indexName))
            ).value();
            
            if (!exists) {
                logger.info("创建产品索引: {}", indexName);
                
                // 创建索引请求
                CreateIndexResponse response = elasticsearchClient.indices().create(
                    CreateIndexRequest.of(c -> c
                        .index(indexName)
                        .mappings(m -> m
                            .properties("id", p -> p.keyword(k -> k))
                            .properties("name", p -> p.text(t -> t))
                            .properties("description", p -> p.text(t -> t))
                            .properties("category", p -> p.keyword(k -> k))
                            .properties("price", p -> p.float_(f -> f))
                            .properties("tags", p -> p.keyword(k -> k))
                            .properties("descriptionVector", p -> p.denseVector(v -> v.dims(1024)))
                            .properties("imageVector", p -> p.denseVector(v -> v.dims(512)))
                            .properties("featureVector", p -> p.denseVector(v -> v.dims(256)))
                            .properties("createTime", p -> p.date(d -> d))
                            .properties("updateTime", p -> p.date(d -> d))
                        )
                    )
                );
                
                logger.info("产品索引创建结果: {}", response.acknowledged());
            } else {
                logger.info("产品索引已存在: {}", indexName);
            }
        } catch (IOException e) {
            logger.error("创建产品索引失败: {}", e.getMessage(), e);
        }
    }
}
