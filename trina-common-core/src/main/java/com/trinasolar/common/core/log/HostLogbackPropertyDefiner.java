package com.trinasolar.common.core.log;

import ch.qos.logback.core.PropertyDefinerBase;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * @usage LOGBACK 获取HOSTNAME
 * <AUTHOR>
 * @date 2020-7-7 19:20
 */
public class HostLogbackPropertyDefiner extends PropertyDefinerBase {
    @Override
    public String getPropertyValue() {
        InetAddress addr;
        try {
            addr = InetAddress.getLocalHost();
            return addr.getHostName();
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        return "defaultHost";
    }
}
