package com.trinasolar.common.security.properties;


import jakarta.annotation.PostConstruct;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

/**
 * 安全属性配置
 *
 * <AUTHOR>
 * @date 2025-06-20 14:03
 */
@Slf4j
@Data
@Validated
@RefreshScope
@ConfigurationProperties(prefix = "trina.iam")
public class SecurityProperties {

    /**
     * 是否启用网关安全功能
     */
    private boolean enabled = true;

    /**
     * 对应iam环境地址，如测试环境：<a href="https://pdweb1.trinasolar.com">...</a>
     */
    @NotEmpty(message = "IAM 地址必须配置")
    private String envUrl;
    /**
     * 客户端ID，需要先到iam申请
     */
    @NotEmpty(message = "IAM 客户端ID必须配置")
    private String clientId;
    /**
     * 客户端密钥，需要先到iam申请
     */
    @NotEmpty(message = "IAM 客户端密钥必须配置")
    private String clientSecret;
    /**
     * 重定向url，注册在iam的应用重定向地址，可以携带path路径和参数
     * <p>
     * 注意：如携带了path路径和参数，整个认证过程使用保持一致
     */
    @NotEmpty(message = "IAM 客户端重定向地址必须配置")
    private String redirectUrl;

    /**
     * TASP 权限平台地址
     */
    private String taspUrl;

    /**
     * TASP 权限平台注册的应用ID
     */
    private String appId;

    /**
     * 鉴权白名单路径，如：/api/cas/*
     */
    private List<String> whiteUris = new ArrayList<>();

    @PostConstruct
    public void checkConfig() {
        log.info("当前 IAM 客户端配置参数, envUrl[{}], clientId[{}], clientSecret[{}], redirectUrl[{}]", desensitizeUrl(envUrl), desensitizeStr(clientId), desensitizeStr(clientSecret), desensitizeUrl(redirectUrl));

        if (!StringUtils.hasText(envUrl) || !StringUtils.hasText(clientId) || !StringUtils.hasText(clientSecret) || !StringUtils.hasText(redirectUrl)) {
            log.error("认证授权安全模块关键配置缺失，应用启动终止");
            throw new IllegalStateException("安全模块配置不完整");
        }
    }

    /**
     * 脱敏url
     *
     * @param url 网址
     * @return {@link String }
     */
    private String desensitizeUrl(String url) {
        if (!StringUtils.hasText(url)) return "";
        int thirdSlash = url.indexOf('/', url.indexOf("//") + 2);
        return thirdSlash != -1 ? url.substring(0, thirdSlash) + "..." : url;
    }

    /**
     * 脱敏str
     *
     * @param str 字符串
     * @return {@link String }
     */
    private String desensitizeStr(String str) {
        if (!StringUtils.hasText(str)) return "";
        return str.length() > 3 ? str.substring(0, 3) + "..." : str;
    }
}
