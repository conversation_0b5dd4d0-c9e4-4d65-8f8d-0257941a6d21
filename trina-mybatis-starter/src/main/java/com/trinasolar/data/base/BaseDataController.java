package com.trinasolar.data.base;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.trinasolar.common.core.base.BaseEntity;
import com.trinasolar.common.core.base.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 通用数据控制器，提供标准的RESTful风格API
 * 包含基础的CRUD操作和高级查询功能
 *
 * @param <S> 服务类泛型，继承自BaseDataService
 * @param <T> 实体类泛型，继承自BaseEntity
 */
public abstract class BaseDataController<S extends BaseDataService<T>, T extends BaseEntity> {

    @Resource
    private S service;

    /**
     * 根据ID获取实体
     *
     * @param id 主键ID
     * @return 查询结果
     */
    @Operation(summary = "根据ID获取实体",
            responses = {@ApiResponse(description = "返回实体对象")})
    @GetMapping("/{id}")
    public R<T> getById(@Parameter(description = "主键ID", required = true)
                        @PathVariable Serializable id) {
        T entity = service.getById(id);
        return entity == null ?
                R.failed("记录不存在") :
                R.ok(entity);
    }

    /**
     * 查询所有实体
     *
     * @return 实体列表
     */
    @Operation(summary = "查询所有实体",
            responses = {@ApiResponse(description = "返回实体列表")})
    @GetMapping
    public R<List<T>> list() {
        List<T> list = service.list();
        return R.ok(list);
    }

    /**
     * 创建实体
     *
     * @param entity 实体对象
     * @return 操作结果
     */
    @Operation(summary = "创建实体",
            responses = {@ApiResponse(description = "创建结果")})
    @PostMapping
    public R<T> create(@RequestBody T entity) {
        // 确保创建操作不带ID
        if (entity.getId() != null) {
            entity.setId(null);
        }
        boolean success = service.save(entity);
        return success ?
                R.ok(entity) :
                R.failed("创建失败");
    }

    /**
     * 批量创建实体
     *
     * @param entityList 实体列表
     * @return 操作结果
     */
    @Operation(summary = "批量创建实体",
            responses = {@ApiResponse(description = "批量创建结果")})
    @PostMapping("/batch")
    public R<Boolean> createBatch(@RequestBody List<T> entityList) {
        boolean success = service.saveBatch(entityList);
        return success ?
                R.ok(true) :
                R.failed("批量创建失败");
    }

    /**
     * 更新实体
     *
     * @param id     主键ID
     * @param entity 实体对象
     * @return 操作结果
     */
    @Operation(summary = "更新实体",
            responses = {@ApiResponse(description = "更新结果")})
    @PutMapping("/{id}")
    public R<Boolean> update(
            @Parameter(description = "主键ID", required = true) @PathVariable Serializable id,
            @RequestBody T entity) {
        // 确保ID一致
        entity.setId(Convert.toLong(id));
        boolean success = service.updateById(entity);
        return success ?
                R.ok(true) :
                R.failed("更新失败，记录可能不存在");
    }

    /**
     * 部分更新实体（Patch方式）
     *
     * @param id     主键ID
     * @param entity 实体对象（包含要更新的字段）
     * @return 操作结果
     */
    @Operation(summary = "部分更新实体",
            responses = {@ApiResponse(description = "部分更新结果")})
    @PatchMapping("/{id}")
    public R<Boolean> updatePartial(
            @Parameter(description = "主键ID", required = true) @PathVariable Serializable id,
            @RequestBody T entity) {
        entity.setId(Convert.toLong(id));
        boolean success = service.updateById(entity);
        return success ?
                R.ok(true) :
                R.failed("部分更新失败，记录可能不存在");
    }

    /**
     * 批量更新实体
     *
     * @param entityList 实体列表
     * @return 操作结果
     */
    @Operation(summary = "批量更新实体",
            responses = {@ApiResponse(description = "批量更新结果")})
    @PutMapping("/batch")
    public R<Boolean> updateBatch(@RequestBody List<T> entityList) {
        boolean success = service.updateBatchById(entityList);
        return success ?
                R.ok(true) :
                R.failed("批量更新失败");
    }

    /**
     * 根据ID删除实体
     *
     * @param id 主键ID
     * @return 操作结果
     */
    @Operation(summary = "根据ID删除实体",
            responses = {@ApiResponse(description = "删除结果")})
    @DeleteMapping("/{id}")
    public R<Boolean> delete(
            @Parameter(description = "主键ID", required = true) @PathVariable Serializable id) {
        boolean success = service.removeById(id);
        return success ?
                R.ok(true) :
                R.failed("删除失败，记录可能不存在");
    }

    /**
     * 批量删除实体
     *
     * @param ids ID列表
     * @return 操作结果
     */
    @Operation(summary = "批量删除实体",
            responses = {@ApiResponse(description = "批量删除结果")})
    @DeleteMapping("/batch")
    public R<Boolean> deleteBatch(@RequestBody List<Serializable> ids) {
        boolean success = service.removeByIds(ids);
        return success ?
                R.ok(true) :
                R.failed("批量删除失败");
    }

    /**
     * 根据示例查询单个实体
     *
     * @param entity 示例实体
     * @return 查询结果
     */
    @Operation(summary = "根据示例查询单个实体",
            responses = {@ApiResponse(description = "返回匹配的实体")})
    @PostMapping("/query/one")
    public R<T> getByExample(@RequestBody T entity) {
        T result = service.getOne(entity);
        return result == null ?
                R.failed("未找到匹配记录") :
                R.ok(result);
    }

    /**
     * 条件查询实体列表
     *
     * @param query 查询条件
     * @return 实体列表
     */
    @Operation(summary = "条件查询实体列表",
            responses = {@ApiResponse(description = "返回符合条件的实体列表")})
    @PostMapping("/query/list")
    public R<List<T>> getList(@RequestBody BaseQuery<T> query) {
        List<T> list = service.getList(query);
        return R.ok(list);
    }

    /**
     * 分页查询实体
     *
     * @param query 查询条件（包含分页参数）
     * @return 分页结果
     */
    @Operation(summary = "分页查询实体",
            responses = {@ApiResponse(description = "返回分页查询结果")})
    @PostMapping("/query/page")
    public R<IPage<T>> getPage(@RequestBody BaseQuery<T> query) {
        IPage<T> page = service.getPage(query);
        return R.ok(page);
    }

    /**
     * 计数查询
     *
     * @param query 查询条件
     * @return 符合条件的记录数
     */
    @Operation(summary = "计数查询",
            responses = {@ApiResponse(description = "返回符合条件的记录数")})
    @PostMapping("/query/count")
    public R<Long> count(@RequestBody BaseQuery<T> query) {
        long count = service.count(query);
        return R.ok(count);
    }

    /**
     * 带过滤条件的查询列表
     *
     * @param filters 过滤条件
     * @param query   查询条件
     * @return 实体列表
     */
    @Operation(summary = "带过滤条件的查询列表",
            responses = {@ApiResponse(description = "返回符合条件的实体列表")})
    @PostMapping("/query/filter/list")
    public R<List<T>> getListWithFilters(
            @RequestParam Map<String, Object> filters,
            @RequestBody BaseQuery<T> query) {
        List<T> list = service.getListWithFilters(filters, query);
        return R.ok(list);
    }

    /**
     * 带过滤条件的分页查询
     *
     * @param filters 过滤条件
     * @param query   查询条件（包含分页参数）
     * @return 分页结果
     */
    @Operation(summary = "带过滤条件的分页查询",
            responses = {@ApiResponse(description = "返回分页查询结果")})
    @PostMapping("/query/filter/page")
    public R<IPage<T>> getPageWithFilters(
            @RequestParam Map<String, Object> filters,
            @RequestBody BaseQuery<T> query) {
        IPage<T> page = service.getPageWithFilters(filters, query);
        return R.ok(page);
    }
}
