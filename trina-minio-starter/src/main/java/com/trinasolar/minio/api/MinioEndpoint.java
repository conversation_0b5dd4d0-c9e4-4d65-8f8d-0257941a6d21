package com.trinasolar.minio.api;

import com.trinasolar.minio.service.FileTemplate;
import com.trinasolar.minio.vo.MinioItem;
import com.trinasolar.minio.vo.MinioObject;
import io.minio.messages.Bucket;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MinIO对象存储服务API
 * 提供对象存储的基本操作，包括存储桶管理和对象管理
 */
@RestController
@AllArgsConstructor
@RequestMapping("${minio.endpoint.name:/minio}")
@Tag(name = "MinIO对象存储", description = "提供存储桶和对象的管理操作")
public class MinioEndpoint {
    private final FileTemplate fileTemplate;

    //===================================================
    // RESTful风格API - 存储桶操作
    //===================================================

    /**
     * 创建存储桶 (RESTful风格)
     */
    @SneakyThrows
    @PostMapping("/bucket/{bucketName}")
    @Operation(
        summary = "创建存储桶", 
        description = "创建一个新的存储桶 - RESTful风格API"
    )
    @ApiResponse(responseCode = "200", description = "存储桶创建成功")
    public Bucket createBucker(
            @PathVariable String bucketName) {
        fileTemplate.createBucket(bucketName);
        return fileTemplate.getBucket(bucketName).get();
    }

    /**
     * 获取所有存储桶 (RESTful风格)
     */
    @SneakyThrows
    @GetMapping("/bucket")
    @Operation(
        summary = "获取所有存储桶", 
        description = "列出所有可用的存储桶 - RESTful风格API"
    )
    public List<Bucket> getBuckets() {
        return fileTemplate.getAllBuckets();
    }

    /**
     * 获取存储桶信息 (RESTful风格)
     */
    @SneakyThrows
    @GetMapping("/bucket/{bucketName}")
    @Operation(
        summary = "获取存储桶信息", 
        description = "获取指定名称存储桶的详细信息 - RESTful风格API"
    )
    public Bucket getBucket(
            @PathVariable String bucketName) {
        return fileTemplate.getBucket(bucketName).orElseThrow(() -> new IllegalArgumentException("Bucket Name not found!"));
    }

    /**
     * 删除存储桶 (RESTful风格)
     */
    @SneakyThrows
    @DeleteMapping("/bucket/{bucketName}")
    @ResponseStatus(HttpStatus.ACCEPTED)
    @Operation(
        summary = "删除存储桶", 
        description = "删除指定名称的存储桶 - RESTful风格API"
    )
    public void deleteBucket(
            @PathVariable String bucketName) {
        fileTemplate.removeBucket(bucketName);
    }

    //===================================================
    // RESTful风格API - 对象操作
    //===================================================

    /**
     * 上传文件 (RESTful风格)
     */
    @SneakyThrows
    @PostMapping(value = "/object/{bucketName}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(
        summary = "上传文件", 
        description = "上传文件到指定存储桶，使用原始文件名 - RESTful风格API"
    )
    public MinioObject createObject(
            @RequestPart("file") MultipartFile object,
            @PathVariable String bucketName) {
        String name = object.getOriginalFilename();
        fileTemplate.putObject(bucketName, name, object.getInputStream(), object.getSize(), object.getContentType());
        return new MinioObject(fileTemplate.getObjectInfo(bucketName, name));
    }

    /**
     * 上传文件并指定名称 (RESTful风格)
     */
    @SneakyThrows
    @PostMapping(value = "/object/{bucketName}/{objectName}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(
        summary = "上传文件(指定名称)", 
        description = "上传文件到指定存储桶并使用指定的对象名称 - RESTful风格API"
    )
    public MinioObject createObject(
            @RequestPart("file") MultipartFile object,
            @PathVariable String bucketName,
            @PathVariable String objectName) {
        fileTemplate.putObject(bucketName, objectName, object.getInputStream(), object.getSize(), object.getContentType());
        return new MinioObject(fileTemplate.getObjectInfo(bucketName, objectName));
    }

    /**
     * 列出对象 (RESTful风格)
     */
    @SneakyThrows
    @GetMapping("/object/{bucketName}/{objectPrefix}")
    @Operation(
        summary = "列出对象", 
        description = "列出存储桶中以指定前缀开始的所有对象 - RESTful风格API"
    )
    public List<MinioItem> filterObject(
            @PathVariable String bucketName,
            @PathVariable String objectPrefix) {
        return fileTemplate.getAllObjectsByPrefix(bucketName, objectPrefix, true);
    }

    /**
     * 获取对象临时URL (RESTful风格)
     */
    @SneakyThrows
    @GetMapping("/object/{bucketName}/{objectName}/{expires}")
    @Operation(
        summary = "获取对象临时URL", 
        description = "获取对象的临时访问URL，指定过期时间（秒） - RESTful风格API"
    )
    public Map<String, Object> getObjectUrl(
            @PathVariable String bucketName,
            @PathVariable String objectName,
            @PathVariable Integer expires) {
        Map<String, Object> responseBody = new HashMap<>(8);
        responseBody.put("bucket", bucketName);
        responseBody.put("object", objectName);
        responseBody.put("url", fileTemplate.getObjectURL(bucketName, objectName, expires));
        responseBody.put("expires", expires);
        return responseBody;
    }

    /**
     * 删除对象 (RESTful风格)
     */
    @SneakyThrows
    @ResponseStatus(HttpStatus.ACCEPTED)
    @DeleteMapping("/object/{bucketName}/{objectName}")
    @Operation(
        summary = "删除对象", 
        description = "从存储桶中删除指定的对象 - RESTful风格API"
    )
    public void deleteObject(
            @PathVariable String bucketName,
            @PathVariable String objectName) {
        fileTemplate.removeObject(bucketName, objectName);
    }

    //===================================================
    // 友好API格式 - 兼容测试脚本
    //===================================================
    
    /**
     * 创建存储桶 (友好API)
     * POST /minio/bucket/create?bucket={bucketName}
     */
    @SneakyThrows
    @PostMapping("/bucket/create")
    @Operation(
        summary = "创建存储桶(友好API)", 
        description = "使用查询参数创建新的存储桶"
    )
    public ResponseEntity<?> createBucketFriendly(
            @RequestParam("bucket") String bucketName) {
        fileTemplate.createBucket(bucketName);
        return ResponseEntity.ok(fileTemplate.getBucket(bucketName).get());
    }
    
    /**
     * 获取所有存储桶 (友好API)
     * GET /minio/buckets
     */
    @SneakyThrows
    @GetMapping("/buckets")
    @Operation(
        summary = "获取所有存储桶(友好API)", 
        description = "列出所有可用的存储桶"
    )
    public ResponseEntity<List<Bucket>> listBuckets() {
        return ResponseEntity.ok(fileTemplate.getAllBuckets());
    }
    
    /**
     * 上传文件 (友好API)
     * POST /minio/upload?bucket={bucketName}&objectName={objectName}
     */
    @SneakyThrows
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(
        summary = "上传文件(友好API)", 
        description = "使用查询参数上传文件"
    )
    @ApiResponse(
        responseCode = "200", 
        description = "上传成功",
        content = @Content(mediaType = "application/json", schema = @Schema(implementation = MinioObject.class))
    )
    public ResponseEntity<?> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("bucket") String bucketName,
            @RequestParam(value = "objectName", required = false) String objectName) {
        
        String name = objectName != null ? objectName : file.getOriginalFilename();
        fileTemplate.putObject(
            bucketName, 
            name,
            file.getInputStream(), 
            file.getSize(), 
            file.getContentType()
        );
        
        try {
            MinioObject obj = new MinioObject(fileTemplate.getObjectInfo(bucketName, name));
            return ResponseEntity.ok(obj);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("文件上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取文件信息 (友好API)
     * GET /minio/stat?bucket={bucketName}&objectName={objectName}
     */
    @SneakyThrows
    @GetMapping("/stat")
    @Operation(
        summary = "获取文件信息(友好API)", 
        description = "使用查询参数获取文件信息"
    )
    public ResponseEntity<?> getObjectInfo(
            @RequestParam("bucket") String bucketName,
            @RequestParam("objectName") String objectName) {
        
        try {
            MinioObject obj = new MinioObject(fileTemplate.getObjectInfo(bucketName, objectName));
            return ResponseEntity.ok(obj);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body("文件不存在或无法访问: " + e.getMessage());
        }
    }
    
    /**
     * 列出存储桶中的文件 (友好API)
     * GET /minio/list?bucket={bucketName}&prefix={prefix}
     */
    @SneakyThrows
    @GetMapping("/list")
    @Operation(
        summary = "列出文件(友好API)", 
        description = "使用查询参数列出文件"
    )
    public ResponseEntity<List<MinioItem>> listObjects(
            @RequestParam("bucket") String bucketName,
            @RequestParam(value = "prefix", required = false, defaultValue = "") String prefix) {
        
        List<MinioItem> items = fileTemplate.getAllObjectsByPrefix(bucketName, prefix, true);
        return ResponseEntity.ok(items);
    }
    
    /**
     * 获取文件下载URL (友好API)
     * GET /minio/url?bucket={bucketName}&objectName={objectName}&expires={expirySeconds}
     */
    @SneakyThrows
    @GetMapping("/url")
    @Operation(
        summary = "获取下载URL(友好API)", 
        description = "使用查询参数获取下载URL"
    )
    public ResponseEntity<Map<String, Object>> getObjectUrlFriendly(
            @RequestParam("bucket") String bucketName,
            @RequestParam("objectName") String objectName,
            @RequestParam(value = "expires", defaultValue = "3600") Integer expires) {
        
        Map<String, Object> response = new HashMap<>();
        response.put("bucket", bucketName);
        response.put("object", objectName);
        response.put("url", fileTemplate.getObjectURL(bucketName, objectName, expires));
        response.put("expires", expires);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 删除文件 (友好API)
     * DELETE /minio/delete?bucket={bucketName}&objectName={objectName}
     */
    @SneakyThrows
    @DeleteMapping("/delete")
    @Operation(
        summary = "删除文件(友好API)", 
        description = "使用查询参数删除文件"
    )
    public ResponseEntity<String> deleteObjectFriendly(
            @RequestParam("bucket") String bucketName,
            @RequestParam("objectName") String objectName) {
        
        fileTemplate.removeObject(bucketName, objectName);
        return ResponseEntity.ok("文件已删除");
    }
    
    /**
     * 下载文件 (友好API)
     * GET /minio/download?bucket={bucketName}&objectName={objectName}
     */
    @SneakyThrows
    @GetMapping("/download")
    @Operation(
        summary = "下载文件(友好API)", 
        description = "使用查询参数下载文件，返回临时URL重定向"
    )
    public ResponseEntity<String> downloadObject(
            @RequestParam("bucket") String bucketName,
            @RequestParam("objectName") String objectName) {
        
        // 获取临时URL并重定向
        String url = fileTemplate.getObjectURL(bucketName, objectName, 60);
        return ResponseEntity.status(HttpStatus.FOUND)
            .header("Location", url)
            .body("重定向到文件下载URL");
    }
}
