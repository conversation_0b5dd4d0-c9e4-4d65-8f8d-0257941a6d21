# 网关安全配置示例
trina:
  iam:
    # 是否启用网关安全功能
    enabled: true
    # IAM 服务器地址
    env-url: https://pdweb1.trinasolar.com
    # IAM 客户端配置
    client-id: your-client-id
    client-secret: your-client-secret
    redirect-url: http://your-gateway.com/callback
    # 权限接口地址
    permission-url: http://trina-test.trinasolar.com/app/perms
    # 白名单配置
    white-uris:
      - /api/public/**
      - /health
      - /actuator/**
      - /swagger-ui/**
      - /v3/api-docs/**
      - /favicon.ico
      - /error
    # 缓存配置
    cache:
      enabled: true
      initial-capacity: 512
      maximum-size: 10000
      expire-after-write: 5m
      expire-after-access: 10m
      weak-keys: true
      record-stats: true
  # CORS 跨域配置
  gateway:
    cors:
      # 是否启用 CORS 过滤器
      enabled: true
      # 允许的域名列表（精确匹配）
      allowed-origins:
        - http://localhost:3000
        - http://localhost:8080
        - http://127.0.0.1:3000
        - http://127.0.0.1:8080
      # 允许的请求头
    # WebClient 配置（避免 PrematureCloseException）
    webclient:
      # 是否启用调试模式（网络日志）
      debug-enabled: false
      # 连接池配置
      connection-pool:
        name: gateway-http-pool
        max-connections: 100
        max-idle-time: 30s
        max-life-time: 5m
        pending-acquire-timeout: 10s
        evict-in-background: 120s
      # 超时配置
      timeout:
        connect-timeout: 10000
        response-timeout: 30s
        token-timeout: 15s
        token-verify-timeout: 15s
        permission-timeout: 10s
        menu-timeout: 10s
        logout-timeout: 10s
      # 重试配置
      retry:
        token-retry-attempts: 2
        token-verify-retry-attempts: 3
        permission-retry-attempts: 2
        menu-retry-attempts: 2
        logout-retry-attempts: 2
        api-path-retry-attempts: 2
        initial-delay: 300ms
        token-verify-initial-delay: 500ms

# Spring Cloud Gateway 配置
spring:
  cloud:
    gateway:
      routes:
        # 示例路由配置
        - id: user-service
          uri: lb://user-service
          predicates:
            - Path=/api/user/**
          filters:
            - StripPrefix=2
        - id: order-service
          uri: lb://order-service
          predicates:
            - Path=/api/order/**
          filters:
            - StripPrefix=2

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always