package com.trinasolar.elasticsearch.base.wrapper;

import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.search.Highlight;
import co.elastic.clients.elasticsearch.core.search.HighlightField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.*;
import java.util.function.Consumer;

/**
 * Elasticsearch查询构建器
 * 提供链式调用的查询构建功能
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Accessors(chain = true)
public class EsQueryWrapper<T> {
    
    /**
     * 查询条件列表
     */
    private List<Query> mustQueries = new ArrayList<>();
    
    /**
     * 应该满足的查询条件列表
     */
    private List<Query> shouldQueries = new ArrayList<>();
    
    /**
     * 不能满足的查询条件列表
     */
    private List<Query> mustNotQueries = new ArrayList<>();
    
    /**
     * 过滤条件列表
     */
    private List<Query> filterQueries = new ArrayList<>();
    
    /**
     * 排序字段列表
     */
    private Map<String, SortOrder> sortFields = new LinkedHashMap<>();
    
    /**
     * 分页起始位置
     */
    private Integer from;
    
    /**
     * 分页大小
     */
    private Integer size;
    
    /**
     * 高亮字段
     */
    private Map<String, HighlightField> highlightFields = new HashMap<>();
    
    /**
     * 包含字段
     */
    private List<String> includeFields = new ArrayList<>();
    
    /**
     * 排除字段
     */
    private List<String> excludeFields = new ArrayList<>();
    
    /**
     * 最小评分
     */
    private Float minScore;
    
    /**
     * 搜索超时时间
     */
    private String timeout;
    
    /**
     * 是否返回版本号
     */
    private Boolean version;
    
    /**
     * 是否返回序列号和主要术语
     */
    private Boolean seqNoPrimaryTerm;
    
    /**
     * 搜索类型
     */
    private String searchType;
    
    /**
     * 路由值
     */
    private String routing;
    
    /**
     * 偏好设置
     */
    private String preference;
    
    /**
     * 是否解释评分
     */
    private Boolean explain;
    
    /**
     * 是否跟踪总命中数
     */
    private Boolean trackTotalHits;
    
    /**
     * 构造函数
     */
    public EsQueryWrapper() {
    }
    
    // ==================== 查询条件方法 ====================
    
    /**
     * 精确匹配
     */
    public EsQueryWrapper<T> eq(String field, Object value) {
        if (value != null) {
            mustQueries.add(TermQuery.of(t -> t.field(field).value(String.valueOf(value)))._toQuery());
        }
        return this;
    }
    
    /**
     * 不等于
     */
    public EsQueryWrapper<T> ne(String field, Object value) {
        if (value != null) {
            mustNotQueries.add(TermQuery.of(t -> t.field(field).value(String.valueOf(value)))._toQuery());
        }
        return this;
    }
    
    /**
     * 大于
     */
    public EsQueryWrapper<T> gt(String field, Object value) {
        if (value != null) {
            mustQueries.add(RangeQuery.of(r -> r.field(field).gt(co.elastic.clients.json.JsonData.of(String.valueOf(value))))._toQuery());
        }
        return this;
    }
    
    /**
     * 大于等于
     */
    public EsQueryWrapper<T> ge(String field, Object value) {
        if (value != null) {
            mustQueries.add(RangeQuery.of(r -> r.field(field).gte(co.elastic.clients.json.JsonData.of(String.valueOf(value))))._toQuery());
        }
        return this;
    }
    
    /**
     * 小于
     */
    public EsQueryWrapper<T> lt(String field, Object value) {
        if (value != null) {
            mustQueries.add(RangeQuery.of(r -> r.field(field).lt(co.elastic.clients.json.JsonData.of(String.valueOf(value))))._toQuery());
        }
        return this;
    }

    /**
     * 小于等于
     */
    public EsQueryWrapper<T> le(String field, Object value) {
        if (value != null) {
            mustQueries.add(RangeQuery.of(r -> r.field(field).lte(co.elastic.clients.json.JsonData.of(String.valueOf(value))))._toQuery());
        }
        return this;
    }

    /**
     * 范围查询
     */
    public EsQueryWrapper<T> between(String field, Object from, Object to) {
        if (from != null && to != null) {
            mustQueries.add(RangeQuery.of(r -> r.field(field)
                .gte(co.elastic.clients.json.JsonData.of(String.valueOf(from)))
                .lte(co.elastic.clients.json.JsonData.of(String.valueOf(to))))._toQuery());
        }
        return this;
    }
    
    /**
     * 模糊匹配
     */
    public EsQueryWrapper<T> like(String field, String value) {
        if (value != null && !value.trim().isEmpty()) {
            mustQueries.add(WildcardQuery.of(w -> w.field(field).value("*" + value + "*"))._toQuery());
        }
        return this;
    }
    
    /**
     * 左模糊匹配
     */
    public EsQueryWrapper<T> likeLeft(String field, String value) {
        if (value != null && !value.trim().isEmpty()) {
            mustQueries.add(WildcardQuery.of(w -> w.field(field).value("*" + value))._toQuery());
        }
        return this;
    }
    
    /**
     * 右模糊匹配
     */
    public EsQueryWrapper<T> likeRight(String field, String value) {
        if (value != null && !value.trim().isEmpty()) {
            mustQueries.add(WildcardQuery.of(w -> w.field(field).value(value + "*"))._toQuery());
        }
        return this;
    }
    
    /**
     * 全文搜索
     */
    public EsQueryWrapper<T> match(String field, String value) {
        if (value != null && !value.trim().isEmpty()) {
            mustQueries.add(MatchQuery.of(m -> m.field(field).query(value))._toQuery());
        }
        return this;
    }
    
    /**
     * 多字段全文搜索
     */
    public EsQueryWrapper<T> multiMatch(String value, String... fields) {
        if (value != null && !value.trim().isEmpty() && fields.length > 0) {
            mustQueries.add(MultiMatchQuery.of(m -> m.query(value).fields(Arrays.asList(fields)))._toQuery());
        }
        return this;
    }
    
    /**
     * 短语匹配
     */
    public EsQueryWrapper<T> matchPhrase(String field, String value) {
        if (value != null && !value.trim().isEmpty()) {
            mustQueries.add(MatchPhraseQuery.of(m -> m.field(field).query(value))._toQuery());
        }
        return this;
    }
    
    /**
     * 前缀匹配
     */
    public EsQueryWrapper<T> prefix(String field, String value) {
        if (value != null && !value.trim().isEmpty()) {
            mustQueries.add(PrefixQuery.of(p -> p.field(field).value(value))._toQuery());
        }
        return this;
    }
    
    /**
     * 正则表达式匹配
     */
    public EsQueryWrapper<T> regexp(String field, String value) {
        if (value != null && !value.trim().isEmpty()) {
            mustQueries.add(RegexpQuery.of(r -> r.field(field).value(value))._toQuery());
        }
        return this;
    }
    
    /**
     * 模糊查询
     */
    public EsQueryWrapper<T> fuzzy(String field, String value) {
        if (value != null && !value.trim().isEmpty()) {
            mustQueries.add(FuzzyQuery.of(f -> f.field(field).value(value))._toQuery());
        }
        return this;
    }
    
    /**
     * 查询字符串
     */
    public EsQueryWrapper<T> queryString(String query) {
        if (query != null && !query.trim().isEmpty()) {
            mustQueries.add(QueryStringQuery.of(q -> q.query(query))._toQuery());
        }
        return this;
    }
    
    /**
     * 存在字段
     */
    public EsQueryWrapper<T> exists(String field) {
        mustQueries.add(ExistsQuery.of(e -> e.field(field))._toQuery());
        return this;
    }
    
    /**
     * 字段为空
     */
    public EsQueryWrapper<T> isNull(String field) {
        mustNotQueries.add(ExistsQuery.of(e -> e.field(field))._toQuery());
        return this;
    }
    
    /**
     * 字段不为空
     */
    public EsQueryWrapper<T> isNotNull(String field) {
        mustQueries.add(ExistsQuery.of(e -> e.field(field))._toQuery());
        return this;
    }
    
    /**
     * 在列表中
     */
    public EsQueryWrapper<T> in(String field, Collection<?> values) {
        if (values != null && !values.isEmpty()) {
            List<String> stringValues = values.stream()
                .map(String::valueOf)
                .toList();
            mustQueries.add(TermsQuery.of(t -> t.field(field).terms(tf -> tf.value(stringValues.stream().map(s -> co.elastic.clients.elasticsearch._types.FieldValue.of(s)).toList())))._toQuery());
        }
        return this;
    }
    
    /**
     * 不在列表中
     */
    public EsQueryWrapper<T> notIn(String field, Collection<?> values) {
        if (values != null && !values.isEmpty()) {
            List<String> stringValues = values.stream()
                .map(String::valueOf)
                .toList();
            mustNotQueries.add(TermsQuery.of(t -> t.field(field).terms(tf -> tf.value(stringValues.stream().map(s -> co.elastic.clients.elasticsearch._types.FieldValue.of(s)).toList())))._toQuery());
        }
        return this;
    }
    
    // ==================== 逻辑操作方法 ====================
    
    /**
     * OR条件
     */
    public EsQueryWrapper<T> or(Consumer<EsQueryWrapper<T>> consumer) {
        EsQueryWrapper<T> wrapper = new EsQueryWrapper<>();
        consumer.accept(wrapper);
        
        List<Query> orQueries = new ArrayList<>();
        orQueries.addAll(wrapper.getMustQueries());
        orQueries.addAll(wrapper.getShouldQueries());
        
        if (!orQueries.isEmpty()) {
            shouldQueries.add(BoolQuery.of(b -> b.should(orQueries))._toQuery());
        }
        return this;
    }
    
    /**
     * AND条件（嵌套）
     */
    public EsQueryWrapper<T> and(Consumer<EsQueryWrapper<T>> consumer) {
        EsQueryWrapper<T> wrapper = new EsQueryWrapper<>();
        consumer.accept(wrapper);
        
        if (!wrapper.getMustQueries().isEmpty() || !wrapper.getShouldQueries().isEmpty()) {
            mustQueries.add(wrapper.buildBoolQuery());
        }
        return this;
    }
    
    // ==================== 排序方法 ====================
    
    /**
     * 升序排序
     */
    public EsQueryWrapper<T> orderByAsc(String field) {
        sortFields.put(field, SortOrder.Asc);
        return this;
    }
    
    /**
     * 降序排序
     */
    public EsQueryWrapper<T> orderByDesc(String field) {
        sortFields.put(field, SortOrder.Desc);
        return this;
    }
    
    // ==================== 分页方法 ====================
    
    /**
     * 设置分页
     * @param pageNum 页码（从0开始，符合Spring Data标准）
     * @param pageSize 每页大小
     */
    public EsQueryWrapper<T> page(int pageNum, int pageSize) {
        // 确保pageNum不为负数，从0开始计算
        this.from = Math.max(0, pageNum) * pageSize;
        this.size = pageSize;
        return this;
    }
    
    /**
     * 设置起始位置
     */
    public EsQueryWrapper<T> from(int from) {
        this.from = from;
        return this;
    }
    
    /**
     * 设置返回数量
     */
    public EsQueryWrapper<T> size(int size) {
        this.size = size;
        return this;
    }
    
    // ==================== 高亮方法 ====================
    
    /**
     * 添加高亮字段
     */
    public EsQueryWrapper<T> highlight(String field) {
        highlightFields.put(field, HighlightField.of(h -> h));
        return this;
    }
    
    /**
     * 添加高亮字段（自定义配置）
     */
    public EsQueryWrapper<T> highlight(String field, Consumer<HighlightField.Builder> consumer) {
        HighlightField.Builder builder = new HighlightField.Builder();
        consumer.accept(builder);
        highlightFields.put(field, builder.build());
        return this;
    }
    
    // ==================== 字段选择方法 ====================
    
    /**
     * 包含字段
     */
    public EsQueryWrapper<T> select(String... fields) {
        if (fields != null && fields.length > 0) {
            includeFields.addAll(Arrays.asList(fields));
        }
        return this;
    }
    
    /**
     * 排除字段
     */
    public EsQueryWrapper<T> exclude(String... fields) {
        if (fields != null && fields.length > 0) {
            excludeFields.addAll(Arrays.asList(fields));
        }
        return this;
    }
    
    // ==================== 构建方法 ====================
    
    /**
     * 构建Bool查询
     */
    public Query buildBoolQuery() {
        BoolQuery.Builder boolBuilder = new BoolQuery.Builder();
        
        if (!mustQueries.isEmpty()) {
            boolBuilder.must(mustQueries);
        }
        
        if (!shouldQueries.isEmpty()) {
            boolBuilder.should(shouldQueries);
        }
        
        if (!mustNotQueries.isEmpty()) {
            boolBuilder.mustNot(mustNotQueries);
        }
        
        if (!filterQueries.isEmpty()) {
            boolBuilder.filter(filterQueries);
        }
        
        return boolBuilder.build()._toQuery();
    }
    
    /**
     * 构建搜索请求
     */
    public SearchRequest.Builder buildSearchRequest(String... indices) {
        SearchRequest.Builder builder = new SearchRequest.Builder();
        
        // 设置索引
        if (indices != null && indices.length > 0) {
            builder.index(Arrays.asList(indices));
        }
        
        // 设置查询
        Query query = buildBoolQuery();
        if (query != null) {
            builder.query(query);
        }
        
        // 设置分页
        if (from != null) {
            builder.from(from);
        }
        if (size != null) {
            builder.size(size);
        }
        
        // 设置排序
        if (!sortFields.isEmpty()) {
            sortFields.forEach((field, order) -> {
                builder.sort(s -> s.field(f -> f.field(field).order(order)));
            });
        }
        
        // 设置高亮
        if (!highlightFields.isEmpty()) {
            builder.highlight(h -> h.fields(highlightFields));
        }
        
        // 设置字段过滤
        if (!includeFields.isEmpty() || !excludeFields.isEmpty()) {
            builder.source(s -> {
                if (!includeFields.isEmpty()) {
                    s.filter(f -> f.includes(includeFields));
                }
                if (!excludeFields.isEmpty()) {
                    s.filter(f -> f.excludes(excludeFields));
                }
                return s;
            });
        }
        
        // 设置其他参数
        if (minScore != null) {
            builder.minScore(minScore.doubleValue());
        }
        
        if (timeout != null) {
            builder.timeout(timeout);
        }
        
        if (version != null) {
            builder.version(version);
        }
        
        if (seqNoPrimaryTerm != null) {
            builder.seqNoPrimaryTerm(seqNoPrimaryTerm);
        }
        
        if (routing != null) {
            builder.routing(routing);
        }
        
        if (preference != null) {
            builder.preference(preference);
        }
        
        if (explain != null) {
            builder.explain(explain);
        }
        
        if (trackTotalHits != null) {
            builder.trackTotalHits(t -> t.enabled(trackTotalHits));
        }
        
        return builder;
    }
    
    /**
     * 清空所有条件
     */
    public EsQueryWrapper<T> clear() {
        mustQueries.clear();
        shouldQueries.clear();
        mustNotQueries.clear();
        filterQueries.clear();
        sortFields.clear();
        highlightFields.clear();
        includeFields.clear();
        excludeFields.clear();
        from = null;
        size = null;
        minScore = null;
        timeout = null;
        version = null;
        seqNoPrimaryTerm = null;
        searchType = null;
        routing = null;
        preference = null;
        explain = null;
        trackTotalHits = null;
        return this;
    }
}