---
title: "15分钟上手Trina Common一站式开发"
description: "从脚手架生成项目到集成MyBatis、<PERSON><PERSON>、<PERSON>cos、Sentinel等模块的完整入门教程"
date: "2024-07-07"
tags: ["Trina", "脚手架", "MyBatis", "Redis", "Nacos", "Sentinel", "入门"]
---

# 15分钟上手Trina Common一站式开发

## 1. 用脚手架生成你的第一个Trina项目（3分钟）

### 1.1 脚手架简介
Trina框架提供了官方Maven Archetype脚手架，帮助你一键生成标准化的微服务项目骨架。

### 1.2 TASP快速生成项目
在TASP创建应用系统和应用程序，创建完成后，使用 git clone 命令将项目克隆到本地：
```shell
git clone https://code.trinasolar.com/ipd/tasp/***/.git
```

### 1.3 ArcheType快速生成项目
在命令行输入（请替换groupId、artifactId等参数）：
```shell
mvn archetype:generate \
  -DarchetypeGroupId=com.trinasolar \
  -DarchetypeArtifactId=trina-archetype \
  -DarchetypeVersion=4.0.0 \
  -DgroupId=com.trinasolar \
  -DartifactId=trina-demo \
  -Dversion=1.0.0
```

### 1.4 生成项目结构说明

```yaml
trina-demo
├── docs/                       # 文档目录，存放 SQL 脚本、API 文档、生成日志等
├── src/
│   └── main/
│       └── java/
│           └── com.trinasolar.demo
│               ├── controller              # 控制器层：REST API 接口入口
│               ├── mapper                  # 数据访问层（DAO）：MyBatis Mapper 接口
│               ├── model                   # 领域模型层
│               │   ├── dto                 # 数据传输对象（DTO）：用于接口传参或返回
│               │   └── entity              # 实体类（Entity）：映射数据库表结构
│               ├── service                 # 业务逻辑层接口定义
│               │   └── impl                # 业务逻辑层实现类
│               ├── core                    # 公共模块，存放通用组件
│               │   ├── config              # 配置类：全局配置信息（如 Bean 定义）
│               │   ├── exception           # 异常定义处理模块：项目自定义异常处理
│               │   ├── constants           # 常量定义模块
│               │   └── utils               # 工具类模块：通用工具方法
│               └── Application.java # Spring Boot 启动类
│
├── resources/                              # 资源文件目录
│   ├── mapper/                             # MyBatis XML 映射文件
│   ├── static/                             # 静态资源（如 HTML、CSS、JS 文件）
│   ├── templates/                          # 模板资源（如 Thymeleaf 页面模板）
│   ├── application.yml                     # 主配置文件
│   ├── application-dev.yml                 # 开发环境配置文件
│   └── logback-spring.xml                  # 日志配置文件
│
├── test/                                   # 测试代码目录
│   └── java/com/trinasolar/demo    # 单元测试类存放位置
│
├── .gitignore                              # Git 忽略提交配置
├── pom.xml                                 # Maven 构建配置文件
└── README.md                               # 项目说明文档
```

## 2. 基础模块说明（3分钟）
- 已自动引入`trina-parent`父POM，统一依赖和插件版本管理
- 默认集成`trina-web-starter`（Web基础）、`trina-xss-starter（跨站防护）`、`trina-openapi-starter`（API文档）、`trina-security-starter`（安全认证）等基础模块
- 目录结构清晰，支持后续按需扩展

| 模块                | 作用                   |
|---------------------|------------------------|
| trina-parent        | 统一依赖和版本管理     |
| trina-web-starter   | Web开发基础能力        |
| trina-web-starter   | 跨站防护基础能力        |
| trina-openapi-starter | 自动生成OpenAPI文档  |
| trina-security-starter | 安全认证与权限控制  |

> 你无需手动配置这些基础模块，开箱即用！

### 2.1 xss 安全配置

`application.yml`配置示例如下：
```yaml
security:
  xss:
    # 开启xss,默认关闭： 建议生产环境开启
    enabled: true
    # 全局：对文件进行首尾 trim
    trim-text: true
    # 模式：clear 清理（默认），escape 转义
    mode: clear
    # [clear 专用] prettyPrint，默认关闭： 保留换行
    pretty-print: false
    #  [clear 专用] 使用转义，默认关闭
    enable-escape: false
    #  拦截的路由，默认为空, 表示所有路由都拦截
    path-patterns:
      - "/api/**"
      - "/admin/**"
    # 放行的路由，默认为空
    path-exclude-patterns:
      - "/api/upload/**"
```

### 2.2 openapi 文档配置

`application.yml`配置示例如下，完成配置后，访问`/swagger-ui/index.html`即可查看文档：
```yaml
swagger:
  enabled: true
  title: 项目名称
  description: 项目描述
  version: 1.0.0
  author: 作者
  gateway: http://localhost:8080
  token-url: /api/scf/token
  scope: server
```

### 2.3 security 认证与授权配置

该模块主要是简化了IAM认证的集成，提供API接口级权限控制和列级数据访问控制。因此需要先到**IAM平台申请客户端认证信息**，以及到**TASP平台配置API接口的权限**。
在未认证的情况下所有接口统一返回状态码为401，未授权则返回状态码为403。

**响应示例**（`response json`）：
```json
{
  "code": 401,
  "message": "Unauthorized",
  "data": ""
}
```
```json
{
  "code": 403,
  "message": "Forbidden",
  "data": ""
}
```

**配置示例**（`application.yml`）：
```yaml
trina:
  iam:
    # 对应iam环境地址，如测试环境：https://pdweb1.trinasolar.com
    env-url:
    # 客户端ID，需要先到iam申请
    client-id:
    # 客户端密钥，需要先到iam申请
    client-secret:
    # 重定向url，注册在iam的应用重定向地址，可以携带path路径和参数
    redirect-url:
    # 获取用户权限接口地址，对应TASP环境地址
    permission-url:
    # 鉴权白名单路径list集合，如：/api/test/*
    white-uris:
      - /api/test/*
      - /health
```

**默认接口**
security starter 模块自带了认证基础接口如下，主要提供给前端脚手架完成登录和鉴权逻辑。

| 接口名称 | 接口描述 | 请求方式 | 请求参数 | 返回参数 | 备注 |
| --- | --- | --- | --- | --- | --- |
|/api/scf/token| 通过授权码从 TAM 远程获取令牌 | GET | query string code 授权码 | 无 | 无 |
|/api/scf/userinfo| 获取用户信息 | GET | header string Authorization Bearer xxx | 无 | 无 |
|/api/scf/logout| 通过token从 TAM 远程注销 | GET | header string Authorization Bearer xxx | 无 | 无 |
|/api/scf/refresh| 刷新token和权限缓存 | GET | header string Authorization Bearer xxx | 无 | 无 |

## 3. 引入常用功能模块（9分钟）

### 3.1 MyBatis 数据访问

**添加依赖**（`pom.xml`）：
```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-mybatis-starter</artifactId>
    <version>4.0.0</version>
</dependency>
```

**配置示例**（`application.yml`）：
```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************
    username: xxx
    password: xxx
```

**代码生成**：
```java
public class CodeGeneratorTest {
    @Test
    void codeGen() {
        CodeGenerator.run("****************************************************************************************************************",
                "xxx", "xxx",
                "xxx", "com.trinasolar.xxx", "", BaseTenantEntity.class,
                new String[]{"upms_user"},
                new String[]{"upms_"},
                new String[]{});
    }
}
```


**简单用法**：
```java
// 实体类
@Data
@TableName("t_user")
@Schema(name = "User", description = "用户")
public class User extends BaseTenantEntity {
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    private String username;
}

// Mapper接口
@Mapper
public interface UserMapper extends BaseDataMapper<User> {}

// service接口
public interface IUserService extends BaseDataService<User> {}

// service实现
@Service
public class UserServiceImpl extends BaseDataServiceImpl<UserMapper, User> implements IUserService {}

// Service用法
private final UserMapper userMapper;

public void demo() {
    User user = new User();
    user.setUsername("张三");
    userMapper.insert(user);
}
```

### 3.2 Redis 缓存

**添加依赖**：
```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-redis-starter</artifactId>
    <version>4.0.0</version>
</dependency>
```

**配置示例**：
```yaml
spring:
  redis:
    host: localhost
    port: 6379
```

**简单用法**：
```java
private final StringRedisTemplate redisTemplate;

public void cacheDemo() {
    redisTemplate.opsForValue().set("key", "value");
    String val = redisTemplate.opsForValue().get("key");
}

// utils用法
RedisUtils.set("key", "value");
String val = RedisUtils.get("key");
```

### 3.3 Nacos 配置与注册中心

**添加依赖**：
```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-openfeign-starter</artifactId>
    <version>4.0.0</version>
</dependency>
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-nacos-starter</artifactId>
    <version>4.0.0</version>
</dependency>
```

**配置示例**：
```yaml
spring:
  application:
    # 应用分组，用于nacos服务发现和配置
    group: ${NACOS_GROUP:DEFAULT_GROUP}
    # 应用命名空间，用于nacos服务发现和配置
    namespace: ${spring.application.name}-${spring.profiles.active}
  # nacos 基础配置
  config:
    import:
      - optional:nacos:application-${spring.profiles.active:dev}.yaml
      - optional:nacos:${spring.application.name:application}-${spring.profiles.active:dev}.yaml
  cloud:
    nacos:
      # 认证信息
      username: ${NACOS_USERNAME:nacos}
      password: ${NACOS_PASSWORD:nacos}
      # 服务发现配置
      discovery:
        server-addr: ${NACOS_HOST:127.0.0.1}:${NACOS_PORT:8848}
        # namespace会在NacosEnvironmentProcessor中自动设置
        namespace: ${spring.application.namespace:public}
        group: ${spring.application.group:DEFAULT_GROUP}
        enabled: true
        register-enabled: true
        # 心跳间隔
        heart-beat-interval: 5000
        # 心跳超时时间
        heart-beat-timeout: 15000
        # IP删除超时时间
        ip-delete-timeout: 30000
      # 配置中心配置
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        # namespace会在NacosEnvironmentProcessor中自动设置
        namespace: ${spring.cloud.nacos.discovery.namespace}
        group: ${spring.cloud.nacos.discovery.group}
        file-extension: ${NACOS_FILE_EXTENSION:yaml}
        refresh-enabled: true
        # 配置长轮询超时时间
        timeout: 3000
        # 最大重试次数
        max-retry: 10
        # 配置监听器长轮询超时时间
        config-long-poll-timeout: 46000
        # 配置重试时间
        config-retry-time: 2333
        # 启用远程同步配置
        enable-remote-sync-config: false
        # 扩展配置（应用特定配置）
        extension-configs:
          - data-id: ${spring.application.name:application}-${spring.profiles.active:dev}.${spring.cloud.nacos.config.file-extension:yaml}
            group: ${spring.application.group:DEFAULT_GROUP}
            refresh: true
        # 共享配置（通用配置）
        shared-configs:
          - data-id: common-config-${spring.profiles.active:dev}.${spring.cloud.nacos.config.file-extension:yaml}
            group: ${spring.application.group:DEFAULT_GROUP}
            refresh: true
```

**简单用法**：
```java
@RefreshScope
@RestController
public class ConfigController {
    @Value("${custom.config:default}")
    private String config;

    @GetMapping("/config")
    public String getConfig() {
        return config;
    }
}
```

### 3.4 Sentinel 服务限流与熔断

**添加依赖**：
```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-sentinel-starter</artifactId>
    <version>4.0.0</version>
</dependency>
```

**配置示例**：
```yaml
spring:
  cloud:
    sentinel:
      transport:
        dashboard: localhost:8858
      eager: true
feign:
  sentinel:
    enabled: true
```

**简单用法**：
```java
@RestController
public class HelloController {
    @GetMapping("/hello")
    @SentinelResource("hello")
    public String hello() {
        return "Hello, Sentinel!";
    }
}
```

## 4. 小结与进阶

- 通过脚手架生成项目，基础模块已自动集成
- 按需引入MyBatis、Redis、Nacos、Sentinel等模块，配置简单
- 推荐阅读各模块的[使用指南](../guides/)和[架构文档](../architecture/)
- 更多最佳实践请参考[15分钟系列教程](./README.mdx)

> 现在，你已具备Trina Common企业级开发的完整起步能力！ 