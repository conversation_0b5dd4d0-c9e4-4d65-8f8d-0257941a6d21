package com.trinasolar.nacos.utils;

import com.alibaba.cloud.nacos.NacosConfigProperties;
import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.PropertyKeyConst;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketTimeoutException;
import java.util.Properties;

/**
 * Nacos连接检查工具
 * 用于诊断Nacos连接问题并提供详细的错误信息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class NacosConnectionChecker {
    private static final Logger logger = LoggerFactory.getLogger(NacosConnectionChecker.class);
    private static final int CONNECTION_TIMEOUT = 3000; // 3秒连接超时

    /**
     * 检查Nacos服务器连接
     *
     * @param discoveryProperties Nacos服务发现属性
     * @return 连接检查结果
     */
    public static ConnectionCheckResult checkDiscoveryConnection(NacosDiscoveryProperties discoveryProperties) {
        // 1. 检查服务器地址格式
        String serverAddr = discoveryProperties.getServerAddr();
        if (!StringUtils.hasText(serverAddr)) {
            return new ConnectionCheckResult(false, "Nacos服务器地址为空");
        }

        // 2. 检查网络连接
        String[] addrParts = serverAddr.split(":");
        if (addrParts.length != 2) {
            return new ConnectionCheckResult(false, "Nacos服务器地址格式错误，应为 'host:port'，当前值: " + serverAddr);
        }

        String host = addrParts[0];
        int port;
        try {
            port = Integer.parseInt(addrParts[1]);
        } catch (NumberFormatException e) {
            return new ConnectionCheckResult(false, "Nacos服务器端口格式错误: " + addrParts[1]);
        }

        // 3. 尝试TCP连接
        try {
            if (!isServerReachable(host, port)) {
                return new ConnectionCheckResult(false, "无法连接到Nacos服务器 " + serverAddr + "，请检查服务器是否启动或网络连接");
            }
        } catch (Exception e) {
            return new ConnectionCheckResult(false, "连接Nacos服务器时发生异常: " + e.getMessage());
        }

        // 4. 尝试创建NamingService
        try {
            Properties properties = new Properties();
            properties.put(PropertyKeyConst.SERVER_ADDR, serverAddr);
            if (StringUtils.hasText(discoveryProperties.getUsername())) {
                properties.put(PropertyKeyConst.USERNAME, discoveryProperties.getUsername());
                properties.put(PropertyKeyConst.PASSWORD, discoveryProperties.getPassword());
            }
            if (StringUtils.hasText(discoveryProperties.getNamespace())) {
                properties.put(PropertyKeyConst.NAMESPACE, discoveryProperties.getNamespace());
            }

            NamingService namingService = NacosFactory.createNamingService(properties);
            // 尝试获取服务列表来验证连接
            namingService.getServicesOfServer(1, 10);

            return new ConnectionCheckResult(true, "成功连接到Nacos服务发现服务");
        } catch (NacosException e) {
            logger.error("创建Nacos服务发现客户端失败", e);
            return new ConnectionCheckResult(false, "创建Nacos服务发现客户端失败: " + e.getErrCode() + " - " + e.getErrMsg());
        } catch (Exception e) {
            logger.error("连接Nacos服务发现服务时发生未知异常", e);
            return new ConnectionCheckResult(false, "连接Nacos服务发现服务时发生未知异常: " + e.getMessage());
        }
    }

    /**
     * 检查Nacos配置中心连接
     *
     * @param configProperties Nacos配置中心属性
     * @return 连接检查结果
     */
    public static ConnectionCheckResult checkConfigConnection(NacosConfigProperties configProperties) {
        logger.info("开始检查Nacos配置中心连接...");

        // 1. 检查服务器地址格式
        String serverAddr = configProperties.getServerAddr();
        if (!StringUtils.hasText(serverAddr)) {
            return new ConnectionCheckResult(false, "Nacos服务器地址为空");
        }

        // 2. 检查网络连接
        String[] addrParts = serverAddr.split(":");
        if (addrParts.length != 2) {
            return new ConnectionCheckResult(false, "Nacos服务器地址格式错误，应为 'host:port'，当前值: " + serverAddr);
        }

        String host = addrParts[0];
        int port;
        try {
            port = Integer.parseInt(addrParts[1]);
        } catch (NumberFormatException e) {
            return new ConnectionCheckResult(false, "Nacos服务器端口格式错误: " + addrParts[1]);
        }

        // 3. 尝试TCP连接
        try {
            if (!isServerReachable(host, port)) {
                return new ConnectionCheckResult(false, "无法连接到Nacos服务器 " + serverAddr + "，请检查服务器是否启动或网络连接");
            }
        } catch (Exception e) {
            return new ConnectionCheckResult(false, "连接Nacos服务器时发生异常: " + e.getMessage());
        }

        // 4. 尝试创建ConfigService
        try {
            Properties properties = new Properties();
            properties.put(PropertyKeyConst.SERVER_ADDR, serverAddr);
            if (StringUtils.hasText(configProperties.getUsername())) {
                properties.put(PropertyKeyConst.USERNAME, configProperties.getUsername());
                properties.put(PropertyKeyConst.PASSWORD, configProperties.getPassword());
            }
            if (StringUtils.hasText(configProperties.getNamespace())) {
                properties.put(PropertyKeyConst.NAMESPACE, configProperties.getNamespace());
            }

            ConfigService configService = NacosFactory.createConfigService(properties);
            // 尝试获取配置来验证连接
            configService.getConfig("test-connection", configProperties.getGroup(), 1000);

            return new ConnectionCheckResult(true, "成功连接到Nacos配置中心服务");
        } catch (NacosException e) {
            if (e.getErrCode() == 300) {
                // 配置不存在是正常的，说明连接成功了
                return new ConnectionCheckResult(true, "成功连接到Nacos配置中心服务");
            }
            logger.error("创建Nacos配置中心客户端失败", e);
            return new ConnectionCheckResult(false, "创建Nacos配置中心客户端失败: " + e.getErrCode() + " - " + e.getErrMsg());
        } catch (Exception e) {
            logger.error("连接Nacos配置中心服务时发生未知异常", e);
            return new ConnectionCheckResult(false, "连接Nacos配置中心服务时发生未知异常: " + e.getMessage());
        }
    }

    /**
     * 检查服务器是否可达
     *
     * @param host 主机名
     * @param port 端口
     * @return 是否可达
     */
    private static boolean isServerReachable(String host, int port) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(host, port), CONNECTION_TIMEOUT);
            return true;
        } catch (SocketTimeoutException e) {
            logger.warn("连接到Nacos服务器超时: {}:{}", host, port);
            return false;
        } catch (Exception e) {
            logger.warn("连接到Nacos服务器失败: {}:{}, 错误: {}", host, port, e.getMessage());
            return false;
        }
    }

    /**
     * 连接检查结果
     */
    public static class ConnectionCheckResult {
        private final boolean success;
        private final String message;

        public ConnectionCheckResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }
    }
}
