---
title: 父模块使用指南
description: Trina Parent 模块的使用方法与最佳实践
---

# Trina Parent 模块使用指南

`trina-parent` 是 Trina Common 框架的核心依赖管理模块，它统一管理了框架中使用的各种依赖版本，提供了一致的依赖配置，简化了项目的依赖管理。

## 1. 功能概述

父模块作为 Trina Common 框架的依赖管理中心，统一定义了 Spring Boot、Spring Cloud、数据库驱动、工具库等各种依赖的版本，确保各个模块之间的依赖版本一致，避免版本冲突。

### 主要功能

- **依赖版本管理**: 统一管理框架使用的依赖版本
- **插件配置**: 提供统一的 Maven 插件配置
- **构建规则**: 定义统一的构建规则和流程
- **发布配置**: 提供统一的发布配置

## 2. 快速开始

### 2.1 在项目中引入父模块

在项目的 `pom.xml` 文件中添加以下配置，引入 Trina Parent 作为父模块：

```xml
<parent>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-parent</artifactId>
    <version>4.0.0</version>
    <relativePath/>
</parent>
```

### 2.2 使用预定义依赖

引入父模块后，可以直接使用预定义的依赖，无需指定版本号：

```xml
<dependencies>
    <!-- Spring Boot 相关依赖 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    
    <!-- Trina Common 相关依赖 -->
    <dependency>
        <groupId>com.trinasolar</groupId>
        <artifactId>trina-common-core</artifactId>
    </dependency>
</dependencies>
```

## 3. 依赖版本管理

### 3.1 核心框架版本

Trina Parent 预定义了以下核心框架的版本：

| 框架 | 版本 | 说明 |
|------|------|------|
| Spring Boot | 3.5.0 | Spring Boot 核心框架 |
| Spring Cloud | 2025.0.0 | Spring Cloud 微服务框架 |
| Spring Cloud Alibaba | 2022.0.0.0 | Spring Cloud Alibaba 组件 |
| Spring | 6.2.1 | Spring 框架 |
| MyBatis | 3.5.19 | MyBatis 框架 |
| MyBatis Plus | 3.5.10 | MyBatis 增强工具 |
| MyBatis Plus Join | 1.5.2 | MyBatis Plus 连表查询 |
| Elasticsearch | 9.0.0 | 分布式搜索和分析引擎 |

### 3.2 工具库版本

| 工具库 | 版本 | 说明 |
|--------|------|------|
| Hutool | 5.8.36 | Java 工具类库 |
| Lombok | 1.18.38 | Java 注解库，简化代码 |
| Fastjson | 2.0.57 | 阿里巴巴 JSON 处理库 |
| Guava | 33.1.0-jre | Google 核心库 |
| Commons Lang3 | 3.11 | Apache Commons 语言工具 |
| Commons IO | 2.7 | Apache Commons IO 工具 |
| SpringDoc | 2.8.8 | API 文档工具 |
| EasyExcel | 4.0.2 | Excel 处理工具 |
| Minio | 8.5.16 | 对象存储客户端 |
| AWS SDK S3 | 2.31.60 | AWS S3 客户端 |
| JSch | 0.1.55 | SSH 连接工具 |
| JSoup | 1.15.1 | HTML 解析工具 |
| POI | 5.3.0 | Office 文档处理 |
| JAXB | 4.0.5 | XML 绑定工具 |
| HttpClient5 | 5.4.2 | HTTP 客户端 |
| Jasypt | 3.0.5 | 配置加密工具 |

### 3.3 数据库相关版本

| 组件 | 版本 | 说明 |
|--------|------|------|
| MySQL | 8.3.0 | MySQL 数据库驱动 |
| Druid | 1.2.24 | 数据库连接池 |
| Dynamic Datasource | 4.3.1 | 动态数据源 |
| JSqlParser | 4.3 | SQL 解析工具 |
| MongoDB Driver | 4.9.1 | MongoDB 驱动 |
| Neo4j Driver | 5.10.0 | Neo4j 图数据库驱动 |

### 3.4 其他组件版本

| 组件 | 版本 | 说明 |
|----------|------|------|
| XXL-Job | 3.0.0 | 分布式任务调度平台 |
| Tomcat | 11.0.6 | Servlet 容器 |
| OAuth2 | 2.2.1.RELEASE | 认证授权框架 |
| Jakarta Validation | 3.1.1 | 数据校验框架 |

## 4. 如何使用父模块依赖

### 4.1 基本用法

1. **引入父模块**：首先在项目的 `pom.xml` 中引入 Trina Parent 作为父模块。
2. **添加依赖**：直接添加需要的依赖，无需指定版本号。
3. **版本覆盖**：如需使用不同版本，可以在项目的 `<properties>` 中覆盖版本属性。

### 4.2 依赖管理示例

**创建一个基于 Trina Common 的 Web 应用**：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <!-- 引入 Trina Parent 作为父模块 -->
    <parent>
        <groupId>com.trinasolar</groupId>
        <artifactId>trina-parent</artifactId>
        <version>4.0.0</version>
        <relativePath/>
    </parent>
    
    <groupId>com.example</groupId>
    <artifactId>my-application</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>我的应用</name>
    <description>使用 Trina Common 的示例项目</description>
    
    <dependencies>
        <!-- Spring Boot 相关依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        
        <!-- Trina Common 核心依赖 -->
        <dependency>
            <groupId>com.trinasolar</groupId>
            <artifactId>trina-common-core</artifactId>
        </dependency>
        
        <!-- 数据库相关依赖 -->
        <dependency>
            <groupId>com.trinasolar</groupId>
            <artifactId>trina-mybatis-starter</artifactId>
        </dependency>
        
        <!-- Redis 相关依赖 -->
        <dependency>
            <groupId>com.trinasolar</groupId>
            <artifactId>trina-redis-starter</artifactId>
        </dependency>
        
        <!-- 测试相关依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
```

### 4.3 版本覆盖示例

如果需要使用不同版本的依赖，可以在项目的 `<properties>` 中覆盖版本属性：

```xml
<properties>
    <!-- 覆盖 Spring Boot 版本 -->
    <spring-boot.version>3.5.1</spring-boot.version>
    
    <!-- 覆盖 MyBatis Plus 版本 -->
    <mybatis-plus.version>3.5.11</mybatis-plus.version>
    
    <!-- 覆盖 MySQL 驱动版本 -->
    <mysql.version>8.3.1</mysql.version>
</properties>
```

## 5. Trina Common 模块依赖

Trina Parent 预定义了所有 Trina Common 模块的版本，您可以直接引用这些模块：

### 5.1 核心模块

| 模块 | 说明 |
|------|------|
| trina-common-core | 核心工具和基础组件 |
| trina-web-starter | Web 应用基础组件 |
| trina-i18n-starter | 国际化支持模块 |
| trina-security-starter | 安全认证模块 |
| trina-openapi-starter | API 文档模块 |

### 5.2 数据访问模块

| 模块 | 说明 |
|------|------|
| trina-mybatis-starter | MyBatis 增强模块 |
| trina-datasource-starter | 多数据源模块 |
| trina-elasticsearch-starter | Elasticsearch 集成模块 |
| trina-redis-starter | Redis 集成模块 |
| trina-mongodb-starter | MongoDB 集成模块 |
| trina-neo4j-starter | Neo4j 集成模块 |
| trina-minio-starter | Minio 对象存储模块 |

### 5.3 微服务相关模块

| 模块 | 说明 |
|------|------|
| trina-gateway-starter | 网关集成模块 |
| trina-remote-starter | 远程调用模块 |
| trina-microservice-starter | 微服务基础模块 |

### 5.4 其他功能模块

| 模块 | 说明 |
|------|------|
| trina-common-excel | Excel 处理模块 |
| trina-common-sensitive | 敏感数据处理模块 |
| trina-kubernetes-starter | Kubernetes 集成模块 |
| trina-build-info-starter | 构建信息和版本跟踪模块 |
| trina-sequence-starter | 序列号生成器模块 |
| trina-sftp-starter | SFTP 集成模块 |
| trina-tagmanagement-starter | 标签管理模块 |
| trina-tenant-starter | 多租户支持模块 |
| trina-xss-starter | XSS 防护模块 |
| trina-xxl-job-starter | XXL-JOB 集成模块 |

## 6. 最佳实践

### 6.1 依赖管理建议

1. **使用依赖继承**：在项目中继承 Trina Parent，利用预定义的依赖版本。

2. **避免直接指定版本**：尽量不要在项目中直接指定依赖版本，而是使用父模块管理的版本。

3. **合理使用版本覆盖**：只在必要时覆盖依赖版本，并记录覆盖原因。

4. **避免传递依赖冲突**：使用 `<exclusions>` 排除可能导致冲突的传递依赖。

### 6.2 模块化项目结构

对于大型项目，建议采用模块化结构：

```
my-application/
  ├── my-application-api/        # API 定义模块
  ├── my-application-common/     # 通用代码模块
  ├── my-application-service/    # 业务逻辑模块
  ├── my-application-web/        # Web 接口模块
  ├── my-application-job/        # 定时任务模块
  └── pom.xml                    # 父 POM
```

父 POM 配置示例：

```xml
<parent>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-parent</artifactId>
    <version>4.0.0</version>
</parent>

<groupId>com.example</groupId>
<artifactId>my-application</artifactId>
<version>0.0.1-SNAPSHOT</version>
<packaging>pom</packaging>

<modules>
    <module>my-application-api</module>
    <module>my-application-common</module>
    <module>my-application-service</module>
    <module>my-application-web</module>
    <module>my-application-job</module>
</modules>

<dependencyManagement>
    <dependencies>
        <!-- 项目内部模块依赖 -->
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>my-application-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>my-application-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>my-application-service</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
</dependencyManagement>
```

### 6.3 集成测试最佳实践

使用 Trina Parent 进行集成测试的建议：

```xml
<dependencies>
    <!-- 测试依赖 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <scope>test</scope>
    </dependency>
    
    <!-- 测试容器支持 -->
    <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>testcontainers</artifactId>
        <scope>test</scope>
    </dependency>
    <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>mysql</artifactId>
        <scope>test</scope>
    </dependency>
</dependencies>
```

## 7. 常见问题解决

### 7.1 依赖冲突

**问题**：项目中出现依赖冲突。

**解决方案**：
1. 使用 `mvn dependency:tree` 命令分析依赖树
2. 通过 `<exclusions>` 排除冲突依赖
3. 在 `<dependencyManagement>` 中指定明确的版本

示例：

```xml
<dependency>
    <groupId>com.example</groupId>
    <artifactId>example-library</artifactId>
    <exclusions>
        <exclusion>
            <groupId>org.conflicting</groupId>
            <artifactId>conflicting-dependency</artifactId>
        </exclusion>
    </exclusions>
</dependency>
```

### 7.2 版本更新

**问题**：如何升级到新版本的 Trina Parent。

**解决方案**：
1. 更新 parent 声明中的版本号
2. 检查新版本的变更日志，了解重大变更
3. 逐步测试功能，确保兼容性

### 7.3 自定义依赖版本

**问题**：需要使用 Trina Parent 未管理的依赖。

**解决方案**：
在项目的 `<dependencyManagement>` 中定义：

```xml
<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>com.custom</groupId>
            <artifactId>custom-library</artifactId>
            <version>1.0.0</version>
        </dependency>
    </dependencies>
</dependencyManagement>
```

## 8. 版本更新记录

### 4.0.0 (2023-09-01)
- 初始版本发布
- 支持 Spring Boot 3.5.0
- 支持 Spring Cloud 2025.0.0
- 整合各类常用依赖

### 3.0.0 (2023-08-01)
- 预发布版本
- 核心功能实现
- 内部测试使用 

### 2.0.0 (2023-07-01)
- 预发布版本
- 核心功能实现
- 内部测试使用 

### 1.0.0 (2023-06-01)
- 初始版本发布
- 支持 Spring Boot 2.7.10
- 支持 Spring Cloud 2021.0.6
- 整合各类常用依赖