<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.trinasolar</groupId>
        <artifactId>trina-common</artifactId>
        <version>4.0.0</version>
    </parent>
    <artifactId>trina-parent</artifactId>
    <packaging>pom</packaging>
    <description>Parent版本控制，公共版本控制，在此pom定义的依赖，全局业务微服务不需要指定版本。</description>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trina-web-starter</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trina-minio-starter</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trina-datasource-starter</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trina-mybatis-starter</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trina-openapi-starter</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trina-redis-starter</artifactId>
                <version>${common.version}</version>
            </dependency>
            <!-- 添加缺失的模块依赖 -->
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trina-common-excel</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trina-common-sensitive</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trina-sequence-starter</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trina-security-starter</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trina-xss-starter</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trina-elasticsearch-starter</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trina-i18n-starter</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trina-openfeign-starter</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trina-nacos-starter</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trina-sentinel-starter</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trina-seata-starter</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trina-gateway-starter</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trina-xxl-job-starter</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trina-stream-starter</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>${spring-boot.version}</version>
                <optional>true</optional>
            </dependency>
            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot-starter</artifactId>
                <version>${jasypt.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.servlet</groupId>
                <artifactId>jakarta.servlet-api</artifactId>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl.job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>${jaxb.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring-boot.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud-dependence.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>


    </dependencyManagement>
</project>
