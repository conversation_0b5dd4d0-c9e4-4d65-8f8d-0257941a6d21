package com.trinasolar.common.security.constants;


/**
 * 安全相关常数
 *
 * <AUTHOR>
 * @date 2025-06-20 13:58
 */
public interface SecurityConstant {

    /**
     * 令牌前缀
     */
    String TOKEN_PREFIX = "Bearer ";
    /**
     * 授权路径
     */
    String AUTHORIZE_PATH = "/mga/sps/oauth/oauth20/authorize?response_type=code&client_id=";
    /**
     * 令牌路径
     */
    String TOKEN_PATH = "/mga/sps/oauth/oauth20/token";
    /**
     * 用户信息路径
     */
    String USERINFO_PATH = "/mga/sps/oauth/oauth20/userinfo";
    /**
     * 注销路径
     */
    String LOGOUT_PATH = "/pkmslogout?client_id=";

    /**
     * 重定向uri
     */
    String REDIRECT_URI = "&redirect_uri=";

    /**
     * 获取用户权限信息地址，包含角色、权限
     */
    String TASP_AUTHORITY_PATH = "/kepler/upms/p/reso/app/permissions/user/authority";
    /**
     * 获取用户菜单权限地址
     */
    String TASP_MENU_PATH = "/kepler/upms/p/reso/app/permissions/current/user/app";
    /**
     * 获取用户按钮权限地址
     */
    String TASP_AUTH_PATH = "/kepler/upms/p/reso/app/permissions/current/user/app/auth";

    /**
     * 获取用户API PATH权限地址
     */
    String TASP_API_AUTH_PATH = "/kepler/upms/p/reso/app/permissions/current/user/app/api/auth";
}
