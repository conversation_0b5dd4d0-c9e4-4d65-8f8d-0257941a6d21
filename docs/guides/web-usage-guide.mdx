---
title: Web 模块使用指南
description: Trina Web 模块的使用方法与最佳实践
---

# Web 模块使用指南

## 1. 简介

Trina Web Starter 是 Trina Common 框架中的基础 Web 应用组件，提供了构建 RESTful API 和 Web 应用所需的核心功能。它集成了 Spring Web MVC 和 Spring Boot Actuator，并提供了统一的响应格式、全局异常处理和其他 Web 开发所需的基础设施。

## 2. 功能特性

- **RESTful API 支持**：简化 REST 接口的开发
- **统一响应格式**：使用 `R<T>` 类型包装响应，确保 API 返回格式一致
- **全局异常处理**：自动捕获并处理异常，转换为友好的错误响应
- **监控端点**：通过 Spring Boot Actuator 提供健康检查、指标收集等功能
- **Web 开发工具**：提供常用 Web 工具类和辅助方法
- **请求参数验证**：支持 JSR-303 参数验证
- **国际化支持**：结合 trina-i18n-starter 提供多语言支持

## 3. 快速开始

### 3.1 添加依赖

在项目的 `pom.xml` 中添加以下依赖：

```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-web-starter</artifactId>
    <version>${trina-common.version}</version>
</dependency>
```

### 3.2 基础配置

在 `application.yml` 中添加 Web 相关配置：

```yaml
spring:
  mvc:
    throw-exception-if-no-handler-found: true  # 开启 404 异常
  web:
    resources:
      add-mappings: false  # 关闭默认静态资源映射
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss  # 日期格式化
    time-zone: GMT+8  # 时区设置

server:
  port: 8080  # 应用端口
  servlet:
    context-path: /  # 上下文路径

# Actuator 配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics  # 暴露的端点
  endpoint:
    health:
      show-details: always  # 显示详细健康信息
```

### 3.3 创建 RESTful 控制器

使用 Trina Web Starter 创建一个简单的 RESTful 控制器：

```java
@RestController
@RequestMapping("/api/users")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @GetMapping
    public R<List<User>> listUsers() {
        return R.ok(userService.findAll());
    }
    
    @GetMapping("/{id}")
    public R<User> getUser(@PathVariable Long id) {
        User user = userService.findById(id);
        if (user == null) {
            return R.fail("用户不存在");
        }
        return R.ok(user);
    }
    
    @PostMapping
    public R<User> createUser(@Valid @RequestBody User user) {
        User created = userService.save(user);
        return R.ok(created, "用户创建成功");
    }
    
    @PutMapping("/{id}")
    public R<User> updateUser(@PathVariable Long id, @Valid @RequestBody User user) {
        user.setId(id);
        User updated = userService.update(user);
        return R.ok(updated);
    }
    
    @DeleteMapping("/{id}")
    public R<Void> deleteUser(@PathVariable Long id) {
        userService.delete(id);
        return R.ok("用户删除成功");
    }
}
```

## 4. 核心组件

### 4.1 统一响应类 R\<T>

`R<T>` 是 Trina Web Starter 提供的统一响应封装类，用于确保所有 API 返回格式一致：

```java
// 成功响应，带数据
R<User> r1 = R.ok(user);

// 成功响应，带消息
R<Void> r2 = R.ok("操作成功");

// 成功响应，带数据和消息
R<User> r3 = R.ok(user, "获取用户成功");

// 错误响应，带消息
R<Void> r4 = R.fail("操作失败");

// 错误响应，带错误码和消息
R<Void> r5 = R.fail(ErrorCodeEnum.PARAM_ERROR, "参数无效");

// 链式调用
R<List<User>> r6 = R.ok()
                    .data(userList)
                    .message("获取用户列表成功")
                    .timestamp(System.currentTimeMillis());
```

`R<T>` 的通用结构：

```json
{
  "code": 200,          // 状态码，200 表示成功，其他表示失败
  "message": "成功",     // 响应消息
  "data": { ... },      // 响应数据，可以是任何类型
  "timestamp": 1633432800000  // 时间戳
}
```

### 4.2 全局异常处理

Trina Web Starter 提供了 `GlobalExceptionHandler`，用于自动捕获并处理常见异常：

```java
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    // 处理业务异常
    @ExceptionHandler(BusinessException.class)
    public R<Void> handleBusinessException(BusinessException e) {
        log.warn("Business exception occurred: {}", e.getMessage());
        return R.fail(e.getCode(), e.getMessage());
    }
    
    // 处理参数验证异常
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R<Void> handleValidationException(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        List<FieldError> fieldErrors = bindingResult.getFieldErrors();
        
        StringBuilder sb = new StringBuilder();
        for (FieldError fieldError : fieldErrors) {
            sb.append(fieldError.getField()).append(": ")
              .append(fieldError.getDefaultMessage()).append("; ");
        }
        
        return R.fail(ErrorCodeEnum.PARAM_ERROR, sb.toString());
    }
    
    // 处理其他未捕获的异常
    @ExceptionHandler(Exception.class)
    public R<Void> handleException(Exception e) {
        log.error("Unexpected exception occurred", e);
        return R.fail(ErrorCodeEnum.SYSTEM_ERROR, "系统错误，请联系管理员");
    }
}
```

### 4.3 异常类型

Trina Web Starter 提供了几种常用的异常类型：

```java
// 业务异常
throw new BusinessException("用户名已存在");
throw new BusinessException(ErrorCodeEnum.BUSINESS_ERROR, "余额不足");

// 参数异常
throw new ParamException("参数无效");

// 认证异常
throw new AuthException("未授权访问");

// 系统异常
throw new SystemException("系统内部错误");
```

### 4.4 请求参数验证

结合 JSR-303 Bean Validation 进行参数验证：

```java
@Data
public class UserDTO {
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;
    
    @Email(message = "邮箱格式不正确")
    private String email;
    
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String mobile;
}

@RestController
@RequestMapping("/api/users")
public class UserController {
    
    @PostMapping("/register")
    public R<User> register(@Valid @RequestBody UserDTO userDTO) {
        // 如果验证失败，会自动抛出异常并由全局异常处理器处理
        User user = userService.register(userDTO);
        return R.ok(user, "注册成功");
    }
}
```

## 5. 高级特性

### 5.1 CORS 配置

跨域资源共享配置：

```java
@Configuration
public class WebConfig implements WebMvcConfigurer {
    
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
            .allowedOriginPatterns("*")
            .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
            .allowedHeaders("*")
            .allowCredentials(true)
            .maxAge(3600);
    }
}
```

### 5.2 拦截器配置

自定义拦截器配置：

```java
@Component
public class AuthInterceptor implements HandlerInterceptor {
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 执行身份验证逻辑
        String token = request.getHeader("Authorization");
        if (StringUtils.isEmpty(token)) {
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            return false;
        }
        
        // 验证 token
        // ...
        
        return true;
    }
}

@Configuration
public class WebConfig implements WebMvcConfigurer {
    
    @Autowired
    private AuthInterceptor authInterceptor;
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(authInterceptor)
            .addPathPatterns("/api/**")
            .excludePathPatterns("/api/auth/login", "/api/auth/register");
    }
}
```

### 5.3 自定义请求日志

记录请求和响应日志：

```java
@Aspect
@Component
@Slf4j
public class WebLogAspect {
    
    @Pointcut("execution(public * com.trinasolar.*.controller.*.*(..))")
    public void webLog() {}
    
    @Around("webLog()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        
        // 记录请求信息
        log.info("========== Request Begin ==========");
        log.info("URL: {}", request.getRequestURL().toString());
        log.info("Method: {}", request.getMethod());
        log.info("IP: {}", request.getRemoteAddr());
        log.info("Class Method: {}.{}", point.getSignature().getDeclaringTypeName(), point.getSignature().getName());
        log.info("Args: {}", Arrays.toString(point.getArgs()));
        
        // 执行方法
        long startTime = System.currentTimeMillis();
        Object result = point.proceed();
        long endTime = System.currentTimeMillis();
        
        // 记录响应信息
        log.info("Response: {}", JSON.toJSONString(result));
        log.info("Time Cost: {}ms", endTime - startTime);
        log.info("========== Request End ==========");
        
        return result;
    }
}
```

### 5.4 文件上传处理

处理文件上传：

```java
@RestController
@RequestMapping("/api/files")
public class FileController {
    
    @Value("${file.upload.dir:/tmp/uploads}")
    private String uploadDir;
    
    @PostMapping("/upload")
    public R<String> uploadFile(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return R.fail("请选择文件");
        }
        
        try {
            // 创建上传目录
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }
            
            // 生成文件名
            String filename = System.currentTimeMillis() + "_" + file.getOriginalFilename();
            Path filePath = uploadPath.resolve(filename);
            
            // 保存文件
            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
            
            return R.ok(filename, "文件上传成功");
        } catch (IOException e) {
            log.error("File upload failed", e);
            return R.fail("文件上传失败：" + e.getMessage());
        }
    }
    
    @GetMapping("/download/{filename:.+}")
    public void downloadFile(@PathVariable String filename, HttpServletResponse response) {
        Path filePath = Paths.get(uploadDir).resolve(filename);
        
        if (!Files.exists(filePath)) {
            response.setStatus(HttpStatus.NOT_FOUND.value());
            return;
        }
        
        try {
            // 设置响应头
            response.setContentType(Files.probeContentType(filePath));
            response.setHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");
            
            // 复制文件到输出流
            Files.copy(filePath, response.getOutputStream());
            response.getOutputStream().flush();
        } catch (IOException e) {
            log.error("File download failed", e);
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        }
    }
}
```

## 6. 集成 Spring Boot Actuator

Trina Web Starter 默认集成了 Spring Boot Actuator，提供了健康检查、指标收集等功能：

```yaml
# 完整的 Actuator 配置
management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: health,info,metrics,prometheus,loggers
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
    info:
      enabled: true
    prometheus:
      enabled: true
  info:
    app:
      name: ${spring.application.name}
      version: 1.0.0
      description: Web Application
```

访问健康检查端点：`GET /actuator/health`

```json
{
  "status": "UP",
  "components": {
    "diskSpace": {
      "status": "UP",
      "details": {
        "total": 500107862016,
        "free": 328803209216,
        "threshold": 10485760
      }
    },
    "db": {
      "status": "UP",
      "details": {
        "database": "MySQL",
        "validationQuery": "isValid()"
      }
    }
  }
}
```

## 7. 最佳实践

### 7.1 控制器设计原则

1. **保持控制器简洁**：控制器只负责参数接收、转换和返回结果，业务逻辑放在服务层
2. **统一返回格式**：始终使用 `R<T>` 包装返回结果
3. **合理分层**：遵循 Controller -> Service -> Repository 的分层架构
4. **RESTful 设计**：遵循 RESTful API 设计规范

### 7.2 异常处理原则

1. **业务异常使用 BusinessException**：用于表示可预期的业务错误
2. **不捕获可恢复异常**：让全局异常处理器统一处理
3. **记录详细日志**：对于系统异常，记录详细的堆栈信息
4. **友好的错误消息**：返回对用户有帮助的错误信息

### 7.3 安全最佳实践

1. **参数验证**：所有输入参数进行验证
2. **防止 XSS 攻击**：使用 trina-xss-starter 进行 XSS 防护
3. **CSRF 保护**：对关键操作实施 CSRF 保护
4. **敏感信息保护**：不直接返回敏感信息

## 8. 常见问题与解决方案

### 8.1 如何自定义 JSON 序列化行为？

可以通过配置 Jackson 的 `ObjectMapper` Bean 或在实体类上使用 Jackson 注解来定制：

```java
@Bean
public ObjectMapper objectMapper() {
    ObjectMapper objectMapper = new ObjectMapper();
    
    // 配置日期格式
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    objectMapper.setDateFormat(dateFormat);
    
    // 忽略未知属性
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    
    // 允许空对象
    objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
    
    return objectMapper;
}

// 实体类上使用 Jackson 注解
@Data
public class User {
    @JsonProperty("user_id")
    private Long id;
    
    private String username;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthDate;
    
    @JsonIgnore
    private String password;
}
```

### 8.2 如何获取当前请求上下文？

使用 `RequestContextHolder` 获取当前请求上下文：

```java
ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
HttpServletRequest request = attributes.getRequest();
HttpServletResponse response = attributes.getResponse();

// 获取请求参数
String parameter = request.getParameter("name");

// 获取请求头
String token = request.getHeader("Authorization");

// 获取客户端 IP
String ip = request.getRemoteAddr();
```

### 8.3 如何处理 API 版本控制？

可以通过多种方式实现 API 版本控制：

1. **URL 路径版本**：`/api/v1/users`、`/api/v2/users`
2. **请求参数版本**：`/api/users?version=1`
3. **请求头版本**：`X-API-Version: 1`
4. **媒体类型版本**：`Accept: application/vnd.company.v1+json`

示例实现：

```java
// URL 路径版本
@RestController
@RequestMapping("/api/v1/users")
public class UserControllerV1 {
    // ...
}

@RestController
@RequestMapping("/api/v2/users")
public class UserControllerV2 {
    // ...
}

// 请求头版本
@RestController
@RequestMapping("/api/users")
public class UserController {
    
    @GetMapping(headers = "X-API-Version=1")
    public R<List<User>> listUsersV1() {
        // V1 实现
        return R.ok(userService.findAllV1());
    }
    
    @GetMapping(headers = "X-API-Version=2")
    public R<List<User>> listUsersV2() {
        // V2 实现
        return R.ok(userService.findAllV2());
    }
}
```