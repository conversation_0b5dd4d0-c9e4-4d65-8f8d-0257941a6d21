import React from 'react';
import type { ReactNode } from 'react';
import clsx from 'clsx';
import Link from '@docusaurus/Link';
import useDocusaurusContext from '@docusaurus/useDocusaurusContext';
import Layout from '@theme/Layout';

import Heading from '@theme/Heading';
import HomepageFeatures from '@site/src/components/HomepageFeatures';

import styles from './index.module.css';

function HomepageHeader() {
  const {siteConfig} = useDocusaurusContext();
  return (
    <header className={clsx('hero', styles.heroBanner)}>

      <div className="container">
        <div className={styles.heroContent}>
          <Heading as="h1" className={styles.heroTitle}>
            {siteConfig.title}
          </Heading>
          <p className={styles.heroSubtitle}>{siteConfig.tagline}</p>
          <div className={styles.heroDescription}>
            <p>基于 Spring Boot 3.4 + Java 17 构建的企业级微服务开发框架</p>
            <p>提供完整的微服务解决方案，让您专注于业务逻辑开发</p>
          </div>
          <div className={styles.buttons}>
            <Link
              className={clsx('button button--primary button--lg', styles.getStartedButton)}
              to="/docs/">
              🚀 快速开始
            </Link>
            <Link
              className={clsx('button button--secondary button--lg', styles.learnMoreButton)}
              to="/docs/architecture/">
              📚 了解架构
            </Link>
          </div>
          <div className={styles.stats}>
            <div className={styles.stat}>
              <div className={styles.statNumber}>20+</div>
              <div className={styles.statLabel}>核心模块</div>
            </div>
            <div className={styles.stat}>
              <div className={styles.statNumber}>Java 17</div>
              <div className={styles.statLabel}>最新技术</div>
            </div>
            <div className={styles.stat}>
              <div className={styles.statNumber}>Spring Boot 3.4</div>
              <div className={styles.statLabel}>稳定框架</div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}



export default function Home(): ReactNode {
  const {siteConfig} = useDocusaurusContext();
  return (
    <Layout
      title={`${siteConfig.title} - 企业级微服务开发框架`}
      description="Trina Framework 是基于 Spring Boot 3.4 和 Java 17 构建的企业级微服务开发框架，提供完整的微服务解决方案">
      <HomepageHeader />
      <main>
        <HomepageFeatures />
      </main>
    </Layout>
  );
}
