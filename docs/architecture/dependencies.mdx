---
title: 依赖关系架构
description: Trina Common 框架依赖关系图和版本管理
---

# 依赖关系架构

## 概述

Trina Common 框架采用模块化设计，各模块之间存在清晰的依赖关系。本文档详细描述了框架的依赖架构、版本管理策略和模块间的交互关系。

## 整体依赖架构

### 依赖层次结构

```mermaid
graph TD
    A[trina-parent] --> B[trina-common-core]
    A --> C[trina-common-excel]
    A --> D[trina-common-sensitive]
    
    B --> E[trina-web-starter]
    B --> F[trina-redis-starter]
    B --> G[trina-mybatis-starter]
    B --> H[trina-elasticsearch-starter]
    B --> I[trina-neo4j-starter]
    B --> J[trina-minio-starter]
    B --> L[trina-xxl-job-starter]
    
    E --> M[trina-gateway-starter]
    E --> N[trina-openapi-starter]
    E --> O[trina-xss-starter]

    G --> Q[trina-datasource-starter]
    
    B --> S[trina-remote-starter]
    B --> U[trina-security-starter]
    B --> V[trina-i18n-starter]
    B --> W[trina-microservice-starter]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style E fill:#e8f5e8
    style F fill:#fff3e0
```

### 核心依赖说明

#### 1. 父模块 (trina-parent)
- **作用**：统一依赖版本管理
- **依赖**：Spring Boot 3.5.0、Spring Cloud 2025.0.0
- **管理范围**：所有子模块的版本控制

#### 2. 核心模块 (trina-common-core)
- **作用**：提供基础工具类和公共组件
- **被依赖**：所有业务模块都依赖此模块
- **核心功能**：异常处理、工具类、基础配置

## 技术栈依赖图

### Spring 生态依赖

```mermaid
graph LR
    A[Spring Boot 3.5.0] --> B[Spring Framework 6.2.1]
    A --> C[Spring Security 6.2.x]
    A --> D[Spring Data 3.2.x]
    
    B --> E[Spring Web]
    B --> F[Spring AOP]
    B --> G[Spring Context]
    
    D --> H[Spring Data JPA]
    D --> I[Spring Data Redis]
    D --> J[Spring Data MongoDB]
    D --> K[Spring Data Elasticsearch]
    
    L[Spring Cloud 2025.0.0] --> M[Spring Cloud Gateway]
    L --> N[Spring Cloud OpenFeign]
    L --> O[Spring Cloud Alibaba 2022.0.0.0]
    
    style A fill:#6db33f
    style L fill:#6db33f
```

### 第三方依赖

```mermaid
graph TD
    A[数据库] --> B[MySQL 8.3.0]
    
    E[缓存] --> F[Redis 6.0+]
    E --> G[Caffeine]
    
    H[搜索] --> I[Elasticsearch 9.0.0]
    
    J[存储] --> K[MinIO 8.5.16]
    J --> L[AWS S3 2.31.60]
    

    O[ORM] --> P[MyBatis 3.5.19]
    O --> Q[MyBatis Plus 3.5.10]
    O --> R[MyBatis Plus Join 1.5.2]
    
    S[工具库] --> T[Hutool 5.8.36]
    S --> U[Lombok 1.18.38]
    S --> V[Fastjson2 2.0.57]
    S --> W[Guava 33.1.0-jre]
    
    X[任务调度] --> Y[XXL-Job 3.0.0]
    
    Z[API文档] --> AA[SpringDoc 2.8.8]
    
    style B fill:#b3e5fc
    style F fill:#ffccbc
    style I fill:#c8e6c9
    style K fill:#e1bee7
    style N fill:#ffe0b2
    style P fill:#d1c4e9
```

## 依赖版本管理

Trina Common 框架采用集中式版本管理策略，所有依赖版本都在根POM中定义，确保整个项目使用一致的依赖版本。

### 版本属性定义

```xml
<properties>
    <java.version>17</java.version>
    <spring-boot.version>3.5.0</spring-boot.version>
    <spring-cloud.version>4.3.0</spring-cloud.version>
    <spring-cloud-dependence.version>2025.0.0</spring-cloud-dependence.version>
    <spring-cloud-alibaba.version>2022.0.0.0</spring-cloud-alibaba.version>
    <mybatis.version>3.5.19</mybatis.version>
    <mybatis-plus.version>3.5.10</mybatis-plus.version>
    <mybatis-plus-join.version>1.5.2</mybatis-plus-join.version>
    <mysql.version>8.3.0</mysql.version>
    <elasticsearch.version>9.0.0</elasticsearch.version>
    <hutool.version>5.8.36</hutool.version>
    <fastjson.version>2.0.57</fastjson.version>
    <guava.version>33.1.0-jre</guava.version>
    <xxl.job.version>3.0.0</xxl.job.version>
    <springdoc.version>2.8.8</springdoc.version>
</properties>
```

### 依赖管理

在根POM的`<dependencyManagement>`部分，使用以下方式管理依赖：

```xml
<dependencyManagement>
    <dependencies>
        <!-- Spring Boot BOM -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-dependencies</artifactId>
            <version>${spring-boot.version}</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
        
        <!-- Spring Cloud BOM -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-dependencies</artifactId>
            <version>${spring-cloud-dependence.version}</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
        
        <!-- Spring Cloud Alibaba BOM -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-alibaba-dependencies</artifactId>
            <version>${spring-cloud-alibaba.version}</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
        
        <!-- 其他依赖 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>${mysql.version}</version>
        </dependency>
        
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>
    </dependencies>
</dependencyManagement>
```

## 模块间依赖关系

### Web层依赖

```mermaid
graph TD
    A[trina-web-starter] --> B[Spring Boot Web]
    A --> C[trina-common-core]
    A --> D[trina-security-starter]
    
    E[trina-gateway-starter] --> F[Spring Cloud Gateway]
    E --> C
    E --> D
    
    G[trina-openapi-starter] --> H[SpringDoc OpenAPI]
    G --> C
    G --> A
```

### 数据访问层依赖

```mermaid
graph TD
    A[trina-mybatis-starter] --> B[MyBatis Plus]
    A --> C[trina-common-core]
    A --> D[trina-datasource-starter]
    
    D --> E[Druid]
    D --> F[Dynamic DataSource]
    D --> C
    
    G[trina-redis-starter] --> H[Spring Data Redis]
    G --> C
    
    I[trina-elasticsearch-starter] --> J[Elasticsearch Java]
    I --> C
    
    K[trina-neo4j-starter] --> L[Neo4j Driver]
    K --> C
```

### 消息队列依赖

```mermaid
graph TD
    A --> C[trina-common-core]
    A --> D[trina-redis-starter]
```

### 存储服务依赖

```mermaid
graph TD
    A[trina-minio-starter] --> B[MinIO Client]
    A --> C[AWS SDK S3]
    A --> D[trina-common-core]
    
    E[trina-sftp-starter] --> F[JSch]
    E --> D
```

## 版本兼容性矩阵

| 框架版本 | Spring Boot | Spring Cloud | JDK | MySQL | Elasticsearch |
|---------|------------|--------------|-----|-------|---------------|
| 4.0.0   | 3.5.0      | 2025.0.0     | 17  | 8.3.0 | 9.0.0         |
| 3.0.0   | 3.0.0      | 2022.0.0     | 17  | 8.0.0 | 8.0.0         |
| 2.0.0   | 2.7.0      | 2021.0.0     | 11  | 8.0.0 | 7.0.0         |
| 1.0.0   | 2.3.0      | 2020.0.0     | 8   | 5.7.0 | 7.0.0         |