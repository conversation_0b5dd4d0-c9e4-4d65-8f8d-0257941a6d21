---
title: Elasticsearch 模块架构
description: Trina Elasticsearch 模块的架构设计与实现原理
---

# Elasticsearch 模块架构

## 1. 整体架构

Trina Elasticsearch 模块基于 Spring Boot 3.4.0 和 Java 17，采用官方 `elasticsearch-rest-client` 9.0.0 实现，提供轻量级、高性能的 Elasticsearch 集成方案。该模块摒弃了 Spring Data Elasticsearch 的复杂性，直接使用官方客户端，提供更好的性能和更灵活的控制。

### 1.1 架构层次

```
+---------------------------+
|      应用层 (Application) |
| Controller/Service        |
+---------------------------+
              ↓
+---------------------------+
|      服务层 (Service)     |
| ElasticsearchService      |
+---------------------------+
              ↓
+---------------------------+
|      工具层 (Utils)       |
| ElasticsearchUtils        |
+---------------------------+
              ↓
+---------------------------+
|      客户端层 (Client)    |
| RestClient                |
+---------------------------+
              ↓
+---------------------------+
|    Elasticsearch 集群     |
+---------------------------+
```

### 1.2 设计原则

- **轻量级**: 移除 Spring Data Elasticsearch 依赖，减少复杂性
- **高性能**: 直接使用官方 REST 客户端，减少抽象层开销
- **易扩展**: 提供工具类和基础组件，便于定制和扩展
- **类型安全**: 支持泛型和强类型操作
- **连接池**: 内置连接池管理，支持高并发场景

## 2. 核心组件

### 2.1 配置组件

#### ElasticsearchProperties

配置属性类，定义了 Elasticsearch 连接和行为的各种参数：

```java
@ConfigurationProperties(prefix = "elasticsearch")
@Data
public class ElasticsearchProperties {
    
    /**
     * Elasticsearch 服务器地址列表
     */
    private List<String> hosts = Arrays.asList("localhost:9200");
    
    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 5000;
    
    /**
     * Socket 超时时间（毫秒）
     */
    private int socketTimeout = 30000;
    
    /**
     * 连接请求超时时间（毫秒）
     */
    private int connectionRequestTimeout = 1000;
    
    /**
     * 最大连接数
     */
    private int maxConnections = 100;
    
    /**
     * 每个路由的最大连接数
     */
    private int maxConnectionsPerRoute = 10;
    
    /**
     * 用户名（可选）
     */
    private String username;
    
    /**
     * 密码（可选）
     */
    private String password;
    
    /**
     * API 密钥（可选）
     */
    private String apiKey;
    
    /**
     * 是否启用 SSL
     */
    private boolean ssl = false;
}
```

#### ElasticsearchAutoConfiguration

自动配置类，负责创建和配置 RestClient Bean：

```java
@Configuration
@EnableConfigurationProperties(ElasticsearchProperties.class)
@ConditionalOnClass(RestClient.class)
public class ElasticsearchAutoConfiguration {
    
    @Bean
    @ConditionalOnMissingBean
    public RestClient restClient(ElasticsearchProperties properties) {
        // 解析主机地址
        HttpHost[] hosts = properties.getHosts().stream()
            .map(this::parseHttpHost)
            .toArray(HttpHost[]::new);
        
        RestClientBuilder builder = RestClient.builder(hosts);
        
        // 配置超时时间
        builder.setRequestConfigCallback(requestConfigBuilder -> {
            requestConfigBuilder.setConnectTimeout(properties.getConnectTimeout());
            requestConfigBuilder.setSocketTimeout(properties.getSocketTimeout());
            requestConfigBuilder.setConnectionRequestTimeout(properties.getConnectionRequestTimeout());
            return requestConfigBuilder;
        });
        
        // 配置连接池和认证
        builder.setHttpClientConfigCallback(httpClientBuilder -> {
            // 连接池配置
            httpClientBuilder.setMaxConnTotal(properties.getMaxConnections());
            httpClientBuilder.setMaxConnPerRoute(properties.getMaxConnectionsPerRoute());
            
            // 认证配置
            configureAuthentication(httpClientBuilder, properties);
            
            return httpClientBuilder;
        });
        
        return builder.build();
    }
}
```

### 2.2 基础实体

`BaseDocument` 提供了文档的通用字段和方法：

```java
@Data
public abstract class BaseDocument {
    
    /**
     * 文档ID
     */
    private String id;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 版本号
     */
    private Long version;
    
    /**
     * 是否删除（逻辑删除标记）
     */
    private Boolean deleted = false;
    
    /**
     * 更新时调用，自动设置更新时间
     */
    public void onUpdate() {
        this.updateTime = LocalDateTime.now();
        if (this.createTime == null) {
            this.createTime = this.updateTime;
        }
    }
}
```

这种设计提供了文档的基础结构，支持时间戳管理和逻辑删除功能。

### 2.3 服务层

#### ElasticsearchService

`ElasticsearchService` 是核心服务接口，提供了完整的 Elasticsearch 操作 API：

```java
public interface ElasticsearchService {
    
    // ========== 索引管理 ==========
    
    /**
     * 创建索引
     */
    boolean createIndex(String indexName, String mapping);
    
    /**
     * 删除索引
     */
    boolean deleteIndex(String indexName);
    
    /**
     * 检查索引是否存在
     */
    boolean indexExists(String indexName);
    
    // ========== 文档操作 ==========
    
    /**
     * 保存文档
     */
    <T extends BaseDocument> boolean save(String indexName, T document);
    
    /**
     * 批量保存文档
     */
    <T extends BaseDocument> boolean saveBatch(String indexName, List<T> documents);
    
    /**
     * 根据ID获取文档
     */
    <T> Optional<T> getById(String indexName, String id, Class<T> clazz);
    
    /**
     * 根据ID删除文档
     */
    boolean deleteById(String indexName, String id);
    
    /**
     * 批量删除文档
     */
    boolean deleteBatch(String indexName, List<String> ids);
    
    /**
     * 更新文档
     */
    <T extends BaseDocument> boolean update(String indexName, T document);
    
    // ========== 搜索操作 ==========
    
    /**
     * 搜索文档
     */
    <T> SearchResult<T> search(String indexName, String query, Class<T> clazz);
    
    /**
     * 分页搜索
     */
    <T> SearchResult<T> search(String indexName, String query, int from, int size, Class<T> clazz);
    
    /**
     * 复杂查询
     */
    <T> SearchResult<T> search(String indexName, Map<String, Object> query, Class<T> clazz);
    
    /**
     * 聚合查询
     */
    Map<String, Object> aggregation(String indexName, Map<String, Object> query);
}
```

#### ElasticsearchServiceImpl

服务实现类，基于 `elasticsearch-rest-client` 实现：

```java
@Service
@Slf4j
public class ElasticsearchServiceImpl implements ElasticsearchService {
    
    private final RestClient restClient;
    private final ObjectMapper objectMapper;
    
    public ElasticsearchServiceImpl(RestClient restClient) {
        this.restClient = restClient;
        this.objectMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }
    
    @Override
    public <T extends BaseDocument> boolean save(String indexName, T document) {
        try {
            document.onUpdate(); // 自动设置时间戳
            
            String endpoint = String.format("/%s/_doc/%s", indexName, document.getId());
            String jsonString = objectMapper.writeValueAsString(document);
            
            Request request = new Request("PUT", endpoint);
            request.setJsonEntity(jsonString);
            
            Response response = restClient.performRequest(request);
            return response.getStatusLine().getStatusCode() < 300;
            
        } catch (Exception e) {
            log.error("保存文档失败: indexName={}, document={}", indexName, document, e);
            return false;
        }
    }
    
    @Override
    public <T> Optional<T> getById(String indexName, String id, Class<T> clazz) {
        try {
            String endpoint = String.format("/%s/_doc/%s", indexName, id);
            Request request = new Request("GET", endpoint);
            
            Response response = restClient.performRequest(request);
            if (response.getStatusLine().getStatusCode() == 200) {
                String responseBody = EntityUtils.toString(response.getEntity());
                JsonNode jsonNode = objectMapper.readTree(responseBody);
                
                if (jsonNode.get("found").asBoolean()) {
                    JsonNode source = jsonNode.get("_source");
                    T document = objectMapper.treeToValue(source, clazz);
                    return Optional.of(document);
                }
            }
            return Optional.empty();
            
        } catch (Exception e) {
            log.error("获取文档失败: indexName={}, id={}", indexName, id, e);
            return Optional.empty();
        }
    }
    
    // ... 其他方法实现
}
```

### 2.4 工具类

#### ElasticsearchUtils

`ElasticsearchUtils` 提供了常用的 Elasticsearch 操作工具方法：

```java
@Component
@Slf4j
public class ElasticsearchUtils {
    
    private final RestClient restClient;
    private final ObjectMapper objectMapper;
    
    public ElasticsearchUtils(RestClient restClient) {
        this.restClient = restClient;
        this.objectMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }
    
    /**
     * 构建查询 DSL
     */
    public static Map<String, Object> buildQuery() {
        return new HashMap<>();
    }
    
    /**
     * 添加匹配查询
     */
    public static Map<String, Object> match(String field, Object value) {
        Map<String, Object> match = new HashMap<>();
        match.put(field, value);
        return Map.of("match", match);
    }
    
    /**
     * 添加词条查询
     */
    public static Map<String, Object> term(String field, Object value) {
        Map<String, Object> term = new HashMap<>();
        term.put(field, value);
        return Map.of("term", term);
    }
    
    /**
     * 添加范围查询
     */
    public static Map<String, Object> range(String field, Object gte, Object lte) {
        Map<String, Object> range = new HashMap<>();
        Map<String, Object> conditions = new HashMap<>();
        if (gte != null) conditions.put("gte", gte);
        if (lte != null) conditions.put("lte", lte);
        range.put(field, conditions);
        return Map.of("range", range);
    }
    
    /**
     * 构建布尔查询
     */
    public static Map<String, Object> bool() {
        return Map.of("bool", new HashMap<String, Object>());
    }
    
    /**
     * 添加 must 条件
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> must(Map<String, Object> boolQuery, Map<String, Object>... conditions) {
        Map<String, Object> bool = (Map<String, Object>) boolQuery.get("bool");
        bool.put("must", Arrays.asList(conditions));
        return boolQuery;
    }
    
    /**
     * 添加 should 条件
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> should(Map<String, Object> boolQuery, Map<String, Object>... conditions) {
        Map<String, Object> bool = (Map<String, Object>) boolQuery.get("bool");
        bool.put("should", Arrays.asList(conditions));
        return boolQuery;
    }
    
    /**
     * 添加 filter 条件
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> filter(Map<String, Object> boolQuery, Map<String, Object>... conditions) {
        Map<String, Object> bool = (Map<String, Object>) boolQuery.get("bool");
        bool.put("filter", Arrays.asList(conditions));
        return boolQuery;
    }
    
    /**
     * 构建聚合查询
     */
    public static Map<String, Object> aggregation(String name, String type, String field) {
        Map<String, Object> agg = new HashMap<>();
        Map<String, Object> aggType = new HashMap<>();
        aggType.put("field", field);
        agg.put(type, aggType);
        return Map.of(name, agg);
    }
    
    /**
     * 执行原生查询
     */
    public <T> SearchResult<T> executeQuery(String indexName, Map<String, Object> query, Class<T> clazz) {
        try {
            String endpoint = String.format("/%s/_search", indexName);
            String queryJson = objectMapper.writeValueAsString(Map.of("query", query));
            
            Request request = new Request("POST", endpoint);
            request.setJsonEntity(queryJson);
            
            Response response = restClient.performRequest(request);
            String responseBody = EntityUtils.toString(response.getEntity());
            
            return parseSearchResponse(responseBody, clazz);
            
        } catch (Exception e) {
            log.error("执行查询失败: indexName={}, query={}", indexName, query, e);
            return SearchResult.empty();
        }
    }
    
    /**
     * 解析搜索响应
     */
    @SuppressWarnings("unchecked")
    private <T> SearchResult<T> parseSearchResponse(String responseBody, Class<T> clazz) throws Exception {
        JsonNode jsonNode = objectMapper.readTree(responseBody);
        JsonNode hits = jsonNode.get("hits");
        
        long total = hits.get("total").get("value").asLong();
        JsonNode hitsArray = hits.get("hits");
        
        List<T> documents = new ArrayList<>();
        for (JsonNode hit : hitsArray) {
            JsonNode source = hit.get("_source");
            T document = objectMapper.treeToValue(source, clazz);
            documents.add(document);
        }
        
        return new SearchResult<>(documents, total);
    }
}
```

### 2.5 搜索结果封装

#### SearchResult

`SearchResult` 类封装了搜索结果和分页信息：

```java
@Data
public class SearchResult<T> {
    
    /**
     * 搜索结果列表
     */
    private List<T> documents;
    
    /**
     * 总命中数
     */
    private long total;
    
    /**
     * 当前页码
     */
    private int pageNo;
    
    /**
     * 页面大小
     */
    private int pageSize;
    
    /**
     * 总页数
     */
    private int totalPages;
    
    /**
     * 是否有下一页
     */
    private boolean hasNext;
    
    /**
     * 是否有上一页
     */
    private boolean hasPrevious;
    
    public SearchResult(List<T> documents, long total) {
        this.documents = documents;
        this.total = total;
    }
    
    public SearchResult(List<T> documents, long total, int pageNo, int pageSize) {
        this.documents = documents;
        this.total = total;
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.totalPages = (int) Math.ceil((double) total / pageSize);
        this.hasNext = pageNo < totalPages - 1;
        this.hasPrevious = pageNo > 0;
    }
    
    /**
     * 创建空结果
     */
    public static <T> SearchResult<T> empty() {
        return new SearchResult<>(Collections.emptyList(), 0);
    }
}
```

### 2.6 异常处理

#### ElasticsearchException

自定义异常类用于处理 Elasticsearch 相关的异常：

```java
public class ElasticsearchException extends RuntimeException {
    
    private final String operation;
    private final String indexName;
    
    public ElasticsearchException(String operation, String indexName, String message) {
        super(String.format("Elasticsearch %s operation failed for index '%s': %s", 
                operation, indexName, message));
        this.operation = operation;
        this.indexName = indexName;
    }
    
    public ElasticsearchException(String operation, String indexName, String message, Throwable cause) {
        super(String.format("Elasticsearch %s operation failed for index '%s': %s", 
                operation, indexName, message), cause);
        this.operation = operation;
        this.indexName = indexName;
    }
    
    public String getOperation() {
        return operation;
    }
    
    public String getIndexName() {
        return indexName;
    }
}
```

## 3. 架构优势

### 3.1 性能优势

- **轻量级客户端**: 基于 `elasticsearch-rest-client`，相比 Spring Data Elasticsearch 更轻量
- **直接 HTTP 通信**: 避免了额外的抽象层，减少性能开销
- **连接池管理**: 内置连接池，支持连接复用和并发控制
- **异步支持**: 支持异步操作，提高并发处理能力

### 3.2 灵活性优势

- **原生 DSL 支持**: 直接使用 Elasticsearch DSL，支持所有查询特性
- **版本兼容性**: 不依赖特定的 Elasticsearch 版本，兼容性更好
- **自定义扩展**: 易于扩展和自定义，满足特殊业务需求
- **多索引操作**: 支持跨索引查询和操作

### 3.3 维护优势

- **简化依赖**: 减少了对 Spring Data Elasticsearch 的依赖
- **清晰架构**: 分层明确，职责单一，易于理解和维护
- **统一接口**: 提供统一的服务接口，屏蔽底层实现细节
- **错误处理**: 完善的异常处理机制，便于问题定位

### 3.4 开发优势

- **类型安全**: 基于泛型设计，提供编译时类型检查
- **工具支持**: 提供丰富的工具类，简化常见操作
- **文档完善**: 详细的文档和示例，降低学习成本
- **测试友好**: 易于进行单元测试和集成测试

## 3. 配置管理

### 3.1 ElasticsearchProperties

配置属性类定义了 Elasticsearch 连接和行为的各种参数：

```java
@ConfigurationProperties(prefix = "elasticsearch")
@Data
public class ElasticsearchProperties {
    
    /**
     * 是否启用 Elasticsearch
     */
    private boolean enabled = true;
    
    /**
     * Elasticsearch 节点列表
     */
    private List<String> uris = Arrays.asList("http://localhost:9200");
    
    /**
     * 用户名（可选）
     */
    private String username;
    
    /**
     * 密码（可选）
     */
    private String password;
    
    /**
     * 连接超时时间（毫秒）
     */
    private Duration connectTimeout = Duration.ofSeconds(10);
    
    /**
     * Socket 超时时间（毫秒）
     */
    private Duration socketTimeout = Duration.ofSeconds(30);
    
    /**
     * 连接池配置
     */
    private Pool pool = new Pool();
    
    /**
     * 向量搜索配置
     */
    private Vector vector = new Vector();
    
    @Data
    public static class Pool {
        /**
         * 最大连接数
         */
        private int maxTotal = 100;
        
        /**
         * 每个路由的最大连接数
         */
        private int maxPerRoute = 10;
        
        /**
         * 连接空闲时间（毫秒）
         */
        private Duration keepAlive = Duration.ofMinutes(5);
    }
    
    @Data
    public static class Vector {
        /**
         * 默认向量维度
         */
        private int dimension = 768;
        
        /**
         * 向量相似度算法
         */
        private String similarity = "cosine";
    }
}
```

### 3.2 ElasticsearchConfiguration

自动配置类负责创建和配置 Elasticsearch 客户端：

```java
@Configuration
@EnableConfigurationProperties(ElasticsearchProperties.class)
@ConditionalOnProperty(prefix = "elasticsearch", name = "enabled", havingValue = "true", matchIfMissing = true)
public class ElasticsearchConfiguration {
    
    @Bean
    @ConditionalOnMissingBean
    public ElasticsearchClient elasticsearchClient(ElasticsearchProperties properties) {
        
        // 解析 URI
        List<HttpHost> hosts = properties.getUris().stream()
                .map(this::parseHttpHost)
                .collect(Collectors.toList());
        
        // 创建 RestClient
        RestClientBuilder builder = RestClient.builder(hosts.toArray(new HttpHost[0]));
        
        // 配置认证
        if (StringUtils.hasText(properties.getUsername()) && StringUtils.hasText(properties.getPassword())) {
            CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(AuthScope.ANY,
                    new UsernamePasswordCredentials(properties.getUsername(), properties.getPassword()));
            
            builder.setHttpClientConfigCallback(httpClientBuilder ->
                    httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider));
        }
        
        // 配置超时时间
        builder.setRequestConfigCallback(requestConfigBuilder ->
                requestConfigBuilder
                        .setConnectTimeout((int) properties.getConnectTimeout().toMillis())
                        .setSocketTimeout((int) properties.getSocketTimeout().toMillis()));
        
        // 配置连接池
        builder.setHttpClientConfigCallback(httpClientBuilder ->
                httpClientBuilder
                        .setMaxConnTotal(properties.getPool().getMaxTotal())
                        .setMaxConnPerRoute(properties.getPool().getMaxPerRoute())
                        .setKeepAliveStrategy((response, context) -> properties.getPool().getKeepAlive().toMillis()));
        
        RestClient restClient = builder.build();
        
        // 创建 ElasticsearchTransport
        ElasticsearchTransport transport = new RestClientTransport(
                restClient, new JacksonJsonpMapper());
        
        return new ElasticsearchClient(transport);
    }
    
    private HttpHost parseHttpHost(String uri) {
        try {
            URI parsedUri = new URI(uri);
            return new HttpHost(parsedUri.getHost(), parsedUri.getPort(), parsedUri.getScheme());
        } catch (URISyntaxException e) {
            throw new IllegalArgumentException("Invalid Elasticsearch URI: " + uri, e);
        }
    }
}
```

### 3.3 配置示例

在 `application.yml` 中的配置示例：

```yaml
elasticsearch:
  enabled: true
  uris:
    - http://localhost:9200
    - http://localhost:9201
  username: elastic
  password: changeme
  connect-timeout: 10s
  socket-timeout: 30s
  pool:
    max-total: 100
    max-per-route: 10
    keep-alive: 5m
  vector:
    dimension: 768
    similarity: cosine
```

## 4. 使用示例

### 4.1 基础使用

```java
@Service
public class ProductService extends BaseEsServiceImpl<ProductDocument> {
    
    @Override
    protected String getVectorFieldName() {
        return "descriptionVector";
    }
    
    /**
     * 根据名称搜索产品
     */
    public SearchHits<ProductDocument> searchByName(String name) {
        BaseEsQuery<ProductDocument> query = new BaseEsQuery<>(ProductDocument.class)
                .match("name", name)
                .orderByDesc("createTime")
                .setPageSize(10);
        
        return search(query);
    }
    
    /**
     * 根据价格范围搜索产品
     */
    public SearchHits<ProductDocument> searchByPriceRange(BigDecimal minPrice, BigDecimal maxPrice) {
        BaseEsQuery<ProductDocument> query = new BaseEsQuery<>(ProductDocument.class)
                .between("price", minPrice, maxPrice)
                .eq("status", "ACTIVE")
                .orderByAsc("price");
        
        return search(query);
    }
    
    /**
     * 向量搜索相似产品
     */
    public SearchHits<ProductDocument> findSimilarProducts(float[] queryVector, int size) {
        return vectorSearch(queryVector, size);
    }
}
```

### 4.2 高级查询

```java
@Service
public class ArticleService extends BaseEsServiceImpl<ArticleDocument> {
    
    /**
     * 复杂搜索示例
     */
    public SearchHits<ArticleDocument> complexSearch(String keyword, List<String> categories, 
                                                   LocalDateTime startTime, LocalDateTime endTime) {
        BaseEsQuery<ArticleDocument> query = new BaseEsQuery<>(ArticleDocument.class);
        
        // 关键词搜索
        if (StringUtils.hasText(keyword)) {
            query.match("title", keyword)
                 .should("content", keyword);
        }
        
        // 分类过滤
        if (CollectionUtils.isNotEmpty(categories)) {
            query.in("category", categories);
        }
        
        // 时间范围
        if (startTime != null || endTime != null) {
            query.between("publishTime", startTime, endTime);
        }
        
        // 排序和分页
        query.orderByDesc("publishTime")
             .orderByDesc("viewCount")
             .setPageNo(0)
             .setPageSize(20);
        
        return search(query);
    }
    
    /**
     * 聚合查询示例
     */
    public Map<String, Long> getCategoryStatistics() {
        BaseEsQuery<ArticleDocument> query = new BaseEsQuery<>(ArticleDocument.class)
                .aggregation("category_stats", "terms", "category");
        
        SearchHits<ArticleDocument> searchHits = search(query);
        
        // 处理聚合结果
        return extractAggregationResults(searchHits, "category_stats");
    }
}
```

## 5. 向量搜索实现

### 5.1 向量字段支持

向量搜索需要在文档中存储向量字段，模块提供了 `VectorDocument` 基类：

```java
public class VectorDocument extends BaseEsEntity {
    
    @Field(type = FieldType.Dense_Vector, dims = "#{@elasticsearchProperties.vectorDimension}")
    private float[] vector;
    
    // Getter/Setter 方法...
}
```

这里使用了 Spring 的表达式语言从配置中动态获取向量维度，使得应用可以根据不同的嵌入模型调整向量大小。

### 5.2 向量操作工具

`VectorUtil` 类提供了一系列向量操作方法：

```java
public class VectorUtil {
    // 生成随机向量
    public static float[] randomVector(int dimensions) {
        float[] vector = new float[dimensions];
        for (int i = 0; i < dimensions; i++) {
            vector[i] = RANDOM.nextFloat() * 2 - 1;
        }
        return normalizeVector(vector);
    }
    
    // 归一化向量
    public static float[] normalizeVector(float[] vector) {
        float sum = 0;
        for (float v : vector) {
            sum += v * v;
        }
        float norm = (float) Math.sqrt(sum);
        
        if (norm > 0) {
            float[] normalized = new float[vector.length];
            for (int i = 0; i < vector.length; i++) {
                normalized[i] = vector[i] / norm;
            }
            return normalized;
        }
        
        return vector;
    }
    
    // 计算余弦相似度
    public static float cosineSimilarity(float[] vector1, float[] vector2) {
        float dotProduct = 0;
        float norm1 = 0;
        float norm2 = 0;
        
        for (int i = 0; i < vector1.length; i++) {
            dotProduct += vector1[i] * vector2[i];
            norm1 += vector1[i] * vector1[i];
            norm2 += vector2[i] * vector2[i];
        }
        
        float normProduct = (float) (Math.sqrt(norm1) * Math.sqrt(norm2));
        return normProduct > 0 ? dotProduct / normProduct : 0;
    }
    
    // 其他方法...
}
```

这些方法使得应用可以在 Java 代码中直接处理向量数据，而不需要依赖外部库。

### 5.3 向量查询构建

向量查询通过 `BaseEsQuery` 的 `vectorSearch` 方法支持：

```java
public BaseEsQuery<T> vectorSearch(String field, float[] vector, int k, VectorSimilarityMethod method) {
    this.vectorField = field;
    this.vectorQuery = vector;
    this.k = k;
    this.vectorSimilarityMethod = method;
    return this;
}
```

内部实现会根据不同的相似度方法构建不同的查询：

```java
private Query buildVectorQuery() {
    if (vectorField == null || vectorQuery == null || k <= 0) {
        return null;
    }
    
    // 这里简化了实现，实际代码会根据 Elasticsearch 版本和 API 构建适当的查询
    if (vectorSimilarityMethod == VectorSimilarityMethod.COSINE) {
        // 构建余弦相似度查询
    } else if (vectorSimilarityMethod == VectorSimilarityMethod.EUCLIDEAN) {
        // 构建欧几里得距离查询
    } else if (vectorSimilarityMethod == VectorSimilarityMethod.DOT_PRODUCT) {
        // 构建点积查询
    }
    
    return query;
}
```

### 5.4 混合搜索

混合搜索结合了向量搜索和传统的布尔查询，允许用户同时按语义相似度和过滤条件搜索：

```java
public BaseEsQuery<T> hybridSearch(String vectorField, float[] vector, int k, 
                                  VectorSimilarityMethod method) {
    // 设置向量搜索参数
    this.vectorSearch(vectorField, vector, k, method);
    
    // 添加过滤条件
    this.eq("category", "books");
    this.gte("rating", 4.0);
    
    return this;
}
```

这种方式可以实现"找到与此文本语义相似且属于特定类别的所有文档"这类复杂查询。

## 6. 自动配置与启动

### 6.1 自动配置机制

该模块利用 Spring Boot 的自动配置机制实现零配置集成：

1. 在 `META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports` 文件中注册配置类：

```
com.trinasolar.elasticsearch.config.ElasticsearchConfiguration
```

2. 通过条件注解控制配置的启用：

```java
@ConditionalOnProperty(prefix = "trina.elasticsearch", name = "enabled", havingValue = "true", matchIfMissing = true)
```

3. 使用 `@ComponentScan` 自动扫描并注册组件：

```java
@ComponentScan(basePackages = "com.trinasolar.elasticsearch")
```

### 6.2 启动流程

模块的启动流程如下：

1. 应用启动时，Spring Boot 加载自动配置类 `ElasticsearchConfiguration`
2. 创建和配置 `ElasticsearchClient` 实例
3. 扫描并注册所有 `@Repository` 和 `@Service` 组件
4. 应用可以直接注入并使用这些组件

```java
@SpringBootApplication
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}

@RestController
@RequiredArgsConstructor
public class ProductController {
    private final ProductEsService productService;
    
    @GetMapping("/products/search")
    public Result<List<ProductDocument>> search(@RequestParam String keyword) {
        BaseEsQuery<ProductDocument> query = new BaseEsQuery<>();
        query.like("name", keyword);
        return Result.success(productService.search(query).getSearchHits()
                .stream().map(SearchHit::getContent).collect(Collectors.toList()));
    }
}
```

## 5. 最佳实践

### 5.1 索引设计

- **合理分片**: 根据数据量和查询模式设计合适的分片数
- **映射优化**: 为字段选择合适的数据类型和分析器
- **别名使用**: 使用索引别名实现零停机时间的索引切换
- **生命周期管理**: 配置 ILM 策略管理索引生命周期

### 5.2 查询优化

- **过滤优先**: 优先使用 filter 查询，利用缓存机制
- **分页限制**: 避免深度分页，使用 scroll 或 search_after
- **字段限制**: 只返回需要的字段，减少网络传输
- **聚合优化**: 合理使用聚合查询，避免内存溢出

### 5.3 性能监控

- **慢查询监控**: 监控慢查询，及时优化性能瓶颈
- **资源监控**: 监控 CPU、内存、磁盘使用情况
- **连接池监控**: 监控连接池状态，避免连接泄漏
- **错误率监控**: 监控操作成功率，及时发现问题

## 8. 扩展点

该模块设计了多个扩展点，便于用户自定义功能：

1. **自定义查询**：扩展 `BaseEsQuery` 添加特定的查询方法

2. **自定义结果处理**：实现自己的 `SearchResultProcessor` 处理搜索结果

3. **集成外部嵌入模型**：自定义 `VectorProvider` 与外部模型集成

4. **自定义映射**：通过继承 `BaseEsEntity` 自定义索引映射

## 6. 总结

新的 Elasticsearch 架构基于 `elasticsearch-rest-client` 实现，具有以下特点：

1. **轻量级**: 移除了 Spring Data Elasticsearch 依赖，减少了项目复杂度
2. **高性能**: 直接使用 HTTP 客户端，性能更优
3. **灵活性**: 支持原生 DSL，功能更强大
4. **易维护**: 架构清晰，代码简洁，易于维护和扩展
5. **兼容性**: 更好的版本兼容性，升级更容易

该架构适用于各种规模的 Elasticsearch 应用场景，从简单的文档存储到复杂的搜索分析都能很好地支持。通过合理的配置和使用，可以充分发挥 Elasticsearch 的强大功能。