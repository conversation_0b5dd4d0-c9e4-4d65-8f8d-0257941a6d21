---
title: 'Trina框架依赖版本管理规范'
description: '本文档描述Trina框架的依赖版本管理策略和最佳实践'
---

# Trina框架依赖版本管理规范

## 1. 依赖清单总览

Trina框架使用了丰富的依赖库来支持各种功能。以下是项目中使用的主要依赖及其版本信息。

### 1.1 核心框架依赖

| 依赖名称 | 版本 | 用途说明 |
|---------|------|---------|
| Spring Boot | 3.5.0 | 应用程序开发框架，提供自动配置、依赖注入等功能 |
| Spring Framework | 6.2.1 | 核心Spring框架，提供IoC、AOP等基础功能 |
| Spring Cloud | 2025.0.0 | 微服务开发框架，提供服务发现、配置中心等功能 |
| Spring Cloud Alibaba | 2023.0.3.3 | 阿里巴巴开源的微服务解决方案 |
| Jakarta Servlet API | 6.0.0 | Java Servlet规范实现 |
| Tomcat Embed | 11.0.6 | 内嵌Tomcat服务器 |

### 1.2 数据访问依赖

| 依赖名称 | 版本 | 用途说明 |
|---------|------|---------|
| MyBatis | 3.5.19 | 持久层框架 |
| MyBatis Plus | 3.5.10 | MyBatis增强工具，简化CRUD操作 |
| MyBatis Plus Join | 1.5.2 | MyBatis Plus连表查询增强 |
| MySQL Connector/J | 8.3.0 | MySQL数据库驱动 |
| Druid | 1.2.24 | 数据库连接池 |
| Dynamic Datasource | 4.3.1 | 动态数据源支持 |
| JSqlParser | 4.3 | SQL解析工具 |
| MongoDB Driver | 4.9.1 | MongoDB数据库驱动 |
| Neo4j Driver | 5.10.0 | Neo4j图数据库驱动 |
| Elasticsearch Java | 9.0.0 | Elasticsearch客户端 |

### 1.3 存储与消息队列

| 依赖名称 | 版本 | 用途说明 |
|---------|------|---------|
| Minio | 8.5.16 | 对象存储服务客户端 |
| AWS SDK S3 | 2.31.60 | Amazon S3兼容存储客户端 |
| JSch | 0.1.55 | SSH连接工具，用于SFTP功能 |

### 1.4 工具库

| 依赖名称 | 版本 | 用途说明 |
|---------|------|---------|
| Lombok | 1.18.38 | Java注解库，简化代码 |
| Hutool | 5.8.36 | Java工具类库 |
| Fastjson2 | 2.0.57 | JSON处理库 |
| Guava | 33.1.0-jre | Google核心库 |
| Commons Lang3 | 3.11 | Apache通用工具库 |
| Commons IO | 2.7 | Apache IO工具库 |
| EasyExcel | 4.0.2 | Excel处理工具 |
| POI | 5.3.0 | Office文档处理 |
| JSoup | 1.15.1 | HTML解析工具 |
| HttpClient5 | 5.4.2 | HTTP客户端 |
| Jasypt | 3.0.5 | 配置文件加密工具 |
| JAXB | 4.0.5 | XML绑定工具 |
| Pinyin4j | 2.5.0 | 拼音转换工具 |

### 1.5 API文档与安全

| 依赖名称 | 版本 | 用途说明 |
|---------|------|---------|
| SpringDoc OpenAPI | 2.8.8 | API文档生成工具 |
| Jakarta Validation | 3.1.1 | 数据校验框架 |
| OAuth2 | 2.2.1.RELEASE | 认证授权框架 |

### 1.6 任务调度

| 依赖名称 | 版本 | 用途说明 |
|---------|------|---------|
| XXL-Job | 3.0.0 | 分布式任务调度平台 |

## 2. 项目模块依赖关系

Trina框架由多个功能模块组成，每个模块专注于特定功能：

| 模块名称 | 功能说明 |
|---------|---------|
| trina-common-core | 核心工具和基础组件 |
| trina-common-excel | Excel处理模块 |
| trina-common-sensitive | 敏感数据处理模块 |
| trina-web-starter | Web应用基础组件 |
| trina-redis-starter | Redis集成模块 |
| trina-minio-starter | 对象存储模块 |
| trina-datasource-starter | 多数据源模块 |
| trina-gateway-starter | 网关集成模块 |
| trina-mybatis-starter | MyBatis增强模块 |
| trina-openapi-starter | API文档模块 |
| trina-remote-starter | 远程调用模块 |
| trina-sequence-starter | 序列号生成器模块 |
| trina-build-info-starter | 构建信息模块 |
| trina-kubernetes-starter | Kubernetes集成模块 |
| trina-security-starter | 安全认证模块 |
| trina-neo4j-starter | Neo4j集成模块 |
| trina-tagmanagement-starter | 标签管理模块 |
| trina-xxl-job-starter | XXL-Job集成模块 |
| trina-sftp-starter | SFTP集成模块 |
| trina-xss-starter | XSS防护模块 |
| trina-elasticsearch-starter | Elasticsearch集成模块 |
| trina-i18n-starter | 国际化支持模块 |
| trina-microservice-starter | 微服务基础模块 |

## 3. 依赖版本管理架构

Trina框架采用分层的依赖版本管理策略，确保版本一致性和依赖冲突最小化。

### 3.1 依赖管理架构

Trina框架的依赖管理采用三层架构：

```
┌─────────────────────────┐
│    业务微服务应用       │  引用trina-parent作为父模块
└───────────┬─────────────┘
            ↓
┌─────────────────────────┐
│      trina-parent       │  为微服务提供固定版本的starter
└───────────┬─────────────┘
            ↓
┌─────────────────────────┐
│      trina-common       │  根POM，管理所有版本属性
└─────────────────────────┘
```

### 3.2 根POM (trina-common)

`trina-common`是整个框架的根POM，负责：

- 在`<properties>`中定义所有依赖的版本号
- 在`<dependencyManagement>`中声明所有可能用到的第三方依赖
- 在`<dependencies>`中定义对所有子模块都通用的依赖

### 3.3 父模块 (trina-parent)

`trina-parent`是提供给业务微服务使用的父模块，负责：

- 固定所有trina-common子模块的版本号
- 提供微服务常用的依赖版本管理
- 简化微服务的依赖配置

### 3.4 子模块 (各种starter)

各个功能模块（如trina-web-starter、trina-redis-starter等）：

- 不应该定义版本号，而是继承自根POM
- 专注于实现特定功能，而不是版本管理

## 4. 版本管理规则

### 4.1 版本属性定义规则

所有版本号必须在根POM的`<properties>`部分定义，遵循以下命名规范：

```xml
<xxx.version>1.2.3</xxx.version>
```

其中，`xxx`应该是依赖的简短名称，例如：

```xml
<spring-boot.version>3.5.0</spring-boot.version>
<mybatis-plus.version>3.5.10</mybatis-plus.version>
```

### 4.2 依赖管理规则

1. **根POM (trina-common)**:
   - 所有第三方依赖的版本号在`<properties>`中定义
   - 所有可能用到的依赖在`<dependencyManagement>`中声明
   - 只有真正通用的依赖才放在`<dependencies>`中

2. **父模块 (trina-parent)**:
   - 固定所有trina-common子模块的版本号
   - 不重复定义已在根POM中定义的版本属性
   - 可以覆盖根POM中的依赖版本，但应谨慎使用

3. **子模块**:
   - 不应该指定依赖版本号
   - 应该从父POM继承版本信息

### 4.3 BOM文件使用规则

对于主流框架，应优先使用其BOM (Bill of Materials) 文件：

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-dependencies</artifactId>
    <version>${spring-boot.version}</version>
    <type>pom</type>
    <scope>import</scope>
</dependency>
```

## 5. 添加新依赖的流程

当需要添加新的依赖时，应遵循以下流程：

1. 确定依赖是否已在根POM的`<dependencyManagement>`中定义
2. 如果未定义：
   - 在根POM的`<properties>`中添加版本属性
   - 在根POM的`<dependencyManagement>`中添加依赖声明
3. 在需要的模块中添加依赖（不指定版本）

## 6. 版本升级策略

### 6.1 小版本升级

对于补丁版本升级（如1.2.3 → 1.2.4），只需在根POM的`<properties>`中更新版本号。

### 6.2 大版本升级

对于主要版本升级（如1.2.3 → 2.0.0）：

1. 在根POM的`<properties>`中更新版本号
2. 全面测试所有模块的兼容性
3. 更新文档和示例代码

## 7. 最佳实践

### 7.1 依赖分类

在根POM的`<dependencyManagement>`中，应按照以下类别组织依赖：

- Spring相关依赖
- 数据库相关依赖
- 工具类依赖
- 日志相关依赖
- 云原生相关依赖
- 其他第三方依赖

### 7.2 版本冲突解决

当遇到版本冲突时：

1. 优先使用排除策略（exclusion）
2. 必要时在trina-parent中覆盖特定依赖的版本
3. 记录版本冲突及解决方案

### 7.3 定期审查

应定期审查依赖版本：

- 检查安全漏洞
- 更新到最新的稳定版本
- 移除未使用的依赖

## 8. 示例

### 8.1 根POM示例片段

```xml
<properties>
    <java.version>17</java.version>
    <spring-boot.version>3.5.0</spring-boot.version>
    <mybatis-plus.version>3.5.10</mybatis-plus.version>
    <!-- 其他版本属性 -->
</properties>

<dependencyManagement>
    <dependencies>
        <!-- Spring Boot -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-dependencies</artifactId>
            <version>${spring-boot.version}</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
        
        <!-- MyBatis Plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        
        <!-- 其他依赖 -->
    </dependencies>
</dependencyManagement>

<dependencies>
    <!-- 所有模块通用的依赖 -->
    <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <scope>provided</scope>
    </dependency>
</dependencies>
```

### 8.2 父模块示例片段

```xml
<parent>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-common</artifactId>
    <version>4.0.0</version>
</parent>

<artifactId>trina-parent</artifactId>
<packaging>pom</packaging>

<dependencyManagement>
    <dependencies>
        <!-- Trina Common 模块 -->
        <dependency>
            <groupId>com.trinasolar</groupId>
            <artifactId>trina-common-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.trinasolar</groupId>
            <artifactId>trina-web-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- 其他模块 -->
    </dependencies>
</dependencyManagement>
```

### 8.3 子模块示例片段

```xml
<parent>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-common</artifactId>
    <version>4.0.0</version>
</parent>

<artifactId>trina-redis-starter</artifactId>

<dependencies>
    <!-- 核心依赖 -->
    <dependency>
        <groupId>com.trinasolar</groupId>
        <artifactId>trina-common-core</artifactId>
    </dependency>
    
    <!-- Redis 依赖 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>
    
    <!-- 其他依赖 -->
</dependencies>
