---
title: Trina Common 框架设计原则和理念
description: Trina Common 框架设计原则和理念
---

## 核心理念

Trina Common 框架的设计遵循现代软件架构的最佳实践，致力于构建一个高质量、可维护、可扩展的企业级微服务框架。

## 设计原则

### 1. 模块化设计 (Modularity)

#### 单一职责原则 (Single Responsibility Principle)
每个模块专注于特定的功能领域，避免功能重叠和职责混乱。

```mermaid
graph LR
    A[trina-web-starter] --> B[Web 层处理]
    C[trina-redis-starter] --> D[缓存处理]
    E[trina-mybatis-starter] --> F[数据访问]
    G[trina-security-starter] --> H[安全防护]
```

#### 松耦合 (Loose Coupling)
- **接口隔离**：模块间通过接口交互，减少直接依赖
- **依赖注入**：使用 Spring 的依赖注入机制
- **事件驱动**：通过事件机制实现模块间通信

```java
/**
 * 模块间通过接口交互示例
 */
@Component
public class UserService {
    
    private final CacheService cacheService;
    private final NotificationService notificationService;
    
    public UserService(CacheService cacheService, 
                      NotificationService notificationService) {
        this.cacheService = cacheService;
        this.notificationService = notificationService;
    }
    
    public void updateUser(User user) {
        // 业务逻辑
        userRepository.save(user);
        
        // 通过接口调用其他模块
        cacheService.evict("user:" + user.getId());
        notificationService.sendUpdateNotification(user);
    }
}
```

#### 高内聚 (High Cohesion)
模块内部功能紧密相关，共同完成特定的业务目标。

### 2. 开箱即用 (Out-of-the-Box)

#### 自动配置 (Auto Configuration)
基于 Spring Boot 的自动配置机制，提供智能的默认配置。

```java
/**
 * 自动配置示例
 */
@Configuration
@ConditionalOnClass(RedisTemplate.class)
@EnableConfigurationProperties(TrinaRedisProperties.class)
public class TrinaRedisAutoConfiguration {
    
    @Bean
    @ConditionalOnMissingBean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);
        // 默认序列化配置
        template.setDefaultSerializer(new GenericJackson2JsonRedisSerializer());
        return template;
    }
}
```

#### 约定优于配置 (Convention over Configuration)
提供合理的默认值和约定，减少配置复杂度。

```yaml
# 最小配置示例
trina:
  redis:
    enabled: true  # 默认启用
    # 其他配置使用默认值
```

#### 渐进式增强 (Progressive Enhancement)
支持从简单配置到复杂定制的渐进式使用方式。

```yaml
# 基础配置
trina:
  redis:
    enabled: true

---
# 高级配置
trina:
  redis:
    enabled: true
    cluster:
      nodes: 
        - 192.168.1.1:6379
        - 192.168.1.2:6379
    pool:
      max-active: 100
      max-idle: 20
    serializer: jackson
    key-prefix: "app:"
```

### 3. 企业级特性 (Enterprise-Grade)

#### 高可用性 (High Availability)

**故障转移机制**
```java
/**
 * 数据源故障转移示例
 */
@Configuration
public class DataSourceConfiguration {
    
    @Bean
    @Primary
    public DataSource dataSource() {
        return DataSourceBuilder.create()
            .type(HikariDataSource.class)
            .url("****************************")
            .build();
    }
    
    @Bean
    @Qualifier("backup")
    public DataSource backupDataSource() {
        return DataSourceBuilder.create()
            .type(HikariDataSource.class)
            .url("***************************")
            .build();
    }
}
```

**健康检查**
```java
/**
 * 自定义健康检查
 */
@Component
public class CustomHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        // 检查业务系统健康状态
        boolean isHealthy = checkBusinessHealth();
        
        if (isHealthy) {
            return Health.up()
                .withDetail("status", "Service is running")
                .build();
        } else {
            return Health.down()
                .withDetail("status", "Service is down")
                .build();
        }
    }
}
```

#### 高性能 (High Performance)

**连接池优化**
```java
/**
 * 连接池配置优化
 */
@ConfigurationProperties(prefix = "trina.datasource")
public class DataSourceProperties {
    
    private int maximumPoolSize = 20;
    private int minimumIdle = 5;
    private long connectionTimeout = 30000;
    private long idleTimeout = 600000;
    private long maxLifetime = 1800000;
    
    // getters and setters
}
```

**缓存策略**
```java
/**
 * 多级缓存策略
 */
@Service
public class UserService {
    
    @Cacheable(value = "users", key = "#id")
    public User findById(Long id) {
        return userRepository.findById(id);
    }
    
    @CacheEvict(value = "users", key = "#user.id")
    public void updateUser(User user) {
        userRepository.save(user);
    }
}
```

#### 可观测性 (Observability)

**监控指标**
```java
/**
 * 自定义监控指标
 */
@Component
public class BusinessMetrics {
    
    private final Counter orderCounter;
    private final Timer orderProcessingTime;
    
    public BusinessMetrics(MeterRegistry meterRegistry) {
        this.orderCounter = Counter.builder("orders.created")
            .description("Number of orders created")
            .register(meterRegistry);
            
        this.orderProcessingTime = Timer.builder("orders.processing.time")
            .description("Order processing time")
            .register(meterRegistry);
    }
    
    public void recordOrderCreated() {
        orderCounter.increment();
    }
    
    public void recordProcessingTime(Duration duration) {
        orderProcessingTime.record(duration);
    }
}
```

### 4. 云原生支持 (Cloud Native)

#### 容器化友好 (Container-Friendly)

**外部化配置**
```yaml
# application.yml
spring:
  datasource:
    url: ${DB_URL:******************************}
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    
trina:
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
```

**健康检查端点**
```yaml
# Kubernetes 健康检查配置
apiVersion: v1
kind: Pod
spec:
  containers:
  - name: app
    image: trina-app:latest
    livenessProbe:
      httpGet:
        path: /actuator/health
        port: 8080
      initialDelaySeconds: 30
      periodSeconds: 10
    readinessProbe:
      httpGet:
        path: /actuator/health/readiness
        port: 8080
      initialDelaySeconds: 5
      periodSeconds: 5
```

#### 微服务架构 (Microservices Architecture)

**服务发现**
```java
/**
 * 服务发现配置
 */
@Configuration
@EnableDiscoveryClient
public class ServiceDiscoveryConfiguration {
    
    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
```

**配置中心**
```yaml
# bootstrap.yml
spring:
  application:
    name: user-service
  cloud:
    nacos:
      config:
        server-addr: ${NACOS_SERVER:localhost:8848}
        namespace: ${NACOS_NAMESPACE:public}
        group: ${NACOS_GROUP:DEFAULT_GROUP}
```

### 5. 安全性 (Security)

#### 输入验证 (Input Validation)

```java
/**
 * 输入验证示例
 */
@RestController
@Validated
public class UserController {
    
    @PostMapping("/users")
    public ResponseEntity<User> createUser(@Valid @RequestBody CreateUserRequest request) {
        // 自动验证输入参数
        User user = userService.createUser(request);
        return ResponseEntity.ok(user);
    }
}

@Data
public class CreateUserRequest {
    
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 20, message = "用户名长度必须在3-20之间")
    private String username;
    
    @Email(message = "邮箱格式不正确")
    private String email;
    
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d]{8,}$", 
             message = "密码必须包含大小写字母和数字，长度至少8位")
    private String password;
}
```

#### XSS 防护

```java
/**
 * XSS 防护过滤器
 */
@Component
public class XssFilter implements Filter {
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, 
                        FilterChain chain) throws IOException, ServletException {
        
        XssHttpServletRequestWrapper wrappedRequest = 
            new XssHttpServletRequestWrapper((HttpServletRequest) request);
            
        chain.doFilter(wrappedRequest, response);
    }
}
```

#### 数据脱敏

```java
/**
 * 敏感数据脱敏
 */
@Data
public class User {
    
    private Long id;
    
    private String username;
    
    @Sensitive(type = SensitiveType.PHONE)
    private String phone;
    
    @Sensitive(type = SensitiveType.EMAIL)
    private String email;
    
    @Sensitive(type = SensitiveType.ID_CARD)
    private String idCard;
}
```

### 6. 可扩展性 (Extensibility)

#### SPI 扩展机制

```java
/**
 * 扩展点定义
 */
public interface MessageProcessor {
    
    /**
     * 处理消息
     */
    void process(Message message);
    
    /**
     * 获取处理器类型
     */
    String getType();
}

/**
 * 扩展点实现
 */
@Component
public class EmailMessageProcessor implements MessageProcessor {
    
    @Override
    public void process(Message message) {
        // 邮件消息处理逻辑
    }
    
    @Override
    public String getType() {
        return "email";
    }
}
```

#### 插件化架构

```java
/**
 * 插件管理器
 */
@Component
public class PluginManager {
    
    private final Map<String, MessageProcessor> processors = new HashMap<>();
    
    public PluginManager(List<MessageProcessor> processorList) {
        processorList.forEach(processor -> 
            processors.put(processor.getType(), processor));
    }
    
    public void processMessage(String type, Message message) {
        MessageProcessor processor = processors.get(type);
        if (processor != null) {
            processor.process(message);
        }
    }
}
```

### 7. 开发者友好 (Developer-Friendly)

#### 丰富的文档
- **API 文档**：自动生成的 OpenAPI 文档
- **使用指南**：详细的使用说明和最佳实践
- **示例代码**：完整的示例项目和代码片段

#### 调试支持

```java
/**
 * 调试信息输出
 */
@Component
@ConditionalOnProperty(name = "trina.debug.enabled", havingValue = "true")
public class DebugInfoCollector {
    
    @EventListener
    public void handleApplicationEvent(ApplicationEvent event) {
        log.debug("Application event: {}", event.getClass().getSimpleName());
    }
    
    @Bean
    public CommandLineRunner debugInfoRunner() {
        return args -> {
            log.info("=== Trina Framework Debug Info ===");
            log.info("Active profiles: {}", Arrays.toString(environment.getActiveProfiles()));
            log.info("Loaded modules: {}", getLoadedModules());
        };
    }
}
```

#### 错误处理

```java
/**
 * 统一异常处理
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<ErrorResponse> handleValidationException(ValidationException e) {
        ErrorResponse error = ErrorResponse.builder()
            .code("VALIDATION_ERROR")
            .message(e.getMessage())
            .timestamp(LocalDateTime.now())
            .build();
            
        return ResponseEntity.badRequest().body(error);
    }
    
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ErrorResponse> handleBusinessException(BusinessException e) {
        ErrorResponse error = ErrorResponse.builder()
            .code(e.getCode())
            .message(e.getMessage())
            .timestamp(LocalDateTime.now())
            .build();
            
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
    }
}
```

## 设计模式应用

### 1. 工厂模式 (Factory Pattern)

```java
/**
 * 数据源工厂
 */
@Component
public class DataSourceFactory {
    
    public DataSource createDataSource(DataSourceConfig config) {
        switch (config.getType()) {
            case "hikari":
                return createHikariDataSource(config);
            case "druid":
                return createDruidDataSource(config);
            default:
                throw new IllegalArgumentException("Unsupported datasource type: " + config.getType());
        }
    }
}
```

### 2. 策略模式 (Strategy Pattern)

```java
/**
 * 缓存策略
 */
public interface CacheStrategy {
    void put(String key, Object value);
    Object get(String key);
    void evict(String key);
}

@Component
public class CacheManager {
    
    private final Map<String, CacheStrategy> strategies;
    
    public void cache(String strategy, String key, Object value) {
        CacheStrategy cacheStrategy = strategies.get(strategy);
        if (cacheStrategy != null) {
            cacheStrategy.put(key, value);
        }
    }
}
```

### 3. 观察者模式 (Observer Pattern)

```java
/**
 * 事件发布订阅
 */
@Component
public class UserEventPublisher {
    
    private final ApplicationEventPublisher eventPublisher;
    
    public void publishUserCreatedEvent(User user) {
        UserCreatedEvent event = new UserCreatedEvent(this, user);
        eventPublisher.publishEvent(event);
    }
}

@EventListener
@Component
public class UserEventListener {
    
    @Async
    public void handleUserCreatedEvent(UserCreatedEvent event) {
        // 处理用户创建事件
        User user = event.getUser();
        // 发送欢迎邮件、初始化用户数据等
    }
}
```

## 总结

Trina Common 框架的设计原则体现了现代软件架构的最佳实践：

1. **模块化设计**确保了代码的可维护性和可扩展性
2. **开箱即用**降低了使用门槛，提高了开发效率
3. **企业级特性**保证了生产环境的稳定性和性能
4. **云原生支持**适应了现代应用部署的需求
5. **安全性**保护了应用和数据的安全
6. **可扩展性**支持了业务的持续发展
7. **开发者友好**提升了开发体验

这些原则共同构成了 Trina Common 框架的核心价值，为企业级微服务开发提供了坚实的基础。