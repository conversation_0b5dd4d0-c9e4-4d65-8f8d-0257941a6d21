import {themes as prismThemes} from 'prism-react-renderer';
import type {Config} from '@docusaurus/types';
import type * as Preset from '@docusaurus/preset-classic';

// This runs in Node.js - Don't use client-side code here (browser APIs, JSX...)

const config: Config = {
  title: 'Trina Framework',
  tagline: '企业级微服务开发框架 - 让开发更简单、更高效',
  favicon: 'img/favicon.ico',
  
  // 版本信息
  customFields: {
    version: '4.0.0',
    description: '基于 Spring Boot 3.4 + Java 17 构建的企业级微服务开发框架',
  },

  // Future flags, see https://docusaurus.io/docs/api/docusaurus-config#future
  future: {
    v4: true, // Improve compatibility with the upcoming Docusaurus v4
  },

  // Set the production url of your site here
  url: 'https://trina-framework.com',
  // Set the /<baseUrl>/ pathname under which your site is served
  // For GitHub pages deployment, it is often '/<projectName>/'
  baseUrl: '/wiki/',

  // 强制在所有环境中使用 /wiki/ 前缀
  trailingSlash: false,

  // GitHub pages deployment config.
  // If you aren't using GitHub pages, you don't need these.
  organizationName: 'trina-framework', // Usually your GitHub org/user name.
  projectName: 'trina-common', // Usually your repo name.

  onBrokenLinks: 'throw',
  onBrokenMarkdownLinks: 'warn',

  // Even if you don't use internationalization, you can use this field to set
  // useful metadata like html lang. For example, if your site is Chinese, you
  // may want to replace "en" with "zh-Hans".
  i18n: {
    defaultLocale: 'zh-Hans',
    locales: ['zh-Hans'],
  },

  presets: [
    [
      'classic',
      {
        docs: {
          path: '../docs',
          sidebarPath: './sidebars.ts',
          // Please change this to your repo.
          // Remove this to remove the "edit this page" links.
          editUrl:
            'https://github.com/trina-framework/trina-common/tree/main/wiki/',
        },
        blog: {
          showReadingTime: true,
          feedOptions: {
            type: ['rss', 'atom'],
            xslt: true,
          },
          // Please change this to your repo.
          // Remove this to remove the "edit this page" links.
          editUrl:
            'https://github.com/trina-framework/trina-common/tree/main/wiki/',
          // Useful options to enforce blogging best practices
          onInlineTags: 'warn',
          onInlineAuthors: <AUTHORS>
          onUntruncatedBlogPosts: 'warn',
        },
        theme: {
          customCss: './src/css/custom.css',
        },
      } satisfies Preset.Options,
    ],
  ],

  themes: [
    '@docusaurus/theme-mermaid'
  ],
  markdown: {
    mermaid: true,
  },
  plugins: [
    [
      '@easyops-cn/docusaurus-search-local',
      {
        hashed: true,
        language: ['zh', 'en'],
        highlightSearchTermsOnTargetPage: true,
        indexBlog: true,
        indexDocs: true
      }
    ],
    [
      '@docusaurus/plugin-client-redirects',
      {
        // 处理客户端重定向
        fromExtensions: ['html', 'htm'],
        toExtensions: ['exe', 'zip'],
        // 移除根路径重定向，因为 baseUrl 已经处理了 /wiki/ 前缀
      },
    ],
  ],

  themeConfig: {
    // Replace with your project's social card
    image: 'img/docusaurus-social-card.jpg',
    navbar: {
      title: 'Trina脚手架',
      logo: {
        alt: 'Trina Framework Logo',
        src: 'img/logo.svg',
      },
      items: [
        {
          type: 'docSidebar',
          sidebarId: 'tutorialSidebar',
          position: 'left',
          label: 'Trina-Common',
        },
        {
          type: 'docSidebar',
          sidebarId: 'scaffoldSidebar',
          position: 'left',
          label: '后端脚手架',
        },
        {
          type: 'docSidebar',
          sidebarId: 'frontendSidebar',
          position: 'left',
          label: '前端脚手架',
        },
        {to: '/blog', label: '博客', position: 'right'},
        {
          type: 'html',
          position: 'right',
          value: '<span class="navbar__item navbar__link">v4.0.0</span>',
        },
      ],
    },
    footer: {
      style: 'dark',
      copyright: `Copyright ${new Date().getFullYear()} Trina Framework. Built with Docusaurus.`,
    },
    colorMode: {
      defaultMode: 'light',
      disableSwitch: false,
      respectPrefersColorScheme: false,
    },
    prism: {
      theme: prismThemes.github,
      darkTheme: prismThemes.dracula,
      additionalLanguages: ['java', 'yaml', 'bash'],
    },

  } satisfies Preset.ThemeConfig,
};

export default config;
