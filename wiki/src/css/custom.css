/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

/* You can override the default Infima variables here. */
:root {
  --ifm-color-primary: #008BD6;
  --ifm-color-primary-dark: #007BC2;
  --ifm-color-primary-darker: #0074B7;
  --ifm-color-primary-darkest: #006096;
  --ifm-color-primary-light: #009BEA;
  --ifm-color-primary-lighter: #00A2EF;
  --ifm-color-primary-lightest: #1AB6FF;
  --ifm-code-font-size: 95%;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
}

/* For readability concerns, you should choose a lighter palette in dark mode. */
[data-theme='dark'] {
  --ifm-color-primary: #4A9EFF;
  --ifm-color-primary-dark: #2B8CFF;
  --ifm-color-primary-darker: #1A7FFF;
  --ifm-color-primary-darkest: #0066CC;
  --ifm-color-primary-light: #6BB1FF;
  --ifm-color-primary-lighter: #7DB8FF;
  --ifm-color-primary-lightest: #A3CCFF;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.3);
}

/* Custom styles for Trina Framework */
.navbar__brand {
  font-weight: 700;
}

.navbar__title {
  color: var(--ifm-color-primary) !important;
  font-weight: 700;
}

.hero__title {
  color: var(--ifm-color-primary);
}

.button--primary {
  background: linear-gradient(45deg, var(--ifm-color-primary), var(--ifm-color-primary-light));
  border: none;
  transition: all 0.3s ease;
}

.button--primary:hover {
  background: linear-gradient(45deg, var(--ifm-color-primary-dark), var(--ifm-color-primary));
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 139, 214, 0.3);
}

.button--secondary {
  border-color: var(--ifm-color-primary);
  color: var(--ifm-color-primary);
}

.button--secondary:hover {
  background: var(--ifm-color-primary);
  border-color: var(--ifm-color-primary);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}
