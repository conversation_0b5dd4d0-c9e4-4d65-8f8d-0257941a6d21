package com.trinasolar.common.security.annotation;

import java.lang.annotation.*;

/**
 * 功能权限注解
 * 
 * 用于标识接口需要的功能权限，支持单个权限或多个权限
 * 
 * 使用示例：
 * @HasPermission("user:add")                           // 需要user:add权限
 * @HasPermission({"user:add", "user:edit"})            // 需要user:add或user:edit权限之一
 * @HasPermission(value = {"user:add", "user:edit"}, logical = Logical.AND) // 需要同时拥有user:add和user:edit权限
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface HasPermission {

    /**
     * 需要的权限列表
     */
    String[] value();

    /**
     * 逻辑关系：AND表示需要拥有所有权限，OR表示拥有任意一个权限即可
     */
    Logical logical() default Logical.OR;

    /**
     * 逻辑关系枚举
     */
    enum Logical {
        /**
         * 逻辑与：需要拥有所有指定的权限
         */
        AND,
        
        /**
         * 逻辑或：拥有任意一个指定的权限即可
         */
        OR
    }
}
