{"hello": "Hello", "world": "World", "welcome": "Welcome to Trina I18n Starter", "common.success": "Operation successful", "common.error": "Operation failed", "common.save": "Save", "common.delete": "Delete", "common.edit": "Edit", "common.view": "View", "common.submit": "Submit", "common.cancel": "Cancel", "common.confirm": "Confirm", "common.back": "Back", "common.search": "Search", "common.reset": "Reset", "common.add": "Add", "common.update": "Update", "common.yes": "Yes", "common.no": "No", "common.loading": "Loading...", "common.pleaseSelect": "Please select", "common.pleaseInput": "Please input", "common.all": "All", "common.noData": "No data", "common.total": "Total {0} items", "common.page": "Page {0} / {1}", "common.required": "Required", "common.optional": "Optional", "common.invalid": "Invalid", "common.enabled": "Enabled", "common.disabled": "Disabled", "common.status": "Status", "common.remark": "Remark", "common.createdTime": "Created time", "common.updatedTime": "Updated time", "common.createdBy": "Created by", "common.updatedBy": "Updated by", "common.unknown": "Unknown", "common.logout": "Logout", "common.login": "<PERSON><PERSON>", "common.register": "Register", "common.username": "Username", "common.password": "Password", "common.email": "Email", "common.phone": "Phone", "common.address": "Address", "common.date": "Date", "common.time": "Time", "common.dateTime": "Date time", "common.startDate": "Start date", "common.endDate": "End date", "common.startTime": "Start time", "common.endTime": "End time", "common.name": "Name", "common.code": "Code", "common.type": "Type", "common.description": "Description", "common.sort": "Sort", "common.config": "Config", "common.setting": "Setting", "common.manage": "Manage", "common.operation": "Operation", "common.more": "More", "common.detail": "Detail", "common.upload": "Upload", "common.download": "Download", "common.import": "Import", "common.export": "Export", "common.preview": "Preview", "common.ok": "OK", "common.close": "Close", "common.open": "Open", "common.clear": "Clear", "common.refresh": "Refresh", "common.collapse": "Collapse", "common.expand": "Expand", "common.next": "Next", "common.prev": "Previous", "common.complete": "Complete", "common.save.success": "Saved successfully", "common.save.error": "Save failed", "common.delete.success": "Deleted successfully", "common.delete.error": "Delete failed", "common.update.success": "Updated successfully", "common.update.error": "Update failed", "common.operate.success": "Operation successful", "common.operate.error": "Operation failed", "common.confirm.delete": "Confirm delete?", "common.confirm.operate": "Confirm operation?", "common.warning": "Warning", "common.info": "Information", "common.error.message": "Error message"}