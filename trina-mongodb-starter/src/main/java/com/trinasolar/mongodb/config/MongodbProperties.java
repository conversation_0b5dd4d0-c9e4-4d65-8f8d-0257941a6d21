package com.trinasolar.mongodb.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;
import java.util.List;

/**
 * MongoDB配置属性类
 */
@ConfigurationProperties(prefix = "trina.mongodb")
public class MongodbProperties {

    /**
     * MongoDB连接URI
     */
    private String uri;

    /**
     * 数据库名称
     */
    private String database;

    /**
     * 主机地址
     */
    private String host = "localhost";

    /**
     * 端口号
     */
    private int port = 27017;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 连接超时时间
     */
    private Duration connectTimeout = Duration.ofSeconds(10);

    /**
     * 套接字超时时间
     */
    private Duration socketTimeout = Duration.ofSeconds(30);

    /**
     * 最大连接数
     */
    private int maxConnectionPoolSize = 100;

    /**
     * 最小连接数
     */
    private int minConnectionPoolSize = 0;

    /**
     * 是否启用事务
     */
    private boolean enableTransaction = false;

    /**
     * 副本集名称
     */
    private String replicaSet;

    /**
     * 认证数据库
     */
    private String authenticationDatabase = "admin";

    /**
     * 认证机制
     */
    private String authenticationMechanism;

    /**
     * 是否启用SSL
     */
    private boolean ssl = false;

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getDatabase() {
        return database;
    }

    public void setDatabase(String database) {
        this.database = database;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Duration getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(Duration connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public Duration getSocketTimeout() {
        return socketTimeout;
    }

    public void setSocketTimeout(Duration socketTimeout) {
        this.socketTimeout = socketTimeout;
    }

    public int getMaxConnectionPoolSize() {
        return maxConnectionPoolSize;
    }

    public void setMaxConnectionPoolSize(int maxConnectionPoolSize) {
        this.maxConnectionPoolSize = maxConnectionPoolSize;
    }

    public int getMinConnectionPoolSize() {
        return minConnectionPoolSize;
    }

    public void setMinConnectionPoolSize(int minConnectionPoolSize) {
        this.minConnectionPoolSize = minConnectionPoolSize;
    }

    public boolean isEnableTransaction() {
        return enableTransaction;
    }

    public void setEnableTransaction(boolean enableTransaction) {
        this.enableTransaction = enableTransaction;
    }

    public String getReplicaSet() {
        return replicaSet;
    }

    public void setReplicaSet(String replicaSet) {
        this.replicaSet = replicaSet;
    }

    public String getAuthenticationDatabase() {
        return authenticationDatabase;
    }

    public void setAuthenticationDatabase(String authenticationDatabase) {
        this.authenticationDatabase = authenticationDatabase;
    }

    public String getAuthenticationMechanism() {
        return authenticationMechanism;
    }

    public void setAuthenticationMechanism(String authenticationMechanism) {
        this.authenticationMechanism = authenticationMechanism;
    }

    public boolean isSsl() {
        return ssl;
    }

    public void setSsl(boolean ssl) {
        this.ssl = ssl;
    }
} 