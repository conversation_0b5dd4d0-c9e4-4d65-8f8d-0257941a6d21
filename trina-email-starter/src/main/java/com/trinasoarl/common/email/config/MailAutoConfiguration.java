package com.trinasoarl.common.email.config;

import com.trinasoarl.common.email.service.MailService;
import com.trinasoarl.common.email.service.impl.MailServiceImpl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.util.Properties;

@Configuration
@ConditionalOnClass(JavaMailSender.class)
@EnableConfigurationProperties({MailProperties.class})
public class MailAutoConfiguration {


    private static final Logger logger = LoggerFactory.getLogger(MailAutoConfiguration.class);

    @Bean
    @Primary
    public JavaMailSender javaMailSender(MailProperties properties) {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        mailSender.setHost(properties.getHost());
        mailSender.setPort(properties.getPort());
        mailSender.setUsername(properties.getUsername());
        mailSender.setPassword(properties.getPassword());

        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", properties.getProtocol());
        props.put("mail.smtp.auth", properties.isAuth());
        // 修复点1：正确映射SSL和STARTTLS属性
        props.put("mail.smtp.ssl.enable", properties.isSsl());
        props.put("mail.smtp.starttls.enable", String.valueOf(properties.isStarttls()));

        // 修复点2：添加超时设置
        props.put("mail.smtp.connectiontimeout", properties.getConnectionTimeout());
        props.put("mail.smtp.timeout", properties.getTimeout());
        props.put("mail.smtp.writetimeout", properties.getWriteTimeout());
//        props.put("mail.debug", "true"); // 生产环境关闭调试
        logger.info("Loading javaMailSender successfully");
        return mailSender;
    }

    @Bean
    @ConditionalOnMissingBean
    public MailService mailService(JavaMailSender mailSender, MailProperties mailProperties) {
        return new MailServiceImpl(mailSender, mailProperties);
    }



}