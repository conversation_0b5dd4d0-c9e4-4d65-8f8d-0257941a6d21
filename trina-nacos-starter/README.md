# Nacos配置加载顺序说明

## 🎯 问题背景

在使用trina-nacos-starter时，`nacos.yaml`中使用了`${spring.application.name}`等占位符，这些值需要从项目的`application.yml`中获取。因此需要确保正确的配置加载顺序。

## 📋 配置加载顺序

### 1. Spring Boot默认配置加载顺序

Spring Boot按以下顺序加载配置：

```
1. 命令行参数
2. SPRING_APPLICATION_JSON中的属性
3. ServletConfig初始化参数
4. ServletContext初始化参数
5. JNDI属性
6. Java系统属性
7. 操作系统环境变量
8. RandomValuePropertySource
9. jar包外的application-{profile}.properties或application-{profile}.yml
10. jar包内的application-{profile}.properties或application-{profile}.yml
11. jar包外的application.properties或application.yml
12. jar包内的application.properties或application.yml
13. @PropertySource注解的配置
14. 默认属性
```

### 2. EnvironmentPostProcessor执行时机

`EnvironmentPostProcessor`在Spring Boot启动过程中的执行时机：

```
SpringApplication.run()
    ↓
prepareEnvironment()
    ↓
configureEnvironment()
    ↓
listeners.environmentPrepared() 
    ↓
EnvironmentPostProcessor.postProcessEnvironment() ← 我们的处理器在这里执行
    ↓
bindToSpringApplication()
    ↓
printBanner()
    ↓
createApplicationContext()
```

### 3. NacosEnvironmentProcessor优化策略

为了确保正确的配置加载顺序，我们采用以下策略：

#### 3.1 执行顺序控制
```java
@Override
public int getOrder() {
    // 在默认的配置处理器之前执行，但在系统属性处理器之后
    // 确保能够读取到spring.application.name等基础配置
    return Ordered.HIGHEST_PRECEDENCE + 20;
}
```

#### 3.2 分阶段处理
```java
@Override
public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
    // 1. 首先处理Profile配置（确保后续配置能正确解析）
    processProfilesFirst(environment, properties);
    
    // 2. 加载nacos.yaml基础配置
    loadNacosYamlConfiguration(environment);
    
    // 3. 处理Namespace配置（基于已设置的Profile）
    processNamespaceConfiguration(environment, properties);
    
    // 4. 处理Nacos配置导入
    processNacosConfigImport(environment, properties);
    
    // 5. 设置默认配置
    setDefaultNacosProperties(environment, properties);
}
```

#### 3.3 智能配置检测
```java
// 检查是否已经在nacos.yaml中配置了shared-configs
if (!environment.containsProperty("spring.cloud.nacos.config.shared-configs[0].data-id")) {
    // 只有在没有配置的情况下才自动配置
    properties.put("spring.cloud.nacos.config.shared-configs[0].data-id", commonConfigDataId);
}
```

## 🔧 配置文件设计

### 1. nacos.yaml设计原则

#### 提供默认值
```yaml
spring:
  config:
    import:
      # 为占位符提供默认值，避免解析失败
      - optional:nacos:application-${spring.profiles.active:dev}.yaml
      - optional:nacos:${spring.application.name}-${spring.profiles.active:dev}.yaml
```

#### 使用环境变量
```yaml
spring:
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_HOST:127.0.0.1}:${NACOS_PORT:8848}
      username: ${NACOS_USERNAME:nacos}
      password: ${NACOS_PASSWORD:nacos}
```

#### 避免循环依赖
```yaml
# ❌ 错误：可能导致循环依赖
namespace: ${spring.application.namespace}

# ✅ 正确：在代码中动态设置
# namespace会在NacosEnvironmentProcessor中自动设置
```

### 2. 项目application.yml要求

项目的`application.yml`必须包含以下基础配置：

```yaml
spring:
  application:
    name: your-application-name  # 必须配置
  profiles:
    active: dev  # 可选，默认为dev
```

## 🚀 使用示例

### 1. 最小配置

#### 项目application.yml
```yaml
spring:
  application:
    name: user-service
```

#### 自动生成的配置
```yaml
spring:
  profiles:
    active: dev  # 自动设置
  application:
    namespace: user-service-dev  # 自动生成
  cloud:
    nacos:
      config:
        namespace: user-service-dev
        shared-configs:
          - data-id: common-config-dev.yaml
        extension-configs:
          - data-id: user-service-dev.yaml
      discovery:
        namespace: user-service-dev
```

### 2. 完整配置

#### 项目application.yml
```yaml
spring:
  application:
    name: user-service
    group: MY_GROUP
  profiles:
    active: prod

# 自定义Nacos配置
spring:
  cloud:
    nacos:
      server-addr: nacos.example.com:8848
      username: admin
      password: admin123
      config:
        group: MY_GROUP
        namespace: user-service-prod  # 可以覆盖自动生成的
```

## ⚠️ 注意事项

### 1. 配置优先级

配置优先级从高到低：
1. NacosEnvironmentProcessor动态设置的属性（最高优先级）
2. 项目application.yml中的配置
3. nacos.yaml中的基础配置
4. 默认值

### 2. 占位符解析

- `${spring.application.name}` - 必须在项目application.yml中定义
- `${spring.profiles.active}` - 可选，默认为dev
- `${NACOS_HOST}` - 环境变量，默认为127.0.0.1
- `${NACOS_PORT}` - 环境变量，默认为8848

### 3. 配置覆盖规则

- 如果项目已经配置了某个属性，NacosEnvironmentProcessor不会覆盖
- nacos.yaml中的配置作为基础配置，可以被项目配置覆盖
- 环境变量具有最高优先级

### 4. 调试建议

#### 启用详细日志
```yaml
logging:
  level:
    com.trinasolar.nacos: DEBUG
    org.springframework.boot.env: DEBUG
```

#### 检查配置加载
```java
// 在应用启动后检查配置
@Component
public class ConfigChecker {
    
    @Value("${spring.application.name}")
    private String applicationName;
    
    @Value("${spring.cloud.nacos.config.namespace}")
    private String namespace;
    
    @PostConstruct
    public void checkConfig() {
        System.out.println("Application Name: " + applicationName);
        System.out.println("Nacos Namespace: " + namespace);
    }
}
```

## 📈 最佳实践

1. **明确定义应用名**：在项目application.yml中明确定义spring.application.name
2. **使用环境变量**：敏感信息如用户名密码使用环境变量
3. **分环境配置**：不同环境使用不同的Profile和Namespace
4. **监控配置加载**：启用详细日志监控配置加载过程
5. **测试配置**：在不同环境下测试配置加载是否正确
