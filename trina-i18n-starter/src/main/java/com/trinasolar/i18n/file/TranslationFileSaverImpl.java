package com.trinasolar.i18n.file;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trinasolar.i18n.config.I18nProperties;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 翻译文件保存器实现
 *
 * <AUTHOR>
 */
public class TranslationFileSaverImpl implements TranslationFileSaver {

    private static final Logger log = LoggerFactory.getLogger(TranslationFileSaverImpl.class);
    
    private final I18nProperties properties;
    private final AsyncTaskExecutor taskExecutor;
    private final ObjectMapper objectMapper;
    private final Map<String, Lock> fileLocks;
    private final TranslationFileLoader fileLoader;

    public TranslationFileSaverImpl(I18nProperties properties, AsyncTaskExecutor taskExecutor) {
        this.properties = properties;
        this.taskExecutor = taskExecutor;
        this.objectMapper = new ObjectMapper();
        this.fileLocks = new HashMap<>();
        this.fileLoader = new TranslationFileLoaderImpl(properties);
    }

    @Override
    public void saveTranslationAsync(String key, String value, String language) {
        if (key == null || value == null || language == null) {
            return;
        }
        
        Map<String, String> translations = new HashMap<>();
        translations.put(key, value);
        saveTranslationsAsync(translations, language);
    }

    @Override
    public void saveTranslationsAsync(Map<String, String> translations, String language) {
        if (translations == null || translations.isEmpty() || language == null) {
            return;
        }
        
        taskExecutor.execute(() -> {
            try {
                saveTranslations(translations, language);
            } catch (Exception e) {
                log.error("Failed to save translations asynchronously for language '{}'", language, e);
            }
        });
    }

    @Override
    public boolean saveTranslation(String key, String value, String language) {
        if (key == null || value == null || language == null) {
            return false;
        }
        
        Map<String, String> translations = new HashMap<>();
        translations.put(key, value);
        return saveTranslations(translations, language);
    }

    @Override
    public boolean saveTranslations(Map<String, String> translations, String language) {
        if (translations == null || translations.isEmpty() || language == null) {
            return false;
        }
        
        String filePath = fileLoader.getFilePath(language, true);
        if (!StringUtils.hasText(filePath)) {
            return false;
        }
        
        Lock lock = getFileLock(filePath);
        lock.lock();
        try {
            // 读取现有翻译
            Map<String, String> existingTranslations = loadExistingTranslations(filePath);
            
            // 合并新的翻译
            existingTranslations.putAll(translations);
            
            // 确保目录存在
            Path path = Paths.get(filePath);
            Files.createDirectories(path.getParent());
            
            // 写入文件
            String json = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(existingTranslations);
            Files.writeString(path, json, StandardCharsets.UTF_8);
            
            return true;
        } catch (IOException e) {
            log.error("Failed to save translations to file: {}", filePath, e);
            return false;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取文件锁
     */
    private Lock getFileLock(String filePath) {
        return fileLocks.computeIfAbsent(filePath, k -> new ReentrantLock());
    }

    /**
     * 加载现有翻译
     */
    private Map<String, String> loadExistingTranslations(String filePath) {
        if (!StringUtils.hasText(filePath)) {
            return new HashMap<>();
        }
        
        try {
            Path path = Paths.get(filePath);
            if (Files.exists(path)) {
                String json = Files.readString(path, StandardCharsets.UTF_8);
                return objectMapper.readValue(json, new TypeReference<Map<String, String>>() {});
            }
        } catch (IOException e) {
            log.error("Failed to load existing translations from file: {}", filePath, e);
        }
        
        return new HashMap<>();
    }
}