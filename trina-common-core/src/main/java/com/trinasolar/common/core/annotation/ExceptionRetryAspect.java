package com.trinasolar.common.core.annotation;

import com.trinasolar.common.core.exception.BusinessException;
import com.trinasolar.common.core.exception.RetryException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;

import java.lang.reflect.Method;

/**
 * 重试
 */
@Aspect
@Slf4j
public class ExceptionRetryAspect {
    /**
     * 默认重试次数
     */
    @Value("${project.retry.times:3}")
    private int defaultTimes;
    /**
     * 默认重试间隔时间
     */
    @Value("${project.retry.waitTime:1000}")
    private long defaultWaitTime;

    @Pointcut("@annotation(com.trinasolar.common.core.annotation.ExceptionRetry)")
    public void retryPointCut() {
    }

    @Around("retryPointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        ExceptionRetry retry = method.getAnnotation(ExceptionRetry.class);
        // 是否使用默认重试次数，默认重试次数项目配置文件配置
        int retryTimes = retry.times() == 3 ? defaultTimes : retry.times();
        int times = retryTimes;
        long waitTime = retry.waitTime() == 1000 ? defaultWaitTime : retry.waitTime();

        // 重试次数如果小于0，则重试一次
        times = times <= 0 ? 1 : times;

        for (; times >= 0; times--) {
            try {
                return joinPoint.proceed();
            } catch (RetryException e) {
                // 如果接下来没有重试机会的话，直接报错
                if (times <= 0) {
                    log.error("Request retry failure: Execute retry {} times, but fail；", retryTimes);
                    throw new BusinessException("Request retry failure");
                }
                log.warn("Request retry failure: Execute retry ，The remaining {} times", retryTimes);
                // 休眠 等待下次执行
                if (waitTime > 0) {
                    Thread.sleep(waitTime);
                }
            }
        }

        return false;
    }
}
