package com.trinasoarl.common.email.model;


import jakarta.mail.Multipart;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;


public class EmailRequest {
    /**
     * 收件人
      */
    private String[] users;
    /**
     * 主题
      */
    private String subject;
    /**
     * 消息
      */
    private String msg;

    /**
     * 是否HTML格式
      */
    private Boolean isHtml=false;
    /**
     * 抄送人
      */
    private String[] ccUsers;
    /**
     *  附件
     */
    private MultipartFile file;

    public MultipartFile getFile() {
        return file;
    }

    public void setFile(MultipartFile file) {
        this.file = file;
    }

    public String[] getUsers() {
        return users;
    }

    public void setUsers(String[] users) {
        this.users = users;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Boolean getHtml() {
        return isHtml;
    }

    public void setHtml(Boolean html) {
        isHtml = html;
    }

    public String[] getCcUsers() {
        return ccUsers;
    }

    public void setCcUsers(String[] ccUsers) {
        this.ccUsers = ccUsers;
    }

}
