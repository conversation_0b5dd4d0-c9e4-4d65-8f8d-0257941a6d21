package com.trinasolar.stream.exception;

import com.trinasolar.common.core.exception.BaseException;

import java.io.Serial;

/**
 * Stream 异常类
 *
 * <AUTHOR>
 * @since 4.0.0
 */
public class StreamException extends BaseException {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    public StreamException(String message) {
        super("STREAM", message);
    }
    
    public StreamException(String message, Throwable cause) {
        super("STREAM", message);
        initCause(cause);
    }
    
    public StreamException(String code, Object[] args) {
        super("STREAM", code, args);
    }
    
    public StreamException(String code, Object[] args, String defaultMessage) {
        super("STREAM", code, args, defaultMessage);
    }
}
