package com.trinasolar.i18n.api;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trinasolar.i18n.config.I18nProperties;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestClient;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.time.Duration;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeoutException;

/**
 * 翻译API客户端实现
 *
 * <AUTHOR>
 */
public class TranslationApiClientImpl implements TranslationApiClient {

    private static final Logger log = LoggerFactory.getLogger(TranslationApiClientImpl.class);
    
    private final I18nProperties properties;
    private final ObjectMapper objectMapper;
    private final WebClient webClient;
    private final RestClient restClient;

    /**
     * 使用WebClient构造
     */
    public TranslationApiClientImpl(I18nProperties properties, WebClient webClient) {
        this.properties = properties;
        this.objectMapper = new ObjectMapper();
        this.webClient = webClient;
        this.restClient = null;
    }

    /**
     * 使用RestClient构造
     */
    public TranslationApiClientImpl(I18nProperties properties, RestClient restClient) {
        this.properties = properties;
        this.objectMapper = new ObjectMapper();
        this.webClient = null;
        this.restClient = restClient;
    }

    @Override
    public Optional<String> translate(String text, String sourceLanguage, String targetLanguage) {
        if (!isAvailable() || !StringUtils.hasText(text) || !StringUtils.hasText(targetLanguage)) {
            return Optional.empty();
        }

        // 如果源语言和目标语言相同，直接返回原文
        if (StringUtils.hasText(sourceLanguage) && sourceLanguage.equals(targetLanguage)) {
            return Optional.of(text);
        }

        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("q", text);
            requestBody.put("source", sourceLanguage);
            requestBody.put("target", targetLanguage);
            requestBody.put("format", "text");

            // 添加API密钥
            if (StringUtils.hasText(properties.getApi().getAppKey())) {
                requestBody.put("api_key", properties.getApi().getAppKey());
            }
            if (StringUtils.hasText(properties.getApi().getAppSecret())) {
                requestBody.put("api_secret", properties.getApi().getAppSecret());
            }

            String responseJson;
            
            // 使用WebClient或RestClient发送请求
            if (webClient != null) {
                responseJson = sendRequestWithWebClient(requestBody);
            } else if (restClient != null) {
                responseJson = sendRequestWithRestClient(requestBody);
            } else {
                return Optional.empty();
            }

            // 解析响应
            if (StringUtils.hasText(responseJson)) {
                JsonNode root = objectMapper.readTree(responseJson);
                JsonNode translationsNode = root.path("translatedText");
                if (!translationsNode.isMissingNode() && !translationsNode.isNull()) {
                    return Optional.of(translationsNode.asText());
                }
            }
        } catch (Exception e) {
            log.error("Failed to translate text '{}' from '{}' to '{}'", text, sourceLanguage, targetLanguage, e);
        }

        return Optional.empty();
    }

    @Override
    public Map<String, String> translateBatch(Map<String, String> texts, String sourceLanguage, String targetLanguage) {
        if (!isAvailable() || texts == null || texts.isEmpty() || !StringUtils.hasText(targetLanguage)) {
            return Collections.emptyMap();
        }

        // 如果源语言和目标语言相同，直接返回原文
        if (StringUtils.hasText(sourceLanguage) && sourceLanguage.equals(targetLanguage)) {
            return new HashMap<>(texts);
        }

        Map<String, String> results = new HashMap<>();
        
        // 由于大多数翻译API不支持批量翻译，我们逐个翻译
        for (Map.Entry<String, String> entry : texts.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            
            Optional<String> translated = translate(value, sourceLanguage, targetLanguage);
            translated.ifPresent(t -> results.put(key, t));
        }

        return results;
    }

    @Override
    public boolean isAvailable() {
        return properties.getApi().isEnabled() && StringUtils.hasText(properties.getApi().getUrl());
    }

    /**
     * 使用WebClient发送请求
     */
    private String sendRequestWithWebClient(Map<String, Object> requestBody) throws TimeoutException {
        Duration timeout = properties.getApi().getTimeout();
        if (timeout == null) {
            timeout = Duration.ofSeconds(5);
        }
        
        try {
            return webClient.post()
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(timeout)
                    .retry(properties.getApi().getRetryCount())
                    .block();
        } catch (WebClientResponseException e) {
            log.error("Translation API returned error: {}", e.getResponseBodyAsString(), e);
            throw e;
        }
    }

    /**
     * 使用RestClient发送请求
     */
    private String sendRequestWithRestClient(Map<String, Object> requestBody) throws JsonProcessingException {
        String requestBodyJson = objectMapper.writeValueAsString(requestBody);
        
        return restClient.post()
                .contentType(MediaType.APPLICATION_JSON)
                .body(requestBodyJson)
                .retrieve()
                .body(String.class);
    }
}