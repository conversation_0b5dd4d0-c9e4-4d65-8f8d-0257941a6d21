---
title: Trina Redis Starter 使用指南
description: Trina Redis Starter 组件的快速开始、配置方法和分布式锁场景使用说明
---

# 快速开始

## 依赖引入
在 `pom.xml` 中添加如下依赖：

```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-redis-starter</artifactId>
    <version>4.0.0</version>
</dependency>
```

## 基本配置（application.yml）
```yaml
spring:
  redis:
    host: 127.0.0.1
    port: 6379
    password: yourpassword
    database: 0
  redis:
    redisson:
      config: classpath:redisson-single.yml # 支持yml/json配置
```

---

# 核心功能

## RedisUtils 工具类常用方法

- KV 操作：
  ```java
  RedisUtils.set("key", "value");
  Object val = RedisUtils.get("key");
  RedisUtils.expire("key", 60); // 设置过期60秒
  RedisUtils.del("key");
  ```
- Hash 操作：
  ```java
  RedisUtils.hset("user:1", "name", "张三");
  Object name = RedisUtils.hget("user:1", "name");
  Map<Object, Object> all = RedisUtils.hmget("user:1");
  ```
- Set/List 操作：
  ```java
  RedisUtils.sSet("tags", "java", "redis");
  Set<Object> tags = RedisUtils.sGet("tags");
  RedisUtils.lSet("queue", "task1");
  List<Object> tasks = RedisUtils.lGet("queue", 0, -1);
  ```

---

# 分布式锁场景

## Redisson 分布式锁用法

```java
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

@Autowired
private RedissonClient redissonClient;

public void doBusiness() {
    RLock lock = redissonClient.getLock("my-lock-key");
    boolean locked = false;
    try {
        locked = lock.tryLock(10, 60, TimeUnit.SECONDS); // 最多等待10秒，锁定60秒
        if (locked) {
            // 业务逻辑
        }
    } catch (Exception e) {
        // 异常处理
    } finally {
        if (locked) {
            lock.unlock();
        }
    }
}
```

**说明：**
- 支持可重入、自动续期、锁失效自动释放。
- 推荐锁粒度细化，避免死锁。

---

# 常见问题

- 启动报错：检查 Redis 服务是否可用，配置项是否正确。
- 分布式锁无效：确认 Redisson 配置已生效，锁 key 唯一。
- 序列化异常：可自定义序列化器，确保对象可序列化。

---

# 最佳实践

- 推荐所有缓存 key 加业务前缀，防止冲突。
- 分布式锁建议 tryLock + finally 解锁，避免死锁。
- 结合 Spring AOP 可实现注解式分布式锁。
- 生产环境建议开启 Redis 持久化与高可用。

---

# 参考资料
- [Redisson 官方文档](https://github.com/redisson/redisson/wiki)
- [Spring Data Redis 文档](https://docs.spring.io/spring-data/redis/docs/current/reference/html/) 