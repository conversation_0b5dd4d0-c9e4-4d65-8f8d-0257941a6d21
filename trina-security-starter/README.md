# Trina Security Starter

Trina Security Starter 是一个统一的认证和权限管理模块，提供了完整的API级接口功能权限和列级数据权限控制。

## 功能特性

### 1. 认证功能
- 基于OAuth2的统一认证
- Token缓存机制
- 用户信息管理

### 2. 功能权限
- 基于角色的访问控制（RBAC）
- 基于权限的访问控制
- 支持方法级和类级权限控制
- 灵活的逻辑关系配置（AND/OR）
- @HasRole注解：基于角色的访问控制，支持单个/多个角色，支持AND/OR逻辑 
- @HasPermission注解：基于权限的访问控制，支持单个/多个权限，支持AND/OR逻辑 
- PermissionAspect：AOP拦截器，自动验证用户角色和权限

### 3. 数据权限
- 列级数据权限控制
- 基于角色的字段过滤
- 支持黑名单和白名单模式
- @ColPermission注解：列级权限控制，支持黑名单/白名单模式
- ColumnPermissionAspect：AOP拦截器，自动过滤返回数据中的敏感字段

### 4. 核心组件

- 扩展的IamUserInfo：增加了角色、权限、数据权限范围信息
- DataScope：数据权限范围模型，支持多种权限类型
- PermissionService：权限服务接口及默认实现
- SecurityUtils：权限工具类，提供便捷的权限检查方法

### 5.架构特点

1. 模块化设计：各组件职责清晰，易于扩展和维护 
2. 注解驱动：使用注解方式，代码简洁，易于使用 
3. 缓存优化：权限信息缓存，减少远程调用，提高性能 
4. 灵活配置：支持多环境配置，可根据需求调整 
5. 异常处理：统一的安全异常处理机制

### 6.权限验证流程时序图

权限验证流程按照您提供的时序图实现：

1. 客户端携带token访问接口 
2. AOP拦截器获取用户信息和权限 
3. 验证角色/功能权限 
4. 执行业务逻辑 
5. 列级权限过滤敏感字段 
6. 返回过滤后的结果

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Interceptor as SecurityInterceptor
    participant Cache as iamCache
    participant IAM as IAM服务器
    participant PermAPI as 权限接口
    participant Context as SecurityContextHolder
    participant AOP as 权限切面

    Client->>Interceptor: 1. 请求携带Token
    Interceptor->>Cache: 2. 检查Token缓存
    
    alt Token缓存命中
        Cache-->>Interceptor: 3. 返回userId
        Interceptor->>Context: 4. 构建SecurityContext(基础信息)
    else Token缓存未命中
        Interceptor->>IAM: 5. 远程验证Token获取用户信息
        IAM-->>Interceptor: 6. 返回用户基本信息
        Interceptor->>Context: 7. 构建SecurityContext(完整信息)
        Interceptor->>Cache: 8. 缓存Token验证结果(只存userId)
    end
    
    Interceptor->>PermAPI: 9. 统一获取用户权限数据
    PermAPI-->>Interceptor: 10. 返回{"roles":[],"permissions":[],"dataScopes":[]}
    Interceptor->>Context: 11. 填充权限信息到SecurityContext
    
    Client->>AOP: 12. 调用业务方法
    AOP->>Context: 13. 获取SecurityContext验证权限
    
    alt 权限验证失败
        AOP-->>Client: 14. 抛出SecurityException
    else 权限验证成功
        AOP->>AOP: 15. 执行业务逻辑
        AOP->>AOP: 16. 列级权限过滤(如有@ColPermission)
        AOP-->>Client: 17. 返回结果
    end
    
    Note over Interceptor: 请求结束时清理SecurityContext
```


## 快速开始

### 1. 添加依赖

在你的项目中添加 trina-security-starter 依赖：

```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-security-starter</artifactId>
    <version>4.0.0</version>
</dependency>
```

### 2. 启用安全功能

在主类上添加 `@EnableSecurity` 注解：

```java
@SpringBootApplication
@EnableSecurity
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

### 3. 配置文件

```yaml
trina:
  iam:
    env-url: https://your-iam-server.com
    client-id: your-client-id
    client-secret: your-client-secret
    redirect-url: http://your-app.com/callback
    white-uris:
      - /api/public/**
      - /health
```

## 使用指南

### 功能权限控制

#### 1. 角色权限注解 @HasRole

```java
@RestController
@RequestMapping("/api/users")
public class UserController {

    // 需要admin角色
    @HasRole("admin")
    @GetMapping("/admin")
    public R<List<User>> getAdminUsers() {
        // 管理员用户列表
    }

    // 需要admin或manager角色之一
    @HasRole({"admin", "manager"})
    @GetMapping("/manage")
    public R<List<User>> getManageUsers() {
        // 管理用户列表
    }

    // 需要同时拥有admin和manager角色
    @HasRole(value = {"admin", "manager"}, logical = HasRole.Logical.AND)
    @GetMapping("/super")
    public R<List<User>> getSuperUsers() {
        // 超级管理用户列表
    }
}
```

#### 2. 功能权限注解 @HasPermission

```java
@RestController
@RequestMapping("/api/users")
public class UserController {

    // 需要user:add权限
    @HasPermission("user:add")
    @PostMapping
    public R<User> addUser(@RequestBody User user) {
        // 添加用户
    }

    // 需要user:edit或user:add权限之一
    @HasPermission({"user:edit", "user:add"})
    @PutMapping("/{id}")
    public R<User> updateUser(@PathVariable Long id, @RequestBody User user) {
        // 更新用户
    }

    // 需要同时拥有user:delete和user:admin权限
    @HasPermission(value = {"user:delete", "user:admin"}, logical = HasPermission.Logical.AND)
    @DeleteMapping("/{id}")
    public R<Void> deleteUser(@PathVariable Long id) {
        // 删除用户
    }
}
```

### 列级数据权限控制

#### 1. 列权限注解 @ColPermission

```java
@RestController
@RequestMapping("/api/employees")
public class EmployeeController {

    // general角色不能看到salary和bonus字段
    @ColPermission(role = "general", exc = {"salary", "bonus"})
    @GetMapping
    public R<List<Employee>> getEmployees() {
        // 返回员工列表，general角色看不到薪资信息
    }

    // admin角色只能看到salary和bonus字段
    @ColPermission(role = "admin", inc = {"salary", "bonus"}, mode = ColPermission.Mode.INCLUDE)
    @GetMapping("/salary")
    public R<List<Employee>> getEmployeeSalaries() {
        // 返回薪资信息，admin角色只能看到薪资相关字段
    }

    // 多个角色的字段过滤
    @ColPermission(roles = {"general", "user"}, exc = {"salary", "bonus", "phone"})
    @GetMapping("/public")
    public R<List<Employee>> getPublicEmployees() {
        // general和user角色看不到敏感信息
    }
}
```

### 工具类使用

```java
@Service
public class UserService {

    public void someBusinessMethod() {
        // 获取当前用户信息
        IamUserInfo currentUser = SecurityUtils.getCurrentUser();
        
        // 检查当前用户角色
        if (SecurityUtils.hasRole("admin")) {
            // 管理员逻辑
        }
        
        // 检查当前用户权限
        if (SecurityUtils.hasPermission("user:edit")) {
            // 有编辑权限的逻辑
        }
        
        // 获取当前用户ID
        String userId = SecurityUtils.getCurrentUserId();
        
        // 检查数据权限
        if (SecurityUtils.hasAllDataPermission()) {
            // 全部数据权限逻辑
        }
    }
}
```

## 自定义权限服务

如果需要自定义权限获取逻辑，可以实现 `PermissionService` 接口：

```java
@Service
@Primary
public class CustomPermissionServiceImpl implements PermissionService {

    @Override
    public Set<String> getUserRoles(String userId) {
        // 从你的权限管理系统获取用户角色
        return roleService.getRolesByUserId(userId);
    }

    @Override
    public Set<String> getUserPermissions(String userId) {
        // 从你的权限管理系统获取用户权限
        return permissionService.getPermissionsByUserId(userId);
    }

    // 实现其他方法...
}
```

## 配置说明

### 缓存配置

```yaml
trina:
  cache:
    caffeine:
      iam:
        enabled: true
        initial-capacity: 512
        maximum-size: 10000
        expire-after-write: PT10M
        expire-after-access: PT30M
```

### 白名单配置

```yaml
trina:
  iam:
    white-uris:
      - /api/public/**
      - /health
      - /actuator/**
      - /swagger-ui/**
      - /v3/api-docs/**
```

## 注意事项

1. **权限缓存**：用户权限信息会被缓存，如果权限发生变化，需要调用 `PermissionService.refreshUserPermissions()` 刷新缓存。

2. **列级权限**：列级权限过滤基于JSON序列化，对于复杂对象结构可能需要特殊处理。

3. **性能考虑**：在高并发场景下，建议适当调整缓存配置以平衡性能和内存使用。

4. **安全性**：敏感操作建议使用 `logical = Logical.AND` 要求多个权限同时满足。

## 异常处理

权限验证失败时会抛出 `SecurityException`，建议在全局异常处理器中统一处理：

```java
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(SecurityException.class)
    public R<Void> handleSecurityException(SecurityException e) {
        return R.failed(403, e.getMessage());
    }
}
```