---
title: MongoDB 模块架构
description: Trina MongoDB 模块的架构设计与实现原理
---

# MongoDB 模块架构

## 1. 整体架构

Trina MongoDB 模块基于 Spring Data MongoDB，提供了一套轻量级的 MongoDB 集成解决方案。该模块采用分层架构设计，包含配置层、仓储层、服务层和控制层，各层职责明确，接口规范统一。

```
+---------------------------+
|      API/Controller层     |
| BaseMongoController       |
+---------------------------+
              ↓
+---------------------------+
|        Service层          |
| BaseMongoService          |
+---------------------------+
              ↓
+---------------------------+
|      Repository层         |
| BaseMongoRepository       |
+---------------------------+
              ↓
+---------------------------+
|     MongoDB Client 层     |
| MongoTemplate             |
+---------------------------+
```

## 2. 核心组件

### 2.1 基础实体

所有 MongoDB 文档实体都继承自 `BaseMongoEntity`，它定义了文档的通用属性：

```java
public class BaseMongoEntity implements Serializable {
    @Id
    private String id;                // 文档ID
    
    @CreatedDate
    @Field("create_time")
    private LocalDateTime createTime; // 创建时间
    
    @LastModifiedDate
    @Field("update_time")
    private LocalDateTime updateTime; // 更新时间
    
    @CreatedBy
    @Field("create_by")
    private String createBy;          // 创建人ID
    
    @LastModifiedBy
    @Field("update_by")
    private String updateBy;          // 更新人ID
    
    @Field("deleted")
    private Integer deleted = 0;      // 逻辑删除标识(0-未删除，1-已删除)
    
    // Getter、Setter 和其他方法...
    
    public void prePersist() {        // 持久化前的处理
        // 设置创建/更新时间和删除标识
    }
}
```

### 2.2 查询构建器

`BaseMongoQuery` 类提供了流式 API 构建 MongoDB 查询条件，支持各种常见的查询操作：

```java
public class BaseMongoQuery<T extends BaseMongoEntity> {
    private Integer pageNo = 0;      // 当前页码
    private Integer pageSize = 10;    // 每页大小
    private String sortField;         // 排序字段
    private String sortDirection;     // 排序方向
    private List<MongoCriterion> criterions = new ArrayList<>();  // 查询条件列表
    
    // 构建查询条件和分页参数的方法
    public Query buildQuery() {
        // 构建 MongoDB 查询对象
    }
    
    public Pageable buildPageable() {
        // 构建分页参数
    }
    
    // 各种查询条件方法
    public BaseMongoQuery<T> eq(String fieldName, Object value) {...}
    public BaseMongoQuery<T> ne(String fieldName, Object value) {...}
    public BaseMongoQuery<T> gt(String fieldName, Object value) {...}
    public BaseMongoQuery<T> gte(String fieldName, Object value) {...}
    public BaseMongoQuery<T> lt(String fieldName, Object value) {...}
    public BaseMongoQuery<T> lte(String fieldName, Object value) {...}
    public BaseMongoQuery<T> like(String fieldName, String value) {...}
    public BaseMongoQuery<T> in(String fieldName, List<?> values) {...}
    public BaseMongoQuery<T> between(String fieldName, Object start, Object end) {...}
    public BaseMongoQuery<T> dateBetween(String fieldName, LocalDateTime start, LocalDateTime end) {...}
    public BaseMongoQuery<T> exists(String fieldName) {...}
    public BaseMongoQuery<T> notExists(String fieldName) {...}
    
    // 内部类：查询条件和操作符
    public enum MongoOperator {
        EQ, NE, GT, GTE, LT, LTE, LIKE, IN, BETWEEN, DATE_BETWEEN, EXISTS
    }
    
    public static class MongoCriterion {
        // 条件构建逻辑
    }
}
```

### 2.3 基础仓库

`BaseMongoRepository` 接口定义了 MongoDB 文档的基本 CRUD 操作和自定义查询方法：

```java
@NoRepositoryBean
public interface BaseMongoRepository<T extends BaseMongoEntity> extends MongoRepository<T, String> {
    // 根据自定义查询条件查找文档
    List<T> findByQuery(BaseMongoQuery<T> query);
    
    // 根据自定义查询条件统计文档数量
    long countByQuery(BaseMongoQuery<T> query);
    
    // 根据自定义查询条件删除文档
    long deleteByQuery(BaseMongoQuery<T> query);
    
    // 分页查询
    PageResult<T> findPage(BaseMongoQuery<T> query, Class<T> clazz);
    
    // 逻辑删除文档
    boolean softDelete(String id);
    
    // 批量逻辑删除文档
    long softDeleteBatch(List<String> ids);
    
    // 根据查询条件逻辑删除文档
    long softDeleteByQuery(BaseMongoQuery<T> query);
}
```

实现类 `BaseMongoRepositoryImpl` 提供了这些方法的具体实现，使用 `MongoTemplate` 执行 MongoDB 操作。

### 2.4 基础服务

`BaseMongoService` 接口和 `BaseMongoServiceImpl` 实现类为业务层提供统一的文档操作服务：

```java
public interface BaseMongoService<T extends BaseMongoEntity> {
    // 保存文档
    T save(T entity);
    
    // 批量保存文档
    List<T> saveAll(List<T> entities);
    
    // 根据ID查找文档
    Optional<T> findById(String id);
    
    // 查找所有文档
    List<T> findAll();
    
    // 根据自定义查询条件查找文档
    List<T> findByQuery(BaseMongoQuery<T> query);
    
    // 分页查询
    PageResult<T> findPage(BaseMongoQuery<T> query);
    
    // 根据ID删除文档
    void deleteById(String id);
    
    // 批量删除文档
    void deleteAllById(List<String> ids);
    
    // 根据自定义查询条件删除文档
    long deleteByQuery(BaseMongoQuery<T> query);
    
    // 逻辑删除文档
    boolean softDelete(String id);
    
    // 批量逻辑删除文档
    long softDeleteBatch(List<String> ids);
    
    // 根据自定义查询条件逻辑删除文档
    long softDeleteByQuery(BaseMongoQuery<T> query);
}
```

### 2.5 基础控制器

`BaseMongoController` 提供了通用的 REST API 接口，实现了标准的 CRUD 操作：

```java
public abstract class BaseMongoController<T extends BaseMongoEntity, S extends BaseMongoService<T>> {
    protected final S service;
    
    // 保存文档
    @PostMapping
    public Result<T> save(@RequestBody T entity) {
        return Result.success(service.save(entity));
    }
    
    // 根据ID查找文档
    @GetMapping("/{id}")
    public Result<T> findById(@PathVariable String id) {
        return service.findById(id)
                .map(Result::success)
                .orElseGet(() -> Result.error("文档不存在"));
    }
    
    // 分页查询
    @PostMapping("/page")
    public Result<PageResult<T>> findPage(@RequestBody BaseMongoQuery<T> query) {
        return Result.success(service.findPage(query));
    }
    
    // 根据ID删除文档
    @DeleteMapping("/{id}")
    public Result<Void> deleteById(@PathVariable String id) {
        service.deleteById(id);
        return Result.success();
    }
    
    // 逻辑删除文档
    @PutMapping("/{id}/soft-delete")
    public Result<Boolean> softDelete(@PathVariable String id) {
        return Result.success(service.softDelete(id));
    }
    
    // 其他接口...
}
```

## 3. 自动配置

### 3.1 配置属性

`MongodbProperties` 类定义了 MongoDB 连接的各种配置属性：

```java
@ConfigurationProperties(prefix = "trina.mongodb")
public class MongodbProperties {
    private String uri;                       // MongoDB连接URI
    private String database;                  // 数据库名称
    private String host = "localhost";        // 主机地址
    private int port = 27017;                 // 端口号
    private String username;                  // 用户名
    private String password;                  // 密码
    private Duration connectTimeout = Duration.ofSeconds(10);  // 连接超时时间
    private Duration socketTimeout = Duration.ofSeconds(30);   // 套接字超时时间
    private int maxConnectionPoolSize = 100;  // 最大连接数
    private int minConnectionPoolSize = 0;    // 最小连接数
    private boolean enableTransaction = false; // 是否启用事务
    private String replicaSet;                // 副本集名称
    private String authenticationDatabase = "admin";  // 认证数据库
    private String authenticationMechanism;   // 认证机制
    private boolean ssl = false;              // 是否启用SSL
    
    // Getter 和 Setter 方法...
}
```

### 3.2 自动配置类

`MongodbAutoConfiguration` 类根据配置属性自动装配 MongoDB 客户端和相关组件：

```java
@AutoConfiguration
@EnableConfigurationProperties(MongodbProperties.class)
@EnableMongoRepositories(basePackages = "com.trinasolar.**.repository")
public class MongodbAutoConfiguration {
    private final MongodbProperties properties;
    
    // 创建MongoDB客户端
    @Bean
    @ConditionalOnMissingBean
    public MongoClient mongoClient() {
        // 根据配置创建MongoDB客户端
    }
    
    // 创建MongoDB数据库工厂
    @Bean
    @ConditionalOnMissingBean
    public MongoDatabaseFactory mongoDbFactory() {
        return new SimpleMongoClientDatabaseFactory(mongoClient(), getDatabaseName());
    }
    
    // 创建MongoDB操作模板
    @Bean
    @ConditionalOnMissingBean
    public MongoTemplate mongoTemplate() {
        MongoDatabaseFactory factory = mongoDbFactory();
        
        // 创建自定义的Converter，去除_class字段
        MappingMongoConverter converter = new MappingMongoConverter(
                new DefaultDbRefResolver(factory), 
                new MongoMappingContext()
        );
        converter.setTypeMapper(new DefaultMongoTypeMapper(null));
        
        return new MongoTemplate(factory, converter);
    }
    
    // 创建MongoDB事务管理器（当启用事务时）
    @Bean
    @ConditionalOnProperty(name = "trina.mongodb.enable-transaction", havingValue = "true")
    public MongoTransactionManager transactionManager(MongoDatabaseFactory dbFactory) {
        return new MongoTransactionManager(dbFactory);
    }
}
```

## 4. 分页和查询结果

### 4.1 分页结果

`PageResult` 类封装了分页查询的结果：

```java
public class PageResult<T> {
    private long total;        // 总记录数
    private List<T> records;   // 当前页的记录列表
    private int pageNo;        // 当前页码
    private int pageSize;      // 每页大小
    private int totalPages;    // 总页数
    
    // 构造方法、Getter和Setter方法...
    
    // 从Spring Data的Page对象创建PageResult
    public static <T> PageResult<T> of(Page<T> page) {
        // 转换逻辑
    }
    
    // 创建空结果
    public static <T> PageResult<T> empty() {
        // 创建空结果的逻辑
    }
}
```

## 5. 设计思想

### 5.1 数据隔离

Trina MongoDB 模块通过以下机制实现数据隔离：

1. **逻辑删除**：所有文档默认包含 `deleted` 字段，通过将此字段设置为 1 实现逻辑删除，而不是物理删除数据
2. **创建者/更新者追踪**：记录文档的创建者和更新者，便于审计和权限控制
3. **自动时间戳**：自动记录文档的创建时间和更新时间

### 5.2 易扩展性

模块设计考虑了易扩展性：

1. **通用基类**：提供通用的基础实体、仓库、服务和控制器，大幅减少重复代码
2. **组件解耦**：各层之间通过接口进行解耦，便于替换和扩展
3. **灵活配置**：通过配置属性可以灵活调整 MongoDB 连接参数

### 5.3 查询优化

提供了高效的查询构建机制：

1. **流式 API**：`BaseMongoQuery` 提供流式 API，便于构建复杂查询
2. **条件封装**：将查询条件封装为独立对象，便于重用和组合
3. **自动处理空值**：查询方法自动忽略空值参数，减少冗余代码

## 6. 与 Spring Data MongoDB 的关系

Trina MongoDB 模块是对 Spring Data MongoDB 的扩展和封装：

1. **保留原生能力**：完全兼容 Spring Data MongoDB 的查询和操作功能
2. **增强易用性**：添加了更加易用的查询构建器和通用 CRUD 操作
3. **统一规范**：统一了文档结构和操作接口，符合 Trina 框架的整体设计风格

## 7. 总结

Trina MongoDB 模块提供了一套完整的 MongoDB 集成解决方案，通过分层设计和通用抽象，大幅简化了 MongoDB 的使用。开发者只需通过继承提供的基类，就能快速构建具备完整 CRUD 功能的 MongoDB 应用，而不必关心底层的连接管理和查询构建细节。
