package com.trinasolar.gateway.security.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;

/**
 * WebClient 配置属性
 * <p>
 * 用于配置 WebClient 的连接池、超时、重试等参数，避免 PrematureCloseException
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@ConfigurationProperties(prefix = "trina.gateway.webclient")
public class WebClientProperties {

    /**
     * 连接池配置
     */
    private ConnectionPool connectionPool = new ConnectionPool();

    /**
     * 超时配置
     */
    private Timeout timeout = new Timeout();

    /**
     * 重试配置
     */
    private Retry retry = new Retry();

    /**
     * 是否启用调试模式（网络日志）
     */
    private boolean debugEnabled = false;

    /**
     * 连接池配置
     */
    @Data
    public static class ConnectionPool {
        /**
         * 连接池名称
         */
        private String name = "gateway-http-pool";

        /**
         * 最大连接数
         */
        private int maxConnections = 100;

        /**
         * 连接空闲时间
         */
        private Duration maxIdleTime = Duration.ofSeconds(30);

        /**
         * 连接最大生存时间
         */
        private Duration maxLifeTime = Duration.ofMinutes(5);

        /**
         * 获取连接超时时间
         */
        private Duration pendingAcquireTimeout = Duration.ofSeconds(10);

        /**
         * 后台清理间隔
         */
        private Duration evictInBackground = Duration.ofSeconds(120);
    }

    /**
     * 超时配置
     */
    @Data
    public static class Timeout {
        /**
         * 连接超时时间（毫秒）
         */
        private int connectTimeout = 10000;

        /**
         * 响应超时时间
         */
        private Duration responseTimeout = Duration.ofSeconds(30);

        /**
         * Token 请求超时时间
         */
        private Duration tokenTimeout = Duration.ofSeconds(15);

        /**
         * Token 验证超时时间
         */
        private Duration tokenVerifyTimeout = Duration.ofSeconds(15);

        /**
         * 权限加载超时时间
         */
        private Duration permissionTimeout = Duration.ofSeconds(10);

        /**
         * 菜单加载超时时间
         */
        private Duration menuTimeout = Duration.ofSeconds(10);

        /**
         * 注销请求超时时间
         */
        private Duration logoutTimeout = Duration.ofSeconds(10);
    }

    /**
     * 重试配置
     */
    @Data
    public static class Retry {
        /**
         * Token 请求重试次数
         */
        private int tokenRetryAttempts = 2;

        /**
         * Token 验证重试次数
         */
        private int tokenVerifyRetryAttempts = 3;

        /**
         * 权限加载重试次数
         */
        private int permissionRetryAttempts = 2;

        /**
         * 菜单加载重试次数
         */
        private int menuRetryAttempts = 2;

        /**
         * 注销请求重试次数
         */
        private int logoutRetryAttempts = 2;

        /**
         * API路径数据加载重试次数
         */
        private int apiPathRetryAttempts = 2;

        /**
         * 重试初始延迟时间
         */
        private Duration initialDelay = Duration.ofMillis(300);

        /**
         * Token 验证重试初始延迟时间
         */
        private Duration tokenVerifyInitialDelay = Duration.ofMillis(500);
    }
}
