spring:
  application:
    # 应用分组，用于nacos服务发现和配置
    group: ${NACOS_GROUP:DEFAULT_GROUP}
    # 应用命名空间，用于nacos服务发现和配置
    namespace: ${spring.application.name}-${spring.profiles.active}
  # nacos 基础配置
  config:
    import:
      - optional:nacos:application-${spring.profiles.active:dev}.yaml
      - optional:nacos:${spring.application.name:application}-${spring.profiles.active:dev}.yaml
  cloud:
    nacos:
      # 认证信息
      username: ${NACOS_USERNAME:nacos}
      password: ${NACOS_PASSWORD:nacos}
      # 服务发现配置
      discovery:
        server-addr: ${NACOS_HOST:127.0.0.1}:${NACOS_PORT:8848}
        # namespace会在NacosEnvironmentProcessor中自动设置
        namespace: ${spring.application.namespace:public}
        group: ${spring.application.group:DEFAULT_GROUP}
        enabled: true
        register-enabled: true
        # 心跳间隔
        heart-beat-interval: 5000
        # 心跳超时时间
        heart-beat-timeout: 15000
        # IP删除超时时间
        ip-delete-timeout: 30000
      # 配置中心配置
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        # namespace会在NacosEnvironmentProcessor中自动设置
        namespace: ${spring.cloud.nacos.discovery.namespace}
        group: ${spring.cloud.nacos.discovery.group}
        file-extension: ${NACOS_FILE_EXTENSION:yaml}
        refresh-enabled: true
        # 配置长轮询超时时间
        timeout: 3000
        # 最大重试次数
        max-retry: 10
        # 配置监听器长轮询超时时间
        config-long-poll-timeout: 46000
        # 配置重试时间
        config-retry-time: 2333
        # 启用远程同步配置
        enable-remote-sync-config: false
        # 扩展配置（应用特定配置）
        extension-configs:
          - data-id: ${spring.application.name:application}-${spring.profiles.active:dev}.${spring.cloud.nacos.config.file-extension:yaml}
            group: ${spring.application.group:DEFAULT_GROUP}
            refresh: true
        # 共享配置（通用配置）
        shared-configs:
          - data-id: common-config-${spring.profiles.active:dev}.${spring.cloud.nacos.config.file-extension:yaml}
            group: ${spring.application.group:DEFAULT_GROUP}
            refresh: true
# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always