# Remote模块架构设计

## 1. 概述

Remote模块是基于Spring 6新特性HTTP Interface实现的微服务远程调用框架，提供了声明式的HTTP客户端，用于简化微服务间的通信。相比传统的OpenFeign，它基于Spring原生支持，性能更好，使用更简洁。

## 2. 核心架构

![Remote模块架构图](../images/remote-architecture.svg)

Remote模块的核心架构包括以下几个部分：

1. **注解驱动层**：通过`@RemoteService`和`@EnableRemoteServices`注解提供声明式服务定义
2. **服务注册与发现**：自动扫描和注册远程服务接口
3. **代理生成层**：基于Spring HTTP Interface生成代理实现
4. **HTTP客户端适配层**：支持多种客户端实现（WebClient、RestClient、RestTemplate）
5. **拦截器链**：提供可扩展的拦截器机制

## 3. 核心组件

### 3.1 核心注解

- `@RemoteService`：标记接口为远程服务，配置服务名称、基础URL、超时等属性
- `@EnableRemoteServices`：启用远程服务扫描，支持指定扫描包路径

### 3.2 工厂与注册器

- `RemoteServiceFactory`：负责创建远程服务接口的代理实现
- `RemoteServiceFactoryBean`：Spring FactoryBean，用于注册服务接口的代理实现
- `RemoteServiceRegistrar`：基于`ImportBeanDefinitionRegistrar`实现的注册器，扫描并注册远程服务接口

### 3.3 拦截器体系

- `RemoteRequestInterceptor`：拦截器接口，定义拦截器的基本行为
- `HeaderPropagationInterceptor`：请求头传递拦截器，将当前请求头传递到远程调用
- `LoggingInterceptor`：日志拦截器，记录请求和响应的详细信息

### 3.4 配置类

- `RemoteProperties`：配置属性类，管理Remote模块的全局配置
- `RemoteAutoConfiguration`：自动配置类，配置各种Bean

## 4. 工作流程

1. **启动阶段**：
   - 应用启动时，`@EnableRemoteServices`注解触发`RemoteServiceRegistrar`
   - `RemoteServiceRegistrar`扫描带有`@RemoteService`注解的接口
   - 为每个接口创建`RemoteServiceFactoryBean`并注册到Spring容器

2. **Bean初始化阶段**：
   - `RemoteServiceFactoryBean`调用`RemoteServiceFactory`创建代理
   - `RemoteServiceFactory`根据配置选择客户端类型（WebClient/RestClient/RestTemplate）
   - 创建对应的`HttpServiceProxyFactory`并应用拦截器

3. **运行时阶段**：
   - 请求通过代理对象转换为HTTP请求
   - 拦截器链处理请求（传递请求头、日志记录等）
   - 通过选定的HTTP客户端执行请求
   - 将响应转换为对应的返回类型

## 5. 扩展点

Remote模块提供了多个扩展点，可以根据需要进行自定义：

1. **自定义拦截器**：实现`RemoteRequestInterceptor`接口
2. **自定义客户端配置**：通过`RemoteProperties`配置不同的客户端行为
3. **自定义错误处理**：通过配置不同客户端的错误处理器
4. **自定义负载均衡策略**：与Spring Cloud集成，使用不同的负载均衡策略

## 6. 与其他模块的关系

Remote模块是一个基础设施模块，主要与以下模块有关系：

- **Security模块**：通过请求头传递认证信息
- **Web模块**：与Web环境集成，传递请求上下文
- **Cloud模块**：与服务发现、负载均衡集成

## 7. 兼容性与限制

- 依赖Spring Boot 3和Java 17
- HTTP Interface是Spring 6.0引入的新特性
- 不同类型的客户端支持不同的功能：
  - WebClient：支持反应式编程，适合高并发场景
  - RestClient：Spring 6.1新增的同步客户端，API更现代
  - RestTemplate：传统的同步客户端，向后兼容

## 8. 未来规划

1. 支持更多的客户端类型
2. 增强与服务网格的集成
3. 增加断路器和限流功能
4. 增加指标收集和监控功能