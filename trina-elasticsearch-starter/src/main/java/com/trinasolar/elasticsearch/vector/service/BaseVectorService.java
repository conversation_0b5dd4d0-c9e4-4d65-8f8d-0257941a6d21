package com.trinasolar.elasticsearch.vector.service;

import com.trinasolar.elasticsearch.base.service.BaseEsService;
import com.trinasolar.elasticsearch.vector.entity.BaseVectorEntity;
import com.trinasolar.elasticsearch.vector.wrapper.VectorQueryWrapper;
import com.trinasolar.elasticsearch.vo.SearchResult;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 向量搜索基础Service接口
 * 扩展BaseEsService，提供向量搜索相关的操作方法
 * 
 * @param <T> 实体类型，必须继承BaseVectorEntity
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface BaseVectorService<T extends BaseVectorEntity> extends BaseEsService<T> {
    
    // ==================== 向量搜索 ====================
    
    /**
     * 向量相似性搜索
     * 
     * @param fieldName 向量字段名
     * @param queryVector 查询向量
     * @param k 返回的相似文档数量
     * @return 搜索结果
     */
    SearchResult<T> vectorSearch(String fieldName, float[] queryVector, int k);
    
    /**
     * 向量相似性搜索（带候选数量控制）
     * 
     * @param fieldName 向量字段名
     * @param queryVector 查询向量
     * @param k 返回的相似文档数量
     * @param numCandidates 候选数量
     * @return 搜索结果
     */
    SearchResult<T> vectorSearch(String fieldName, float[] queryVector, int k, int numCandidates);
    
    /**
     * 带过滤条件的向量搜索
     * 
     * @param fieldName 向量字段名
     * @param queryVector 查询向量
     * @param k 返回的相似文档数量
     * @param filterWrapper 过滤条件
     * @return 搜索结果
     */
    SearchResult<T> vectorSearchWithFilter(String fieldName, float[] queryVector, int k, 
                                          VectorQueryWrapper<T> filterWrapper);
    
    /**
     * 混合搜索（文本+向量）
     * 
     * @param textQuery 文本查询条件
     * @param vectorField 向量字段名
     * @param queryVector 查询向量
     * @param k 返回的相似文档数量
     * @param textWeight 文本搜索权重
     * @param vectorWeight 向量搜索权重
     * @return 搜索结果
     */
    SearchResult<T> hybridSearch(String textQuery, String vectorField, float[] queryVector, 
                                int k, float textWeight, float vectorWeight);
    
    /**
     * 批量向量搜索
     * 
     * @param fieldName 向量字段名
     * @param queryVectors 查询向量列表
     * @param k 每个向量返回的相似文档数量
     * @return 搜索结果列表
     */
    List<SearchResult<T>> batchVectorSearch(String fieldName, List<float[]> queryVectors, int k);
    
    /**
     * 使用向量查询包装器进行搜索
     * 
     * @param vectorWrapper 向量查询包装器
     * @return 搜索结果
     */
    SearchResult<T> vectorSearch(VectorQueryWrapper<T> vectorWrapper);
    
    // ==================== 向量管理 ====================
    
    /**
     * 更新文档的向量字段
     * 
     * @param docId 文档ID
     * @param fieldName 向量字段名
     * @param vector 新的向量值
     * @return 是否更新成功
     * @throws IOException IO异常
     */
    boolean updateVector(String docId, String fieldName, float[] vector);
    
    /**
     * 批量更新向量
     * 
     * @param vectorUpdates 向量更新映射 (docId -> fieldName -> vector)
     * @return 是否更新成功
     * @throws IOException IO异常
     */
    boolean updateVectorsBatch(Map<String, Map<String, float[]>> vectorUpdates);
    
    /**
     * 获取文档的向量值
     * 
     * @param docId 文档ID
     * @param fieldName 向量字段名
     * @return 向量值
     * @throws IOException IO异常
     */
    float[] getVector(String docId, String fieldName);
    
    /**
     * 计算两个向量的相似度
     * 
     * @param vector1 向量1
     * @param vector2 向量2
     * @param similarity 相似度算法
     * @return 相似度分数
     */
    double calculateSimilarity(float[] vector1, float[] vector2, String similarity);
    
    // ==================== 向量索引管理 ====================
    
    /**
     * 重建向量索引
     * 
     * @return 是否重建成功
     * @throws IOException IO异常
     */
    boolean reindexVectors();
    
    /**
     * 优化向量索引
     * 
     * @return 是否优化成功
     * @throws IOException IO异常
     */
    boolean optimizeVectorIndex();
    
    /**
     * 获取向量索引统计信息
     * 
     * @return 索引统计信息
     * @throws IOException IO异常
     */
    Map<String, Object> getVectorIndexStats();
    
    /**
     * 验证向量字段配置
     * 
     * @param fieldName 向量字段名
     * @return 是否配置有效
     */
    boolean validateVectorField(String fieldName);
    
    // ==================== 向量质量评估 ====================
    
    /**
     * 评估向量搜索质量
     * 
     * @param testQueries 测试查询列表
     * @param expectedResults 期望结果列表
     * @return 质量评估结果
     */
    Map<String, Double> evaluateVectorSearchQuality(List<float[]> testQueries, 
                                                    List<List<String>> expectedResults);
    
    /**
     * 分析向量分布
     * 
     * @param fieldName 向量字段名
     * @param sampleSize 样本大小
     * @return 分布分析结果
     * @throws IOException IO异常
     */
    Map<String, Object> analyzeVectorDistribution(String fieldName, int sampleSize);
    
    // ==================== 向量缓存 ====================
    
    /**
     * 缓存热点向量查询结果
     * 
     * @param queryKey 查询键
     * @param result 查询结果
     * @param ttlSeconds 缓存时间（秒）
     */
    void cacheVectorSearchResult(String queryKey, SearchResult<T> result, long ttlSeconds);
    
    /**
     * 获取缓存的向量查询结果
     * 
     * @param queryKey 查询键
     * @return 缓存的查询结果
     */
    SearchResult<T> getCachedVectorSearchResult(String queryKey);
    
    /**
     * 清除向量搜索缓存
     * 
     * @param pattern 缓存键模式
     */
    void clearVectorSearchCache(String pattern);
}