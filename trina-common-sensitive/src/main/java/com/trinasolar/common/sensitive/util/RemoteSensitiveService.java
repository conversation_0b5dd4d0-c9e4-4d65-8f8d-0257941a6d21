package com.trinasolar.common.sensitive.util;

import com.trinasolar.common.core.base.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/7
 */
@FeignClient(contextId = "remoteSensitiveService", value = ServiceNameConstants.UPMS_SERVICE)
public interface RemoteSensitiveService {

    @GetMapping("/sysSensitiveWord/remote/list/{type}")
    R<List<String>> list(@PathVariable String type);
}
