package com.trinasolar.mongodb.base;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * MongoDB基础查询类
 * 用于构建MongoDB查询条件
 *
 * @param <T> 实体类型
 */
public class BaseMongoQuery<T extends BaseMongoEntity> {

    /**
     * 当前页码（从0开始）
     */
    private Integer pageNo = 0;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /**
     * 排序字段
     */
    private String sortField;

    /**
     * 排序方向（asc/desc）
     */
    private String sortDirection;

    /**
     * 查询条件列表
     */
    private List<MongoCriterion> criterions = new ArrayList<>();

    /**
     * 构建分页参数
     *
     * @return 分页参数对象
     */
    public Pageable buildPageable() {
        Sort sort = null;
        if (StringUtils.hasText(sortField)) {
            sort = Sort.by(
                    Sort.Direction.fromString(
                            StringUtils.hasText(sortDirection) ? sortDirection : "asc"
                    ),
                    sortField
            );
        }
        
        return sort != null ? 
                PageRequest.of(pageNo, pageSize, sort) : 
                PageRequest.of(pageNo, pageSize);
    }

    /**
     * 构建查询条件
     *
     * @return 查询对象
     */
    public Query buildQuery() {
        Query query = new Query();
        
        // 添加非删除条件（deleted = 0）
        query.addCriteria(Criteria.where("deleted").is(0));
        
        // 添加其他查询条件
        if (!criterions.isEmpty()) {
            for (MongoCriterion criterion : criterions) {
                query.addCriteria(criterion.buildCriteria());
            }
        }
        
        // 添加分页排序
        Pageable pageable = buildPageable();
        if (pageable.getSort().isSorted()) {
            query.with(pageable.getSort());
        }
        
        return query;
    }

    /**
     * 添加等于条件
     *
     * @param fieldName 字段名
     * @param value 值
     * @return 当前查询对象
     */
    public BaseMongoQuery<T> eq(String fieldName, Object value) {
        if (value != null) {
            criterions.add(new MongoCriterion(MongoOperator.EQ, fieldName, value));
        }
        return this;
    }

    /**
     * 添加不等于条件
     *
     * @param fieldName 字段名
     * @param value 值
     * @return 当前查询对象
     */
    public BaseMongoQuery<T> ne(String fieldName, Object value) {
        if (value != null) {
            criterions.add(new MongoCriterion(MongoOperator.NE, fieldName, value));
        }
        return this;
    }

    /**
     * 添加大于条件
     *
     * @param fieldName 字段名
     * @param value 值
     * @return 当前查询对象
     */
    public BaseMongoQuery<T> gt(String fieldName, Object value) {
        if (value != null) {
            criterions.add(new MongoCriterion(MongoOperator.GT, fieldName, value));
        }
        return this;
    }

    /**
     * 添加大于等于条件
     *
     * @param fieldName 字段名
     * @param value 值
     * @return 当前查询对象
     */
    public BaseMongoQuery<T> gte(String fieldName, Object value) {
        if (value != null) {
            criterions.add(new MongoCriterion(MongoOperator.GTE, fieldName, value));
        }
        return this;
    }

    /**
     * 添加小于条件
     *
     * @param fieldName 字段名
     * @param value 值
     * @return 当前查询对象
     */
    public BaseMongoQuery<T> lt(String fieldName, Object value) {
        if (value != null) {
            criterions.add(new MongoCriterion(MongoOperator.LT, fieldName, value));
        }
        return this;
    }

    /**
     * 添加小于等于条件
     *
     * @param fieldName 字段名
     * @param value 值
     * @return 当前查询对象
     */
    public BaseMongoQuery<T> lte(String fieldName, Object value) {
        if (value != null) {
            criterions.add(new MongoCriterion(MongoOperator.LTE, fieldName, value));
        }
        return this;
    }

    /**
     * 添加Like条件（使用正则表达式模糊匹配）
     *
     * @param fieldName 字段名
     * @param value 值
     * @return 当前查询对象
     */
    public BaseMongoQuery<T> like(String fieldName, String value) {
        if (StringUtils.hasText(value)) {
            criterions.add(new MongoCriterion(MongoOperator.LIKE, fieldName, value));
        }
        return this;
    }

    /**
     * 添加In条件
     *
     * @param fieldName 字段名
     * @param values 值集合
     * @return 当前查询对象
     */
    public BaseMongoQuery<T> in(String fieldName, List<?> values) {
        if (values != null && !values.isEmpty()) {
            criterions.add(new MongoCriterion(MongoOperator.IN, fieldName, values));
        }
        return this;
    }

    /**
     * 添加范围条件
     *
     * @param fieldName 字段名
     * @param start 开始值
     * @param end 结束值
     * @return 当前查询对象
     */
    public BaseMongoQuery<T> between(String fieldName, Object start, Object end) {
        if (start != null && end != null) {
            criterions.add(new MongoCriterion(MongoOperator.BETWEEN, fieldName, start, end));
        }
        return this;
    }

    /**
     * 添加日期范围条件
     *
     * @param fieldName 字段名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 当前查询对象
     */
    public BaseMongoQuery<T> dateBetween(String fieldName, LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime != null || endTime != null) {
            criterions.add(new MongoCriterion(MongoOperator.DATE_BETWEEN, fieldName, startTime, endTime));
        }
        return this;
    }
    
    /**
     * 添加存在条件（字段必须存在）
     *
     * @param fieldName 字段名
     * @return 当前查询对象
     */
    public BaseMongoQuery<T> exists(String fieldName) {
        if (StringUtils.hasText(fieldName)) {
            criterions.add(new MongoCriterion(MongoOperator.EXISTS, fieldName, true));
        }
        return this;
    }
    
    /**
     * 添加不存在条件（字段必须不存在）
     *
     * @param fieldName 字段名
     * @return 当前查询对象
     */
    public BaseMongoQuery<T> notExists(String fieldName) {
        if (StringUtils.hasText(fieldName)) {
            criterions.add(new MongoCriterion(MongoOperator.EXISTS, fieldName, false));
        }
        return this;
    }

    // Getter and Setter methods
    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo != null && pageNo >= 0 ? pageNo : 0;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize != null && pageSize > 0 ? pageSize : 10;
    }

    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        this.sortField = sortField;
    }

    public String getSortDirection() {
        return sortDirection;
    }

    public void setSortDirection(String sortDirection) {
        this.sortDirection = sortDirection;
    }

    /**
     * 查询条件枚举
     */
    public enum MongoOperator {
        EQ, NE, GT, GTE, LT, LTE, LIKE, IN, BETWEEN, DATE_BETWEEN, EXISTS
    }

    /**
     * MongoDB查询条件类
     */
    public static class MongoCriterion {
        private MongoOperator operator;
        private String fieldName;
        private Object value;
        private Object secondValue;

        public MongoCriterion(MongoOperator operator, String fieldName, Object value) {
            this.operator = operator;
            this.fieldName = fieldName;
            this.value = value;
        }

        public MongoCriterion(MongoOperator operator, String fieldName, Object value, Object secondValue) {
            this.operator = operator;
            this.fieldName = fieldName;
            this.value = value;
            this.secondValue = secondValue;
        }

        /**
         * 构建MongoDB查询条件
         */
        public Criteria buildCriteria() {
            Criteria criteria = Criteria.where(fieldName);
            
            switch (operator) {
                case EQ:
                    return criteria.is(value);
                case NE:
                    return criteria.ne(value);
                case GT:
                    return criteria.gt(value);
                case GTE:
                    return criteria.gte(value);
                case LT:
                    return criteria.lt(value);
                case LTE:
                    return criteria.lte(value);
                case LIKE:
                    // MongoDB使用正则表达式实现模糊查询
                    Pattern pattern = Pattern.compile(value.toString(), Pattern.CASE_INSENSITIVE);
                    return criteria.regex(pattern);
                case IN:
                    return criteria.in((List<?>) value);
                case BETWEEN:
                    return criteria.gte(value).lte(secondValue);
                case DATE_BETWEEN:
                    LocalDateTime startTime = (LocalDateTime) value;
                    LocalDateTime endTime = (LocalDateTime) secondValue;
                    
                    if (startTime != null && endTime != null) {
                        return criteria.gte(startTime).lte(endTime);
                    } else if (startTime != null) {
                        return criteria.gte(startTime);
                    } else {
                        return criteria.lte(endTime);
                    }
                case EXISTS:
                    return criteria.exists((Boolean) value);
                default:
                    return criteria;
            }
        }
    }
} 