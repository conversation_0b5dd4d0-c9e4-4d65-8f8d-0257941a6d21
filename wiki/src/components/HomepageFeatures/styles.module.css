.features {
  display: flex;
  align-items: center;
  padding: 2rem 0;
  width: 100%;
  flex-direction: column;
}

.featureSvg {
  height: 200px;
  width: 200px;
}

/* 技术栈样式 */
.techStack {
  margin-top: 4rem;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 30px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.techStack::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
  pointer-events: none;
}

.techStackTitle {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #ffffff;
  font-weight: 800;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 1;
}

.techStackSubtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 3rem;
  font-weight: 300;
  position: relative;
  z-index: 1;
}

.techCloudContainer {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 3rem;
  margin: 3rem 0;
  position: relative;
  z-index: 1;
}

.techCloudSection {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  transition: all 0.3s ease;
}

.techCloudSection:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.techSectionTitle {
  font-size: 1.5rem;
  color: #ffffff;
  margin-bottom: 1.5rem;
  font-weight: 600;
  text-align: center;
}

.techTagCloud {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  justify-content: center;
  align-items: center;
}

.techTag {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.2rem;
  border-radius: 25px;
  font-weight: 600;
  color: white;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
}

.techTag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.techTag:hover::before {
  left: 100%;
}

.techTag:hover {
  transform: scale(1.1) rotate(2deg);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.techIcon {
  font-size: 1.2rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* 不同尺寸的标签 */
.techTagSmall {
  font-size: 0.85rem;
  padding: 0.6rem 1rem;
}

.techTagMedium {
  font-size: 1rem;
  padding: 0.8rem 1.2rem;
}

.techTagLarge {
  font-size: 1.1rem;
  padding: 1rem 1.5rem;
  font-weight: 700;
}

/* 不同类别的颜色主题 */
.techTagCore {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  border-color: rgba(255, 107, 107, 0.3);
}

.techTagCore:hover {
  background: linear-gradient(135deg, #ff5252, #d63031);
  border-color: rgba(255, 107, 107, 0.6);
}

.techTagData {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  border-color: rgba(78, 205, 196, 0.3);
}

.techTagData:hover {
  background: linear-gradient(135deg, #26d0ce, #2d3748);
  border-color: rgba(78, 205, 196, 0.6);
}

.techTagService {
  background: linear-gradient(135deg, #a8e6cf, #7fcdcd);
  border-color: rgba(168, 230, 207, 0.3);
}

.techTagService:hover {
  background: linear-gradient(135deg, #88d8a3, #6bb6ff);
  border-color: rgba(168, 230, 207, 0.6);
}

.techTagInfra {
  background: linear-gradient(135deg, #ffd93d, #ff6b6b);
  border-color: rgba(255, 217, 61, 0.3);
}

.techTagInfra:hover {
  background: linear-gradient(135deg, #ffbe0b, #fb8500);
  border-color: rgba(255, 217, 61, 0.6);
}

/* 技术统计 */
.techStats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-top: 3rem;
  position: relative;
  z-index: 1;
}

.techStat {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 1.5rem 2rem;
  transition: all 0.3s ease;
}

.techStat:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.2);
}

.techStatNumber {
  font-size: 2.5rem;
  font-weight: 800;
  color: #ffffff;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.techStatLabel {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .techStack {
    padding: 3rem 1rem;
  }
  
  .techStackTitle {
    font-size: 2.5rem;
  }
  
  .techCloudContainer {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .techStats {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .techTag {
    font-size: 0.9rem;
    padding: 0.6rem 1rem;
  }
  
  .techTagLarge {
    font-size: 1rem;
    padding: 0.8rem 1.2rem;
  }
}

/* 核心模块样式 */
.modules {
  margin-top: 4rem;
  padding: 3rem 0;
  text-align: center;
}

.modulesTitle {
  font-size: 2.5rem;
  margin-bottom: 2rem;
  color: #2e3440;
  font-weight: 700;
}

.moduleGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.moduleCard {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  transition: all 0.3s ease;
  text-align: left;
}

.moduleCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #008BD6;
}

.moduleCard h4 {
  color: #008BD6;
  margin-bottom: 1rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.moduleCard p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* 响应式设计 */
@media screen and (max-width: 996px) {
  .techGrid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
  }
  
  .moduleGrid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }
  
  .techStackTitle,
  .modulesTitle {
    font-size: 2rem;
  }
}

@media screen and (max-width: 768px) {
  .techGrid {
    grid-template-columns: 1fr;
  }
  
  .moduleGrid {
    grid-template-columns: 1fr;
  }
  
  .techStack,
  .modules {
    padding: 2rem 0;
    margin-top: 2rem;
  }
  
  .techStackTitle,
  .modulesTitle {
    font-size: 1.8rem;
  }
}
