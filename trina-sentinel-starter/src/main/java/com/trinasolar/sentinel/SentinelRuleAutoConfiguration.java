package com.trinasolar.sentinel;

import com.alibaba.csp.sentinel.slots.block.RuleConstant;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRule;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRuleManager;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import jakarta.annotation.PostConstruct;

import java.util.ArrayList;
import java.util.List;

/**
 * Sentinel规则自动配置类
 * 用于初始化Sentinel的流控规则和熔断规则
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@ConditionalOnProperty(prefix = "spring.cloud.sentinel", name = "enabled", havingValue = "true", matchIfMissing = true)
public class SentinelRuleAutoConfiguration {
    
    private static final Logger logger = LoggerFactory.getLogger(SentinelRuleAutoConfiguration.class);

    public SentinelRuleAutoConfiguration() {
        // 使用默认构造函数
    }

    @PostConstruct
    public void init() {
        // 初始化流控规则
        initFlowRules();
        // 初始化熔断规则
        initDegradeRules();
        
        logger.info("🛡️ Sentinel规则初始化完成");
    }

    /**
     * 初始化流控规则
     * 注意：如果配置了Nacos数据源，这些规则会被Nacos中的规则覆盖
     */
    private void initFlowRules() {
        List<FlowRule> rules = new ArrayList<>();
        
        // 示例：创建一个针对资源"testResource"的流控规则
        FlowRule rule = new FlowRule();
        rule.setResource("testResource");
        rule.setGrade(RuleConstant.FLOW_GRADE_QPS);  // 基于QPS限流
        rule.setCount(5);  // 每秒允许5个请求
        rules.add(rule);
        
        // 加载规则
        FlowRuleManager.loadRules(rules);
        logger.info("✅ 已初始化流控规则: {} 条", rules.size());
    }
    
    /**
     * 初始化熔断规则
     * 注意：如果配置了Nacos数据源，这些规则会被Nacos中的规则覆盖
     */
    private void initDegradeRules() {
        List<DegradeRule> rules = new ArrayList<>();
        
        // 示例：创建一个针对资源"testResource"的熔断规则
        DegradeRule rule = new DegradeRule();
        rule.setResource("testResource");
        rule.setGrade(RuleConstant.DEGRADE_GRADE_EXCEPTION_RATIO);  // 基于异常比例熔断
        rule.setCount(0.5);  // 异常比例阈值，超过50%触发熔断
        rule.setTimeWindow(10);  // 熔断时长，单位为秒
        rules.add(rule);
        
        // 加载规则
        DegradeRuleManager.loadRules(rules);
        logger.info("✅ 已初始化熔断规则: {} 条", rules.size());
    }
}
