---
title: 架构概览
description: Trina Framework 整体架构概览
---

# 架构概览

本文档提供 Trina Framework 的整体架构概览。

## 核心模块

### 基础模块
- **trina-common-core**: 核心工具类和基础功能
- **trina-web-starter**: Web开发基础组件
- **trina-mybatis-starter**: 数据库访问层

### 分布式模块
- **trina-redis-starter**: 缓存解决方案
- **trina-microservice-starter**: 微服务基础框架

### 云服务模块
- **trina-minio-starter**: 对象存储
- **trina-elasticsearch-starter**: 搜索引擎
- **trina-neo4j-starter**: 图数据库

## 技术栈

- **Java 17**: 编程语言
- **Spring Boot 3.5.0**: 应用框架
- **Spring Cloud 2025.0.0**: 微服务框架
- **Spring Cloud Alibaba 2022.0.0.0**: 阿里云微服务组件
- **MyBatis Plus 3.5.10**: ORM框架
- **MySQL 8.3.0**: 关系型数据库
- **Elasticsearch 9.0.0**: 搜索引擎

## 设计原则

1. **模块化**: 每个功能独立成模块
2. **可扩展**: 支持自定义扩展
3. **高性能**: 优化性能和资源使用
4. **易用性**: 简化配置和使用

## 架构图

```mermaid
graph TD
    A[业务应用] --> B[trina-parent]
    B --> C[trina-common-core]
    
    C --> D[trina-web-starter]
    C --> E[trina-redis-starter]
    C --> F[trina-mybatis-starter]
    C --> G[trina-minio-starter]
    C --> H[trina-elasticsearch-starter]
    C --> J[trina-security-starter]
    
    D --> K[trina-gateway-starter]
    D --> L[trina-openapi-starter]
    
    F --> M[trina-datasource-starter]
    
    style A fill:#f9f9f9
    style B fill:#e1f5fe
    style C fill:#f3e5f5
    style D fill:#e8f5e8
    style E fill:#fff3e0
```

## 依赖管理

Trina Framework 采用集中式依赖管理策略：

1. **根POM管理所有版本**: 所有依赖版本在根POM的`<properties>`中定义
2. **BOM导入**: 使用Spring Boot/Cloud的BOM进行依赖管理
3. **模块无版本声明**: 子模块不声明依赖版本，统一从根POM继承

## 扩展机制

Trina Framework 提供多种扩展机制：

1. **自动配置**: 基于Spring Boot的自动配置机制
2. **接口扩展**: 关键功能点提供SPI接口
3. **条件化配置**: 使用`@ConditionalOn*`注解实现按需加载
4. **属性配置**: 丰富的配置属性支持自定义行为

## 最佳实践

1. **使用starter模块**: 直接引入所需功能的starter模块
2. **遵循版本管理**: 不要在业务代码中覆盖框架管理的依赖版本
3. **配置优先**: 通过配置文件调整行为，避免代码修改
4. **关注更新**: 定期更新到最新版本以获取安全修复和新特性