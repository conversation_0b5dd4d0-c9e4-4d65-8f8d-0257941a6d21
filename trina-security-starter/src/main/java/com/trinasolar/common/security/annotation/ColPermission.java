package com.trinasolar.common.security.annotation;

import java.lang.annotation.*;

/**
 * 列级数据权限注解
 * 
 * 用于标识接口返回数据的列级权限控制，根据用户角色过滤敏感字段
 * 
 * 使用示例：
 * @ColPermission(role = "general", exc = {"salary", "bonus"})  // general角色不能看到salary和bonus字段
 * @ColPermission(role = "admin", inc = {"salary", "bonus"})    // admin角色只能看到salary和bonus字段
 * @ColPermission(roles = {"general", "user"}, exc = {"salary", "bonus", "phone"}) // 多个角色的排除字段
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ColPermission {

    /**
     * 单个角色（与roles互斥，优先使用roles）
     */
    String role() default "";

    /**
     * 多个角色
     */
    String[] roles() default {};

    /**
     * 排除字段列表（黑名单模式）
     * 指定角色不能看到这些字段
     */
    String[] exc() default {};

    /**
     * 包含字段列表（白名单模式）
     * 指定角色只能看到这些字段
     */
    String[] inc() default {};

    /**
     * 权限模式
     */
    Mode mode() default Mode.EXCLUDE;

    /**
     * 权限模式枚举
     */
    enum Mode {
        /**
         * 排除模式：排除指定字段，显示其他字段
         */
        EXCLUDE,
        
        /**
         * 包含模式：只显示指定字段，隐藏其他字段
         */
        INCLUDE
    }
}
