package com.trinasolar.common.security.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 安全上下文模型
 * <p>
 * 按业内规范优化，包含完整的用户认证和授权信息
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
public class SecurityContext implements Serializable {

    @Serial
    private static final long serialVersionUID = -6635618517728581803L;
    /**
     * 用户基本信息
     */
    private UserPrincipal principal;

    /**
     * 认证信息
     */
    private Authentication authentication;

    /**
     * 授权信息
     */
    private Authorization authorization;

    /**
     * 用户主体信息
     */
    @Data
    @Schema(description = "用户主体信息")
    public static class UserPrincipal implements Serializable {
        @Serial
        private static final long serialVersionUID = -3456334421963374837L;
        /**
         * 用户ID
         */
        @Schema(description = "用户唯一标识", example = "USER_001")
        private String userId;

        /**
         * 用户名
         */
        @Schema(description = "登录用户名", example = "john.doe")
        private String username;

        /**
         * 显示名称
         */
        @Schema(description = "显示名称", example = "张三")
        private String displayName;

        /**
         * 邮箱
         */
        @Schema(description = "电子邮箱", example = "<EMAIL>")
        private String email;

        /**
         * 部门编号
         */
        @Schema(description = "所属部门编号", example = "DEPT_001")
        private String deptId;

        /**
         * 员工编号
         */
        @Schema(description = "员工工号", example = "EMP_1001")
        private String employeeNumber;

        /**
         * 职位类型
         */
        @Schema(description = "职位类型", example = "技术岗")
        private String postType;

        /**
         * 位置
         */
        @Schema(description = "所在地区", example = "上海")
        private String location;

        /**
         * 是否已填充
         */
        @Schema(description = "是否已完整填充用户信息", example = "true")
        private boolean filled;
    }

    /**
     * 认证信息
     */
    @Data
    @Schema(description = "认证信息")
    public static class Authentication implements Serializable {
        @Serial
        private static final long serialVersionUID = -6122487148505232191L;
        @Schema(description = "访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
        private String accessToken;

        @Schema(description = "刷新令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
        private String refreshToken;

        @Schema(description = "令牌类型", example = "Bearer")
        private String tokenType;

        @Schema(description = "授权范围", example = "read write")
        private String scope;

        @Schema(description = "令牌过期时间", example = "2025-07-01T12:00:00")
        private LocalDateTime expiresAt;

        @Schema(description = "认证时间", example = "2025-06-26T09:30:00")
        private LocalDateTime authenticatedAt;

        @Schema(description = "认证状态", example = "true")
        private boolean authenticated;
    }

    /**
     * 授权信息
     */
    @Data
    @Schema(description = "权限信息")
    public static class Authorization implements Serializable {
        @Serial
        private static final long serialVersionUID = -3200283999609872511L;
        @Schema(description = "用户角色列表", example = "[\"ADMIN\", \"USER\"]")
        private List<String> roleCodes;

        @Schema(description = "权限列表", example = "[\"user:read\", \"user:write\"]")
        private List<String> permissions;

        @Schema(description = "数据权限范围")
        private List<Role> roles;

        @Schema(description = "权限加载时间", example = "2025-06-26T09:30:00")
        private LocalDateTime permissionsLoadedAt;
    }

    @Data
    public static class Role implements Serializable {
        @Serial
        private static final long serialVersionUID = -6684332587206736992L;
        private String id;
        private String code;
        private String name;
        private Integer dsType;
        private List<String> dsScope;

    }

    /**
     * 检查用户是否已认证
     */
    public boolean isAuthenticated() {
        return authentication != null && authentication.isAuthenticated();
    }

    /**
     * 检查用户信息是否已填充
     */
    public boolean isFilled() {
        return principal != null && principal.isFilled();
    }

    /**
     * 检查用户是否拥有指定角色
     */
    public boolean hasRole(String role) {
        return authorization != null &&
                authorization.getRoleCodes() != null &&
                authorization.getRoleCodes().contains(role);
    }

    /**
     * 检查用户是否拥有任意一个角色
     */
    public boolean hasAnyRole(String... roles) {
        if (authorization == null || authorization.getRoleCodes() == null) {
            return false;
        }
        for (String role : roles) {
            if (authorization.getRoleCodes().contains(role)) {
                return true;
            }
        }
        return false;
    }


    /**
     * 检查用户是否拥有所有角色
     */
    public boolean hasAllRoles(String... roles) {
        if (authorization == null || authorization.getRoleCodes() == null) {
            return false;
        }
        for (String role : roles) {
            if (!authorization.getRoleCodes().contains(role)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查用户是否拥有指定权限
     */
    public boolean hasPermission(String permission) {
        return authorization != null &&
                authorization.getPermissions() != null &&
                authorization.getPermissions().contains(permission);
    }

    /**
     * 检查用户是否拥有任意一个权限
     */
    public boolean hasAnyPermission(String... permissions) {
        if (authorization == null || authorization.getPermissions() == null) {
            return false;
        }
        for (String permission : permissions) {
            if (authorization.getPermissions().contains(permission)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查用户是否拥有所有权限
     */
    public boolean hasAllPermissions(String... permissions) {
        if (authorization == null || authorization.getPermissions() == null) {
            return false;
        }
        for (String permission : permissions) {
            if (!authorization.getPermissions().contains(permission)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取AccessToken
     */
    public String getAccessToken() {
        return authentication != null ? authentication.getAccessToken() : null;
    }

    /**
     * 获取用户ID
     */
    public String getUserId() {
        return principal != null ? principal.getUserId() : null;
    }

    /**
     * 获取用户名
     */
    public String getUsername() {
        return principal != null ? principal.getUsername() : null;
    }

    /**
     * 获取显示名称
     */
    public String getDisplayName() {
        return principal != null ? principal.getDisplayName() : null;
    }

    /**
     * 获取用户角色列表
     */
    public List<String> getRoleCodes() {
        return authorization != null ? authorization.getRoleCodes() : null;
    }

    /**
     * 获取用户权限列表
     */
    public List<String> getPermissions() {
        return authorization != null ? authorization.getPermissions() : null;
    }

    /**
     * 获取数据权限范围
     */
    public List<Role> getRoles() {
        return authorization != null ? authorization.getRoles() : null;
    }
}
