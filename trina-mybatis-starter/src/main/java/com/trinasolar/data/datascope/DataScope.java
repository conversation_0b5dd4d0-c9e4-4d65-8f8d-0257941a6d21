package com.trinasolar.data.datascope;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/8/30 数据权限查询参数
 */
@Data
public class DataScope {

    /**
     * 跳过数据权限，直接在参数设置 skip
     */
    private boolean skip = false;

    /**
     * 限制范围的字段名称
     */
    private String scopeDeptName = "org_code";

    /**
     * 本人权限范围字段
     */
    private String scopeUserName = "create_by";

    /**
     * 具体的数据范围
     */
    private List<String> orgIdList = new ArrayList<>();

    /**
     * 具体查询的用户数据权限范围
     */
    private String username;

    /**
     * 是否只查询本部门
     */
    private Boolean isOnly = false;

    /**
     * 函数名称，默认 SELECT * ;
     *
     * <ul>
     * <li>COUNT(1)</li>
     * </ul>
     */
    private DataScopeFuncEnum func = DataScopeFuncEnum.ALL;

    /**
     * of 获取实例
     */
    public static DataScope of() {
        return new DataScope();
    }

    public DataScope orgIds(List<String> orgIds) {
        this.orgIdList = orgIds;
        return this;
    }

    public DataScope only(boolean isOnly) {
        this.isOnly = isOnly;
        return this;
    }

    public DataScope func(DataScopeFuncEnum func) {
        this.func = func;
        return this;
    }

}
