package com.trinasolar.openapi.annotation;

import com.trinasolar.common.core.helper.YamlPropertySourceHelper;
import com.trinasolar.openapi.config.OpenAPIDefinitionImportSelector;
import com.trinasolar.openapi.support.SwaggerProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.PropertySource;

import java.lang.annotation.*;

/**
 * 开启 spring doc
 *
 * <AUTHOR>
 * @date 2022-03-26
 */
@Target({ ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@EnableConfigurationProperties(SwaggerProperties.class)
@Import({ OpenAPIDefinitionImportSelector.class })
@PropertySource(value = "classpath:openapi-config.yaml", factory = YamlPropertySourceHelper.class)
public @interface EnableOpenApi {

	/**
	 * 网关路由前缀
	 * @return String
	 */
	String value() default "";

	/**
	 * 是否是微服务架构
	 * @return true
	 */
	boolean isMicro() default true;

}
