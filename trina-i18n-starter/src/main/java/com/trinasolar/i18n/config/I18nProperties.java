package com.trinasolar.i18n.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;
import java.util.HashSet;
import java.util.Set;

/**
 * 国际化配置属性
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "trina.i18n")
public class I18nProperties {

    /**
     * 是否启用国际化功能
     */
    private boolean enabled = true;

    /**
     * 翻译文件目录路径
     */
    private String filePath = "i18n";

    /**
     * 默认语言
     */
    private String defaultLanguage = "zh-CN";

    /**
     * 支持的语言列表
     */
    private Set<String> supportedLanguages = new HashSet<>();

    /**
     * 翻译API配置
     */
    private ApiConfig api = new ApiConfig();

    /**
     * 缓存配置
     */
    private CacheConfig cache = new CacheConfig();

    /**
     * 异步任务配置
     */
    private AsyncConfig async = new AsyncConfig();

    /**
     * 拦截器配置
     */
    private InterceptorConfig interceptor = new InterceptorConfig();

    /**
     * 获取启用状态
     */
    public boolean isEnabled() {
        return enabled;
    }

    /**
     * 获取文件路径
     */
    public String getFilePath() {
        return filePath;
    }

    /**
     * 获取默认语言
     */
    public String getDefaultLanguage() {
        return defaultLanguage;
    }

    /**
     * 获取支持的语言列表
     */
    public Set<String> getSupportedLanguages() {
        return supportedLanguages;
    }

    /**
     * 获取API配置
     */
    public ApiConfig getApi() {
        return api;
    }

    /**
     * 获取缓存配置
     */
    public CacheConfig getCache() {
        return cache;
    }

    /**
     * 获取异步配置
     */
    public AsyncConfig getAsync() {
        return async;
    }

    /**
     * 获取拦截器配置
     */
    public InterceptorConfig getInterceptor() {
        return interceptor;
    }

    /**
     * 翻译API配置
     */
    @Data
    public static class ApiConfig {
        /**
         * 是否启用翻译API
         */
        private boolean enabled = false;

        /**
         * 翻译API地址
         */
        private String url;

        /**
         * API密钥
         */
        private String appKey;

        /**
         * API密钥
         */
        private String appSecret;

        /**
         * 超时时间
         */
        private Duration timeout = Duration.ofSeconds(5);

        /**
         * 重试次数
         */
        private int retryCount = 3;

        public boolean isEnabled() {
            return enabled;
        }

        public String getUrl() {
            return url;
        }

        public String getAppKey() {
            return appKey;
        }

        public String getAppSecret() {
            return appSecret;
        }

        public Duration getTimeout() {
            return timeout;
        }

        public int getRetryCount() {
            return retryCount;
        }
    }

    /**
     * 缓存配置
     */
    @Data
    public static class CacheConfig {
        /**
         * 是否启用缓存
         */
        private boolean enabled = true;

        /**
         * 最大缓存条目数
         */
        private int maximumSize = 10000;

        /**
         * 缓存过期时间
         */
        private Duration expireAfterWrite = Duration.ofHours(24);

        public boolean isEnabled() {
            return enabled;
        }

        public int getMaximumSize() {
            return maximumSize;
        }

        public Duration getExpireAfterWrite() {
            return expireAfterWrite;
        }
    }

    /**
     * 异步任务配置
     */
    public static class AsyncConfig {
        /**
         * 线程池核心线程数
         */
        private int corePoolSize = 2;

        /**
         * 线程池最大线程数
         */
        private int maxPoolSize = 5;

        /**
         * 队列容量
         */
        private int queueCapacity = 100;

        /**
         * 线程名称前缀
         */
        private String threadNamePrefix = "i18n-async-";

        /**
         * 获取核心线程数
         */
        public int getCorePoolSize() {
            return corePoolSize;
        }

        /**
         * 获取最大线程数
         */
        public int getMaxPoolSize() {
            return maxPoolSize;
        }

        /**
         * 获取队列容量
         */
        public int getQueueCapacity() {
            return queueCapacity;
        }

        /**
         * 获取线程名称前缀
         */
        public String getThreadNamePrefix() {
            return threadNamePrefix;
        }
    }

    /**
     * 拦截器配置
     */
    public static class InterceptorConfig {
        /**
         * 是否启用拦截器
         */
        private boolean enabled = true;

        /**
         * 语言参数名
         */
        private String languageParam = "lang";

        /**
         * 是否优先使用Header中的语言
         */
        private boolean preferHeader = true;

        /**
         * 语言Header名
         */
        private String languageHeader = "Accept-Language";

        /**
         * 排除的路径
         */
        private Set<String> excludePaths = new HashSet<>();

        /**
         * 获取启用状态
         */
        public boolean isEnabled() {
            return enabled;
        }

        /**
         * 获取语言参数名
         */
        public String getLanguageParam() {
            return languageParam;
        }

        /**
         * 获取是否优先使用Header
         */
        public boolean isPreferHeader() {
            return preferHeader;
        }

        /**
         * 获取语言Header名
         */
        public String getLanguageHeader() {
            return languageHeader;
        }

        /**
         * 获取排除路径
         */
        public Set<String> getExcludePaths() {
            return excludePaths;
        }
    }
}