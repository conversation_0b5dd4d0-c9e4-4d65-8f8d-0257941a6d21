---
title: 15分钟教程系列
description: 快速学习现代Java开发技术的教程集合
---

# ⚡ 15分钟教程系列

本目录包含一系列15分钟快速学习教程，帮助您快速掌握现代Java开发中的核心技术和最佳实践。

## 🎯 学习路径

1. **[Java 17新特性](./15-min-java17.mdx)** - 了解现代Java特性
2. **[Spring Boot 3](./15-min-springboot3.mdx)** - 掌握最新框架版本
3. **[Trina架构快速上手](./15-min-trina-architecture.mdx)** - 框架核心概念
4. **[微服务核心概念](./15min-microservices-core.mdx)** - 微服务架构基础

### 🏗️ 架构师路径
1. **[微服务核心概念](./15min-microservices-core.mdx)** - 微服务设计原则
2. **[Spring Cloud](./15-min-spring-cloud.mdx)** - 微服务生态
3. **[Seata分布式事务](./15-min-seata.mdx)** - 事务一致性
4. **[安全基础](./15min-security-essentials.mdx)** - 安全架构设计

### 💻 开发者路径
1. **[MyBatis-Plus](./15-min-mybatis-plus.mdx)** - 数据访问层
2. **[Redis最佳实践](./15-min-redis-best-practices.mdx)** - 缓存策略
3. **[XXL-JOB调度](./15-min-xxl-job.mdx)** - 任务调度
4. **[Arthas诊断](./15-min-arthas.mdx)** - 问题排查

## 📚 分类浏览

### 🔧 基础技术

#### Java核心
- **[Java 17新特性](./15-min-java17.mdx)** - 现代Java语言特性详解
- **[Java 17并发编程](./15-min-java17-concurrency.mdx)** - 并发编程最佳实践

#### Spring生态
- **[Spring Boot 3](./15-min-springboot3.mdx)** - 新版本特性和迁移指南
- **[Spring Cloud](./15-min-spring-cloud.mdx)** - 微服务开发框架
- **[Spring AI](./15-min-spring-ai.mdx)** - AI集成开发实践

### 🏛️ 架构设计

#### 微服务架构
- **[微服务核心概念](./15min-microservices-core.mdx)** - 微服务设计原则和模式
- **[Trina架构快速上手](./15-min-trina-architecture.mdx)** - 框架架构理解

#### 分布式技术
- **[Seata分布式事务](./15-min-seata.mdx)** - 分布式事务解决方案
- **[XXL-JOB调度](./15-min-xxl-job.mdx)** - 分布式任务调度

### 💾 数据访问

#### 数据库技术
- **[MyBatis-Plus](./15-min-mybatis-plus.mdx)** - ORM框架进阶使用
- **[Redis最佳实践](./15-min-redis-best-practices.mdx)** - 缓存设计和优化

### 🔒 安全与运维

#### 安全防护
- **[安全基础](./15min-security-essentials.mdx)** - Web安全防护要点

#### 运维工具
- **[Arthas诊断](./15-min-arthas.mdx)** - Java应用诊断工具

## 🎯 按技能等级分类

### 🌱 初级（入门必读）
- [Java 17新特性](./15-min-java17.mdx)
- [Spring Boot 3](./15-min-springboot3.mdx)
- [Trina架构快速上手](./15-min-trina-architecture.mdx)
- [MyBatis-Plus](./15-min-mybatis-plus.mdx)

### 🌿 中级（进阶提升）
- [Java 17并发编程](./15-min-java17-concurrency.mdx)
- [Spring Cloud](./15-min-spring-cloud.mdx)
- [微服务核心概念](./15min-microservices-core.mdx)
- [Redis最佳实践](./15-min-redis-best-practices.mdx)
- [XXL-JOB调度](./15-min-xxl-job.mdx)

### 🌳 高级（专家级别）
- [Seata分布式事务](./15-min-seata.mdx)
- [Spring AI](./15-min-spring-ai.mdx)
- [安全基础](./15min-security-essentials.mdx)
- [Arthas诊断](./15-min-arthas.mdx)

---

开始您的15分钟学习之旅吧！🚀