#!/bin/bash

# 颜色定义
GREEN="\033[0;32m"
YELLOW="\033[1;33m"
RED="\033[0;31m"
NC="\033[0m" # No Color

echo -e "${YELLOW}===== Trina Wiki 部署脚本 =====${NC}"

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}错误: Docker未安装，请先安装Docker${NC}"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}错误: Docker Compose未安装，请先安装Docker Compose${NC}"
    exit 1
fi

echo -e "${GREEN}开始构建Docker镜像...${NC}"
if docker-compose build; then
    echo -e "${GREEN}Docker镜像构建成功!${NC}"
else
    echo -e "${RED}Docker镜像构建失败!${NC}"
    exit 1
fi

echo -e "${GREEN}启动容器...${NC}"
if docker-compose up -d; then
    echo -e "${GREEN}容器启动成功!${NC}"
else
    echo -e "${RED}容器启动失败!${NC}"
    exit 1
fi

# 等待服务启动
echo -e "${YELLOW}等待服务启动...${NC}"
sleep 10

# 检查服务是否正常运行
echo -e "${GREEN}验证服务是否正常运行...${NC}"
if curl -s -o /dev/null -w "%{http_code}" http://localhost:80/wiki/ | grep -q "200"; then
    echo -e "${GREEN}服务验证成功! Trina Wiki 已成功部署并运行在 http://localhost:80/wiki/${NC}"
else
    echo -e "${RED}服务验证失败! 请检查日志:${NC}"
    docker-compose logs
    exit 1
fi

echo -e "${YELLOW}===== 部署完成 =====${NC}"
echo -e "${GREEN}您可以通过以下命令管理服务:${NC}"
echo -e "  ${YELLOW}查看日志:${NC} docker-compose logs -f"
echo -e "  ${YELLOW}停止服务:${NC} docker-compose down"
echo -e "  ${YELLOW}重启服务:${NC} docker-compose restart"