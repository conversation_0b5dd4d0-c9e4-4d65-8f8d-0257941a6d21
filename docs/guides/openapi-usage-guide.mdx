---
title: Trina OpenAPI Starter 使用指南
description: Trina OpenAPI Starter 组件的快速开始、配置方法和使用说明
---

# Trina OpenAPI Starter 使用指南

## 1. 快速开始

### 1.1 引入依赖

在 `pom.xml` 中添加如下依赖：

```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-openapi-starter</artifactId>
    <version>4.0.0</version>
</dependency>
```

### 1.2 基础配置

在 `application.yml` 中添加基本配置：

```yaml
swagger:
  enabled: true                # 是否启用swagger
  base-package: com.example    # 需要扫描的包路径
  title: "企业API文档"
  description: "企业级微服务API文档自动生成"
  version: "1.0.0"
  author: "yourname"
  gateway: "http://localhost:8080"   # 网关地址（微服务聚合时必填）
  token-url: "/oauth/token"          # OAuth2获取token的地址
  scope: "server"
  base-path:
    - "/**"
  exclude-path:
    - "/error"
    - "/actuator/**"
```

### 1.3 启用注解

在 Spring Boot 启动类或配置类上添加注解：

```java
import com.trinasolar.openapi.annotation.EnableOpenApi;

@EnableOpenApi
@SpringBootApplication
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

## 2. 核心功能

### 2.1 API文档自动生成

- 访问 `http://localhost:8080/swagger-ui.html` 或 `/doc.html` 查看自动生成的API文档
- 支持 OpenAPI 3.0 规范，兼容主流前端工具
- 支持包路径、接口路径过滤，灵活控制文档内容

### 2.2 安全配置（OAuth2集成）

- 支持 OAuth2 Password 模式，保护敏感API
- 配置 `swagger.token-url`、`swagger.scope`，后端需实现对应认证接口
- 文档页面自动集成授权按钮，便于调试

### 2.3 多服务与微服务支持

- 配置 `swagger.gateway`，服务启动时自动注册元数据，便于网关聚合所有服务的API文档
- 支持服务排除、路径排除等高级配置

## 3. 高级特性

### 3.1 自定义元数据

- 通过 `swagger.title`、`swagger.description`、`swagger.version`、`swagger.author` 自定义文档信息
- 支持多语言描述和版本管理

### 3.2 生产环境关闭

- 推荐仅在开发/测试环境开启 Swagger，生产环境通过 `swagger.enabled=false` 关闭
- 可结合 Spring Profile 实现多环境灵活切换

### 3.3 其他配置项

| 配置项           | 说明                       |
|------------------|----------------------------|
| base-package     | 需要扫描的包路径           |
| base-path        | 需要包含的接口路径         |
| exclude-path     | 需要排除的接口路径         |
| ignore-providers | 需要排除的服务             |
| gateway          | 网关地址                   |
| token-url        | OAuth2获取token的地址      |
| scope            | OAuth2作用域               |
| title            | 文档标题                   |
| description      | 文档描述                   |
| version          | 文档版本                   |
| author           | 文档作者                   |

## 4. 常见问题

### 4.1 生产环境如何关闭Swagger？

```yaml
swagger:
  enabled: false
```
或使用 Spring Profile：
```yaml
---
spring:
  profiles: prod
swagger:
  enabled: false
```

### 4.2 网关聚合API文档不生效？
- 检查 `swagger.gateway` 配置是否正确
- 检查服务元数据是否注册成功
- 确认网关已拉取所有服务的 spring-doc 元数据

### 4.3 OAuth2授权调试失败？
- 检查 `swagger.token-url`、`swagger.scope` 配置
- 确认后端已实现对应的认证接口
- 检查授权服务器可用性

### 4.4 如何只暴露部分接口？
- 配置 `base-package` 精确扫描业务包
- 配置 `base-path`、`exclude-path` 控制接口暴露范围

## 5. 最佳实践

- 仅在开发/测试环境开启 Swagger，生产环境务必关闭
- 精确配置 `base-package`，避免暴露内部接口
- 配置 OAuth2 保护敏感API，提升安全性
- 结合 CI/CD 自动校验 API 文档完整性
- 定期维护文档元信息，提升团队协作效率

## 6. 参考资料

- [Trina OpenAPI 架构文档](../architecture/openapi-architecture.mdx)
- [SpringDoc 官方文档](https://springdoc.org/)
- [OpenAPI 3.0 规范](https://swagger.io/specification/)
- [Trina Common Core 使用指南](./common-core-usage-guide.mdx) 