package com.trinasolar.gateway.security.endpoint;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Cache;
import com.trinasolar.common.core.base.R;
import com.trinasolar.common.core.constant.CacheConstants;
import com.trinasolar.common.security.constants.SecurityConstant;
import com.trinasolar.common.security.context.SecurityContextHolder;
import com.trinasolar.common.security.domain.IamTokenInfo;
import com.trinasolar.common.security.domain.IamUserInfo;
import com.trinasolar.common.security.domain.SecurityContext;
import com.trinasolar.common.security.properties.SecurityProperties;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientRequestException;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.PrematureCloseException;
import reactor.util.retry.Retry;

import java.time.Duration;

/**
 * 网关 auth 端点
 *
 * <AUTHOR>
 * @date 2025-07-13
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/scf")
@Tag(name = "认证管理", description = "IAM认证相关接口")
public class GatewayAuthEndpoint {

    private final SecurityProperties securityProperties;
    private final WebClient webClient;
    private final Cache<String, Object> iamCache;

    /**
     * 通过授权码从 TAM 远程获取令牌
     *
     * @param code 授权码
     * @return {@link R }<{@link IamTokenInfo }>
     */
    @Operation(
            summary = "获取访问令牌",
            description = "通过授权码模式获取IAM访问令牌",
            responses = {
                    @ApiResponse(responseCode = "200", description = "令牌获取成功"),
                    @ApiResponse(responseCode = "400", description = "无效的授权码")
            }
    )
    @GetMapping("/token")
    @Parameter(description = "授权码", required = true, in = ParameterIn.QUERY)
    public Mono<R<IamTokenInfo>> getToken(@RequestParam String code) {
        return remoteGetTokenInfo(code)
                .map(tokenInfo -> {
                    if (ObjUtil.isNull(tokenInfo)) {
                        return R.failed("获取token失败");
                    }
                    String tokenCacheKey = CacheConstants.buildTokenInfoKey(tokenInfo.getAccess_token());
                    iamCache.put(tokenCacheKey, tokenInfo.getAccess_token());
                    return R.ok(tokenInfo);
                });
    }

    /**
     * 获取用户信息
     *
     * @return {@link R }<{@link IamUserInfo }>
     */
    @Operation(
            summary = "获取用户信息",
            description = "获取当前认证用户的详细信息",
            security = @SecurityRequirement(name = "BearerAuth")
    )
    @ApiResponse(responseCode = "200", description = "用户信息获取成功")
    @GetMapping("/userinfo")
    public Mono<R<SecurityContext.UserPrincipal>> getUserInfo() {
        return Mono.fromCallable(() -> R.ok(SecurityContextHolder.getContext().getPrincipal()));
    }

    /**
     * 通过token从 TAM 远程注销
     *
     * @param authorization 授权
     * @return {@link R }<{@link Boolean }>
     */
    @Operation(
            summary = "注销登录",
            description = "注销当前登录状态及上下文权限，使当前访问令牌失效",
            security = @SecurityRequirement(name = "BearerAuth")
    )
    @ApiResponse(responseCode = "200", description = "注销成功")
    @Parameter(description = "访问令牌", required = true, in = ParameterIn.HEADER, example = "Bearer access_token")
    @GetMapping("/logout")
    public Mono<R<Boolean>> logout(@RequestHeader String authorization) {
        return remoteLogout(authorization)
                .map(R::ok);
    }

    /**
     * 刷新 SecurityContextHolder及缓存中的token信息
     * <p>
     * 注意：这里只做清除，清除后下一次请求接口时会走过滤器重新缓存token和加载上下文权限信息
     *
     * @param authorization 授权
     * @return {@link R }<{@link Boolean }>
     * @see com.trinasolar.gateway.security.filter.GatewaySecurityFilter
     */
    @Operation(
            summary = "刷新权限上下文信息",
            description = "重新加载权限数据",
            security = @SecurityRequirement(name = "BearerAuth")
    )
    @ApiResponse(responseCode = "200", description = "刷新成功")
    @Parameter(description = "访问令牌", required = true, in = ParameterIn.HEADER, example = "Bearer access_token")
    @GetMapping("/refresh")
    public Mono<R<Boolean>> refresh(@RequestHeader String authorization) {
        return Mono.fromCallable(() -> {
            SecurityContextHolder.clearContext();
            String token = authorization.substring(SecurityConstant.TOKEN_PREFIX.length());
            iamCache.asMap().remove(CacheConstants.buildTokenInfoKey(token));
            iamCache.asMap().remove(CacheConstants.buildMenuInfoKey(token));
            iamCache.asMap().remove(CacheConstants.buildAuthInfoKey(token));
            return R.ok(true);
        });
    }

    @Operation(
            summary = "获取用户应用菜单信息",
            description = "获取当前认证用户的已有应用菜单树信息",
            security = @SecurityRequirement(name = "BearerAuth")
    )
    @ApiResponse(responseCode = "200", description = "用户应用菜单信息获取成功")
    @GetMapping("/menu")
    public Mono<R<JSONArray>> getMenu(@RequestHeader String authorization) {
        return remoteMenu(authorization).map(R::ok);
    }

    @Operation(
            summary = "获取用户应用按钮权限信息",
            description = "获取当前认证用户的已有应用按钮信息",
            security = @SecurityRequirement(name = "BearerAuth")
    )
    @ApiResponse(responseCode = "200", description = "用户应用按钮权限信息获取成功")
    @GetMapping("/auth")
    public Mono<R<JSONObject>> getBtnAuth(@RequestHeader String authorization) {
        return remoteBtnAuth(authorization).map(R::ok);
    }

    /**
     * 远程获取token信息
     *
     * @param code 授权码
     * @return {@link Mono<IamTokenInfo> }
     */
    private Mono<IamTokenInfo> remoteGetTokenInfo(String code) {
        String url = securityProperties.getEnvUrl() + SecurityConstant.TOKEN_PATH;

        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add("grant_type", "authorization_code");
        formData.add("client_id", securityProperties.getClientId());
        formData.add("client_secret", securityProperties.getClientSecret());
        formData.add("redirect_uri", securityProperties.getRedirectUrl());
        formData.add("code", code);

        return webClient.post()
                .uri(url)
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .bodyValue(formData)
                .retrieve()
                .onStatus(HttpStatusCode::isError, response ->
                        response.bodyToMono(String.class)
                                .flatMap(errorBody -> {
                                    log.error("Token request failed with status: {}, body: {}", response.statusCode(), errorBody);
                                    return Mono.error(new RuntimeException("Token request failed: " + response.statusCode()));
                                })
                )
                .bodyToMono(String.class)
                .timeout(Duration.ofSeconds(15))  // Token获取超时
                .retryWhen(Retry.backoff(2, Duration.ofMillis(500))
                        .filter(throwable -> {
                            // 只对连接相关异常进行重试
                            return throwable instanceof PrematureCloseException ||
                                   throwable instanceof WebClientRequestException ||
                                   (throwable instanceof WebClientResponseException &&
                                    ((WebClientResponseException) throwable).getStatusCode().is5xxServerError());
                        })
                        .onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> {
                            log.warn("Token request retry exhausted for URL: {}, attempts: {}",
                                    url, retrySignal.totalRetries());
                            return retrySignal.failure();
                        }))
                .flatMap(body -> {
                    try {
                        IamTokenInfo tokenInfo = JSONUtil.toBean(body, IamTokenInfo.class);
                        if (tokenInfo != null && StringUtils.hasText(tokenInfo.getAccess_token())) {
                            log.debug("Remote get tokenInfo success: {}", tokenInfo);
                            return Mono.just(tokenInfo);
                        } else {
                            log.warn("Invalid token response: {}", body);
                            return Mono.just(new IamTokenInfo());
                        }
                    } catch (Exception e) {
                        log.error("Failed to parse token response: {}", body, e);
                        try {
                            JSONObject parseObj = JSONObject.parseObject(body);
                            if (parseObj != null) {
                                log.error("Token error [{}], description [{}]",
                                        parseObj.getString("error"), parseObj.getString("error_description"));
                            }
                        } catch (Exception parseException) {
                            log.error("Failed to parse error response", parseException);
                        }
                        return Mono.just(new IamTokenInfo());
                    }
                })
                .doOnError(throwable -> {
                    if (throwable instanceof PrematureCloseException) {
                        log.warn("Connection prematurely closed during token request for URL: {}", url);
                    } else if (throwable instanceof WebClientRequestException) {
                        log.warn("Network error during token request for URL: {}, error: {}", url, throwable.getMessage());
                    } else {
                        log.error("Error during token request for URL: {}", url, throwable);
                    }
                })
                .onErrorReturn(new IamTokenInfo());
    }

    /**
     * 远程注销
     *
     * @param authorization 授权token
     * @return {@link Mono<Boolean> }
     */
    private Mono<Boolean> remoteLogout(String authorization) {
        String token = authorization.substring(SecurityConstant.TOKEN_PREFIX.length());
        String url = securityProperties.getEnvUrl() + SecurityConstant.LOGOUT_PATH +
                securityProperties.getClientId() + SecurityConstant.REDIRECT_URI +
                securityProperties.getRedirectUrl();

        return webClient.get()
                .uri(url)
                .header(HttpHeaders.AUTHORIZATION, authorization)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofSeconds(10))  // 注销请求超时
                .retryWhen(Retry.backoff(2, Duration.ofMillis(300))
                        .filter(throwable -> {
                            // 只对连接相关异常进行重试
                            return throwable instanceof PrematureCloseException ||
                                   throwable instanceof WebClientRequestException ||
                                   (throwable instanceof WebClientResponseException &&
                                    ((WebClientResponseException) throwable).getStatusCode().is5xxServerError());
                        })
                        .onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> {
                            log.warn("Logout request retry exhausted for URL: {}, attempts: {}",
                                    url, retrySignal.totalRetries());
                            return retrySignal.failure();
                        }))
                .map(body -> {
                    log.debug("remote logout success");
                    SecurityContextHolder.clearContext();
                    iamCache.asMap().remove(CacheConstants.buildTokenInfoKey(token));
                    iamCache.asMap().remove(CacheConstants.buildMenuInfoKey(token));
                    iamCache.asMap().remove(CacheConstants.buildAuthInfoKey(token));
                    return true;
                })
                .doOnError(throwable -> {
                    if (throwable instanceof PrematureCloseException) {
                        log.warn("Connection prematurely closed during logout for URL: {}", url);
                    } else if (throwable instanceof WebClientRequestException) {
                        log.warn("Network error during logout for URL: {}, error: {}", url, throwable.getMessage());
                    } else {
                        log.error("Error during logout for URL: {}", url, throwable);
                    }
                })
                .onErrorReturn(throwable -> {
                    log.error("Remote logout failed, clearing local context anyway", throwable);
                    SecurityContextHolder.clearContext();
                    iamCache.asMap().remove(CacheConstants.buildTokenInfoKey(token));
                    iamCache.asMap().remove(CacheConstants.buildMenuInfoKey(token));
                    iamCache.asMap().remove(CacheConstants.buildAuthInfoKey(token));
                    return true;
                }, false);
    }

    /**
     * 远程获取菜单
     *
     * @param authorization 授权
     * @return {@link JSONArray }
     */
    private Mono<JSONArray> remoteMenu(String authorization) {
        String url = securityProperties.getTaspUrl() + SecurityConstant.TASP_MENU_PATH + StrPool.SLASH + securityProperties.getAppId();
        String token = authorization.substring(SecurityConstant.TOKEN_PREFIX.length());
        Object cacheMenu = iamCache.getIfPresent(CacheConstants.buildMenuInfoKey(token));
        if (!ObjectUtils.isEmpty(cacheMenu)) {
            return Mono.just((JSONArray) cacheMenu);
        }

        return webClient.get()
                .uri(url)
                .header(HttpHeaders.AUTHORIZATION, authorization)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofSeconds(10))  // 菜单加载超时
                .retryWhen(Retry.backoff(2, Duration.ofMillis(300))
                        .filter(throwable -> {
                            // 只对连接相关异常进行重试
                            return throwable instanceof PrematureCloseException ||
                                   throwable instanceof WebClientRequestException ||
                                   (throwable instanceof WebClientResponseException &&
                                    ((WebClientResponseException) throwable).getStatusCode().is5xxServerError());
                        })
                        .onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> {
                            log.warn("Menu loading retry exhausted for URL: {}, attempts: {}",
                                    url, retrySignal.totalRetries());
                            return retrySignal.failure();
                        }))
                .map(body -> {
                    JSONObject bodyObj = JSONObject.parseObject(body);
                    JSONArray data = bodyObj.getJSONArray("data");
                    iamCache.put(CacheConstants.buildMenuInfoKey(token), data);
                    return data;
                })
                .doOnError(throwable -> {
                    if (throwable instanceof PrematureCloseException) {
                        log.warn("Connection prematurely closed during menu loading for URL: {}", url);
                    } else if (throwable instanceof WebClientRequestException) {
                        log.warn("Network error during menu loading for URL: {}, error: {}", url, throwable.getMessage());
                    } else {
                        log.error("Error during menu loading for URL: {}", url, throwable);
                    }
                })
                .onErrorResume(throwable -> {
                    log.error("Remote menu loading failed, returning empty array", throwable);
                    return Mono.just(new JSONArray());
                });
    }

    /**
     * 远程获取按钮权限信息
     *
     * @param authorization 授权
     * @return {@link JSONObject }
     */
    private Mono<JSONObject> remoteBtnAuth(String authorization) {
        String url = securityProperties.getTaspUrl() + SecurityConstant.TASP_AUTH_PATH + StrPool.SLASH + securityProperties.getAppId();
        String token = authorization.substring(SecurityConstant.TOKEN_PREFIX.length());
        Object cacheAuth = iamCache.getIfPresent(CacheConstants.buildAuthInfoKey(token));
        if (!ObjectUtils.isEmpty(cacheAuth)) {
            return Mono.just((JSONObject) cacheAuth);
        }

        return webClient.get()
                .uri(url)
                .header(HttpHeaders.AUTHORIZATION, authorization)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofSeconds(10))  // 按钮权限加载超时
                .retryWhen(Retry.backoff(2, Duration.ofMillis(300))
                        .filter(throwable -> {
                            // 只对连接相关异常进行重试
                            return throwable instanceof PrematureCloseException ||
                                   throwable instanceof WebClientRequestException ||
                                   (throwable instanceof WebClientResponseException &&
                                    ((WebClientResponseException) throwable).getStatusCode().is5xxServerError());
                        })
                        .onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> {
                            log.warn("Button auth loading retry exhausted for URL: {}, attempts: {}",
                                    url, retrySignal.totalRetries());
                            return retrySignal.failure();
                        }))
                .map(body -> {
                    JSONObject bodyObj = JSONObject.parseObject(body);
                    JSONObject data = bodyObj.getJSONObject("data");
                    iamCache.put(CacheConstants.buildAuthInfoKey(token), data);
                    return data;
                })
                .doOnError(throwable -> {
                    if (throwable instanceof PrematureCloseException) {
                        log.warn("Connection prematurely closed during button auth loading for URL: {}", url);
                    } else if (throwable instanceof WebClientRequestException) {
                        log.warn("Network error during button auth loading for URL: {}, error: {}", url, throwable.getMessage());
                    } else {
                        log.error("Error during button auth loading for URL: {}", url, throwable);
                    }
                })
                .onErrorResume(throwable -> {
                    log.error("Remote button auth loading failed, returning empty object", throwable);
                    return Mono.just(new JSONObject());
                });
    }
}
