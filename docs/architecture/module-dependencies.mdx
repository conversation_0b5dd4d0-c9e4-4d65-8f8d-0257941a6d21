---
title: 模块依赖关系
description: Trina Common 框架中的模块结构和依赖关系
---

# Trina Common 项目结构

## 项目模块结构

```mermaid
graph TD
    A[Trina Common] --> B[trina-parent]
    A --> C[trina-common-core]
    A --> D[trina-common-excel]
    A --> E[trina-common-sensitive]
    
    %% Web 相关模块
    A --> F[trina-web-starter]
    A --> G[trina-gateway-starter]
    A --> H[trina-openapi-starter]
    
    %% 数据存储相关模块
    A --> I[trina-mybatis-starter]
    A --> J[trina-redis-starter]
    A --> K[trina-datasource-starter]
    A --> M[trina-minio-starter]
    A --> N1[trina-elasticsearch-starter]
    A --> N2[trina-neo4j-starter]
    
    %% 安全相关模块
    A --> O[trina-security-starter]
    A --> P[trina-xss-starter]
    
    %% 分布式相关模块
    A --> Q[trina-microservice-starter]
    A --> S[trina-kubernetes-starter]
    
    %% 其他功能模块
    A --> T[trina-xxl-job-starter]
    A --> U[trina-tagmanagement-starter]
    A --> V[trina-remote-starter]
    A --> W[trina-sequence-starter]
    A --> X[trina-sftp-starter]
    A --> Y[trina-i18n-starter]
    A --> Z[trina-cxf-webservice-starter]
    A --> AA[trina-build-info-starter]
```

## 核心模块依赖关系

```mermaid
graph TD
    A[trina-common-core] --> B[trina-parent]
    
    %% Web 层依赖
    C[trina-web-starter] --> A
    C --> D[trina-security-starter]
    C --> E[trina-xss-starter]
    
    %% 数据访问层依赖
    F[trina-mybatis-starter] --> A
    F --> G[trina-datasource-starter]
    F --> H[trina-redis-starter]
    
    %% 存储服务依赖
    L[trina-minio-starter] --> A
    M[trina-neo4j-starter] --> A
    M1[trina-elasticsearch-starter] --> A
    
    %% 网关依赖
    N[trina-gateway-starter] --> A
    N --> D
    N --> E
```

## MyBatis 模块内部结构

```mermaid
graph TD
    A[trina-mybatis-starter] --> B[BaseQuery]
    A --> C[BaseDataService]
    A --> D[SecureQueryWrapperBuilder]
    A --> E[EntityFieldMapper]
    
    B --> F[QueryConditionType]
    B --> G[LogicalOperator]
    B --> H[QueryMode]
    
    C --> B
```

## 微服务模块依赖关系

```mermaid
graph TD
    A[trina-microservice-starter] --> B[trina-common-core]
    A --> C[trina-web-starter]
    A --> D[trina-redis-starter]
    
    E[trina-gateway-starter] --> B
    E --> F[trina-security-starter]
    
    G[业务微服务] --> A
    G --> H[trina-mybatis-starter]

    J[API网关] --> E
```

## 安全模块依赖关系

```mermaid
graph TD
    A[trina-security-starter] --> B[trina-common-core]
    A --> C[trina-redis-starter]
    
    D[trina-web-starter] --> A
    E[trina-gateway-starter] --> A
    
    A --> F[Spring Security]
    A --> G[JWT]
    A --> H[OAuth2]
```

## 模块功能说明

1. **核心模块**
   - trina-common-core: 核心工具类和通用功能
   - trina-common-excel: Excel 导入导出功能
   - trina-common-sensitive: 敏感数据处理

2. **Web 相关**
   - trina-web-starter: Web 应用基础配置
   - trina-gateway-starter: API 网关
   - trina-openapi-starter: OpenAPI 文档

3. **数据存储**
   - trina-mybatis-starter: MyBatis 增强
   - trina-redis-starter: Redis 缓存
   - trina-datasource-starter: 数据源管理
   - trina-neo4j-starter: 图数据库支持
   - trina-minio-starter: 对象存储
   - trina-elasticsearch-starter: Elasticsearch 支持

5. **安全相关**
   - trina-security-starter: 安全框架
   - trina-xss-starter: XSS 防护

6. **分布式服务**
   - trina-microservice-starter: 微服务框架
   - trina-kubernetes-starter: K8s 集成

7. **其他功能**
   - trina-xxl-job-starter: 任务调度
   - trina-tagmanagement-starter: 标签管理
   - trina-remote-starter: 远程调用
   - trina-sequence-starter: 序列生成
   - trina-sftp-starter: SFTP 文件传输
   - trina-i18n-starter: 国际化支持
   - trina-cxf-webservice-starter: WebService 支持
   - trina-build-info-starter: 构建信息和版本跟踪模块