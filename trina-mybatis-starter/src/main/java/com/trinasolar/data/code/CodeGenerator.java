package com.trinasolar.data.code;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.IFill;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.engine.VelocityTemplateEngine;
import com.baomidou.mybatisplus.generator.fill.Column;
import com.trinasolar.data.base.BaseDataController;
import com.trinasolar.data.base.BaseDataMapper;
import com.trinasolar.data.base.BaseDataService;
import com.trinasolar.data.base.BaseDataServiceImpl;
import lombok.experimental.UtilityClass;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.type.JdbcType;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 代码生成器
 *
 * <AUTHOR>
 * @since 2025-06-06 15:36
 **/
@UtilityClass
public class CodeGenerator {

    /**
     * 表字段填充 列
     */
    private final List<IFill> COLUMN_LIST = Arrays.asList(
            new Column("create_at", FieldFill.INSERT),
            new Column("update_at", FieldFill.INSERT_UPDATE),
            new Column("create_by", FieldFill.INSERT),
            new Column("update_by", FieldFill.INSERT_UPDATE),
            new Column("is_deleted", FieldFill.INSERT),
            new Column("version", FieldFill.INSERT)
    );

    /**
     * 跑
     *
     * @param url              数据库连接字符串
     * @param username         用户名
     * @param password         密码
     * @param author           作者
     * @param parent           父包
     * @param moduleName       模块名称
     * @param superEntityClass 父级实体类
     * @param include          包括
     * @param tablePrefix      表前缀
     * @param tableSuffix      表后缀
     */
    public void run(String url, String username, String password,
                    String author, String parent, String moduleName, Class<?> superEntityClass,
                    String[] include, String[] tablePrefix, String[] tableSuffix) {
        // 使用 FastAutoGenerator 快速配置代码生成器
        FastAutoGenerator.create(url, username, password)
                .dataSourceConfig(builder ->
                        builder.typeConvertHandler((globalConfig, typeRegistry, metaInfo) -> {
                            // 兼容旧版本转换成Integer
                            if (JdbcType.TINYINT == metaInfo.getJdbcType()) {
                                return DbColumnType.INTEGER;
                            }
                            return typeRegistry.getColumnType(metaInfo);
                        })
                )
                .globalConfig(builder -> {
                    builder.author(author)
                            .outputDir("src/main/java")
                            .enableSpringdoc()
                            .disableOpenDir();
                })
                .packageConfig(builder -> {
                    builder.parent(parent)
                            .moduleName(moduleName)
                            .entity("model.entity")
                            .mapper("mapper")
                            .service("service")
                            .serviceImpl("service.impl")
                            .controller("controller")
                            .xml("mapper.xml")
                            .pathInfo(Collections.singletonMap(OutputFile.xml, System.getProperty("user.dir") + "/src/main/resources/mapper/")
                            );
                })

                //.injectionConfig(builder -> {
                //    builder.customMap(Collections.singletonMap("isTenant", isTenant));
                //})
                .strategyConfig(builder -> {
                    builder.addInclude(include)
                            .addTablePrefix(tablePrefix)
                            .addTableSuffix(tableSuffix)

                            .entityBuilder()
                            .superClass(superEntityClass)
                            .javaTemplate("/templates/entity.java.vm")
                            .enableChainModel()
                            .enableLombok()
                            .enableRemoveIsPrefix()
                            .enableTableFieldAnnotation()
                            .versionColumnName("version")
                            .logicDeleteColumnName("is_deleted")
                            .idType(IdType.ASSIGN_ID)
                            .addTableFills(COLUMN_LIST)
                            .enableFileOverride()

                            .mapperBuilder()
                            .mapperAnnotation(Mapper.class)
                            .superClass(BaseDataMapper.class)
                            .mapperTemplate("/templates/mapper.java.vm")
                            .mapperXmlTemplate("/templates/mapper.xml.vm")

                            .serviceBuilder()
                            .serviceTemplate("/templates/service.java.vm")
                            .superServiceClass(BaseDataService.class)
                            .superServiceImplClass(BaseDataServiceImpl.class)
                            .serviceImplTemplate("/templates/serviceImpl.java.vm")

                            .controllerBuilder()
                            .superClass(BaseDataController.class)
                            .template("/templates/controller.java.vm")
                            .enableRestStyle();
                })
                .templateEngine(new VelocityTemplateEngine())
                .execute();
    }
}
