package com.trinasolar.web.advice;

import com.trinasolar.common.core.base.R;
import com.trinasolar.common.core.exception.AuthenticationException;
import com.trinasolar.common.core.exception.AuthorizationException;
import com.trinasolar.common.core.exception.BusinessException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

/**
 * 全局异常处理
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    @ExceptionHandler(AuthenticationException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public R<String> authenticationException(AuthenticationException e) {
        log.error("登录已失效:", e);
        return R.failed(e.getMessage());
    }

    @ExceptionHandler(AuthorizationException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public R<String> authorizationException(AuthorizationException e) {
        log.error("授权失败:", e);
        return R.failed(e.getMessage());
    }

    @ExceptionHandler(NoResourceFoundException.class)
    public void handleNoResourceFoundException(NoResourceFoundException ex, HttpServletResponse response) {
        // 如果是请求favicon.ico，直接返回204 No Content
        if (ex.getResourcePath().equals("favicon.ico")) {
            response.setStatus(HttpServletResponse.SC_NO_CONTENT);
            return;
        }
        // 其他资源未找到的情况，返回404
        response.setStatus(HttpServletResponse.SC_NOT_FOUND);
    }

    @ExceptionHandler(value = {IllegalArgumentException.class, MethodArgumentTypeMismatchException.class})
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<String> exceptionHandler(IllegalArgumentException e) {
        log.error("IllegalArgumentException:", e);
        return R.failed("请求输入参数有误，请检查输入内容");
    }

    /**
     * http exception advice
     *
     * @param e e
     * @return {@link ResponseEntity }<{@link R }<{@link String }>>
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseBody
    public R<String> businessException(BusinessException e) {
        HttpStatus status = HttpStatus.OK;
        log.error("BusinessException:", e);
        return R.failed(e.getMessage());
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<String> httpMessageNotReadableException(HttpMessageNotReadableException e) {
        log.error("HttpMessageNotReadableException:", e);
        return R.failed("请求输入参数有误，请检查输入内容");
    }


    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<String> methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
        BindingResult result = e.getBindingResult();
        StringBuilder rs = new StringBuilder();
        if (result.hasErrors()) {
            for (ObjectError p : result.getAllErrors()) {
                FieldError fieldError = (FieldError) p;
                rs.append(fieldError.getField()).append(fieldError.getDefaultMessage()).append(".");
            }
        }

        return R.failed(rs.toString());
    }

    @ExceptionHandler(NumberFormatException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<String> numberFormatException(NumberFormatException e) {
        log.error("NumberFormatException:", e);
        return R.failed("参数格式有误，请检查输入内容");
    }

    @ExceptionHandler({Exception.class, Throwable.class})
    @ResponseBody
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R<Object> handleException(Exception e) {
        log.error("接口执行错误：{}", e.getMessage(), e);
        return R.failed("系统执行错误，请联系管理员！");
    }

}
