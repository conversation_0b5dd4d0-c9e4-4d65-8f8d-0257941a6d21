package com.trinasolar.common.datasource.config;

import com.baomidou.dynamic.datasource.creator.DataSourceProperty;
import com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator;
import com.baomidou.dynamic.datasource.creator.druid.DruidConfig;
import com.baomidou.dynamic.datasource.provider.AbstractDataSourceProvider;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

import static com.trinasolar.common.datasource.support.DataSourceConstants.DS_MASTER;

/**
 * <AUTHOR>
 * @date 2025/1/14
 * <p>
 * 保证原有的 druid 配置有效性，只需要增加其他扩展数据源即可
 */
public class MasterDataSourceProvider extends AbstractDataSourceProvider {

    private static final String SQL_LOG_FILTER = "sqlLogFilter";

    private final DruidDataSourceProperties properties;

    private final DefaultDataSourceCreator defaultDataSourceCreator;

    public MasterDataSourceProvider(DefaultDataSourceCreator defaultDataSourceCreator, DruidDataSourceProperties properties) {
        super(defaultDataSourceCreator);
        this.properties = properties;
        this.defaultDataSourceCreator = defaultDataSourceCreator;
    }


    /**
     * 加载所有数据源
     *
     * @return 所有数据源，key为数据源名称
     */
    @Override
    public Map<String, DataSource> loadDataSources() {
        Map<String, DataSource> map = new HashMap<>();
        // 添加默认主数据源
        DataSourceProperty property = new DataSourceProperty();
        property.setUsername(properties.getUsername());
        property.setPassword(properties.getPassword());
        property.setUrl(properties.getUrl());

        DruidConfig druidConfig = new DruidConfig();
        druidConfig.setProxyFilters(SQL_LOG_FILTER);
        property.setDruid(druidConfig);
        map.put(DS_MASTER, defaultDataSourceCreator.createDataSource(property));
        return map;
    }
}
