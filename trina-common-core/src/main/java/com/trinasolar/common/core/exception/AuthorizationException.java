
package com.trinasolar.common.core.exception;

import java.io.Serial;

/**
 * 授权异常
 * <p>
 * 用于权限验证失败时抛出的异常
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
public class AuthorizationException extends RuntimeException {


    @Serial
    private static final long serialVersionUID = 5841660990211935104L;

    public AuthorizationException() {
        super();
    }

    public AuthorizationException(String message) {
        super(message);
    }

    public AuthorizationException(String message, Throwable cause) {
        super(message, cause);
    }

    public AuthorizationException(Throwable cause) {
        super(cause);
    }
}
