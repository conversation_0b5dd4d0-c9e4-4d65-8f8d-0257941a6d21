# Trina Security Starter - 整改版本

Trina Security Starter 是一个统一的认证和权限管理模块，提供了完整的API级接口功能权限和列级数据权限控制。

## 🚀 整改亮点（2025-06-26）

### 1. 统一权限接口
- **统一数据源**：所有角色、权限、数据权限统一从 `http://trina-test.trinasolar.com/app/perms` 获取
- **标准响应格式**：`{"code":0,"message":"",data:{"roles":[],"permissons":[],"dataScops":[]}}`
- **缓存优化**：权限信息统一缓存，提高性能

### 2. WebClient替换RestTemplate
- **响应式编程**：使用WebClient支持非阻塞IO
- **连接池优化**：配置连接超时、读写超时等参数
- **错误处理**：更好的异常处理和重试机制

### 3. 规范化安全上下文
- **SecurityContextHolder优化**：按业内规范重构，支持完整安全上下文
- **缓存键规范化**：使用统一的缓存键命名规范
- **向后兼容**：保持与旧版本的兼容性

### 4. AOP修复
- **@EnableAspectJAutoProxy**：添加AOP自动代理配置
- **切面执行顺序**：明确定义切面执行顺序
- **测试验证**：添加完整的AOP测试用例

## 架构设计

### 核心组件

```mermaid
graph TB
    A[SecurityInterceptor] --> B[WebClient]
    A --> C[SecurityContextHolder]
    A --> D[PermissionService]
    
    D --> E[WebClientPermissionServiceImpl]
    E --> F[权限接口]
    E --> G[Caffeine Cache]
    
    H[PermissionAspect] --> D
    I[ColumnPermissionAspect] --> D
    
    C --> J[SecurityContext]
    J --> K[UserPrincipal]
    J --> L[Authentication]
    J --> M[Authorization]
```

### 权限验证流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Interceptor as SecurityInterceptor
    participant Context as SecurityContextHolder
    participant Service as PermissionService
    participant API as 权限接口
    participant Cache as Caffeine缓存
    participant AOP as 权限切面

    Client->>Interceptor: 1. 请求携带Token
    Interceptor->>Context: 2. 验证Token并设置用户上下文
    Client->>AOP: 3. 调用业务方法
    AOP->>Context: 4. 获取当前用户信息
    AOP->>Service: 5. 验证用户权限
    Service->>Cache: 6. 查询权限缓存
    
    alt 缓存未命中
        Service->>API: 7. 调用权限接口
        API-->>Service: 8. 返回权限数据
        Service->>Cache: 9. 更新缓存
    end
    
    Service-->>AOP: 10. 返回权限验证结果
    
    alt 权限验证失败
        AOP-->>Client: 11. 抛出SecurityException
    else 权限验证成功
        AOP->>AOP: 12. 执行业务逻辑
        AOP-->>Client: 13. 返回结果（可能经过列级权限过滤）
    end
```

## 配置说明

### 基础配置

```yaml
trina:
  iam:
    # IAM服务器地址
    env-url: https://your-iam-server.com
    # 权限接口地址
    permission-url: http://trina-test.trinasolar.com/app/perms
    # 客户端配置
    client-id: your-client-id
    client-secret: your-client-secret
    redirect-url: http://your-app.com/callback
    
    # WebClient配置
    web-client:
      connect-timeout: 5000
      read-timeout: 10000
      write-timeout: 10000
      max-in-memory-size: 1048576
    
    # 白名单配置
    white-uris:
      - /api/public/**
      - /health
      - /actuator/**

# 缓存配置
trina:
  iam:
    cache:
      enabled: true
      initial-capacity: 512
      maximum-size: 10000
      expire-after-write: PT10M
      expire-after-access: PT30M
```

## 使用指南

### 1. 启用安全功能

```java
@SpringBootApplication
@EnableSecurity
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

### 2. 功能权限控制

```java
@RestController
@RequestMapping("/api/users")
public class UserController {

    // 角色权限
    @HasRole("admin")
    @GetMapping("/admin")
    public R<List<User>> getAdminUsers() {
        return R.ok(userService.getAdminUsers());
    }

    // 功能权限
    @HasPermission("user:add")
    @PostMapping
    public R<User> addUser(@RequestBody User user) {
        return R.ok(userService.addUser(user));
    }

    // 复合权限（需要同时拥有多个权限）
    @HasPermission(value = {"user:delete", "user:admin"}, logical = HasPermission.Logical.AND)
    @DeleteMapping("/{id}")
    public R<Void> deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
        return R.ok();
    }
}
```

### 3. 列级权限控制

```java
@RestController
@RequestMapping("/api/employees")
public class EmployeeController {

    // 排除敏感字段
    @ColPermission(role = "general", exc = {"salary", "bonus"})
    @GetMapping
    public R<List<Employee>> getEmployees() {
        return R.ok(employeeService.getAllEmployees());
    }

    // 只显示指定字段
    @ColPermission(role = "hr", inc = {"id", "name", "salary"}, mode = ColPermission.Mode.INCLUDE)
    @GetMapping("/salaries")
    public R<List<Employee>> getEmployeeSalaries() {
        return R.ok(employeeService.getAllEmployees());
    }
}
```

### 4. 工具类使用

```java
@Service
public class BusinessService {

    public void someBusinessMethod() {
        // 获取当前用户信息
        SecurityContext context = SecurityContextHolder.getContext();
        
        // 检查权限
        if (SecurityContextHolder.hasRole("admin")) {
            // 管理员逻辑
        }
        
        if (SecurityContextHolder.hasPermission("user:edit")) {
            // 编辑权限逻辑
        }
        
        // 获取用户基本信息
        String userId = SecurityContextHolder.getCurrentUserId();
        String username = SecurityContextHolder.getCurrentUsername();
    }
}
```

## 自定义扩展

### 自定义权限服务

```java
@Service
@Primary
public class CustomPermissionServiceImpl implements PermissionService {

    @Override
    public Set<String> getUserRoles(String userId) {
        // 自定义角色获取逻辑
        return customRoleService.getRolesByUserId(userId);
    }

    @Override
    public Set<String> getUserPermissions(String userId) {
        // 自定义权限获取逻辑
        return customPermissionService.getPermissionsByUserId(userId);
    }

    // 实现其他方法...
}
```

## 测试验证

### AOP功能测试

```java
@SpringBootTest
@EnableAspectJAutoProxy
class SecurityAspectTest {

    @Test
    void testPermissionAspect() {
        // 设置用户上下文
        IamUserInfo userInfo = new IamUserInfo();
        userInfo.setUid("testUser");
        userInfo.setRoles(Set.of("admin"));
        SecurityContextHolder.setUserInfo(userInfo);

        // 测试权限验证
        assertDoesNotThrow(() -> testService.adminOnlyMethod());
    }
}
```

## 性能优化

### 缓存策略
- **权限信息缓存**：用户权限信息缓存10分钟
- **Token验证缓存**：Token验证结果缓存5分钟
- **分层缓存**：本地缓存 + 远程缓存

### 监控指标
- 缓存命中率
- 权限接口响应时间
- AOP执行时间
- 安全异常统计

## 注意事项

1. **权限接口依赖**：确保权限接口 `http://trina-test.trinasolar.com/app/perms` 可访问
2. **AOP配置**：必须添加 `@EnableAspectJAutoProxy` 注解
3. **缓存一致性**：权限变更后需要刷新缓存
4. **异常处理**：建议配置全局异常处理器处理 `SecurityException`

## 故障排查

### AOP不生效
1. 检查是否添加 `@EnableAspectJAutoProxy` 注解
2. 确认Spring AOP依赖已添加
3. 验证切面Bean是否正确注册

### 权限接口调用失败
1. 检查网络连接
2. 验证接口地址配置
3. 查看WebClient超时配置

### 缓存问题
1. 检查Caffeine配置
2. 监控缓存命中率
3. 验证缓存键命名规范
