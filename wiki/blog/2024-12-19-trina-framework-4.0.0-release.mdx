---
title: Trina Framework 4.0.0 正式发布
authors: [trina-team]
tags: [release, framework, spring-boot, java17]
date: 2025-5-30
---

# 🎉 Trina Framework 4.0.0 正式发布！

我们很高兴地宣布 **Trina Framework 4.0.0** 正式发布！这是一个重要的里程碑版本，标志着我们向现代化企业级微服务开发框架的全面升级。

{/* truncate */}

## 🚀 主要特性

### 技术栈全面升级
- **Spring Boot 3.4.0** - 最新稳定版本，性能和安全性大幅提升
- **Java 17** - 采用最新 LTS 版本，享受现代 Java 特性
- **Spring Cloud 2023** - 微服务生态全面升级
- **Maven 多模块** - 优化的项目结构和依赖管理

### 20+ 核心模块

#### 🏗️ 基础模块
- **trina-common-core** - 核心工具类、异常处理、通用接口
- **trina-common-excel** - Excel 导入导出增强
- **trina-common-sensitive** - 数据脱敏处理

#### 💾 数据访问层
- **trina-mybatis-starter** - MyBatis-Plus 增强，支持多数据源
- **trina-redis-starter** - Redis 缓存、分布式锁、限流
- **trina-elasticsearch-starter** - 全文搜索和数据分析
- **trina-mongodb-starter** - 文档数据库支持
- **trina-datasource-starter** - 动态数据源、读写分离

#### 🌐 Web 和网关
- **trina-web-starter** - Web 应用开发、RESTful API
- **trina-gateway-starter** - API 网关、路由配置
- **trina-openapi-starter** - API 文档自动生成

#### 🔐 安全和认证
- **trina-security-starter** - 安全认证框架
- **trina-xss-starter** - XSS 防护
- **trina-i18n-starter** - 国际化支持

#### 🔧 服务治理
- **trina-sentinel-starter** - 限流熔断
- **trina-seata-starter** - 分布式事务
- **trina-remote-starter** - 远程服务调用

#### 📦 基础设施
- **trina-minio-starter** - 对象存储
- **trina-mq-starter** - 消息队列
- **trina-sftp-starter** - 文件传输
- **trina-sequence-starter** - 序列号生成
- **trina-xxl-starter** - 分布式任务调度

#### 🤖 AI 和扩展
- **trina-ai-starter** - AI 集成支持
- **trina-kubernetes-starter** - K8s 部署支持
- **trina-neo4j-starter** - 图数据库支持
- **trina-build-info-starter** - 构建信息和版本跟踪模块

## ✨ 核心亮点

### 1. 统一查询框架
```java
// 基于 BaseQuery 的统一查询
BaseQuery query = BaseQuery.builder()
    .eq("status", 1)
    .like("name", "trina")
    .between("createTime", startTime, endTime)
    .orderByDesc("id")
    .page(1, 10)
    .build();
```

### 2. 模块化设计
```xml
<!-- 按需引入模块 -->
<dependency>
    <groupId>com.trina</groupId>
    <artifactId>trina-web-starter</artifactId>
</dependency>
```

### 3. 生产就绪
- 内置监控和链路追踪
- 限流熔断和降级
- 分布式事务支持
- 多数据源和读写分离

## 📚 完整文档

我们为 4.0.0 版本提供了全面的文档：

- **[架构设计](/docs/architecture/)** - 了解框架设计理念
- **[使用指南](/docs/guides/)** - 详细的使用说明
- **[教程](/docs/tutorials/)** - 15分钟快速上手系列

## 🔄 迁移指南

### 从 3.x 升级到 4.0.0

1. **Java 版本升级**
   ```bash
   # 确保使用 Java 17
   java -version
   ```

2. **依赖版本更新**
   ```xml
   <parent>
       <groupId>com.trina</groupId>
       <artifactId>trina-parent</artifactId>
       <version>4.0.0</version>
   </parent>
   ```

3. **配置文件调整**
   - Spring Boot 3.x 配置变更
   - 新增模块配置项

## 🎯 未来规划

- **4.1.0** - AI 模块增强，支持更多 AI 服务
- **4.2.0** - 云原生支持，Kubernetes 集成优化
- **4.3.0** - 性能优化，响应式编程支持
