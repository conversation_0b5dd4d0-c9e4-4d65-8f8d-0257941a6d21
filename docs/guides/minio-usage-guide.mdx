---
title: MinIO 模块使用指南
description: Trina MinIO 模块的使用方法与最佳实践
---

# MinIO 模块使用指南

## 1. 简介

Trina MinIO Starter 是 Trina Common 框架中的对象存储模块，基于 MinIO 客户端库实现。该模块提供了完整的对象存储功能和标准化的 RESTful API，支持文件上传、下载、管理等操作，帮助开发者快速集成对象存储服务，同时保持代码的灵活性和可维护性。

### 1.1 核心特性

- **标准化的 RESTful API 设计**：提供统一的 API 接口
- **文件操作**：支持文件上传、下载、删除等基本操作
- **临时访问 URL**：支持生成带有时效性的访问链接
- **存储桶管理**：支持存储桶的创建、检查和列举
- **文件元数据管理**：支持管理文件的元数据信息
- **Spring Boot 集成**：与 Spring Boot 无缝集成

## 2. 快速开始

### 2.1 添加依赖

在项目的 `pom.xml` 中添加以下依赖：

```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-minio-starter</artifactId>
    <version>${trina-common.version}</version>
</dependency>
```

### 2.2 配置属性

在 `application.yml` 中添加 MinIO 配置：

```yaml
trina:
  minio:
    endpoint: ${MINIO_URL:http://localhost:9000}
    access-key: ${ACCESS_KEY:minioadmin}
    secret-key: ${SECRET_KEY:minioadmin}
    bucket: ${BUCKET_NAME:default-bucket}
    connect-timeout: 5000
    write-timeout: 60000
    read-timeout: 30000
```

### 2.3 使用 MinioService

通过依赖注入使用 `MinioService`：

```java
@Service
public class FileService {
    
    @Autowired
    private MinioService minioService;
    
    /**
     * 上传文件
     */
    public String uploadFile(MultipartFile file, String objectName) {
        try {
            // 确保存储桶存在
            String bucket = "my-bucket";
            if (!minioService.bucketExists(bucket)) {
                minioService.createBucket(bucket);
            }
            
            // 上传文件
            return minioService.uploadFile(file, bucket, objectName);
        } catch (Exception e) {
            throw new RuntimeException("文件上传失败", e);
        }
    }
    
    /**
     * 下载文件
     */
    public void downloadFile(String bucket, String objectName, HttpServletResponse response) {
        try {
            minioService.downloadFile(bucket, objectName, response);
        } catch (Exception e) {
            throw new RuntimeException("文件下载失败", e);
        }
    }
    
    /**
     * 获取文件临时访问链接
     */
    public String getFileUrl(String bucket, String objectName) {
        try {
            return minioService.getPresignedUrl(bucket, objectName, 3600); // 1小时有效期
        } catch (Exception e) {
            throw new RuntimeException("获取文件链接失败", e);
        }
    }
}
```

## 3. 核心 API

### 3.1 MinioService 接口

```java
public interface MinioService {
    /**
     * 存储桶操作
     */
    boolean createBucket(String bucket);
    boolean bucketExists(String bucket);
    List<Bucket> listBuckets();
    
    /**
     * 文件操作
     */
    String uploadFile(MultipartFile file, String bucket, String objectName);
    void downloadFile(String bucket, String objectName, HttpServletResponse response);
    boolean deleteFile(String bucket, String objectName);
    StatObjectResponse getFileInfo(String bucket, String objectName);
    
    /**
     * 列表操作
     */
    List<Item> listObjects(String bucket, String prefix, boolean recursive);
    
    /**
     * URL 操作
     */
    String getPresignedUrl(String bucket, String objectName, int expires);
    String getPresignedPutUrl(String bucket, String objectName, int expires);
}
```

### 3.2 存储桶管理

```java
// 创建存储桶
boolean created = minioService.createBucket("my-documents");

// 检查存储桶是否存在
boolean exists = minioService.bucketExists("my-documents");

// 列出所有存储桶
List<Bucket> buckets = minioService.listBuckets();
for (Bucket bucket : buckets) {
    System.out.println("存储桶名称: " + bucket.name());
    System.out.println("创建时间: " + bucket.creationDate());
}
```

### 3.3 文件上传

```java
// 简单上传
String url = minioService.uploadFile(multipartFile, "my-bucket", "path/to/file.txt");

// 自定义文件名上传
String fileName = UUID.randomUUID().toString() + "-" + multipartFile.getOriginalFilename();
String url = minioService.uploadFile(multipartFile, "my-bucket", "uploads/" + fileName);

// 带文件夹结构上传
String yearMonth = new SimpleDateFormat("yyyy/MM").format(new Date());
String objectName = "documents/" + yearMonth + "/" + multipartFile.getOriginalFilename();
String url = minioService.uploadFile(multipartFile, "my-bucket", objectName);
```

### 3.4 文件下载

```java
// 直接下载到响应
@GetMapping("/download")
public void download(@RequestParam String bucket, 
                    @RequestParam String objectName, 
                    HttpServletResponse response) {
    minioService.downloadFile(bucket, objectName, response);
}

// 获取临时下载链接
@GetMapping("/url")
public String getDownloadUrl(@RequestParam String bucket, 
                           @RequestParam String objectName) {
    return minioService.getPresignedUrl(bucket, objectName, 3600);
}
```

### 3.5 文件操作

```java
// 删除文件
boolean deleted = minioService.deleteFile("my-bucket", "path/to/file.txt");

// 获取文件信息
StatObjectResponse info = minioService.getFileInfo("my-bucket", "path/to/file.txt");
System.out.println("文件大小: " + info.size());
System.out.println("最后修改时间: " + info.lastModified());
System.out.println("Content-Type: " + info.contentType());

// 列出文件
List<Item> items = minioService.listObjects("my-bucket", "documents/", true);
for (Item item : items) {
    System.out.println("文件名: " + item.objectName());
    System.out.println("大小: " + item.size());
    System.out.println("最后修改时间: " + item.lastModified());
}
```

## 4. 控制器示例

### 4.1 文件上传控制器

```java
@RestController
@RequestMapping("/api/files")
public class FileController {
    
    @Autowired
    private MinioService minioService;
    
    @PostMapping("/upload")
    public R<String> uploadFile(@RequestParam("file") MultipartFile file,
                              @RequestParam(value = "bucket", required = false) String bucket) {
        try {
            // 使用默认存储桶（如果未指定）
            if (StringUtils.isEmpty(bucket)) {
                bucket = "default-bucket";
            }
            
            // 确保存储桶存在
            if (!minioService.bucketExists(bucket)) {
                minioService.createBucket(bucket);
            }
            
            // 生成文件路径
            String fileName = UUID.randomUUID().toString() + "-" + file.getOriginalFilename();
            String yearMonth = new SimpleDateFormat("yyyy/MM").format(new Date());
            String objectName = yearMonth + "/" + fileName;
            
            // 上传文件
            String url = minioService.uploadFile(file, bucket, objectName);
            
            return R.ok(url, "文件上传成功");
        } catch (Exception e) {
            return R.fail("文件上传失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/download/{bucket}/{objectName}")
    public void downloadFile(@PathVariable String bucket,
                           @PathVariable String objectName,
                           HttpServletResponse response) {
        try {
            minioService.downloadFile(bucket, objectName, response);
        } catch (Exception e) {
            throw new RuntimeException("文件下载失败", e);
        }
    }
    
    @GetMapping("/url/{bucket}/{objectName}")
    public R<String> getFileUrl(@PathVariable String bucket,
                              @PathVariable String objectName,
                              @RequestParam(defaultValue = "3600") Integer expires) {
        try {
            String url = minioService.getPresignedUrl(bucket, objectName, expires);
            return R.ok(url);
        } catch (Exception e) {
            return R.fail("获取文件链接失败: " + e.getMessage());
        }
    }
    
    @DeleteMapping("/{bucket}/{objectName}")
    public R<Boolean> deleteFile(@PathVariable String bucket,
                               @PathVariable String objectName) {
        try {
            boolean deleted = minioService.deleteFile(bucket, objectName);
            return R.ok(deleted, deleted ? "文件删除成功" : "文件删除失败");
        } catch (Exception e) {
            return R.fail("文件删除失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/list/{bucket}")
    public R<List<Map<String, Object>>> listFiles(@PathVariable String bucket,
                                               @RequestParam(required = false) String prefix,
                                               @RequestParam(defaultValue = "false") boolean recursive) {
        try {
            List<Item> items = minioService.listObjects(bucket, prefix, recursive);
            List<Map<String, Object>> result = new ArrayList<>();
            
            for (Item item : items) {
                Map<String, Object> fileInfo = new HashMap<>();
                fileInfo.put("name", item.objectName());
                fileInfo.put("size", item.size());
                fileInfo.put("lastModified", item.lastModified());
                fileInfo.put("isDir", item.isDir());
                result.add(fileInfo);
            }
            
            return R.ok(result);
        } catch (Exception e) {
            return R.fail("获取文件列表失败: " + e.getMessage());
        }
    }
}
```

## 5. 高级特性

### 5.1 分片上传

对于大文件，建议使用分片上传：

```java
@Service
public class FileUploadService {
    
    @Autowired
    private MinioClient minioClient;
    
    public String multipartUpload(String bucket, String objectName, File file) throws Exception {
        // 创建分片上传请求
        CreateMultipartUploadResponse createResponse = minioClient.createMultipartUpload(
            CreateMultipartUploadArgs.builder()
                .bucket(bucket)
                .object(objectName)
                .build());
        
        String uploadId = createResponse.result().uploadId();
        
        // 计算分片大小（5MB）
        int partSize = 5 * 1024 * 1024;
        long fileSize = file.length();
        int partCount = (int) Math.ceil((double) fileSize / partSize);
        
        // 上传分片
        List<Part> parts = new ArrayList<>();
        try (RandomAccessFile randomAccessFile = new RandomAccessFile(file, "r")) {
            for (int i = 1; i <= partCount; i++) {
                long partPos = (i - 1) * partSize;
                long currentPartSize = Math.min(partSize, fileSize - partPos);
                
                // 读取分片数据
                byte[] partData = new byte[(int) currentPartSize];
                randomAccessFile.seek(partPos);
                randomAccessFile.read(partData);
                
                // 上传分片
                ByteArrayInputStream partInputStream = new ByteArrayInputStream(partData);
                UploadPartResponse uploadResponse = minioClient.uploadPart(
                    UploadPartArgs.builder()
                        .bucket(bucket)
                        .object(objectName)
                        .uploadId(uploadId)
                        .partNumber(i)
                        .stream(partInputStream, currentPartSize, -1)
                        .build());
                
                // 记录分片信息
                parts.add(new Part(i, uploadResponse.etag()));
            }
        }
        
        // 完成分片上传
        minioClient.completeMultipartUpload(
            CompleteMultipartUploadArgs.builder()
                .bucket(bucket)
                .object(objectName)
                .uploadId(uploadId)
                .parts(parts)
                .build());
        
        // 返回文件访问链接
        return minioClient.getPresignedObjectUrl(
            GetPresignedObjectUrlArgs.builder()
                .bucket(bucket)
                .object(objectName)
                .method(Method.GET)
                .build());
    }
}
```

### 5.2 文件类型限制

通过白名单控制可上传的文件类型：

```java
@Component
public class FileTypeValidator {
    
    private static final Set<String> ALLOWED_EXTENSIONS = new HashSet<>(Arrays.asList(
        "jpg", "jpeg", "png", "gif", "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt"
    ));
    
    /**
     * 检查文件类型是否允许
     */
    public boolean isAllowedFileType(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        return ALLOWED_EXTENSIONS.contains(extension);
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int dotIndex = fileName.lastIndexOf('.');
        return (dotIndex == -1) ? "" : fileName.substring(dotIndex + 1);
    }
}
```

### 5.3 文件命名规范

为了避免文件覆盖和提高查询效率，建议使用以下命名规则：

```
/{业务模块}/{年月日}/{随机UUID}_{原始文件名}
```

示例实现：

```java
public String generateObjectName(String module, String originalFilename) {
    // 生成日期路径（yyyy/MM/dd）
    String datePath = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
    
    // 生成随机UUID
    String uuid = UUID.randomUUID().toString().replaceAll("-", "");
    
    // 构建对象名
    return String.format("%s/%s/%s_%s", module, datePath, uuid, originalFilename);
}
```

## 6. 最佳实践

### 6.1 存储桶设计

- **按业务划分存储桶**：不同业务使用不同存储桶，如 `user-avatars`、`product-images`、`documents` 等
- **存储桶命名规范**：使用小写字母、数字和连字符，避免使用下划线和特殊字符
- **避免过多存储桶**：控制存储桶数量，一般不超过 10-20 个

### 6.2 文件组织

- **使用文件夹结构**：通过前缀模拟文件夹结构，如 `users/avatars/123.jpg`
- **时间分层**：对于大量文件，使用时间进行分层，如 `logs/2023/04/01/app.log`
- **元数据管理**：使用标准元数据字段，如 `Content-Type`、`Content-Disposition` 等

### 6.3 安全考虑

- **使用临时 URL**：对外部访问使用带时效性的临时 URL
- **权限控制**：合理设置存储桶和对象的访问权限
- **加密传输**：使用 HTTPS 进行数据传输
- **内容验证**：上传前验证文件类型、大小和内容

### 6.4 性能优化

- **预签名 URL**：使用预签名 URL 减轻服务器负担
- **分片上传**：大文件（>100MB）使用分片上传
- **并行上传**：多文件并行上传
- **CDN 加速**：结合 CDN 加速文件分发

## 7. 常见问题与解决方案

### 7.1 连接问题

**问题**：MinIO 服务连接失败

**解决方案**：
1. 检查 MinIO 服务是否正常运行
2. 确认 endpoint、accessKey 和 secretKey 是否正确
3. 验证网络连接是否通畅
4. 检查防火墙设置是否允许相应端口

### 7.2 权限问题

**问题**：操作 MinIO 时出现权限错误

**解决方案**：
1. 确认 accessKey 和 secretKey 正确且有足够权限
2. 检查存储桶和对象的访问策略
3. 确认存储桶已存在且可访问

### 7.3 文件大小限制

**问题**：上传大文件失败

**解决方案**：
1. 使用分片上传处理大文件
2. 调整 MinIO 客户端连接超时设置
3. 增加 Web 服务器的文件上传大小限制（如 Tomcat 的 `maxSwallowSize`） 