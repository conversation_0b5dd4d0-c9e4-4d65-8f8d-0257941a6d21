---
title: Trina Security Starter 架构文档
description: Trina Security Starter 组件的技术架构、设计原理与实现说明
---

# Trina Security Starter 架构文档

## 1. 概述

Trina Security Starter 是 Trina Common 框架的统一认证与权限管理组件，支持 OAuth2 认证、RBAC 角色权限、注解式功能权限、列级数据权限、缓存优化等能力。该模块通过注解和自动配置，极大简化了企业级项目的安全开发和权限管理。

## 2. 核心功能

- 基于 OAuth2 的统一认证与 Token 校验
- 角色（RBAC）与功能权限注解（@HasRole/@HasPermission）
- 列级数据权限控制（@ColPermission）
- 权限与用户信息缓存（Caffeine）
- 白名单路径配置
- 注解驱动与 AOP 拦截
- 灵活的权限逻辑（AND/OR）
- 支持多环境配置与扩展

## 3. 技术实现

### 3.1 主要依赖

| 依赖组件 | 版本 | 说明 |
|----------|------|------|
| spring-boot-starter-web      | 最新 | Web 支持 |
| spring-boot-starter-aop      | 最新 | AOP 权限切面 |
| spring-boot-starter-actuator | 最新 | 健康检查与监控 |
| caffeine                    | 最新 | 本地缓存 |
| trina-common-core            | 4.0.0 | 基础工具支持 |

### 3.2 关键类与结构

- `@EnableSecurity` 注解：一键启用安全能力，自动导入配置
- `SecurityProperties`：核心安全配置，支持 yml 配置
- `AutoSecurityConfiguration`：自动配置入口，注册拦截器、切面、服务等
- `SecurityInterceptor`：Token 校验与权限上下文构建
- `PermissionService`：权限服务接口，支持自定义实现
- `RolePermissionAspect`、`PermissionAspect`、`ColumnPermissionAspect`：注解驱动的权限切面
- `IamCaffeineCacheConfiguration`：Caffeine 缓存配置

### 3.3 配置原理

- 通过 `@EnableSecurity` 注解自动导入配置类和属性绑定
- 支持 `trina.iam.*` 配置项，灵活适配不同 IAM 平台
- 支持白名单路径、权限接口、客户端信息等多项配置
- 支持自定义权限服务与缓存策略

## 4. 架构图

### 4.1 组件架构

```mermaid
graph TD
    A[应用主类] -->> B[EnableSecurity 注解]
    B -->> C{AutoSecurityConfiguration}
    C -- 注册 --> D[SecurityInterceptor]
    C -- 注册 --> E[RolePermissionAspect]
    C -- 注册 --> F[PermissionAspect]
    C -- 注册 --> G[ColumnPermissionAspect]
    C -- 注册 --> H[PermissionService]
    C -- 注册 --> I[Caffeine 缓存]
    D -- 校验Token --> J[IAM服务器]
    D -- 获取权限 --> K[权限接口]
    D -- 构建 --> L[SecurityContext]
    E -- AOP拦截 --> M[业务方法]
    F -- AOP拦截 --> M
    G -- AOP拦截 --> M
```

### 4.2 权限验证流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Interceptor as SecurityInterceptor
    participant Cache as iamCache
    participant IAM as IAM服务器
    participant PermAPI as 权限接口
    participant Context as SecurityContextHolder
    participant AOP as 权限切面

    Client->>Interceptor: 请求携带Token
    Interceptor->>Cache: 检查Token缓存
    alt Token缓存命中
        Cache-->>Interceptor: 返回userId
        Interceptor->>Context: 构建SecurityContext(基础信息)
    else Token缓存未命中
        Interceptor->>IAM: 远程验证Token获取用户信息
        IAM-->>Interceptor: 返回用户基本信息
        Interceptor->>Context: 构建SecurityContext(完整信息)
        Interceptor->>Cache: 缓存Token验证结果(只存userId)
    end
    Interceptor->>PermAPI: 统一获取用户权限数据
    PermAPI-->>Interceptor: 返回{"roles":[],"permissions":[],"dataScopes":[]}
    Interceptor->>Context: 填充权限信息到SecurityContext
    Client->>AOP: 调用业务方法
    AOP->>Context: 获取SecurityContext验证权限
    alt 权限验证失败
        AOP-->>Client: 抛出SecurityException
    else 权限验证成功
        AOP->>AOP: 执行业务逻辑
        AOP->>AOP: 列级权限过滤(如有@ColPermission)
        AOP-->>Client: 返回结果
    end
    Note over Interceptor: 请求结束时清理SecurityContext
```

## 5. 最佳实践

- 仅在主类或配置类上添加一次 `@EnableSecurity` 注解
- 精确配置 `trina.iam.white-uris`，避免误放开敏感接口
- 合理设置缓存参数，提升性能
- 结合自定义 `PermissionService` 实现复杂权限逻辑
- 结合 CI/CD 自动校验权限注解覆盖率
- 生产环境务必配置 IAM 地址、clientId、clientSecret、redirectUrl

## 6. 常见问题

### 6.1 启动报"安全模块配置不完整"？
- 检查 `trina.iam.env-url`、`client-id`、`client-secret`、`redirect-url` 是否配置完整

### 6.2 权限注解不生效？
- 检查是否已启用 `@EnableSecurity` 注解
- 检查切面和拦截器是否被 Spring 扫描

### 6.3 Token 校验失败？
- 检查 IAM 服务可用性和配置项
- 检查 Token 是否过期或被篡改

### 6.4 如何自定义权限服务？
- 实现 `PermissionService` 接口并注入为 Bean

## 7. 参考资料

- [Spring Security 官方文档](https://docs.spring.io/spring-security/)
- [Caffeine 缓存文档](https://github.com/ben-manes/caffeine)
- [Trina Common Core 架构文档](./common-core-architecture.mdx)
- [Trina Security 使用指南](../guides/security-usage-guide.mdx) 