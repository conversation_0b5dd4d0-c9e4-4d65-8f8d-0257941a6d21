---
title: 文档导航
description: Trina Common 框架文档导航指南
---

# 📚 Trina Common 文档导航

欢迎使用 Trina Common 开发框架！本导航将帮助您快速找到所需的文档内容。

## 📋 顶部菜单

### 🏗️ Trina-Common

#### 🚀 快速开始
- **[项目概览](./README.mdx)** - 了解框架特性和技术栈
- **[设计原则](./architecture/principles.mdx)** - 理解框架设计理念
- **[架构概览](./architecture/trina-architecture.mdx)** - 掌握整体架构
- **[15分钟熟悉架构](./tutorials/15-min-trina-architecture.mdx)** - 快速体验框架
- **[15分钟快速上手](./tutorials/15-min-trina-common.mdx)** - 快速体验框架

#### 📖 使用指南
- **[父模块配置](./guides/parent-module-guide.mdx)** - 项目基础配置
- **[核心功能使用](./guides/common-core-usage-guide.mdx)** - 核心工具使用
- **[Web开发指南](./guides/web-usage-guide.mdx)** - Web应用开发

#### 💾 数据访问
- **[MyBatis使用指南](./guides/mybatis-usage-guide.mdx)** - 数据库操作
- **[多数据源配置](./guides/datasource-usage-guide.mdx)** - 多数据源管理
- **[Redis缓存](./guides/redis-usage-guide.mdx)** - 缓存使用
- **[MongoDB集成](./guides/mongodb-usage-guide.mdx)** - 文档数据库
- **[Elasticsearch搜索](./guides/elasticsearch-usage-guide.mdx)** - 全文搜索

#### 🌐 分布式服务
- **[分布式ID](./guides/sequence-usage-guide.mdx)** - ID生成策略
- **[分布式事务](./guides/seata-usage-guide.mdx)** - Seata事务管理

#### 🔒 安全与监控
- **[XSS防护](./guides/xss-guide.mdx)** - 跨站脚本防护
- **[API文档](./guides/openapi-usage-guide.mdx)** - OpenAPI集成
- **[网关配置](./guides/gateway-usage-guide.mdx)** - API网关

#### ☁️ 云服务集成
- **[对象存储](./guides/minio-usage-guide.mdx)** - MinIO文件存储
- **[文件传输](./guides/sftp-usage-guide.mdx)** - SFTP集成
- **[任务调度](./guides/xxl-usage-guide.mdx)** - XXL-JOB定时任务

#### 🏛️ 架构文档
- **[整体架构](./architecture/trina-architecture.mdx)** - 框架整体设计
- **[模块依赖关系](./architecture/module-dependencies.mdx)** - 模块间依赖图
- **[核心组件架构](./architecture/common-core-architecture.mdx)** - 核心组件设计
- **[Web架构](./architecture/web-architecture.mdx)** - Web层设计
- **[MyBatis架构](./architecture/mybatis-architecture.mdx)** - 数据访问层
- **[Redis架构](./architecture/redis-architecture.mdx)** - 缓存架构
- **[MongoDB架构](./architecture/mongodb-architecture.mdx)** - 文档数据库架构
- **[Elasticsearch架构](./architecture/elasticsearch-architecture.mdx)** - 搜索引擎架构
- **[微服务架构](./architecture/microservice-architecture.mdx)** - 微服务架构设计
- **[MinIO架构](./architecture/minio-architecture.mdx)** - 对象存储架构
- **[远程调用架构](./architecture/remote-architecture.mdx)** - RPC架构
- **[Seata架构](./architecture/seata-architecture.mdx)** - 分布式事务架构
- **[依赖管理](./architecture/dependencies.mdx)** - 依赖关系管理

### 🖥️ 后端脚手架

#### 📚 技术教程
- **[Java 17新特性](./tutorials/15-min-java17.mdx)** - 现代Java特性
- **[Java 17并发编程](./tutorials/15-min-java17-concurrency.mdx)** - 并发编程实践
- **[Spring Boot 3](./tutorials/15-min-springboot3.mdx)** - 新版本特性
- **[Spring Cloud](./tutorials/15-min-spring-cloud.mdx)** - 微服务架构
- **[MyBatis-Plus](./tutorials/15-min-mybatis-plus.mdx)** - ORM框架进阶
- **[Redis最佳实践](./tutorials/15-min-redis-best-practices.mdx)** - 缓存优化
- **[微服务核心](./tutorials/15min-microservices-core.mdx)** - 微服务设计
- **[Seata分布式事务](./tutorials/15-min-seata.mdx)** - 事务一致性
- **[XXL-JOB调度](./tutorials/15-min-xxl-job.mdx)** - 分布式任务
- **[Spring AI](./tutorials/15-min-spring-ai.mdx)** - AI集成开发
- **[Arthas诊断](./tutorials/15-min-arthas.mdx)** - 应用诊断工具
- **[安全基础](./tutorials/15min-security-essentials.mdx)** - Web安全防护
- **[Elasticsearch](./tutorials/15min-elasticsearch.mdx)** - 搜索引擎
- **[消息队列](./tutorials/15min-message-queue.mdx)** - 消息中间件

#### 🎯 项目脚手架
- **[脚手架概览](./scaffold/README.mdx)** - 项目脚手架介绍
- **[快速创建项目](./scaffold/README.mdx#快速开始)** - 一键生成项目
- **[项目结构说明](./scaffold/README.mdx#项目结构)** - 标准项目结构
- **[配置说明](./scaffold/README.mdx#配置文件)** - 配置文件详解

### 🎨 前端脚手架

#### 📱 前端开发
- **[前端脚手架](./frontend/README.md)** - Vue 3 + TypeScript + Vite + Element Plus
- **[开发规范](./frontend/README.md#开发规范)** - 代码规范和最佳实践
- **[环境配置](./frontend/README.md#环境配置)** - 开发环境搭建
- **[项目结构](./frontend/README.md#项目结构)** - 前端项目结构说明
- **[开发流程](./frontend/README.md#开发流程)** - 标准开发流程

## 🎯 使用场景导航

### 我想要...

#### 🆕 开始新项目
1. 阅读 [项目概览](./README.mdx)
2. 配置 [父模块](./guides/parent-module-guide.mdx)
3. 学习 [核心功能](./guides/common-core-usage-guide.mdx)
4. 参考 [15分钟快速上手](./tutorials/15-min-trina-architecture.mdx)

#### 🔧 集成数据库
1. 配置 [MyBatis](./guides/mybatis-usage-guide.mdx)
2. 设置 [多数据源](./guides/datasource-usage-guide.mdx)
3. 了解 [架构设计](./architecture/mybatis-architecture.mdx)

#### 🚀 构建微服务
2. 学习 [微服务核心](./tutorials/15min-microservices-core.mdx)

#### 🔒 加强安全性
1. 启用 [XSS防护](./guides/xss-guide.mdx)
2. 学习 [安全基础](./tutorials/15min-security-essentials.mdx)
3. 配置 [API网关](./guides/gateway-usage-guide.mdx)

#### 📊 性能优化
1. 配置 [Redis缓存](./guides/redis-usage-guide.mdx)
2. 学习 [Redis最佳实践](./tutorials/15-min-redis-best-practices.mdx)
3. 使用 [Arthas诊断](./tutorials/15-min-arthas.mdx)

## 📋 文档状态

### ✅ 完整文档
- 核心功能模块
- Web开发指南
- 数据访问组件
- 基础教程系列

### 🚧 持续更新
- 新兴技术集成
- 最佳实践案例
- 性能优化指南

### 📝 计划中
- 更多实战案例
- 故障排查指南
- 部署运维文档

---

## 💡 阅读建议

- **初学者**：按照"新手入门"顺序阅读
- **有经验开发者**：直接查看相关组件文档
- **架构师**：重点关注架构设计文档
- **运维人员**：关注部署和监控相关内容

如有疑问，请查看具体模块的详细文档或提交 Issue。