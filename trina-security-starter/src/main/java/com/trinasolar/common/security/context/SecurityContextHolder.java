package com.trinasolar.common.security.context;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.trinasolar.common.security.domain.SecurityContext;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 安全上下文持有者
 * <p>
 * 统一使用SecurityContext存储所有安全信息
 * 使用TransmittableThreadLocal支持异步线程传递
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@UtilityClass
public class SecurityContextHolder {

    /**
     * 安全上下文ThreadLocal
     */
    private final ThreadLocal<SecurityContext> SECURITY_CONTEXT = new TransmittableThreadLocal<>();

    /**
     * 获取安全上下文
     *
     * @return {@link SecurityContext}
     */
    public SecurityContext getContext() {
        return SECURITY_CONTEXT.get();
    }

    /**
     * 设置安全上下文
     *
     * @param context 安全上下文
     */
    public void setContext(SecurityContext context) {
        if (context == null) {
            clearContext();
        } else {
            SECURITY_CONTEXT.set(context);
            log.debug("Security context set for user: {}", context.getUserId());
        }
    }

    /**
     * 清除安全上下文
     */
    public void clearContext() {
        SECURITY_CONTEXT.remove();
        log.debug("Security context cleared");
    }

    /**
     * 获取当前用户ID
     */
    public String getCurrentUserId() {
        SecurityContext context = getContext();
        return context != null ? context.getUserId() : null;
    }

    /**
     * 获取当前用户名
     */
    public String getCurrentUsername() {
        SecurityContext context = getContext();
        return context != null ? context.getUsername() : null;
    }

    /**
     * 获取当前用户显示名称
     */
    public String getCurrentDisplayName() {
        SecurityContext context = getContext();
        return context != null ? context.getDisplayName() : null;
    }

    /**
     * 检查当前用户是否已认证
     */
    public boolean isAuthenticated() {
        SecurityContext context = getContext();
        return context != null && context.isAuthenticated();
    }

    /**
     * 检查当前用户是否拥有指定角色
     */
    public boolean hasRole(String role) {
        SecurityContext context = getContext();
        return context != null && context.hasRole(role);
    }

    /**
     * 检查当前用户是否拥有任意一个角色
     */
    public boolean hasAnyRole(String... roles) {
        SecurityContext context = getContext();
        return context != null && context.hasAnyRole(roles);
    }

    /**
     * 检查用户是否拥有所有角色
     */
    public boolean hasAllRoles(String... roles) {
        SecurityContext context = getContext();
        return context != null && context.hasAllRoles(roles);
    }

    /**
     * 检查当前用户是否拥有指定权限
     */
    public boolean hasPermission(String permission) {
        SecurityContext context = getContext();
        return context != null && context.hasPermission(permission);
    }

    /**
     * 检查当前用户是否拥有任意一个权限
     */
    public boolean hasAnyPermission(String... permissions) {
        SecurityContext context = getContext();
        return context != null && context.hasAnyPermission(permissions);
    }

    /**
     * 检查用户是否拥有所有权限
     */
    public boolean hasAllPermissions(String... permissions) {
        SecurityContext context = getContext();
        return context != null && context.hasAllPermissions(permissions);
    }

    /**
     * 获取当前用户角色
     */
    public List<String> getCurrentUserRoleCodes() {
        SecurityContext context = getContext();
        return context != null && context.getAuthorization() != null
                ? context.getAuthorization().getRoleCodes() : null;
    }

    /**
     * 获取当前用户权限
     */
    public List<String> getCurrentUserPermissions() {
        SecurityContext context = getContext();
        return context != null && context.getAuthorization() != null
                ? context.getAuthorization().getPermissions() : null;
    }

    /**
     * 获取当前用户数据权限范围
     */
    public List<SecurityContext.Role> getCurrentUserRoles() {
        SecurityContext context = getContext();
        return context != null && context.getAuthorization() != null
                ? context.getAuthorization().getRoles() : null;
    }
}
