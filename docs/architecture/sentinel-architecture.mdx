---
title: Sentinel Starter 架构文档
description: Trina Sentinel Starter 的架构设计、核心实现与规则动态管理原理
---

# 1. 概述
Trina Sentinel Starter 基于 Spring Cloud Alibaba Sentinel，增强了自动装配、Nacos规则动态管理、配置增强、RestTemplate限流熔断等能力，助力微服务流量防护和高可用。

# 2. 核心功能
- 自动装配 Sentinel 及其依赖，开箱即用
- 支持 Nacos 动态规则管理（流控/熔断）
- RestTemplate/Feign 限流与熔断处理
- 配置增强器自动补全常用配置
- 规则优先级：Nacos > 本地配置 > 默认规则
- 启动日志详细，便于排查

# 3. 技术实现
- SentinelAutoConfiguration 自动装配 RestTemplate 支持限流/熔断
- SentinelNacosAutoConfiguration 动态注册 Nacos 规则数据源
- SentinelConfigurationEnhancer 自动补全常用配置和 Nacos 数据源
- SentinelRuleAutoConfiguration 提供本地默认规则
- 支持通过环境变量灵活配置 dashboard、规则数据源等

# 4. 架构图
```mermaid
flowchart TD
    A[应用启动] --> B[SentinelConfigurationEnhancer]
    B --> C[补全默认配置]
    C --> D[SentinelAutoConfiguration]
    D --> E[RestTemplate/Feign限流熔断]
    C --> F[SentinelNacosAutoConfiguration]
    F --> G[Nacos规则数据源注册]
    C --> H[SentinelRuleAutoConfiguration]
    H --> I[本地规则加载]
    G --> J[动态规则下发]
    I --> J
    J --> K[流控/熔断生效]
    style F fill:#E3EFFF,stroke:#4A90E2
    style G fill:#FFF7E3,stroke:#F5A623
```

# 5. 最佳实践
- 推荐通过 Nacos 管理所有规则，便于动态调整
- 项目 application.yml 建议配置 dashboard 地址和端口
- 关注启动日志，及时发现规则加载和配置问题
- 规则优先级：Nacos > 本地配置 > 默认规则

# 6. 常见问题
- 规则未生效：检查 Nacos 配置、group/dataId 是否一致
- dashboard 无法连接：确认端口和地址配置正确
- RestTemplate/Feign 未限流：确认注解和依赖已生效
- 本地规则被覆盖：Nacos 规则优先生效

# 7. 参考资料
- [Spring Cloud Alibaba Sentinel 官方文档](https://github.com/alibaba/Sentinel/wiki)
- [Sentinel Nacos 数据源文档](https://github.com/alibaba/Sentinel/wiki/%E4%B8%AD%E6%96%87%E6%96%87%E6%A1%A3#nacos-%E6%95%B0%E6%8D%AE%E6%BA%90) 