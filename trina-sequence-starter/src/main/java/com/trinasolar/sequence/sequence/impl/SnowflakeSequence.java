package com.trinasolar.sequence.sequence.impl;


import com.trinasolar.sequence.exception.SeqException;
import com.trinasolar.sequence.sequence.Sequence;
import lombok.extern.slf4j.Slf4j;

/**
 * 使用雪花算法
 * 一个long类型的数据，64位。以下是每位的具体含义。
 * <br>
 * snowflake的结构如下(每部分用-分开):
 * <br>
 * 0 - 0000000000 0000000000 0000000000 0000000000 0 - 00000 - 00000 - 000000000000
 * <br>
 * （1）第一位为未使用
 * （2）接下来的41位为毫秒级时间(41位的长度可以使用69年)
 * （3）然后是5位datacenterId
 * （4）5位workerId
 * （5）最后12位是毫秒内的计数（12位的计数顺序号支持每个节点每毫秒产生4096个ID序号）
 * <br>
 * 一共加起来刚好64位，为一个Long型。(转换成字符串长度为18)
 */
@Slf4j
public class SnowflakeSequence implements Sequence {

    /**
     * 开始时间截 (2018-01-01)
     */
    private final long twepoch = 1514736000000L;

    /**
     * 机器id所占的位数
     */
    private final long workerIdBits = 10L;

    /**
     * 数据标识id所占的位数
     */
    private final long datacenterIdBits = 2L;

    /**
     * 支持的最大机器id，结果是1023 (这个移位算法可以很快的计算出几位二进制数所能表示的最大十进制数)
     */
    private final long maxWorkerId = -1L ^ (-1L << workerIdBits);

    /**
     * 支持的最大数据标识id，结果是3
     */
    private final long maxDatacenterId = -1L ^ (-1L << datacenterIdBits);

    /**
     * 序列在id中占的位数
     */
    private final long sequenceBits = 12L;

    /**
     * 机器ID向左移12位
     */
    private final long workerIdShift = sequenceBits;

    /**
     * 数据标识id向左移
     */
    private final long datacenterIdShift = sequenceBits + workerIdBits;

    /**
     * 时间截向左移22位
     */
    private final long timestampLeftShift = sequenceBits + workerIdBits + datacenterIdBits;

    /**
     * 生成序列的掩码，这里为4095 (0b111111111111=0xfff=4095)
     */
    private final long sequenceMask = -1L ^ (-1L << sequenceBits);

    /**
     * 工作机器ID(0~1023)
     */
    private long workerId;

    /**
     * 数据中心ID(0~4)
     */
    private long datacenterId = 0L;

    /**
     * 毫秒内序列(0~4096)
     */
    private long sequence = 0L;

    /**
     * 上次生成ID的时间截
     */
    private long lastTimestamp = -1L;

    @Override
    public synchronized long nextValue() throws SeqException {
        long timestamp = timeGen();
        long timeOffset = lastTimestamp - timestamp;
        //如果当前时间小于上一次ID生成的时间戳，说明系统时钟回退过这个时候应当抛出异常
        if (timeOffset > 0) {
            if (timeOffset > 3000) {
                log.error("SnowflakeSequence严重故障，时钟偏移大于1s，系统无法正常使用.当前时间：{},缓存上次时间：{}", timestamp, lastTimestamp);
                throw new SeqException("SnowflakeSequence严重故障，时钟偏移大于1s，系统无法正常使用");
            } else {
                log.error("[SnowflakeSequence-nextValue error] 当前时间小于上次生成序列号的时间，时间被回退了，请确认服务器时间的设置.时间偏移量：{}", lastTimestamp - timestamp);
                // 如果发生时钟偏移，采用应对策略，采取秒竞争
                timestamp = lastTimestamp;
            }
        }
        //如果是同一时间生成的，则进行毫秒内序列
        if (lastTimestamp == timestamp) {
            sequence = (sequence + 1) & sequenceMask;
            //毫秒内序列溢出
            if (sequence == 0) {
                //阻塞到下一个毫秒,获得新的时间戳
                timestamp = tilNextMillis(lastTimestamp);
            }
        } else {
            //时间戳改变，毫秒内序列重置
            sequence = 0L;
        }

        //上次生成ID的时间截
        lastTimestamp = timestamp;

        //移位并通过或运算拼到一起组成64位的ID
        return ((timestamp - twepoch) << timestampLeftShift)
                | (datacenterId << datacenterIdShift)
                | (workerId << workerIdShift)
                | sequence;
    }

    /**
     * 阻塞到下一个毫秒，直到获得新的时间戳
     *
     * @param lastTimestamp 上次生成ID的时间截
     * @return 当前时间戳
     */
    private long tilNextMillis(long lastTimestamp) {
        long timestamp = timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = timeGen();
        }
        return timestamp;
    }

    /**
     * 返回以毫秒为单位的当前时间
     *
     * @return 当前时间(毫秒)
     */
    private long timeGen() {
        return System.currentTimeMillis();
    }

    public void setWorkerId(long workerId) {
        if (workerId > maxWorkerId) {
            throw new SeqException("[SnowflakeSequence-setWorkerId] workerId 不能大于" + maxWorkerId + ".");
        }

        this.workerId = workerId;
    }

    public void setDatacenterId(long datacenterId) {
        if (datacenterId > maxDatacenterId) {
            throw new SeqException("[SnowflakeSequence-setDatacenterId] datacenterId 不能大于" + maxDatacenterId + ".");
        }

        this.datacenterId = datacenterId;
    }

    /**
     * 下一个生成序号（带格式）
     *
     * @return
     * @throws SeqException
     */
    @Override
    public String nextNo() throws SeqException {
        return String.valueOf(nextValue());
    }
}
