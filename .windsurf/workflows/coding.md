---
description: 代码开发指南
---

已知信息：开发者-gjq-ai

## 1. 设计准则

1. **先设计后编码**：复杂功能必须先编写设计文档，明确接口、数据流和依赖关系。设计文档统一放在 `docs/architecture` 目录下。
2. **问题拆解**：大型功能需拆分为小步骤，逐步实现，每步都有明确目标和验收标准。
3. **接口一致性**：实现同一接口的不同实现类必须保持行为一致，返回值类型和异常处理应当统一。
4. **设计文档维护**：代码变更时必须同步更新设计文档；若设计文档不存在，则应分析代码并创建文档。
5. **建构式开发**： 采用小步快跑的方式进行开发，比如先实现基础功能，并调试通过，然后逐步在骨架里填充内容。
   
## 2. 代码结构与实现

1. **骨架先行**：大型类先完成代码结构和方法签名，再逐个实现方法内容。
2. **分段实现**：复杂方法应分为多个逻辑段落，每段有明确注释说明其功能。
3. **代码长度控制**：单个方法不超过50行，类不超过500行，超过则考虑拆分。
4. **异常处理**：明确异常传递路径，避免吞掉异常，提供有意义的错误信息。
5. **了解代码现状**： 了解代码的现状，知道代码结构，已经实现什么，每个文件什么作用，精确找到修改点。
6. **结束复盘**： 开发完成后，进行开发过程对话内容、错误信息的复盘，找出不足和可以改进的地方，记录经验教训。
   
## 3. 技术调研与文档

1. **先调研后使用**：使用新技术前必须查阅官方文档和最佳实践。
2. **文档总结**：调研后编写简要技术总结，包括关键API、使用方法和注意事项。
3. **示例代码**：为关键功能提供示例代码，便于团队其他成员理解和使用。
4. **版本兼容性**：记录所用库的版本和已知兼容性问题，避免依赖冲突。
5. **DeepWiki**： 调用DeepWiki MCP，可以获取实现。

## 4. 代码质量保障

1. **注释率要求**：代码注释率不低于70%，重点解释"为什么"而非"是什么"。
2. **单元测试**：核心功能必须有单元测试，覆盖正常路径和异常路径。
3. **模拟外部依赖**：测试中使用Mock对象模拟外部依赖，避免环境依赖。
4. **编译验证**：代码提交前必须通过编译和单元测试，确保基本可靠性。

## 5. 常见问题避免

1. **构造vs赋值**：检查目标类是否提供了setter方法或必须使用构造函数。
2. **导入清理**：及时清理未使用的导入，避免代码混乱。
3. **反射使用谨慎**：仅在必要时使用反射，并做好异常处理。
4. **URL处理**：处理URL时注意MalformedURLException异常。
5. **参数验证**：公共方法必须验证参数有效性，提供清晰的错误信息。

## 6. 文档与代码一致性

1. **同步更新**：代码变更时同步更新相关文档，保持一致性。
2. **接口文档**：公共API必须有详细文档，包括参数说明、返回值和异常说明。
3. **配置说明**：所有配置项必须有说明文档，包括用途、默认值和取值范围。
4. **变更记录**：重要变更必须记录在文档中，便于追踪和理解。

## 7. 提交与验证

1. **小步提交**：频繁提交小的变更，而非一次提交大量修改。每次改动解决一个具体问题。
2. **提交信息规范**：提交信息应包含修改内容摘要和详细说明。
3. **代码自测**：提交前必须自测功能，确保基本可用。
4. **代码评审**：重要变更需要团队评审，确保质量和一致性。

## 8. 重构与改进

1. **重构方法论**：采用《重构》一书中的核心方法进行代码改进。
2. **单一职责**：每次代码变更只解决一个具体问题，避免混合多个无关改动。
3. **测试保障**：重构前确保有足够的测试覆盖，重构后验证所有测试通过。
4. **渐进式改进**：大型重构分解为多个小步骤，每步都可独立提交和验证。

## 9. 运行调试

1. **运行**：请在固定端口运行代码，程序需要运行在后台。
2. **测试**：必须执行可验证的单元测试或其他测试方法确保程序可用。


## 10. 开发日志

1. **日志文件**：每个模块维护一个 `CHANGELOG.md` 文件，记录所有代码变更。
2. **记录格式**：每次变更以追加方式记录日期、开发者、变更内容和影响范围。
3. **关联提交**：日志条目应关联对应的Git提交ID，便于追踪详细变更。
4. **版本标记**：重要版本发布时在日志中添加版本标记，便于查找特定版本的变更。

#### 开发日志编写规范

**日志位置**：开发日志采用**追加写**必须放在 `docs/changelog.md` 文件中。

**日志格式**：开发日志采用以下标准格式，按需选择新增功能、修复问题、代码重构、性能优化，：

```markdown

### 新增功能
- [@开发者] 添加了[功能描述]
  - 影响范围: [改动的类/文件]

### 修复问题
- [@开发者] 修复了[问题描述]
  - 影响范围: [改动的类/文件]

### 代码重构
- [@开发者] 重构了[重构内容]
  - 影响范围: [改动的类/文件]

### 性能优化
- [@开发者] 优化了[优化内容]
  - 影响范围: [改动的类/文件]

### 经验
- 记录优化过程中的经验和教训，避免重复踩坑。

```

**日志维护原则**：

1. **变更追加**：每次代码变更必须以追加方式更新日志文件
2. **内容分类**：按新增功能、修复问题、代码重构、性能优化等分类记录
3. **关联提交**：每条记录必须关联对应的Git提交ID
4. **影响范围**：清晰指出受影响的类或文件
5. **相关文档**：引用相关的架构文档或使用指南

## 经验
- **包路径验证规则**: 在创建新类时，必须先搜索现有代码库确认正确的包路径，避免使用不存在的包
- **编译验证**: 每次代码修改后必须执行 `mvn compile` 验证编译通过
- **导入路径一致性**: 同一项目中的注解和枚举类应使用统一的包结构，避免路径混乱
- **错误定位**: 编译错误信息中的"找不到符号"通常指向导入路径问题，应优先检查import语句，编写代码引入外部类时候，必须import类。
- **代码生成工具**: 使用代码生成工具时要特别注意生成的导入路径是否正确
- **泛型类型转换**: 避免强制类型转换泛型类型，应确保搜索时使用正确的类型参数
- **在进行大规模重构时**，应该分阶段进行，每个阶段都要确保编译通过，应严格遵守重构方法论。
- **目录迁移**: 大规模目录重构时，应先中断开发，提醒用户进行迁移，用户可以使用IDE高效完成迁移工作。
**Java经验**
- Java中double到float的转换需要显式类型转换以避免精度丢失警告
- Java中float[]不能直接使用Arrays.stream()，需要通过IntStream转换
- 方法覆盖时必须确保方法签名完全匹配，包括方法名、参数类型和返回值类型
- 调用父类方法时要确认方法的可见性和正确的方法名
- **禁止**在业务中直接抛出IOException，而应该使用BusinessException或者其他自定义异常来中断程序