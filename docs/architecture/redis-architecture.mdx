---
title: Trina Redis Starter 架构文档
description: 详细介绍 Trina Redis Starter 的架构设计、核心实现与分布式锁场景。
---

# 概述
Trina Redis Starter 是一款基于 Spring Boot 的 Redis 集成组件，旨在为企业级应用提供高效、易用、可扩展的 Redis 操作能力，支持常规缓存、分布式锁、数据结构操作等多种场景。

---

## 组件定位
- 统一封装 RedisTemplate，简化常用操作，提升开发效率。
- 集成 Redisson，支持分布式锁、限流等高级特性。
- 提供自动装配与灵活扩展能力，适配多种业务需求。

---

## 主要依赖与技术选型
- Spring Boot 3.x
- spring-boot-starter-data-redis
- Redisson 3.50.x
- Hutool 工具集
- Jackson（序列化）

---

## 关键配置类与自动装配机制
- `RedisTemplateConfiguration`：统一配置 RedisTemplate，指定序列化方式，自动注入 RedisUtils。
- `RedissonAutoConfiguration`：自动装配 RedissonClient，支持多种连接模式（单机、哨兵、集群）。
- `RedissonProperties`：支持通过 yml/json 配置 Redisson。

自动装配机制基于 Spring Boot 条件注解，确保与原生 Redis 配置兼容。

---

## RedisTemplate 与 Redisson 集成方式
- RedisTemplate 负责常规 KV、Hash、List、Set 等数据结构操作。
- Redisson 提供分布式锁、分布式对象、限流等高级能力。
- 通过 RedisUtils 工具类对 RedisTemplate 进行二次封装，提升易用性。

---

## 线程安全与序列化策略
- RedisTemplate 采用 Jackson JSON 序列化，支持 Java8 时间类型。
- RedisUtils 内部持有 RedisTemplate 单例，线程安全。
- RedissonClient 由 Spring 容器托管，线程安全。

---

## 架构图：分布式锁场景

<svg width="680" height="320" viewBox="0 0 680 320" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect x="30" y="40" width="180" height="60" rx="12" fill="#F5F6FA" stroke="#B0B3B8"/>
  <text x="60" y="75" font-size="18" fill="#222">业务服务A</text>
  <rect x="30" y="180" width="180" height="60" rx="12" fill="#F5F6FA" stroke="#B0B3B8"/>
  <text x="60" y="215" font-size="18" fill="#222">业务服务B</text>
  <rect x="260" y="110" width="180" height="60" rx="12" fill="#E3EFFF" stroke="#4A90E2"/>
  <text x="295" y="145" font-size="18" fill="#174EA6">RedissonClient</text>
  <rect x="500" y="110" width="140" height="60" rx="12" fill="#FFF7E3" stroke="#F5A623"/>
  <text x="535" y="145" font-size="18" fill="#B9770E">Redis Server</text>
  <path d="M210 70 Q245 80 260 140" stroke="#4A90E2" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  <path d="M210 210 Q245 200 260 170" stroke="#4A90E2" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  <path d="M440 140 Q480 140 500 140" stroke="#F5A623" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
  <defs>
    <marker id="arrow" markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto" markerUnits="strokeWidth">
      <path d="M0,0 L10,5 L0,10 L2,5 Z" fill="#4A90E2" />
    </marker>
  </defs>
  <text x="270" y="100" font-size="14" fill="#4A90E2">加锁/解锁</text>
  <text x="450" y="100" font-size="14" fill="#F5A623">SET/DEL</text>
</svg>

**说明：**
- 多个业务服务通过 RedissonClient 竞争同一分布式锁，Redisson 负责加锁/解锁逻辑，最终操作 Redis Server。
- 支持高可用、自动续期、锁失效自动释放。

---

## 扩展点与自定义建议
- 支持自定义序列化器，可按需替换为 Kryo、Fastjson 等。
- Redisson 配置支持多环境切换，可通过 yml/json 灵活配置。
- 可扩展 RedisUtils，增加业务常用方法或分布式工具。
- 推荐结合 Spring AOP 实现注解式分布式锁。

---

## 进一步优化建议
- 可引入监控（如 Micrometer）对 Redis 操作进行埋点。
- 支持多 Redis 实例动态切换。
- 提供更多分布式场景（如队列、信号量）示例。 