package com.trinasolar.stream.exception;

import java.io.Serial;

/**
 * 消息消费异常
 *
 * <AUTHOR>
 * @since 4.0.0
 */
public class MessageConsumeException extends StreamException {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    public MessageConsumeException(String message) {
        super("消息消费失败: " + message);
    }
    
    public MessageConsumeException(String message, Throwable cause) {
        super("消息消费失败: " + message, cause);
    }
}
