---
title: My<PERSON>atis Starter 使用指南
description: Trina MyBatis Starter 的快速开始、配置方法与常见问题
---

# 1. 快速开始

## 1.1 引入依赖
在 `pom.xml` 中添加如下依赖：
```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-mybatis-starter</artifactId>
    <version>4.0.0</version>
</dependency>
```

## 1.2 基础配置
在 `application.yml` 或 `mybatis.yaml` 中配置：
```yaml
mybatis-plus:
  mapper-locations: classpath*:/mapper/*Mapper.xml
  global-config:
    banner: false
    db-config:
      id-type: auto
      where-strategy: not_empty
      insert-strategy: not_empty
      update-strategy: not_null
  type-handlers-package: com.trinasolar.data.handler
  configuration:
    jdbc-type-for-null: 'null'
    call-setters-on-nulls: true
    shrink-whitespaces-in-sql: true
mybatis-plus-join:
  banner: false
plus:
  mybatis:
    show-sql: true
    tenant-column: tenant_id
```

# 2. 核心功能
- 通用 CRUD 操作，无需编写 SQL
- 条件构造器与 Lambda 表达式
- 分页查询与插件支持
- 多租户与数据隔离
- 动态表名与 SQL 注入器
- 逻辑删除与自动填充
- 乐观锁与数据一致性

# 3. 高级特性
- 自定义 TypeHandler 类型处理器
- 数据权限拦截器（DataScope）
- 审计字段自动填充（如 createTime, updateBy）
- 支持自定义 BaseEntity、BaseService、BaseController 扩展
- 支持复杂查询条件组装（ConditionGroup）

# 4. 常见问题
- 配置未生效：检查 mapper-locations 路径与包名是否一致
- 分页无效：确认分页插件已注入
- 多租户隔离失败：检查 tenant-column 配置
- 自动填充无效：确认实体字段注解与 MetaObjectHandler 实现
- SQL 报错：检查自定义 SQL 与参数类型

# 5. 最佳实践
- 统一继承 BaseEntity、BaseService、BaseController，减少重复代码
- 推荐使用 Lambda 条件构造器，避免硬编码字段名
- 分页查询建议设置合理的最大单页条数
- 审计字段自动填充，提升数据可追溯性
- 类型处理器与数据权限拦截器按需扩展

# 6. 参考资料
- [MyBatis-Plus 官方文档](https://baomidou.com/)
- [Trina MyBatis 架构文档](../architecture/mybatis-architecture.mdx)