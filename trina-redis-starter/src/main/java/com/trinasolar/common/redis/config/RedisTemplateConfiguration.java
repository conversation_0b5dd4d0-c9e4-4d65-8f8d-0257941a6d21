package com.trinasolar.common.redis.config;


import cn.hutool.core.util.ReflectUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trinasolar.common.core.jackson.CustomJavaTimeModule;
import com.trinasolar.common.redis.util.RedisUtils;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;

/**
 * RedisTemplate 统一序列化配置
 * <p>
 * 开启缓存
 *
 * <AUTHOR>
 * @since 2025-06-13 14:35
 **/
@EnableCaching
@Configuration(proxyBeanMethods = false)
@AutoConfigureBefore(name = {
        "org.redisson.spring.starter.RedissonAutoConfigurationV2",
        "org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration"})
public class RedisTemplateConfiguration {

    @Bean
    @Primary
    @ConditionalOnMissingBean(StringRedisTemplate.class)
    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setKeySerializer(RedisSerializer.string());
        template.setHashKeySerializer(RedisSerializer.string());

        template.setValueSerializer(RedisSerializer.string());
        template.setDefaultSerializer(RedisSerializer.string());

        template.setConnectionFactory(redisConnectionFactory);
        return template;
    }

    @Bean
    @Primary
    @ConditionalOnMissingBean(name = "redisTemplate")
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setKeySerializer(RedisSerializer.string());
        template.setHashKeySerializer(RedisSerializer.string());

        // 设置 redisTemplate 的序列化器为 json
        RedisSerializer<Object> jsonRedisSerializer = RedisSerializer.json();
        ObjectMapper objectMapper = (ObjectMapper) ReflectUtil.getFieldValue(jsonRedisSerializer, "mapper");
        objectMapper.registerModules(new CustomJavaTimeModule());
        template.setValueSerializer(jsonRedisSerializer);
        template.setHashValueSerializer(jsonRedisSerializer);
        template.setDefaultSerializer(jsonRedisSerializer);

        template.setConnectionFactory(redisConnectionFactory);

        // 装配 redisTemplate 类，并将实例注入到 RedisUtil 中
        RedisUtils.setRedisTemplate(template);
        return template;
    }
}
