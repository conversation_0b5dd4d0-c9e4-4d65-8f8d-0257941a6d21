---
title: 文档搜索使用指南
description: 如何使用 Trina Framework 文档网站的搜索功能
---

# 📖 文档搜索使用指南

本指南将帮助您充分利用 Trina Framework 文档网站的搜索功能，快速找到所需的技术信息。

## 🔍 搜索功能概述

我们的文档网站集成了轻量级的本地搜索插件 `@easyops-cn/docusaurus-search-local`，提供以下特性：

- **🚀 快速搜索** - 无需外部服务，本地索引确保快速响应
- **🌐 中英文支持** - 支持中文和英文内容搜索
- **📚 全面覆盖** - 搜索范围包括文档和博客内容
- **🎯 精准匹配** - 高亮显示搜索关键词
- **📱 响应式设计** - 适配桌面和移动设备

## 🎯 如何使用搜索

### 1. 访问搜索框

搜索框位于网站顶部导航栏的右侧，您可以：

- **点击搜索图标** 🔍 打开搜索框
- **使用快捷键** `Ctrl + K` (Windows/Linux) 或 `Cmd + K` (Mac) 快速打开
- **直接点击搜索框** 开始输入搜索内容

### 2. 搜索技巧

#### 基础搜索
```
# 搜索单个关键词
Spring Boot

# 搜索多个关键词
Redis 缓存 配置

# 搜索特定模块
trina-web-starter
```

#### 高级搜索技巧

| 搜索类型 | 示例 | 说明 |
|---------|------|------|
| 精确匹配 | `"Spring Boot 3.4"` | 使用双引号搜索精确短语 |
| 模块搜索 | `trina-redis` | 搜索特定模块相关内容 |
| 功能搜索 | `JWT 认证` | 搜索特定功能实现 |
| 配置搜索 | `application.yml` | 搜索配置相关内容 |
| 错误排查 | `异常处理` | 搜索问题解决方案 |

### 3. 搜索结果解读

搜索结果页面包含以下信息：

- **📄 文档标题** - 包含搜索关键词的文档名称
- **📝 内容摘要** - 包含关键词的文档片段预览
- **🔗 直接链接** - 点击即可跳转到相关文档
- **🎯 关键词高亮** - 搜索关键词在结果中高亮显示

## 📚 搜索范围

### 包含的内容

✅ **文档内容**
- 架构设计文档 (`/docs/architecture/`)
- 使用指南 (`/docs/guides/`)
- 教程文档 (`/docs/tutorials/`)
- 脚手架文档 (`/docs/scaffold/`)

✅ **博客文章**
- 技术分享
- 最佳实践
- 更新日志

### 不包含的内容

❌ **静态页面** - 如首页、关于页面等
❌ **代码注释** - 源代码中的注释内容
❌ **外部链接** - 指向外部网站的内容

## 🎨 搜索界面说明

### 搜索框状态

| 状态 | 描述 | 操作 |
|------|------|------|
| 🔍 默认状态 | 显示搜索图标 | 点击或使用快捷键激活 |
| ✏️ 输入状态 | 光标闪烁，可输入内容 | 输入搜索关键词 |
| 📋 结果状态 | 显示搜索结果列表 | 选择结果或继续搜索 |
| ❌ 无结果 | 显示"没有找到相关结果" | 尝试其他关键词 |

### 搜索结果限制

- **最大显示结果数**: 8 条
- **内容摘要长度**: 50 个字符
- **搜索响应时间**: < 100ms

## 🚀 搜索优化建议

### 提高搜索效果

1. **使用具体关键词**
   ```
   ❌ 不好: "配置"
   ✅ 更好: "Redis 配置"
   ✅ 最好: "Redis 连接池配置"
   ```

2. **组合多个关键词**
   ```
   ❌ 单一: "JWT"
   ✅ 组合: "JWT Token 验证"
   ```

3. **使用模块名称**
   ```
   ✅ 推荐: "trina-security-starter JWT"
   ✅ 推荐: "trina-redis-starter 缓存"
   ```

### 常见搜索场景

#### 🔧 配置相关
```
# 搜索配置示例
"application.yml Redis"
"数据库连接配置"
"JWT 密钥配置"
```

#### 🐛 问题排查
```
# 搜索解决方案
"连接超时 解决"
"启动失败 排查"
"性能优化 建议"
```

#### 📖 学习教程
```
# 搜索教程内容
"15分钟 Spring Boot"
"快速开始 教程"
"最佳实践 指南"
```

## 🔧 技术实现

### 搜索插件配置

我们使用的搜索插件配置如下：

```typescript title="docusaurus.config.ts"
plugins: [
  [
    '@easyops-cn/docusaurus-search-local',
    {
      hashed: true,
      language: ['zh', 'en'],
      highlightSearchTermsOnTargetPage: true,
      explicitSearchResultPath: true,
      indexBlog: true,
      indexDocs: true,
      indexPages: false,
      docsRouteBasePath: '/docs',
      blogRouteBasePath: '/blog',
      searchResultLimits: 8,
      searchResultContextMaxLength: 50
    }
  ]
]
```

### 配置说明

| 配置项 | 值 | 说明 |
|-------|----|----- |
| `hashed` | `true` | 启用哈希文件名，提高缓存效率 |
| `language` | `['zh', 'en']` | 支持中文和英文搜索 |
| `highlightSearchTermsOnTargetPage` | `true` | 在目标页面高亮搜索关键词 |
| `indexBlog` | `true` | 索引博客内容 |
| `indexDocs` | `true` | 索引文档内容 |
| `indexPages` | `false` | 不索引静态页面 |
| `searchResultLimits` | `8` | 最多显示8个搜索结果 |

## 📱 移动端使用

### 移动设备优化

- **触摸友好** - 搜索框和结果针对触摸操作优化
- **响应式布局** - 自适应不同屏幕尺寸
- **快速加载** - 本地索引确保快速响应

### 移动端快捷操作

1. **点击搜索图标** 打开搜索框
2. **输入关键词** 实时显示搜索建议
3. **点击结果** 直接跳转到相关文档
4. **滑动浏览** 查看更多搜索结果

## 🆘 常见问题

### Q: 为什么搜索不到某些内容？

**A:** 可能的原因：
- 内容可能在不被索引的页面中（如静态页面）
- 搜索关键词可能不够精确
- 内容可能是最近添加的，需要重新构建索引

### Q: 如何提高搜索准确性？

**A:** 建议：
- 使用更具体的关键词
- 组合多个相关关键词
- 使用模块名称限定搜索范围
- 尝试不同的关键词组合

### Q: 搜索结果太多怎么办？

**A:** 优化策略：
- 添加更多限定词
- 使用精确匹配（双引号）
- 结合模块名称搜索
- 使用更具体的技术术语

### Q: 移动端搜索体验如何？

**A:** 移动端特性：
- 完全响应式设计
- 触摸优化的界面
- 快速本地搜索
- 与桌面端功能一致

## 🔄 搜索索引更新

### 自动更新

搜索索引会在以下情况自动更新：
- 网站重新构建时
- 文档内容发生变化时
- 新增或删除文档时

### 手动更新

如果需要手动更新搜索索引：

```bash
# 清理缓存
npm run clear

# 重新构建
npm run build

# 或重启开发服务器
npm start
```

## 📈 搜索统计

### 性能指标

- **索引大小**: 约 2-5MB（取决于文档数量）
- **搜索响应时间**: < 100ms
- **支持的文档数量**: 无限制
- **同时搜索用户**: 无限制

### 搜索覆盖范围

- **文档页面**: 100% 覆盖
- **博客文章**: 100% 覆盖
- **代码示例**: 包含在文档中的代码
- **配置示例**: 所有配置文件示例

## 🎯 最佳实践

### 文档编写者

1. **使用清晰的标题** - 便于搜索定位
2. **添加关键词标签** - 提高搜索匹配度
3. **编写描述性内容** - 帮助用户理解搜索结果
4. **使用一致的术语** - 避免同义词混淆

### 文档使用者

1. **从宽泛到具体** - 先用宽泛关键词，再逐步细化
2. **利用模块名称** - 结合具体模块名称搜索
3. **查看相关文档** - 浏览搜索结果中的相关链接
4. **反馈搜索体验** - 帮助我们改进搜索功能

---

## 📞 获取帮助

如果您在使用搜索功能时遇到问题，可以：

1. **查看本指南** - 详细的使用说明和技巧
2. **尝试不同关键词** - 使用同义词或相关术语
3. **浏览文档目录** - 通过导航菜单查找内容
4. **提交反馈** - 通过 GitHub Issues 报告问题

*希望这个搜索功能能帮助您更高效地使用 Trina Framework 文档！* 🚀