---
title: XXL Job 模块使用指南
description: Trina XXL Job 模块的使用方法与最佳实践
---

# XXL Job 模块使用指南

## 1. 简介

Trina XXL Job Starter 基于 XXL-JOB 分布式任务调度框架，提供了轻量级、高性能的分布式任务调度解决方案。XXL-JOB 采用"调度中心"与"执行器"分离的架构设计，支持多种任务执行模式，具备高可用、易扩展、运维简单等特点。

## 2. 功能特性

- **分布式调度**：支持集群部署，任务调度中心支持集群部署，保障调度系统的高可用
- **弹性扩容缩容**：一旦有新执行器机器上线或者下线，下次调度时将会重新分配任务
- **故障转移**：任务路由策略选择"故障转移"情况下，如果执行器集群中某一台机器故障，将会自动切换到一台正常的执行器发送调度请求
- **Rolling实时日志**：支持在线查看调度结果，并且支持以Rolling方式实时查看执行器输出的完整的执行日志
- **GLUE**：提供Web IDE，支持在线开发任务逻辑代码，动态发布，实时编译生效，省略部署上线的过程
- **任务依赖**：支持配置子任务依赖，当父任务执行结束且执行成功后将会主动触发一次子任务的执行
- **一致性**："调度中心"通过DB锁保证集群分布式调度的一致性，一次任务调度只会触发一次执行
- **自定义任务参数**：支持在线配置调度任务入参，即时生效
- **调度线程池**：调度系统多线程触发调度运行，确保调度精确执行，不被堵塞
- **数据加密**：调度中心和执行器之间的通讯进行数据加密，提升调度信息安全性
- **邮件报警**：任务失败时支持邮件报警，支持配置多邮件地址群发报警邮件
- **推送maven中央仓库**：将会把最新稳定版推送到maven中央仓库，方便用户接入和使用
- **运行报表**：支持实时查看运行数据，如任务数量、调度次数、执行器数量等；以及调度报表，如调度日期分布图，调度成功分布图等

## 3. 快速开始

### 3.1 添加依赖

在项目的 `pom.xml` 中添加以下依赖：

```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-xxl-job-starter</artifactId>
    <version>${trina-common.version}</version>
</dependency>
```

### 3.2 启用 XXL Job

在 Spring Boot 主类上添加 `@EnableXxlJob` 注解：

```java
@EnableXxlJob
@SpringBootApplication
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

### 3.3 基础配置

在 `application.yml` 中添加 XXL Job 配置：

```yaml
xxl:
  job:
    admin:
      addresses: http://127.0.0.1:8083/xxl-job-admin
    executor:
      appname: ${spring.application.name}
```

## 4. 任务开发

### 4.1 BEAN模式任务

BEAN模式任务是最常用的任务开发方式，通过 `@XxlJob` 注解标记任务方法：

```java
@Component
public class SampleXxlJob {
    private static Logger logger = LoggerFactory.getLogger(SampleXxlJob.class);

    /**
     * 简单任务示例（Bean模式）
     */
    @XxlJob("demoJobHandler")
    public void demoJobHandler() throws Exception {
        XxlJobHelper.log("XXL-JOB, Hello World.");
        
        for (int i = 0; i < 5; i++) {
            XxlJobHelper.log("beat at:" + i);
            TimeUnit.SECONDS.sleep(2);
        }
        // default success
    }

    /**
     * 带参数的任务示例
     */
    @XxlJob("paramJobHandler")
    public void paramJobHandler() throws Exception {
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("接收到参数：" + param);
        
        // 业务逻辑处理
        // ...
        
        XxlJobHelper.handleSuccess("任务执行成功，参数：" + param);
    }

    /**
     * 分片任务示例
     */
    @XxlJob("shardingJobHandler")
    public void shardingJobHandler() throws Exception {
        // 分片参数
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        
        XxlJobHelper.log("分片参数：当前分片序号 = {}, 总分片数 = {}", shardIndex, shardTotal);
        
        // 根据分片参数处理不同的数据
        // 例如：处理 id % shardTotal == shardIndex 的数据
        
        XxlJobHelper.handleSuccess();
    }

    /**
     * 生命周期任务示例
     */
    @XxlJob("lifecycleJobHandler")
    public void lifecycleJobHandler() throws Exception {
        XxlJobHelper.log("任务开始执行");
        
        try {
            // 业务逻辑
            for (int i = 0; i < 10; i++) {
                if (XxlJobHelper.isStopped()) {
                    XxlJobHelper.log("任务被中止");
                    return;
                }
                
                XxlJobHelper.log("处理第 {} 条数据", i + 1);
                TimeUnit.SECONDS.sleep(1);
            }
            
            XxlJobHelper.handleSuccess("任务执行完成");
        } catch (Exception e) {
            XxlJobHelper.log("任务执行异常：" + e.getMessage());
            XxlJobHelper.handleFail("任务执行失败：" + e.getMessage());
        }
    }
}
```

### 4.2 任务参数处理

```java
@Component
public class ParameterJobHandler {
    
    @XxlJob("jsonParamJobHandler")
    public void jsonParamJobHandler() throws Exception {
        String param = XxlJobHelper.getJobParam();
        
        if (StringUtils.hasText(param)) {
            // 解析JSON参数
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> paramMap = mapper.readValue(param, Map.class);
            
            String type = (String) paramMap.get("type");
            Integer count = (Integer) paramMap.get("count");
            
            XxlJobHelper.log("任务类型：{}, 处理数量：{}", type, count);
            
            // 根据参数执行不同的业务逻辑
            switch (type) {
                case "data_sync":
                    handleDataSync(count);
                    break;
                case "data_clean":
                    handleDataClean(count);
                    break;
                default:
                    XxlJobHelper.handleFail("未知的任务类型：" + type);
                    return;
            }
        }
        
        XxlJobHelper.handleSuccess();
    }
    
    private void handleDataSync(Integer count) {
        // 数据同步逻辑
        XxlJobHelper.log("执行数据同步，数量：{}", count);
    }
    
    private void handleDataClean(Integer count) {
        // 数据清理逻辑
        XxlJobHelper.log("执行数据清理，数量：{}", count);
    }
}
```

## 5. 高级特性

### 5.1 分片广播任务

分片广播任务适用于大数据量处理场景，可以将一个任务分发到多个执行器并行处理：

```java
@Component
public class ShardingJobHandler {
    
    @XxlJob("bigDataProcessJobHandler")
    public void bigDataProcessJobHandler() throws Exception {
        // 获取分片参数
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        
        XxlJobHelper.log("开始处理分片 {}/{}", shardIndex + 1, shardTotal);
        
        // 根据分片参数查询需要处理的数据
        List<DataEntity> dataList = dataService.findBySharding(shardIndex, shardTotal);
        
        int processedCount = 0;
        for (DataEntity data : dataList) {
            try {
                // 处理单条数据
                processData(data);
                processedCount++;
                
                // 定期输出进度
                if (processedCount % 100 == 0) {
                    XxlJobHelper.log("分片 {} 已处理 {} 条数据", shardIndex, processedCount);
                }
                
            } catch (Exception e) {
                XxlJobHelper.log("处理数据失败，ID：{}, 错误：{}", data.getId(), e.getMessage());
            }
        }
        
        XxlJobHelper.log("分片 {} 处理完成，共处理 {} 条数据", shardIndex, processedCount);
        XxlJobHelper.handleSuccess(String.format("分片 %d 处理完成，共处理 %d 条数据", shardIndex, processedCount));
    }
    
    private void processData(DataEntity data) {
        // 具体的数据处理逻辑
    }
}
```

### 5.2 任务依赖（子任务）

```java
@Component
public class DependentJobHandler {
    
    /**
     * 父任务：数据导入
     */
    @XxlJob("dataImportJobHandler")
    public void dataImportJobHandler() throws Exception {
        XxlJobHelper.log("开始数据导入");
        
        try {
            // 执行数据导入逻辑
            importData();
            
            XxlJobHelper.log("数据导入完成");
            XxlJobHelper.handleSuccess("数据导入成功");
            
            // 父任务执行成功后，会自动触发子任务执行
            
        } catch (Exception e) {
            XxlJobHelper.log("数据导入失败：" + e.getMessage());
            XxlJobHelper.handleFail("数据导入失败：" + e.getMessage());
        }
    }
    
    /**
     * 子任务：数据处理
     */
    @XxlJob("dataProcessJobHandler")
    public void dataProcessJobHandler() throws Exception {
        XxlJobHelper.log("开始数据处理");
        
        try {
            // 执行数据处理逻辑
            processData();
            
            XxlJobHelper.log("数据处理完成");
            XxlJobHelper.handleSuccess("数据处理成功");
            
        } catch (Exception e) {
            XxlJobHelper.log("数据处理失败：" + e.getMessage());
            XxlJobHelper.handleFail("数据处理失败：" + e.getMessage());
        }
    }
    
    private void importData() {
        // 数据导入逻辑
    }
    
    private void processData() {
        // 数据处理逻辑
    }
}
```

### 5.3 动态参数和上下文

```java
@Component
public class ContextJobHandler {
    
    @XxlJob("contextJobHandler")
    public void contextJobHandler() throws Exception {
        // 获取任务参数
        String jobParam = XxlJobHelper.getJobParam();
        
        // 获取任务ID
        long jobId = XxlJobHelper.getJobId();
        
        // 获取分片信息
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        
        XxlJobHelper.log("任务上下文信息：");
        XxlJobHelper.log("任务ID：{}", jobId);
        XxlJobHelper.log("任务参数：{}", jobParam);
        XxlJobHelper.log("分片信息：{}/{}", shardIndex, shardTotal);
        
        // 业务逻辑处理
        // ...
        
        XxlJobHelper.handleSuccess();
    }
}
```

## 6. 配置详解

### 6.1 完整配置示例

```yaml
xxl:
  job:
    # 调度中心配置
    admin:
      # 调度中心部署地址，多个地址用逗号分隔
      addresses: http://127.0.0.1:8080/xxl-job-admin,http://127.0.0.1:8081/xxl-job-admin
    
    # 执行器配置
    executor:
      # 执行器AppName，执行器心跳注册分组依据
      appname: my-job-executor
      
      # 执行器注册地址，优先使用该配置作为注册地址
      # 为空时使用内嵌服务 "IP:PORT" 作为注册地址
      address: http://*************:9999
      
      # 执行器IP，默认为空表示自动获取IP
      # 多网卡时可手动设置指定IP
      ip: *************
      
      # 执行器端口号，小于等于0则自动获取
      # 默认端口为9999，单机部署多个执行器时注意配置不同端口
      port: 9999
      
      # 执行器通讯TOKEN，非空时启用
      access-token: default_token
      
      # 执行器运行日志文件存储磁盘路径
      # 需要对该路径拥有读写权限，为空则使用默认路径
      log-path: /data/applogs/xxl-job/jobhandler
      
      # 执行器日志保存天数，值大于3时生效
      # 启用执行器Log文件定期清理功能
      log-retention-days: 7
```

### 6.2 环境配置

#### 开发环境

```yaml
xxl:
  job:
    admin:
      addresses: http://localhost:8080/xxl-job-admin
    executor:
      appname: dev-job-executor
      port: 9999
      log-path: logs/xxl-job
      log-retention-days: 3
```

#### 生产环境

```yaml
xxl:
  job:
    admin:
      addresses: http://xxl-job-admin-1:8080/xxl-job-admin,http://xxl-job-admin-2:8080/xxl-job-admin
    executor:
      appname: prod-job-executor
      access-token: ${XXL_JOB_ACCESS_TOKEN}
      log-path: /data/logs/xxl-job
      log-retention-days: 30
```

## 7. 最佳实践

### 7.1 任务设计原则

1. **幂等性**：确保任务可以重复执行而不产生副作用
2. **异常处理**：妥善处理异常情况，避免任务异常终止
3. **日志记录**：详细记录任务执行过程，便于问题排查
4. **参数验证**：对输入参数进行验证，确保数据安全
5. **资源管理**：合理使用系统资源，避免资源泄漏

### 7.2 性能优化

```java
@Component
public class OptimizedJobHandler {
    
    @Autowired
    private DataService dataService;
    
    @XxlJob("batchProcessJobHandler")
    public void batchProcessJobHandler() throws Exception {
        String param = XxlJobHelper.getJobParam();
        int batchSize = StringUtils.hasText(param) ? Integer.parseInt(param) : 1000;
        
        XxlJobHelper.log("开始批量处理，批次大小：{}", batchSize);
        
        int offset = 0;
        int totalProcessed = 0;
        
        while (true) {
            // 检查任务是否被中止
            if (XxlJobHelper.isStopped()) {
                XxlJobHelper.log("任务被中止，已处理 {} 条数据", totalProcessed);
                return;
            }
            
            // 分批查询数据
            List<DataEntity> dataList = dataService.findByPage(offset, batchSize);
            if (dataList.isEmpty()) {
                break;
            }
            
            // 批量处理
            try {
                dataService.batchProcess(dataList);
                totalProcessed += dataList.size();
                
                XxlJobHelper.log("已处理 {} 条数据", totalProcessed);
                
            } catch (Exception e) {
                XxlJobHelper.log("批量处理失败，偏移量：{}，错误：{}", offset, e.getMessage());
                // 根据业务需求决定是否继续处理
                // throw e; // 抛出异常终止任务
            }
            
            offset += batchSize;
            
            // 适当休眠，避免对数据库造成过大压力
            TimeUnit.MILLISECONDS.sleep(100);
        }
        
        XxlJobHelper.log("批量处理完成，共处理 {} 条数据", totalProcessed);
        XxlJobHelper.handleSuccess(String.format("处理完成，共 %d 条数据", totalProcessed));
    }
}
```

### 7.3 错误处理和重试

```java
@Component
public class RetryJobHandler {
    
    private static final int MAX_RETRY_COUNT = 3;
    
    @XxlJob("retryJobHandler")
    public void retryJobHandler() throws Exception {
        String param = XxlJobHelper.getJobParam();
        
        for (int retry = 0; retry < MAX_RETRY_COUNT; retry++) {
            try {
                XxlJobHelper.log("第 {} 次尝试执行任务", retry + 1);
                
                // 执行业务逻辑
                executeBusinessLogic(param);
                
                XxlJobHelper.log("任务执行成功");
                XxlJobHelper.handleSuccess();
                return;
                
            } catch (Exception e) {
                XxlJobHelper.log("第 {} 次执行失败：{}", retry + 1, e.getMessage());
                
                if (retry == MAX_RETRY_COUNT - 1) {
                    // 最后一次重试失败
                    XxlJobHelper.handleFail(String.format("任务执行失败，已重试 %d 次：%s", MAX_RETRY_COUNT, e.getMessage()));
                    throw e;
                } else {
                    // 等待后重试
                    TimeUnit.SECONDS.sleep(5);
                }
            }
        }
    }
    
    private void executeBusinessLogic(String param) throws Exception {
        // 具体的业务逻辑
        // 可能抛出异常的代码
    }
}
```

### 7.4 监控和告警

```java
@Component
public class MonitorJobHandler {
    
    @Autowired
    private MetricsService metricsService;
    
    @XxlJob("monitorJobHandler")
    public void monitorJobHandler() throws Exception {
        long startTime = System.currentTimeMillis();
        
        try {
            XxlJobHelper.log("开始执行监控任务");
            
            // 执行业务逻辑
            executeMonitorLogic();
            
            long duration = System.currentTimeMillis() - startTime;
            
            // 记录执行时间
            metricsService.recordExecutionTime("monitor_job", duration);
            
            XxlJobHelper.log("监控任务执行完成，耗时：{} ms", duration);
            XxlJobHelper.handleSuccess(String.format("执行成功，耗时 %d ms", duration));
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            
            // 记录失败指标
            metricsService.recordFailure("monitor_job", e.getMessage());
            
            XxlJobHelper.log("监控任务执行失败，耗时：{} ms，错误：{}", duration, e.getMessage());
            XxlJobHelper.handleFail(String.format("执行失败，耗时 %d ms：%s", duration, e.getMessage()));
            
            throw e;
        }
    }
    
    private void executeMonitorLogic() throws Exception {
        // 监控逻辑实现
    }
}
```

## 8. 故障排查

### 8.1 常见问题

#### 执行器注册失败

**问题现象**：执行器无法注册到调度中心

**排查步骤**：
1. 检查调度中心地址配置是否正确
2. 检查网络连通性
3. 检查执行器端口是否被占用
4. 检查访问令牌配置是否一致

**解决方案**：
```yaml
xxl:
  job:
    admin:
      addresses: http://correct-admin-host:8080/xxl-job-admin
    executor:
      appname: unique-executor-name
      port: 9999  # 确保端口未被占用
      access-token: correct-token
```

#### 任务执行超时

**问题现象**：任务执行时间过长，被调度中心标记为超时

**排查步骤**：
1. 检查任务逻辑是否存在死循环或长时间阻塞
2. 检查数据库连接是否正常
3. 检查外部服务调用是否响应缓慢

**解决方案**：
```java
@XxlJob("timeoutJobHandler")
public void timeoutJobHandler() throws Exception {
    // 设置合理的超时时间
    long timeout = 30 * 60 * 1000; // 30分钟
    long startTime = System.currentTimeMillis();
    
    while (hasMoreWork()) {
        // 检查是否超时
        if (System.currentTimeMillis() - startTime > timeout) {
            XxlJobHelper.log("任务执行超时，主动退出");
            XxlJobHelper.handleFail("任务执行超时");
            return;
        }
        
        // 检查是否被中止
        if (XxlJobHelper.isStopped()) {
            XxlJobHelper.log("任务被中止");
            return;
        }
        
        // 执行具体工作
        doWork();
    }
    
    XxlJobHelper.handleSuccess();
}
```

### 8.2 日志分析

```java
@Component
public class LoggingJobHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(LoggingJobHandler.class);
    
    @XxlJob("loggingJobHandler")
    public void loggingJobHandler() throws Exception {
        // 记录任务开始
        XxlJobHelper.log("=== 任务开始执行 ===");
        logger.info("XXL Job 任务开始执行，任务ID：{}", XxlJobHelper.getJobId());
        
        try {
            // 记录关键步骤
            XxlJobHelper.log("步骤1：初始化数据");
            initializeData();
            
            XxlJobHelper.log("步骤2：处理业务逻辑");
            processBusinessLogic();
            
            XxlJobHelper.log("步骤3：清理资源");
            cleanup();
            
            XxlJobHelper.log("=== 任务执行完成 ===");
            XxlJobHelper.handleSuccess("任务执行成功");
            
        } catch (Exception e) {
            XxlJobHelper.log("=== 任务执行失败 ===");
            XxlJobHelper.log("错误信息：{}", e.getMessage());
            XxlJobHelper.log("错误堆栈：", e);
            
            logger.error("XXL Job 任务执行失败，任务ID：{}", XxlJobHelper.getJobId(), e);
            
            XxlJobHelper.handleFail("任务执行失败：" + e.getMessage());
            throw e;
        }
    }
    
    private void initializeData() {
        // 初始化逻辑
    }
    
    private void processBusinessLogic() {
        // 业务逻辑
    }
    
    private void cleanup() {
        // 清理逻辑
    }
}
```

## 9. 安全配置

### 9.1 访问令牌配置

```yaml
xxl:
  job:
    admin:
      addresses: http://admin-host:8080/xxl-job-admin
    executor:
      access-token: ${XXL_JOB_ACCESS_TOKEN:your-secret-token}
```

### 9.2 网络安全

```yaml
# 生产环境建议配置
xxl:
  job:
    admin:
      addresses: https://secure-admin-host:8443/xxl-job-admin
    executor:
      # 指定内网IP，避免暴露公网
      ip: **********
      port: 9999
      access-token: ${XXL_JOB_ACCESS_TOKEN}
```

## 10. 总结

Trina XXL Job Starter 提供了完整的分布式任务调度解决方案，具有以下优势：

- **简单易用**：通过注解即可快速开发任务
- **功能丰富**：支持多种任务类型和执行策略
- **高可用性**：支持集群部署和故障转移
- **运维友好**：提供完善的监控和日志功能
- **扩展性强**：支持动态扩容和分片执行

在使用过程中，建议遵循最佳实践，合理设计任务逻辑，做好异常处理和监控告警，确保任务调度系统的稳定运行。