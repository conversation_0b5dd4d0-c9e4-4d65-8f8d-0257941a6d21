# 网关统一异常处理说明

## 概述

trina-gateway-starter 现已实现统一的异常处理机制，确保所有错误响应都返回标准的 R 结构（code、msg、data），替代原有的不一致错误格式。

## 统一响应格式

所有错误响应都遵循以下 R 结构：

```json
{
  "code": 1,           // 错误码：0=成功，1=失败
  "msg": "错误描述",    // 错误消息
  "data": {            // 错误详细信息（可选）
    "timestamp": "2025-07-30T10:30:00",
    "path": "/api/demo/users/page",
    "requestId": "ce7088d6-7"
  }
}
```

## 支持的异常类型

### 1. 认证相关异常 (401)

**AuthenticationException**
```json
{
  "code": 1,
  "msg": "用户未登录或登录已过期",
  "data": {
    "authorizeUrl": "https://iam.example.com/oauth/authorize?client_id=xxx&redirect_uri=xxx",
    "timestamp": "2025-07-30T10:30:00"
  }
}
```

### 2. 授权相关异常 (403)

**AuthorizationException, SecurityException**
```json
{
  "code": 1,
  "msg": "权限不足，无法访问该资源",
  "data": {
    "timestamp": "2025-07-30T10:30:00"
  }
}
```

### 3. 请求参数异常 (400)

**BusinessException, IllegalArgumentException**
```json
{
  "code": 1,
  "msg": "请求参数错误: 参数值不合法",
  "data": {
    "timestamp": "2025-07-30T10:30:00"
  }
}
```

### 4. 资源未找到异常 (404)

**ResponseStatusException(NOT_FOUND)**
```json
{
  "code": 1,
  "msg": "Not Found",
  "data": {
    "path": "/api/demo/users/page",
    "timestamp": "2025-07-30T10:30:00"
  }
}
```

### 5. 服务不可用异常 (503)

**WebClientRequestException, PrematureCloseException**
```json
{
  "code": 1,
  "msg": "上游服务不可用，请稍后重试",
  "data": {
    "timestamp": "2025-07-30T10:30:00"
  }
}
```

### 6. 网关超时异常 (504)

**TimeoutException**
```json
{
  "code": 1,
  "msg": "请求超时，请稍后重试",
  "data": {
    "timestamp": "2025-07-30T10:30:00"
  }
}
```

### 7. 系统内部错误 (500)

**Exception, RuntimeException, SurenessNoInitException, SurenessLoadDataException**
```json
{
  "code": 1,
  "msg": "系统内部错误，请联系管理员",
  "data": {
    "requestId": "ce7088d6-7",
    "timestamp": "2025-07-30T10:30:00"
  }
}
```

## 核心组件

### 1. ErrorResponseUtils

统一的错误响应工具类，提供各种错误响应的创建方法：

```java
// 写入认证失败响应
ErrorResponseUtils.writeUnauthorizedResponse(response, objectMapper, message, authorizeUrl);

// 写入权限不足响应
ErrorResponseUtils.writeForbiddenResponse(response, objectMapper, message);

// 写入资源未找到响应
ErrorResponseUtils.writeNotFoundResponse(response, objectMapper, path);

// 写入服务器内部错误响应
ErrorResponseUtils.writeInternalServerErrorResponse(response, objectMapper, message, requestId);
```

### 2. GatewayGlobalExceptionHandler

全局异常处理器，捕获并处理所有未被过滤器处理的异常：

```java
@RestControllerAdvice
@Order(-1)
public class GatewayGlobalExceptionHandler {
    
    @ExceptionHandler(AuthenticationException.class)
    public Mono<Void> handleAuthenticationException(AuthenticationException ex, ServerWebExchange exchange);
    
    @ExceptionHandler(Exception.class)
    public Mono<Void> handleGenericException(Exception ex, ServerWebExchange exchange);
}
```

### 3. GatewayErrorHandler

Spring Cloud Gateway 错误处理器，处理网关级别的错误（如路由不匹配、服务不可用等）：

```java
@Component
@Order(-2)
public class GatewayErrorHandler implements ErrorWebExceptionHandler {
    // 处理 404、500、503 等网关错误
}
```

## 配置说明

### 基础配置

无需额外配置，异常处理器会自动注册。

### 开发环境测试

在开发环境可以启用错误测试端点：

```yaml
trina:
  gateway:
    error-test:
      enabled: true  # 仅开发环境启用
```

启用后可以访问以下测试端点：

- `GET /api/scf/error-test/help` - 获取所有测试端点
- `GET /api/scf/error-test/auth` - 测试认证异常 (401)
- `GET /api/scf/error-test/authz` - 测试授权异常 (403)
- `GET /api/scf/error-test/business` - 测试业务异常 (400)
- `GET /api/scf/error-test/runtime` - 测试运行时异常 (500)

## 与原有格式对比

### 原有格式（不统一）
```json
{
  "timestamp": 1753850795952,
  "path": "/api/api/demo/users/page",
  "status": 404,
  "error": "Not Found",
  "requestId": "ce7088d6-7"
}
```

### 新格式（统一 R 结构）
```json
{
  "code": 1,
  "msg": "Not Found",
  "data": {
    "path": "/api/api/demo/users/page",
    "timestamp": "2025-07-30T10:30:00",
    "requestId": "ce7088d6-7"
  }
}
```

## 最佳实践

### 1. 自定义异常处理

如需添加自定义异常处理，可以扩展 `GatewayGlobalExceptionHandler`：

```java
@ExceptionHandler(CustomException.class)
public Mono<Void> handleCustomException(CustomException ex, ServerWebExchange exchange) {
    ServerHttpResponse response = exchange.getResponse();
    return ErrorResponseUtils.writeBadRequestResponse(response, objectMapper, ex.getMessage());
}
```

### 2. 错误日志记录

异常处理器会自动记录适当级别的日志：
- 5xx 错误：ERROR 级别
- 4xx 错误：WARN 级别  
- 404 错误：DEBUG 级别

### 3. 请求追踪

支持通过 `X-Request-ID` 请求头进行请求追踪，错误响应中会包含该信息。

### 4. 生产环境注意事项

- 确保 `trina.gateway.error-test.enabled=false`
- 敏感信息不会暴露在错误响应中
- 系统内部错误统一返回通用错误消息

## 兼容性说明

- **向后兼容**：现有的过滤器错误处理逻辑已更新使用新的工具类
- **API 兼容**：SecurityUtils 中的错误处理方法保持不变，内部实现已优化
- **配置兼容**：无需修改现有配置，新的异常处理机制自动生效

## 监控和调试

### 错误统计

可以通过日志监控各类错误的发生频率：

```bash
# 统计各类错误
grep "Authentication failed" gateway.log | wc -l
grep "Authorization failed" gateway.log | wc -l
grep "Business exception" gateway.log | wc -l
```

### 调试模式

启用 DEBUG 日志级别可以查看详细的异常处理过程：

```yaml
logging:
  level:
    com.trinasolar.gateway.security.exception: DEBUG
```

## 故障排查

### 常见问题

1. **异常处理器不生效**
   - 检查是否正确注册了 `GatewayGlobalExceptionHandler`
   - 确认异常处理器的 `@Order` 优先级设置

2. **响应格式不统一**
   - 检查是否有其他异常处理器覆盖了默认行为
   - 确认 `ObjectMapper` 配置正确

3. **错误信息丢失**
   - 检查日志级别配置
   - 确认异常链没有被截断

### 诊断工具

```bash
# 测试各种错误场景
curl -X GET "http://localhost:8080/api/scf/error-test/help"

# 查看异常处理器注册情况
curl -X GET "http://localhost:8080/actuator/beans" | grep Exception
```

通过以上统一异常处理机制，网关现在能够提供一致的错误响应格式，提升了 API 的可用性和开发体验。
