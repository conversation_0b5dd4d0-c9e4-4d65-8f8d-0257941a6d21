package com.trinasolar.elasticsearch.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Elasticsearch配置属性类
 * 用于从配置文件中读取Elasticsearch相关配置
 */
@Component
@ConfigurationProperties(prefix = "elasticsearch")
public class ElasticsearchProperties {

    /**
     * Elasticsearch服务器地址列表，多个地址用逗号分隔
     */
    private String hosts;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 5000;

    /**
     * 套接字超时时间（毫秒）
     */
    private int socketTimeout = 60000;

    /**
     * 连接请求超时时间（毫秒）
     */
    private int connectionRequestTimeout = 1000;

    /**
     * 最大连接数
     */
    private int maxConnTotal = 100;

    /**
     * 每个路由的最大连接数
     */
    private int maxConnPerRoute = 20;

    /**
     * 是否启用SSL
     */
    private boolean sslEnabled = false;

    /**
     * 证书路径
     */
    private String certificatePath;

    /**
     * 获取Elasticsearch服务器地址列表
     *
     * @return Elasticsearch服务器地址列表
     */
    public String getHosts() {
        return hosts;
    }

    /**
     * 设置Elasticsearch服务器地址列表
     *
     * @param hosts Elasticsearch服务器地址列表
     */
    public void setHosts(String hosts) {
        this.hosts = hosts;
    }

    /**
     * 获取用户名
     *
     * @return 用户名
     */
    public String getUsername() {
        return username;
    }

    /**
     * 设置用户名
     *
     * @param username 用户名
     */
    public void setUsername(String username) {
        this.username = username;
    }

    /**
     * 获取密码
     *
     * @return 密码
     */
    public String getPassword() {
        return password;
    }

    /**
     * 设置密码
     *
     * @param password 密码
     */
    public void setPassword(String password) {
        this.password = password;
    }

    /**
     * 获取连接超时时间
     *
     * @return 连接超时时间
     */
    public int getConnectTimeout() {
        return connectTimeout;
    }

    /**
     * 设置连接超时时间
     *
     * @param connectTimeout 连接超时时间
     */
    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    /**
     * 获取套接字超时时间
     *
     * @return 套接字超时时间
     */
    public int getSocketTimeout() {
        return socketTimeout;
    }

    /**
     * 设置套接字超时时间
     *
     * @param socketTimeout 套接字超时时间
     */
    public void setSocketTimeout(int socketTimeout) {
        this.socketTimeout = socketTimeout;
    }

    /**
     * 获取连接请求超时时间
     *
     * @return 连接请求超时时间
     */
    public int getConnectionRequestTimeout() {
        return connectionRequestTimeout;
    }

    /**
     * 设置连接请求超时时间
     *
     * @param connectionRequestTimeout 连接请求超时时间
     */
    public void setConnectionRequestTimeout(int connectionRequestTimeout) {
        this.connectionRequestTimeout = connectionRequestTimeout;
    }

    /**
     * 获取最大连接数
     *
     * @return 最大连接数
     */
    public int getMaxConnTotal() {
        return maxConnTotal;
    }

    /**
     * 设置最大连接数
     *
     * @param maxConnTotal 最大连接数
     */
    public void setMaxConnTotal(int maxConnTotal) {
        this.maxConnTotal = maxConnTotal;
    }

    /**
     * 获取每个路由的最大连接数
     *
     * @return 每个路由的最大连接数
     */
    public int getMaxConnPerRoute() {
        return maxConnPerRoute;
    }

    /**
     * 设置每个路由的最大连接数
     *
     * @param maxConnPerRoute 每个路由的最大连接数
     */
    public void setMaxConnPerRoute(int maxConnPerRoute) {
        this.maxConnPerRoute = maxConnPerRoute;
    }

    /**
     * 是否启用SSL
     *
     * @return 是否启用SSL
     */
    public boolean isSslEnabled() {
        return sslEnabled;
    }

    /**
     * 设置是否启用SSL
     *
     * @param sslEnabled 是否启用SSL
     */
    public void setSslEnabled(boolean sslEnabled) {
        this.sslEnabled = sslEnabled;
    }

    /**
     * 获取证书路径
     *
     * @return 证书路径
     */
    public String getCertificatePath() {
        return certificatePath;
    }

    /**
     * 设置证书路径
     *
     * @param certificatePath 证书路径
     */
    public void setCertificatePath(String certificatePath) {
        this.certificatePath = certificatePath;
    }
}
