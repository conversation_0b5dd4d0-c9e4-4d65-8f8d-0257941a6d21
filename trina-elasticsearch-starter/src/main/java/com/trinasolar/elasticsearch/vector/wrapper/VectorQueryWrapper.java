package com.trinasolar.elasticsearch.vector.wrapper;

import com.trinasolar.elasticsearch.base.entity.BaseEsEntity;
import com.trinasolar.elasticsearch.base.wrapper.EsQueryWrapper;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 向量查询包装器
 * 扩展EsQueryWrapper，添加向量搜索功能
 * 
 * @param <T> 实体类型
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
public class VectorQueryWrapper<T extends BaseEsEntity> extends EsQueryWrapper<T> {
    
    /**
     * kNN查询配置列表
     */
    private final List<KnnQuery> knnQueries = new ArrayList<>();
    
    /**
     * 混合搜索权重配置
     */
    private HybridSearchConfig hybridConfig;
    
    /**
     * 添加向量相似性搜索
     * 
     * @param fieldName 向量字段名
     * @param queryVector 查询向量
     * @param k 返回的相似文档数量
     * @return 当前查询包装器
     */
    public VectorQueryWrapper<T> vectorSearch(String fieldName, float[] queryVector, int k) {
        return vectorSearch(fieldName, queryVector, k, null, null);
    }
    
    /**
     * 添加向量相似性搜索
     * 
     * @param fieldName 向量字段名
     * @param queryVector 查询向量
     * @param k 返回的相似文档数量
     * @param numCandidates 候选数量
     * @return 当前查询包装器
     */
    public VectorQueryWrapper<T> vectorSearch(String fieldName, float[] queryVector, int k, Integer numCandidates) {
        return vectorSearch(fieldName, queryVector, k, numCandidates, null);
    }
    
    /**
     * 添加向量相似性搜索
     * 
     * @param fieldName 向量字段名
     * @param queryVector 查询向量
     * @param k 返回的相似文档数量
     * @param numCandidates 候选数量
     * @param boost 权重提升
     * @return 当前查询包装器
     */
    public VectorQueryWrapper<T> vectorSearch(String fieldName, float[] queryVector, int k, Integer numCandidates, Float boost) {
        KnnQuery knnQuery = KnnQuery.builder()
                .field(fieldName)
                .queryVector(queryVector)
                .k(k)
                .numCandidates(numCandidates != null ? numCandidates : Math.max(k * 10, 100))
                .boost(boost != null ? boost : 1.0f)
                .build();
        
        knnQueries.add(knnQuery);
        return this;
    }
    
    /**
     * 添加带过滤条件的向量搜索
     * 
     * @param fieldName 向量字段名
     * @param queryVector 查询向量
     * @param k 返回的相似文档数量
     * @param filterWrapper 过滤条件
     * @return 当前查询包装器
     */
    public VectorQueryWrapper<T> vectorSearchWithFilter(String fieldName, float[] queryVector, int k, 
                                                        EsQueryWrapper<T> filterWrapper) {
        KnnQuery knnQuery = KnnQuery.builder()
                .field(fieldName)
                .queryVector(queryVector)
                .k(k)
                .numCandidates(Math.max(k * 10, 100))
                .boost(1.0f)
                .filter(filterWrapper)
                .build();
        
        knnQueries.add(knnQuery);
        return this;
    }
    
    /**
     * 配置混合搜索权重
     * 
     * @param textWeight 文本搜索权重
     * @param vectorWeight 向量搜索权重
     * @return 当前查询包装器
     */
    public VectorQueryWrapper<T> hybridSearch(float textWeight, float vectorWeight) {
        this.hybridConfig = HybridSearchConfig.builder()
                .textWeight(textWeight)
                .vectorWeight(vectorWeight)
                .build();
        return this;
    }
    
    /**
     * 批量向量搜索
     * 
     * @param fieldName 向量字段名
     * @param queryVectors 查询向量列表
     * @param k 每个向量返回的相似文档数量
     * @return 当前查询包装器
     */
    public VectorQueryWrapper<T> batchVectorSearch(String fieldName, List<float[]> queryVectors, int k) {
        for (float[] queryVector : queryVectors) {
            vectorSearch(fieldName, queryVector, k);
        }
        return this;
    }
    
    /**
     * 获取kNN查询配置列表
     * 
     * @return kNN查询配置列表
     */
    public List<KnnQuery> getKnnQueries() {
        return new ArrayList<>(knnQueries);
    }
    
    /**
     * 获取混合搜索配置
     * 
     * @return 混合搜索配置
     */
    public HybridSearchConfig getHybridConfig() {
        return hybridConfig;
    }
    
    /**
     * 是否包含向量搜索
     * 
     * @return 是否包含向量搜索
     */
    public boolean hasVectorSearch() {
        return !knnQueries.isEmpty();
    }
    
    /**
     * 清除所有向量搜索条件
     * 
     * @return 当前查询包装器
     */
    public VectorQueryWrapper<T> clearVectorSearch() {
        knnQueries.clear();
        hybridConfig = null;
        return this;
    }
    
    /**
     * kNN查询配置
     */
    public static class KnnQuery {
        private String field;
        private float[] queryVector;
        private int k;
        private int numCandidates;
        private float boost;
        private EsQueryWrapper<?> filter;
        
        public static KnnQueryBuilder builder() {
            return new KnnQueryBuilder();
        }
        
        // Getters
        public String getField() { return field; }
        public float[] getQueryVector() { return queryVector; }
        public int getK() { return k; }
        public int getNumCandidates() { return numCandidates; }
        public float getBoost() { return boost; }
        public EsQueryWrapper<?> getFilter() { return filter; }
        
        public static class KnnQueryBuilder {
            private String field;
            private float[] queryVector;
            private int k;
            private int numCandidates;
            private float boost = 1.0f;
            private EsQueryWrapper<?> filter;
            
            public KnnQueryBuilder field(String field) {
                this.field = field;
                return this;
            }
            
            public KnnQueryBuilder queryVector(float[] queryVector) {
                this.queryVector = queryVector;
                return this;
            }
            
            public KnnQueryBuilder k(int k) {
                this.k = k;
                return this;
            }
            
            public KnnQueryBuilder numCandidates(int numCandidates) {
                this.numCandidates = numCandidates;
                return this;
            }
            
            public KnnQueryBuilder boost(float boost) {
                this.boost = boost;
                return this;
            }
            
            public KnnQueryBuilder filter(EsQueryWrapper<?> filter) {
                this.filter = filter;
                return this;
            }
            
            public KnnQuery build() {
                KnnQuery query = new KnnQuery();
                query.field = this.field;
                query.queryVector = this.queryVector;
                query.k = this.k;
                query.numCandidates = this.numCandidates;
                query.boost = this.boost;
                query.filter = this.filter;
                return query;
            }
        }
    }
    
    /**
     * 混合搜索配置
     */
    public static class HybridSearchConfig {
        private float textWeight;
        private float vectorWeight;
        
        public static HybridSearchConfigBuilder builder() {
            return new HybridSearchConfigBuilder();
        }
        
        // Getters
        public float getTextWeight() { return textWeight; }
        public float getVectorWeight() { return vectorWeight; }
        
        public static class HybridSearchConfigBuilder {
            private float textWeight = 0.5f;
            private float vectorWeight = 0.5f;
            
            public HybridSearchConfigBuilder textWeight(float textWeight) {
                this.textWeight = textWeight;
                return this;
            }
            
            public HybridSearchConfigBuilder vectorWeight(float vectorWeight) {
                this.vectorWeight = vectorWeight;
                return this;
            }
            
            public HybridSearchConfig build() {
                HybridSearchConfig config = new HybridSearchConfig();
                config.textWeight = this.textWeight;
                config.vectorWeight = this.vectorWeight;
                return config;
            }
        }
    }
}