package com.trinasolar.i18n.api;

import java.util.Map;
import java.util.Optional;

/**
 * 翻译API客户端接口
 *
 * <AUTHOR>
 */
public interface TranslationApiClient {

    /**
     * 翻译文本
     *
     * @param text 源文本
     * @param sourceLanguage 源语言
     * @param targetLanguage 目标语言
     * @return 翻译结果，失败则返回空
     */
    Optional<String> translate(String text, String sourceLanguage, String targetLanguage);

    /**
     * 批量翻译文本
     *
     * @param texts 源文本列表
     * @param sourceLanguage 源语言
     * @param targetLanguage 目标语言
     * @return 翻译结果映射，失败则返回空映射
     */
    Map<String, String> translateBatch(Map<String, String> texts, String sourceLanguage, String targetLanguage);

    /**
     * 检查API是否可用
     *
     * @return 是否可用
     */
    boolean isAvailable();
} 