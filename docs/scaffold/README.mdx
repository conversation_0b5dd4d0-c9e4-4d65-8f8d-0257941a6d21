---
title: 项目脚手架
description: Trina Common 框架项目脚手架和示例代码
---

# 🏗️ 项目脚手架

本目录提供基于 Trina Common 框架的项目脚手架、示例代码和最佳实践模板，帮助您快速启动新项目。

## 🚀 快速开始

### 选择合适的脚手架

根据您的项目需求选择对应的脚手架模板：

1. **单体应用** - 适合中小型项目
2. **微服务应用** - 适合大型分布式系统
3. **API服务** - 适合纯后端API项目
4. **全栈应用** - 包含前后端的完整应用

## 📦 脚手架模板

### 🏢 单体应用脚手架

**适用场景：**
- 中小型企业应用
- 快速原型开发
- 学习和演示项目

**包含功能：**
- 用户认证与授权
- CRUD操作示例
- 文件上传下载
- 缓存使用示例
- 定时任务配置

**技术栈：**
```
├── Spring Boot 3.x
├── MyBatis-Plus
├── Redis
├── MySQL
├── Spring Security
└── Swagger/OpenAPI
```

### 🌐 微服务脚手架

**适用场景：**
- 大型分布式系统
- 高并发应用
- 云原生应用

**包含服务：**
- 用户服务 (User Service)
- 订单服务 (Order Service)
- 支付服务 (Payment Service)
- 网关服务 (Gateway Service)
- 配置中心 (Config Center)

**技术栈：**
```
├── Spring Cloud 2023.x
├── Nacos (服务发现/配置中心)
├── Sentinel (流量控制)
├── Seata (分布式事务)
└── Docker/Kubernetes
```

### 🔌 API服务脚手架

**适用场景：**
- RESTful API服务
- 第三方集成服务
- 数据处理服务

**核心特性：**
- 标准化API响应格式
- 完整的异常处理
- API文档自动生成
- 参数校验和转换
- 接口限流和熔断

### 🖥️ 全栈应用脚手架

**适用场景：**
- 管理后台系统
- 企业内部系统
- 原型验证项目

**前端技术：**
- React/Vue.js
- TypeScript
- Ant Design/Element UI

**后端技术：**
- Trina Common 全套组件
- 完整的权限管理
- 多租户支持

## 🛠️ 使用指南

### 1. 环境准备

**基础环境：**
```bash
# Java 17+
java -version

# Maven 3.8+
mvn -version

# Docker (可选)
docker --version
```

**数据库准备：**
```bash
# MySQL 8.0+
# Redis 6.0+
# 其他依赖服务...
```

### 2. 项目初始化

**克隆脚手架：**
```bash
# 选择合适的脚手架模板
git clone [脚手架仓库地址]
cd [项目目录]
```

**配置修改：**
```yaml
# application.yml
spring:
  datasource:
    url: ***********************************
    username: your_username
    password: your_password
  
  redis:
    host: localhost
    port: 6379
```

**依赖安装：**
```bash
mvn clean install
```

### 3. 项目启动

**单体应用：**
```bash
mvn spring-boot:run
```

**微服务应用：**
```bash
# 启动基础服务
docker-compose up -d nacos redis mysql

# 启动各个微服务
./start-services.sh
```

## 📁 目录结构

### 标准项目结构
```
project-name/
├── src/main/java/
│   ├── controller/     # 控制器层
│   ├── service/        # 业务逻辑层
│   ├── mapper/         # 数据访问层
│   ├── entity/         # 实体类
│   ├── dto/           # 数据传输对象
│   ├── config/        # 配置类
│   └── Application.java
├── src/main/resources/
│   ├── mapper/        # MyBatis映射文件
│   ├── application.yml
│   └── logback-spring.xml
├── src/test/
├── docs/              # 项目文档
├── scripts/           # 部署脚本
├── docker/            # Docker配置
├── pom.xml
└── README.md
```

### 微服务项目结构
```
microservice-project/
├── gateway-service/   # 网关服务
├── user-service/      # 用户服务
├── order-service/     # 订单服务
├── common/           # 公共模块
├── docker-compose.yml
├── kubernetes/       # K8s部署文件
└── README.md
```

## 🎯 最佳实践

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用统一的代码格式化配置
- 编写完整的单元测试
- 添加详细的API文档

### 架构设计
- 分层架构清晰
- 依赖注入合理使用
- 异常处理统一管理
- 配置外部化

### 性能优化
- 合理使用缓存
- 数据库连接池配置
- JVM参数调优
- 监控指标完善

### 安全考虑
- 输入参数校验
- SQL注入防护
- XSS攻击防护
- 敏感信息加密

## 📚 学习资源

### 相关文档
- [使用指南](../guides/) - 详细的组件使用说明
- [架构设计](../architecture/) - 架构设计原理
- [15分钟教程](../tutorials/) - 快速学习教程

### 示例代码
- GitHub仓库中的完整示例
- 单元测试用例
- 集成测试示例

## 🔄 持续集成

### CI/CD配置
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up JDK 17
        uses: actions/setup-java@v2
        with:
          java-version: '17'
      - name: Run tests
        run: mvn test
```

### 部署脚本
```bash
#!/bin/bash
# deploy.sh
echo "开始部署..."
mvn clean package -DskipTests
docker build -t app:latest .
docker-compose up -d
echo "部署完成！"
```

## 📞 技术支持

**获取帮助：**
- 查看项目README文档
- 参考在线文档
- 提交GitHub Issue
- 加入技术交流群

**贡献代码：**
- Fork项目仓库
- 创建功能分支
- 提交Pull Request
- 参与代码审查

---

选择合适的脚手架，开始您的开发之旅！🚀
