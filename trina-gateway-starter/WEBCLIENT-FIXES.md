# WebClient PrematureCloseException 修复说明

## 问题描述

在使用 trina-gateway-starter 时，可能会遇到以下错误：
```
reactor.netty.http.client.PrematureCloseException: Connection prematurely closed BEFORE response
```

这个错误通常由以下原因引起：
1. 连接池配置不当，连接被过早关闭
2. 缺乏适当的重试机制
3. 超时配置不合理
4. 网络不稳定导致的连接中断

## 修复方案

### 1. 增强的 WebClient 配置

#### 连接池优化
- **连接池管理**：使用 `ConnectionProvider` 管理连接池
- **连接复用**：配置合理的连接空闲时间和生存时间
- **Keep-Alive**：启用 TCP 和 HTTP Keep-Alive
- **连接参数**：优化 TCP_NODELAY 和 SO_KEEPALIVE

#### 超时配置
- **连接超时**：10秒连接超时
- **响应超时**：30秒响应超时
- **分场景超时**：不同操作使用不同的超时时间

#### 重试机制
- **智能重试**：只对连接相关异常进行重试
- **退避策略**：使用指数退避算法
- **重试限制**：不同操作配置不同的重试次数

### 2. 配置属性

在 `application.yml` 中添加以下配置：

```yaml
trina:
  gateway:
    webclient:
      # 是否启用调试模式（网络日志）
      debug-enabled: false
      # 连接池配置
      connection-pool:
        name: gateway-http-pool
        max-connections: 100
        max-idle-time: 30s
        max-life-time: 5m
        pending-acquire-timeout: 10s
        evict-in-background: 120s
      # 超时配置
      timeout:
        connect-timeout: 10000
        response-timeout: 30s
        token-timeout: 15s
        token-verify-timeout: 15s
        permission-timeout: 10s
        menu-timeout: 10s
        logout-timeout: 10s
      # 重试配置
      retry:
        token-retry-attempts: 2
        token-verify-retry-attempts: 3
        permission-retry-attempts: 2
        menu-retry-attempts: 2
        logout-retry-attempts: 2
        api-path-retry-attempts: 2
        initial-delay: 300ms
        token-verify-initial-delay: 500ms
```

### 3. 修复的组件

#### AutoGatewaySecurityConfiguration
- 使用配置化的连接池参数
- 启用 TCP Keep-Alive 和 HTTP Keep-Alive
- 根据配置启用调试日志

#### SecurityUtils
- 增强 token 验证的重试机制
- 优化权限加载的错误处理
- 统一的连接错误日志记录

#### RemotePathTreeProvider
- API 路径数据加载重试
- 连接异常的专门处理

#### GatewayAuthEndpoint
- Token 请求重试优化
- 菜单和权限数据加载重试
- 注销请求的容错处理

#### WebClientRetryUtils
- 统一的重试策略工具类
- 智能异常过滤
- 详细的重试日志记录

### 4. 重试策略

#### 可重试异常
- `PrematureCloseException`：连接过早关闭
- `WebClientRequestException`：网络请求异常
- `WebClientResponseException`：5xx 服务器错误

#### 不重试异常
- 4xx 客户端错误（除了网络相关）
- 业务逻辑异常
- 解析异常

#### 重试配置
- **Token 验证**：最多重试 3 次，初始延迟 500ms
- **权限加载**：最多重试 2 次，初始延迟 300ms
- **Token 请求**：最多重试 2 次，初始延迟 500ms
- **其他操作**：最多重试 2 次，初始延迟 300ms

### 5. 监控和调试

#### 日志级别
- **DEBUG**：重试详情和网络调试信息
- **WARN**：连接问题和重试耗尽警告
- **ERROR**：严重错误和异常堆栈

#### 网络调试
启用 `debug-enabled: true` 可以查看详细的网络请求日志：
```yaml
trina:
  gateway:
    webclient:
      debug-enabled: true
```

#### 监控指标
- 连接池使用情况
- 重试次数统计
- 错误类型分布
- 响应时间分布

### 6. 最佳实践

#### 生产环境配置
```yaml
trina:
  gateway:
    webclient:
      connection-pool:
        max-connections: 200
        max-idle-time: 60s
        max-life-time: 10m
      timeout:
        response-timeout: 45s
      retry:
        token-verify-retry-attempts: 5
```

#### 开发环境配置
```yaml
trina:
  gateway:
    webclient:
      debug-enabled: true
      timeout:
        response-timeout: 15s
      retry:
        token-verify-retry-attempts: 2
```

### 7. 故障排查

#### 常见问题
1. **连接池耗尽**：增加 `max-connections`
2. **响应超时**：调整 `response-timeout`
3. **频繁重试**：检查网络稳定性
4. **内存泄漏**：确保连接正确释放

#### 诊断命令
```bash
# 查看连接状态
netstat -an | grep :8080

# 查看 JVM 网络参数
java -XX:+PrintFlagsFinal -version | grep -i network

# 监控连接池
jcmd <pid> VM.classloader_stats
```

## 升级说明

### 兼容性
- 向后兼容现有配置
- 新增配置项有合理默认值
- 不影响现有业务逻辑

### 迁移步骤
1. 更新 trina-gateway-starter 版本
2. 添加新的配置项（可选）
3. 重启应用验证
4. 监控错误日志减少情况

## 总结

通过以上修复，可以有效解决 `PrematureCloseException` 问题，提高网关的稳定性和可靠性。建议在生产环境中根据实际负载情况调整连接池和超时参数。
