package com.trinasolar.data.base;

import lombok.Data;
import java.util.ArrayList;
import java.util.List;

/**
 * 条件组，用于组合多个查询条件
 */
@Data
public class ConditionGroup {
    /**
     * 条件列表
     */
    private List<QueryCondition> conditions = new ArrayList<>();
    
    /**
     * 连接类型（AND 或 OR）
     */
    private LogicalOperator joinType = LogicalOperator.AND;
    
    public ConditionGroup() {
    }
    
    /**
     * 创建一个指定连接类型的条件组
     */
    public ConditionGroup(LogicalOperator joinType) {
        this.joinType = joinType;
    }
    
    /**
     * 添加一个条件
     */
    public ConditionGroup addCondition(String field, QueryConditionType type, Object value) {
        conditions.add(new QueryCondition(field, type, value));
        return this;
    }
    
    /**
     * 使用AND连接
     */
    public ConditionGroup useAnd() {
        this.joinType = LogicalOperator.AND;
        return this;
    }
    
    /**
     * 使用OR连接
     */
    public ConditionGroup useOr() {
        this.joinType = LogicalOperator.OR;
        return this;
    }
    
    /**
     * 添加等于条件
     */
    public ConditionGroup eq(String field, Object value) {
        return addCondition(field, QueryConditionType.EQUAL, value);
    }
    
    /**
     * 添加不等于条件
     */
    public ConditionGroup ne(String field, Object value) {
        return addCondition(field, QueryConditionType.NOT_EQUAL, value);
    }
    
    /**
     * 添加大于条件
     */
    public ConditionGroup gt(String field, Object value) {
        return addCondition(field, QueryConditionType.GREATER_THAN, value);
    }
    
    /**
     * 添加大于等于条件
     */
    public ConditionGroup ge(String field, Object value) {
        return addCondition(field, QueryConditionType.GREATER_THAN_OR_EQUAL, value);
    }
    
    /**
     * 添加小于条件
     */
    public ConditionGroup lt(String field, Object value) {
        return addCondition(field, QueryConditionType.LESS_THAN, value);
    }
    
    /**
     * 添加小于等于条件
     */
    public ConditionGroup le(String field, Object value) {
        return addCondition(field, QueryConditionType.LESS_THAN_OR_EQUAL, value);
    }
    
    /**
     * 添加IN条件
     */
    public ConditionGroup in(String field, List<?> values) {
        return addCondition(field, QueryConditionType.IN, values);
    }
    
    /**
     * 添加NOT IN条件
     */
    public ConditionGroup notIn(String field, List<?> values) {
        return addCondition(field, QueryConditionType.NOT_IN, values);
    }
    
    /**
     * 添加LIKE条件
     */
    public ConditionGroup like(String field, String value) {
        return addCondition(field, QueryConditionType.LIKE, value);
    }
    
    /**
     * 添加IS NULL条件
     */
    public ConditionGroup isNull(String field) {
        return addCondition(field, QueryConditionType.IS_NULL, null);
    }
    
    /**
     * 添加IS NOT NULL条件
     */
    public ConditionGroup isNotNull(String field) {
        return addCondition(field, QueryConditionType.IS_NOT_NULL, null);
    }
    
    /**
     * 添加BETWEEN条件
     */
    public ConditionGroup between(String field, Object start, Object end) {
        List<Object> values = new ArrayList<>();
        values.add(start);
        values.add(end);
        return addCondition(field, QueryConditionType.BETWEEN, values);
    }
} 