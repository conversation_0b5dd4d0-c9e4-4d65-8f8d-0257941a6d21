package com.trinasolar.elasticsearch.base.annotation;

import java.lang.annotation.*;

/**
 * Elasticsearch字段注解
 * 用于配置实体类字段的Elasticsearch映射信息
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface EsField {
    
    /**
     * 字段类型
     * @return 字段类型
     */
    FieldType type() default FieldType.AUTO;
    
    /**
     * 字段名称，默认使用属性名
     * @return 字段名称
     */
    String name() default "";
    
    /**
     * 索引分析器
     * @return 分析器名称
     */
    String analyzer() default "";
    
    /**
     * 搜索分析器
     * @return 搜索分析器名称
     */
    String searchAnalyzer() default "";
    
    /**
     * 标准化器
     * @return 标准化器名称
     */
    String normalizer() default "";
    
    /**
     * 字段权重，用于提升搜索相关性
     * @return 权重值，默认1.0
     */
    float boost() default 1.0f;
    
    /**
     * 是否存储原始值
     * @return 是否存储，默认false
     */
    boolean store() default false;
    
    /**
     * 是否索引
     * @return 是否索引，默认true
     */
    boolean index() default true;
    
    /**
     * 是否启用doc_values
     * @return 是否启用doc_values，默认true
     */
    boolean docValues() default true;
    
    /**
     * 是否启用字段数据
     * @return 是否启用字段数据，默认false
     */
    boolean fielddata() default false;
    
    /**
     * 日期格式
     * @return 日期格式字符串
     */
    String format() default "";
    
    /**
     * 是否强制类型转换
     * @return 是否强制类型转换，默认true
     */
    boolean coerce() default true;
    
    /**
     * 忽略格式错误
     * @return 是否忽略格式错误，默认false
     */
    boolean ignoreMalformed() default false;
    
    /**
     * 空值处理
     * @return 空值替换字符串
     */
    String nullValue() default "";
    
    /**
     * 复制到其他字段
     * @return 目标字段名数组
     */
    String[] copyTo() default {};
    
    /**
     * 相似性算法
     * @return 相似性算法名称
     */
    String similarity() default "";
    
    /**
     * 词项向量
     * @return 词项向量配置
     */
    String termVector() default "";
    
    /**
     * 索引选项
     * @return 索引选项
     */
    String indexOptions() default "";
    
    /**
     * 是否启用位置增量间隔
     * @return 是否启用位置增量间隔，默认true
     */
    boolean positionIncrementGap() default true;
    
    /**
     * 忽略超过长度限制的字符
     * @return 字符长度限制，默认-1表示不限制
     */
    int ignoreAbove() default -1;
    
    /**
     * 缩放因子（用于scaled_float类型）
     * @return 缩放因子，默认1.0
     */
    double scalingFactor() default 1.0;
    
    /**
     * 维度（用于dense_vector类型）
     * @return 向量维度，默认-1
     */
    int dims() default -1;
    
    /**
     * 是否启用嵌套字段包含在父文档中
     * @return 是否包含在父文档中，默认false
     */
    boolean includeInParent() default false;
    
    /**
     * 是否启用嵌套字段包含在根文档中
     * @return 是否包含在根文档中，默认false
     */
    boolean includeInRoot() default false;
    
    /**
     * 动态映射
     * @return 动态映射策略
     */
    DynamicMapping dynamic() default DynamicMapping.TRUE;
    
    /**
     * 是否启用
     * @return 是否启用字段，默认true
     */
    boolean enabled() default true;
    
    /**
     * 字段数据频率过滤器
     * @return 字段数据频率过滤器配置
     */
    String fielddataFrequencyFilter() default "";
    
    /**
     * 是否忽略该字段
     * @return 是否忽略，默认false
     */
    boolean ignore() default false;
}