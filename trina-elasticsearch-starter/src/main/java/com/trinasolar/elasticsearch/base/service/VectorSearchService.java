package com.trinasolar.elasticsearch.base.service;

import com.trinasolar.elasticsearch.base.entity.BaseEsEntity;

import java.util.List;

/**
 * 向量搜索服务接口
 * 提供向量化和向量搜索的基础能力
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface VectorSearchService<T extends BaseEsEntity> {

    /**
     * 为文本生成向量
     * 
     * @param text 输入文本
     * @return 向量数组
     */
    float[] generateEmbedding(String text);

    /**
     * 批量生成向量
     * 
     * @param texts 文本数组
     * @return 向量数组列表
     */
    float[][] batchGenerateEmbedding(String[] texts);

    /**
     * 为实体生成向量并保存
     * 
     * @param entity 实体对象
     * @param titleField 标题字段名
     * @param contentField 内容字段名
     * @return 处理后的实体
     */
    T generateAndSetVectors(T entity, String titleField, String contentField);

    /**
     * 向量相似度搜索
     * 
     * @param queryVector 查询向量
     * @param vectorField 向量字段名（content_vector 或 title_vector）
     * @param threshold 相似度阈值
     * @param size 返回数量
     * @return 搜索结果
     */
    List<T> vectorSearch(float[] queryVector, String vectorField, float threshold, int size);

    /**
     * 文本向量搜索
     * 
     * @param queryText 查询文本
     * @param vectorField 向量字段名
     * @param threshold 相似度阈值
     * @param size 返回数量
     * @return 搜索结果
     */
    List<T> textVectorSearch(String queryText, String vectorField, float threshold, int size);

    /**
     * 混合搜索（关键词 + 向量）
     * 
     * @param keyword 关键词
     * @param keywordFields 关键词搜索字段
     * @param vectorField 向量字段名
     * @param keywordWeight 关键词权重
     * @param vectorWeight 向量权重
     * @param threshold 向量相似度阈值
     * @param size 返回数量
     * @return 搜索结果
     */
    List<T> hybridSearch(String keyword, String[] keywordFields, String vectorField,
                        float keywordWeight, float vectorWeight, float threshold, int size);

    /**
     * 计算两个向量的余弦相似度
     * 
     * @param vector1 向量1
     * @param vector2 向量2
     * @return 相似度值（0-1）
     */
    float calculateCosineSimilarity(float[] vector1, float[] vector2);

    /**
     * 查找相似实体
     * 
     * @param entity 目标实体
     * @param vectorField 向量字段名
     * @param threshold 相似度阈值
     * @param size 返回数量
     * @return 相似实体列表
     */
    List<T> findSimilarEntities(T entity, String vectorField, float threshold, int size);

    /**
     * 检查向量搜索服务是否可用
     * 
     * @return 是否可用
     */
    boolean isVectorSearchAvailable();
}
