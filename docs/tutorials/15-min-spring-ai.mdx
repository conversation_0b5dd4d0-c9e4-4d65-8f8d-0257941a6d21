---
title: "15分钟快速上手Spring AI开发"
description: "快速掌握Spring AI框架的核心概念、使用方法及实践案例"
date: "2024-05-27"
tags: ["Spring", "AI", "LLM", "Java", "生成式AI"]
---

# 15分钟快速上手Spring AI开发

## 🔍 技术简介 (3分钟)

### 什么是Spring AI？

Spring AI是VMware于2023年推出的开源框架，旨在简化大语言模型(LLM)与Spring应用的集成。它提供了统一的API抽象，支持多种LLM供应商，包括OpenAI、阿里云通义千问、百度文心一言等，使Java开发者能够快速构建AI赋能的应用程序。

Spring AI的设计理念是"AI应用开发的统一抽象"，类似于Spring Data为不同数据库提供的统一接口，使开发者能够用相同的代码接入不同的AI服务提供商，大大降低了切换成本和学习曲线。

### 核心优势

1. **统一API**：提供一致的接口调用不同的AI模型，轻松切换供应商而无需修改业务代码
2. **Spring生态整合**：与Spring Boot、Spring Data等无缝集成，享受依赖注入、自动配置等便利
3. **丰富的模型支持**：支持OpenAI、阿里云通义千问、百度文心一言、智谱AI等主流大模型
4. **多模态能力**：支持文本生成、文本嵌入、图像生成等多种AI能力
5. **RAG支持**：内置检索增强生成(RAG)功能，轻松构建知识库问答系统

### 技术架构

Spring AI的架构设计清晰，主要包含以下核心组件：

- **模型抽象层**：提供ChatClient、ChatModel等统一接口
- **提示工程工具**：PromptTemplate支持变量替换和模板管理
- **Embedding支持**：向量化文本并支持向量数据库集成
- **Function Calling**：支持工具调用，让模型调用本地函数
- **Advisor机制**：提供RAG、记忆管理等高级功能的统一扩展点

## 💡 核心功能 (4分钟)

### 1. 聊天模型接口

Spring AI提供了两种与大模型交互的核心接口：

- **ChatClient**：高级接口，支持Prompt、工具调用等
- **ChatModel**：底层接口，提供更灵活的定制能力

```java
// 使用ChatClient进行简单对话
String response = chatClient.prompt()
    .system("你是一位Java专家")
    .user("如何使用Java调用REST API?")
    .call()
    .content();

// 使用ChatModel进行流式输出
Flux<ChatResponse> responseStream = chatModel.generate(messages);
```

### 2. 多模态支持

Spring AI不仅支持文本生成，还支持图像生成、语音识别等多模态能力：

```java
// 文生图示例
Image image = imageClient.generate("一只坐在Java代码上的猫咪")
    .create();

// 语音转文字示例
String transcription = speechClient.transcribe(audioFile);
```

### 3. Function Calling工具调用

Spring AI允许大模型调用本地Java方法，实现复杂交互：

```java
@Service
public class WeatherService {
    @Tool(name = "getWeather", description = "获取指定城市的天气信息")
    public String getWeather(@ToolParameter(name = "city") String city) {
        // 实际实现会调用天气API
        return "北京，晴，25℃";
    }
}

// 在对话中使用工具调用
chatClient.prompt()
    .user("今天北京天气怎么样？")
    .toolCallbacks(ToolCallbacks.from(weatherService))
    .call();
```

### 4. 向量检索与RAG

Spring AI提供了完整的RAG（检索增强生成）支持：

```java
// 文本向量化
Embedding embedding = embeddingClient.embed("如何使用Spring Boot开发微服务？");

// 使用QuestionAnswerAdvisor构建RAG应用
Advisor advisor = QuestionAnswerAdvisor.builder(vectorStore)
    .searchRequest(SearchRequest.builder().topK(3).build())
    .build();

chatClient.prompt()
    .advisors(advisor)
    .user("Spring Boot如何实现服务发现？")
    .call();
```

### 5. 记忆管理

Spring AI提供了对话历史管理功能，支持多轮对话：

```java
// 使用消息记忆Advisor
MessageChatMemoryAdvisor memoryAdvisor = MessageChatMemoryAdvisor.builder()
    .chatMemoryStore(inMemoryChatMemoryStore)
    .memoryId("user-123")
    .maxMessages(10)
    .build();

chatClient.prompt()
    .advisors(memoryAdvisor)
    .user("继续我们刚才的话题")
    .call();
```

## 📊 应用场景 (3分钟)

### 1. 智能客服系统

**问题**：传统客服系统面临人力成本高、响应慢、24小时服务难等问题。

**解决方案**：使用Spring AI构建智能客服系统，结合RAG技术接入企业知识库。

**实现**：
1. 使用Embedding模型将企业文档向量化存入向量数据库
2. 通过QuestionAnswerAdvisor实现基于知识库的问答
3. 使用MessageChatMemoryAdvisor实现多轮对话记忆
4. 通过Function Calling调用CRM系统API获取客户信息

**优势**：
- 24/7全天候服务
- 一致的回答质量
- 能够处理专业领域问题
- 与现有系统无缝集成

### 2. 内容生成系统

**问题**：企业需要大量内容创作，如产品描述、营销文案、技术文档等。

**解决方案**：使用Spring AI构建内容生成系统，提供各类文本生成服务。

**实现**：
1. 使用PromptTemplate定义各类内容模板
2. 通过ChatClient生成内容
3. 使用流式输出实时展示生成过程
4. 通过Function Calling调用企业数据API获取产品信息

**优势**：
- 提高内容创作效率
- 确保风格一致性
- 轻松适应不同内容类型
- 与企业数据系统集成

### 3. 智能编码助手

**问题**：开发者需要更高效的编码辅助工具，减少重复劳动。

**解决方案**：使用Spring AI构建智能编码助手，辅助代码生成、代码解释和问题排查。

**实现**：
1. 使用ChatClient处理代码相关问题
2. 通过Function Calling集成版本控制、编译工具
3. 使用VectorStoreChatMemoryAdvisor接入编程文档
4. 支持代码生成和代码解释

**优势**：
- 加速开发流程
- 降低编程错误
- 辅助文档阅读
- 促进知识分享

## 🚀 快速入门 (5分钟)

### 环境准备

要开始使用Spring AI，你需要：

- JDK 17或更高版本
- Maven或Gradle构建工具
- Spring Boot 3.x项目
- AI服务提供商的API密钥（如OpenAI、阿里云等）

### 项目设置

**Step 1: 创建Spring Boot项目**

使用Spring Initializr创建一个基础Spring Boot项目：https://start.spring.io/

**Step 2: 添加Spring AI依赖**

在`pom.xml`中添加Spring AI依赖：

```xml
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-openai-spring-boot-starter</artifactId>
    <version>1.0.0-M1</version>
</dependency>
```

**Step 3: 配置API密钥**

在`application.properties`中配置AI服务提供商的API密钥：

```properties
# OpenAI配置
spring.ai.openai.api-key=your-api-key
spring.ai.openai.chat.model=gpt-3.5-turbo

# 或使用阿里云通义千问
# spring.ai.tongyi.api-key=your-api-key
# spring.ai.tongyi.chat.model=qwen-turbo
```

### 实现简单对话应用

**Step 4: 创建REST控制器**

```java
@RestController
@RequestMapping("/api/chat")
@RequiredArgsConstructor
public class ChatController {
    
    private final ChatClient chatClient;
    
    @PostMapping
    public String chat(@RequestBody String userMessage) {
        return chatClient.prompt()
            .system("你是一个友好的AI助手")
            .user(userMessage)
            .call()
            .content();
    }
    
    @GetMapping("/stream")
    public Flux<String> streamChat(@RequestParam String userMessage) {
        return chatClient.prompt()
            .system("你是一个友好的AI助手")
            .user(userMessage)
            .stream()
            .content();
    }
}
```

**Step 5: 添加Function Calling功能**

```java
@Service
public class CalculatorService {

    @Tool(name = "calculator", description = "计算数学表达式的结果")
    public double calculate(@ToolParameter(name = "expression") String expression) {
        // 简单示例，实际应用中需要更安全的实现
        return new javax.script.ScriptEngineManager()
            .getEngineByName("JavaScript")
            .eval(expression);
    }
}

// 在ChatController中添加
@PostMapping("/tools")
public String chatWithTools(@RequestBody String userMessage) {
    return chatClient.prompt()
        .system("你是一个数学助手，可以帮助用户计算数学表达式")
        .user(userMessage)
        .toolCallbacks(ToolCallbacks.from(calculatorService))
        .call()
        .content();
}
```

**Step 6: 运行应用**

```bash
./mvnw spring-boot:run
```

现在你可以通过以下端点测试你的AI应用：

- `POST /api/chat` - 基本对话
- `GET /api/chat/stream?userMessage=...` - 流式对话
- `POST /api/chat/tools` - 带工具调用的对话

### 添加RAG功能

**Step 7: 添加向量数据库依赖**

```xml
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-pgvector-store</artifactId>
    <version>1.0.0-M1</version>
</dependency>
```

**Step 8: 配置向量数据库**

```properties
spring.datasource.url=*****************************************
spring.datasource.username=postgres
spring.datasource.password=password
```

**Step 9: 实现RAG功能**

```java
@Service
@RequiredArgsConstructor
public class DocumentService {

    private final EmbeddingClient embeddingClient;
    private final PgVectorStore vectorStore;
    
    @PostConstruct
    public void init() {
        // 示例文档
        List<Document> documents = List.of(
            new Document("Spring Boot是一个开源的Java框架，用于创建微服务应用"),
            new Document("Spring AI是Spring官方推出的AI集成框架")
        );
        
        // 向量化并存储文档
        vectorStore.add(documents);
    }
    
    public String askDocument(String question) {
        Advisor advisor = QuestionAnswerAdvisor.builder(vectorStore)
            .searchRequest(SearchRequest.builder().topK(3).build())
            .build();
            
        return chatClient.prompt()
            .advisors(advisor)
            .user(question)
            .call()
            .content();
    }
}
```

## 总结

Spring AI为Java开发者提供了一种简便方式来集成和使用大型语言模型，它不仅支持基本的文本生成，还提供了RAG、Function Calling等高级功能。通过统一的API抽象，开发者可以轻松切换不同的AI服务提供商，而无需大幅修改代码。

Spring AI的设计理念与Spring生态系统一脉相承，强调约定优于配置、面向接口编程，使Java开发者能够以熟悉的方式构建AI应用。无论是构建智能客服、内容生成系统还是智能编码助手，Spring AI都能提供强大而灵活的支持。

随着AI技术的不断发展，Spring AI也在持续迭代，引入更多功能和模型支持。对于想要在Java生态系统中探索生成式AI应用开发的开发者来说，Spring AI无疑是一个理想的起点。

## 相关资源

- 官方文档：[Spring AI Reference Documentation](https://docs.spring.io/spring-ai/reference/)
- GitHub仓库：[spring-projects/spring-ai](https://github.com/spring-projects/spring-ai)
- 示例项目：[spring-ai-examples](https://github.com/spring-projects/spring-ai/tree/main/spring-ai-samples)
- 博客文章：[Introducing Spring AI](https://spring.io/blog/2023/12/30/introducing-spring-ai) 