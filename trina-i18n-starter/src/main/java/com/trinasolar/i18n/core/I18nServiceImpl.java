package com.trinasolar.i18n.core;

import com.trinasolar.i18n.api.TranslationApiClient;
import com.trinasolar.i18n.cache.TranslationCache;
import com.trinasolar.i18n.config.I18nProperties;
import com.trinasolar.i18n.file.TranslationFileLoader;
import com.trinasolar.i18n.file.TranslationFileSaver;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 国际化服务实现
 *
 * <AUTHOR>
 */
public class I18nServiceImpl implements I18nService {

    private static final Logger log = LoggerFactory.getLogger(I18nServiceImpl.class);
    
    private final I18nProperties properties;
    private final TranslationCache translationCache;
    private final TranslationFileLoader translationFileLoader;
    private final TranslationFileSaver translationFileSaver;
    private final TranslationApiClient translationApiClient;
    private final ThreadLocal<String> currentLanguage = new ThreadLocal<>();

    /**
     * 默认构造方法
     */
    public I18nServiceImpl(
            I18nProperties properties,
            TranslationCache translationCache,
            TranslationFileLoader translationFileLoader,
            TranslationFileSaver translationFileSaver,
            TranslationApiClient translationApiClient) {
        this.properties = properties;
        this.translationCache = translationCache;
        this.translationFileLoader = translationFileLoader;
        this.translationFileSaver = translationFileSaver;
        this.translationApiClient = translationApiClient;
        
        // 预加载支持的语言
        if (properties.getSupportedLanguages() != null && !properties.getSupportedLanguages().isEmpty()) {
            properties.getSupportedLanguages().forEach(this::preloadTranslations);
        } else if (StringUtils.hasText(properties.getDefaultLanguage())) {
            preloadTranslations(properties.getDefaultLanguage());
        }
    }

    @Override
    public String getMessage(String key, String lang) {
        return getMessage(key, key, lang);
    }

    @Override
    public String getMessage(String key, String defaultMessage, String language) {
        if (key == null) {
            return defaultMessage;
        }
        
        String targetLanguage = getEffectiveLanguage(language);
        return findTranslation(key, defaultMessage, targetLanguage);
    }

    @Override
    public String getMessage(String key) {
        return getMessage(key, key, getCurrentLanguage());
    }

    @Override
    public String getMessageWithDefault(String key, String defMsg) {
        return getMessage(key, defMsg, getCurrentLanguage());
    }

    @Override
    public Map<String, String> getAllMessages(String language) {
        if (language == null) {
            return Collections.emptyMap();
        }
        
        String targetLanguage = getEffectiveLanguage(language);
        
        // 从文件加载所有翻译
        return translationFileLoader.loadAllTranslations(targetLanguage);
    }

    @Override
    public void reloadTranslations() {
        translationFileLoader.reloadAllFiles();
        translationCache.invalidateAll();
    }

    @Override
    public void setCurrentLanguage(String language) {
        if (StringUtils.hasText(language)) {
            currentLanguage.set(language);
        } else {
            clearCurrentLanguage();
        }
    }

    @Override
    public String getCurrentLanguage() {
        String language = currentLanguage.get();
        if (!StringUtils.hasText(language)) {
            // 如果未设置，尝试从LocaleContextHolder获取
            Locale locale = LocaleContextHolder.getLocale();
            language = locale.toLanguageTag();
        }
        return getEffectiveLanguage(language);
    }

    @Override
    public void clearCurrentLanguage() {
        currentLanguage.remove();
    }

    /**
     * 查找翻译，按照缓存 -> 文件 -> API的顺序
     */
    private String findTranslation(String key, String defaultMessage, String language) {
        // 1. 从缓存查找
        Optional<String> cachedTranslation = translationCache.get(key, language);
        if (cachedTranslation.isPresent()) {
            return cachedTranslation.get();
        }
        
        // 2. 从文件查找
        Optional<String> fileTranslation = translationFileLoader.loadTranslation(key, language);
        if (fileTranslation.isPresent()) {
            // 更新缓存
            String translation = fileTranslation.get();
            translationCache.put(key, language, translation);
            return translation;
        }
        
        // 3. 从API查找
        if (translationApiClient != null && translationApiClient.isAvailable()) {
            try {
                // 默认使用默认语言作为源语言
                String sourceLanguage = properties.getDefaultLanguage();
                Optional<String> apiTranslation = translationApiClient.translate(
                    StringUtils.hasText(defaultMessage) ? defaultMessage : key,
                    sourceLanguage,
                    language
                );
                
                if (apiTranslation.isPresent()) {
                    String translation = apiTranslation.get();
                    
                    // 更新缓存
                    translationCache.put(key, language, translation);
                    
                    // 异步保存到文件
                    translationFileSaver.saveTranslationAsync(key, translation, language);
                    
                    return translation;
                }
            } catch (Exception e) {
                log.error("Failed to translate key '{}' to language '{}' via API", key, language, e);
            }
        }
        
        // 未找到翻译，返回默认消息
        return defaultMessage;
    }

    /**
     * 获取有效的语言
     */
    private String getEffectiveLanguage(String language) {
        if (!StringUtils.hasText(language)) {
            return properties.getDefaultLanguage();
        }
        
        // 如果支持的语言列表不为空，验证是否支持该语言
        if (properties.getSupportedLanguages() != null && !properties.getSupportedLanguages().isEmpty()) {
            if (!properties.getSupportedLanguages().contains(language)) {
                return properties.getDefaultLanguage();
            }
        }
        
        return language;
    }

    /**
     * 预加载指定语言的翻译
     */
    private void preloadTranslations(String language) {
        if (!StringUtils.hasText(language)) {
            return;
        }
        
        try {
            Map<String, String> translations = translationFileLoader.loadAllTranslations(language);
            if (!translations.isEmpty()) {
                translationCache.putAll(translations, language);
                log.info("Preloaded {} translations for language '{}'", translations.size(), language);
            }
        } catch (Exception e) {
            log.error("Failed to preload translations for language '{}'", language, e);
        }
    }
}