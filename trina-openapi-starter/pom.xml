<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.trinasolar</groupId>
        <artifactId>trina-common</artifactId>
        <version>4.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>trina-openapi-starter</artifactId>
    <packaging>jar</packaging>

    <properties>
        <!-- Updated to use Swagger v3 instead of v2 -->
        <swagger.v3.version>2.2.15</swagger.v3.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.trinasolar</groupId>
            <artifactId>trina-common-core</artifactId>
            <version>${common.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-commons</artifactId>
        </dependency>
    </dependencies>

</project>