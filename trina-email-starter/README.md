# Trina Elasticsearch Starter

基于 `JavaMailSender`、`Spring Mail` 的 e-mail邮件发送服务，提供简单易用的邮件发送接口。

## 特性

- 基于 `JavaMailSender`、`Spring Mail` 实现
- 支持多收件人
- 支持多抄送人
- 支持简单文本、html、附件格式
- 支持邮件服务器配置（动态变化）
- 支持原生文件上传无需转换

## 快速开始

### 1. 添加依赖

在你的项目中添加以下依赖：

```xml

<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-email-starter</artifactId>
    <version>4.0.0</version>
</dependency>
```

### 2. 配置

在 `application.yml` 中添加 e-mail 服务器配置：以QQ服务器为例

```yaml
spring:
  mail:
    host: smtp.qq.com
    port: 587
    username: 邮箱地址(<EMAIL>)
    password: 授权码
    protocol: smtp
    from: 邮箱地址(<EMAIL>)
    auth: true
    ssl: false
    starttls: true
```

### 3. 使用

注入 `MailService` 即可使用：

```java

@Autowired
private MailService mailService;
```

## 核心组件

### MailService

核心服务类，提供邮件发送接口：

- `sendSimpleMail(EmailRequest emailRequest)` - 发送邮件

### BaseDocument

邮件发送请求体，包含：

- `users` - 收件人
- `subject` - 主题
- `msg` - 邮件内容
- `isHtml` - 是否为html格式邮件
- `ccUsers` - 邮件抄送人
- `file` - 附件

## 示例

完整的使用示例请参考 `ElasticsearchExample` 类，包含：

- 创建索引和映射
- 索引文档
- 搜索和高亮
- 更新和删除文档
- 批量操作
- 集群状态检查

## 配置说明
- （以QQ邮箱SMTP配置为例）

| 配置项      | 说明               | 默认值                   |
|----------|------------------|-----------------------|
| host     | 邮件服务器的主机地址       | 例如smtp.qq.com         |
| port     | 邮件服务器的端口号        | 587 （SMTP 端口）         |
| username | 邮件服务器的用户名        | 通常是邮箱地址               |
| password | 邮件服务器的授权码        | 授权码                   |
| protocol | 邮件协议，通常是邮件协议，通常是 | smtp                  |
| from     | 默认发件人的邮箱地址       | 通常是username           |
| auth     | 启用SMTP认证         | 必须为true               |
| ssl      | 是否启用SSL加密        | 应设为false（与starttls互斥） |
| starttls | 启用STARTTLS加密（推荐） | 推荐配置（比SSL更安全）         |


## 注意事项

1. 确认服务器开启SMTP服务
2. 确认服务器端口正确
3. 确认用户名、密码正确

## 版本历史
- 4.0.0: 基于 JavaMailSender、Spring Mail实现.