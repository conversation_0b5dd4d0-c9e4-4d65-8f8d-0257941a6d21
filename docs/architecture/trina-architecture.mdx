---
title: Trina Common 架构设计与概述
description: Trina Common 框架完整架构设计文档，包含整体架构、核心理念和技术实现
---

## 架构理念

Trina Common 框架采用模块化、分层化的架构设计，遵循微服务架构的最佳实践，为企业级应用提供完整的技术栈支持。该架构被设计为一个模块化的生态系统，由可互操作的组件组成，为构建企业级 Java 应用程序提供基础。

## 架构原则

### 1. 模块化设计
- **松耦合**：各模块间依赖关系清晰，可独立开发和部署
- **高内聚**：模块内部功能紧密相关，职责单一
- **可插拔**：支持按需引入，灵活组合
- **功能分离**：功能被分离成具有明确责任的内聚模块

### 2. 分层架构
- **表现层**：Web 模块、网关模块
- **业务层**：核心业务逻辑处理
- **服务层**：微服务治理、远程调用
- **数据层**：数据访问、缓存、存储
- **基础层**：工具类、配置管理

### 3. 设计理念
- **标准化**：跨模块标准化常见模式和实践
- **可扩展性**：基础组件可以扩展以添加自定义功能
- **约定优于配置**：合理的默认值减少了对显式配置的需求
- **渐进式揭示**：简单用例容易实现，复杂场景也可实现
- **互操作性**：模块可以独立使用或一起使用

### 4. 云原生支持
- **容器化**：支持 Docker 容器部署
- **编排化**：原生支持 Kubernetes
- **配置外部化**：支持 Nacos 配置中心
- **服务发现**：集成服务注册与发现

## 整体架构图

### 高级架构视图

```mermaid
graph TB
    subgraph "应用层"
        A[Web 应用]
        B[微服务应用]
        C[网关服务]
    end
    
    subgraph "框架层"
        D[trina-web-starter]
        E[trina-gateway-starter]
        F[trina-remote-starter]
    end
    
    subgraph "服务治理层"
        G[trina-nacos-starter]
        H[trina-microservice-starter]

    end
    
    subgraph "数据访问层"
        J[trina-mybatis-starter]
        K[trina-redis-starter]
        L[trina-elasticsearch-starter]
        M[trina-mongodb-starter]
    end
    
    subgraph "基础设施层"
        N[trina-minio-starter]
        P[trina-xxl-job-starter]
        Q[trina-sftp-starter]
    end
    
    subgraph "核心层"
        R[trina-common-core]
        S[trina-security-starter]
        T[trina-xss-starter]
    end
    
    A --> D
    B --> F
    C --> E
    
    D --> G
    E --> H
    F --> I
    
    G --> J
    H --> K
    I --> L
    
    J --> N
    K --> O
    L --> P
    M --> Q
    
    N --> R
    O --> S
    P --> T
```

### 系统架构层次图

下图展示了从前端到基础组件库的完整系统架构层次关系：

![系统架构层次图](/docs/images/architecture-diagram.svg)

系统架构分为三个主要层次：
1. **前端层**：包括前端主框架和各种前端应用，负责用户界面交互和展示
2. **后端层**：通过API网关连接多个后端服务应用，处理业务逻辑和数据处理
3. **基础组件库**：由TrinaCommon和TrinaDesignUI组成，为前后端提供基础支持
   - TrinaCommon：提供微服务适配框架、后端常用工具库等后端组件
   - TrinaDesignUI：提供表格组件、表单组件等前端UI组件

这种分层架构设计确保了系统的模块化、可维护性和可扩展性，同时促进了前后端组件的复用。

## 核心模块

Trina Common 由几个核心模块组成，每个模块都专注于应用程序开发的特定方面：

### 1. trina-common-core
**核心基础模块**，提供框架的基础功能：
- 基础实体类和通用接口
- 统一响应格式和异常处理
- 常用工具类和配置管理
- 全局控制器增强

### 2. trina-web-starter
**Web 应用开发模块**：
- RESTful API 开发支持
- 统一异常处理
- 请求响应拦截
- 跨域配置

### 3. trina-mybatis-starter
**数据访问层模块**：
- MyBatis-Plus 集成
- 多数据源支持
- 分页查询
- 审计字段自动填充

### 4. trina-redis-starter
**缓存模块**：
- Redis 操作封装
- 分布式锁
- 缓存注解
- 限流功能

### 5. trina-security-starter
**安全模块**：
- 权限控制
- 用户认证
- 安全配置
- 密码加密

### 6. trina-gateway-starter
**网关模块**：
- 路由配置
- 负载均衡
- 限流熔断
- 统一鉴权

### 7. trina-microservice-starter
**微服务治理模块**：
- 服务注册与发现
- 负载均衡
- 远程调用
- 服务容错

## 技术栈

### 核心框架
- **Spring Boot 3.4**：应用框架
- **Spring Cloud 2023**：微服务框架
- **Java 17**：编程语言
- **Maven**：构建工具

### 数据访问
- **MyBatis-Plus**：ORM 框架
- **Redis**：缓存数据库
- **Elasticsearch**：搜索引擎
- **MongoDB**：文档数据库
- **MinIO**：对象存储

### 服务治理
- **Nacos**：配置中心和服务发现
- **微服务框架**：服务治理和负载均衡
- **Seata**：分布式事务
- **OpenFeign**：服务调用
- **Gateway**：API 网关

### 基础设施
- **XXL-Job**：分布式任务调度
- **SFTP**：文件传输
- **Docker**：容器化
- **Kubernetes**：容器编排

## 模块依赖关系

```mermaid
graph TD
    A[trina-common-core] --> B[trina-web-starter]
    A --> C[trina-mybatis-starter]
    A --> D[trina-redis-starter]
    A --> E[trina-security-starter]
    
    B --> F[trina-gateway-starter]
    C --> G[trina-elasticsearch-starter]
    D --> H[trina-microservice-starter]
    
    E --> I[trina-xss-starter]
    F --> J[trina-remote-starter]
    
    A --> K[trina-minio-starter]
    A --> M[trina-xxl-job-starter]
    A --> N[trina-sftp-starter]
```

## 部署架构

### 单体应用部署
```
┌─────────────────────────────────────┐
│           Spring Boot 应用          │
│  ┌─────────────────────────────────┐ │
│  │        Trina Starters          │ │
│  │  ┌─────┐ ┌─────┐ ┌─────┐ ┌───┐ │ │
│  │  │Web  │ │Data │ │Cache│ │...│ │ │
│  │  └─────┘ └─────┘ └─────┘ └───┘ │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### 微服务部署
```
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│   Gateway   │ │  Service A  │ │  Service B  │
│             │ │             │ │             │
│ ┌─────────┐ │ │ ┌─────────┐ │ │ ┌─────────┐ │
│ │Trina    │ │ │ │Trina    │ │ │ │Trina    │ │
│ │Starters │ │ │ │Starters │ │ │ │Starters │ │
│ └─────────┘ │ │ └─────────┘ │ │ └─────────┘ │
└─────────────┘ └─────────────┘ └─────────────┘
       │               │               │
       └───────────────┼───────────────┘
                       │
              ┌─────────────┐
              │   Nacos     │
              │ (配置中心)   │
              └─────────────┘
```

## 开发流程

### 1. 项目初始化
1. 引入 `trina-parent` 作为父 POM
2. 根据需求选择相应的 starter 模块
3. 配置应用属性文件

### 2. 开发阶段
1. 使用 `trina-common-core` 的基础类
2. 利用各 starter 提供的自动配置
3. 遵循框架的编码规范

### 3. 测试部署
1. 单元测试和集成测试
2. 容器化打包
3. 部署到目标环境

## 最佳实践

### 1. 模块选择
- 按需引入，避免不必要的依赖
- 优先使用框架提供的功能
- 遵循单一职责原则

### 2. 配置管理
- 使用外部配置中心
- 环境隔离配置
- 敏感信息加密

### 3. 异常处理
- 使用统一异常处理
- 定义业务异常类型
- 提供友好的错误信息

### 4. 性能优化
- 合理使用缓存
- 数据库连接池优化
- 异步处理长时间任务

## 扩展指南

### 自定义 Starter
1. 创建自动配置类
2. 定义配置属性
3. 编写 `spring.factories` 文件
4. 提供使用文档

### 集成第三方组件
1. 评估组件兼容性
2. 创建适配器层
3. 提供配置选项
4. 编写集成测试

---

通过这种架构设计，Trina Common 框架能够为企业级应用提供稳定、高效、可扩展的技术基础，支持从单体应用到微服务架构的平滑演进。