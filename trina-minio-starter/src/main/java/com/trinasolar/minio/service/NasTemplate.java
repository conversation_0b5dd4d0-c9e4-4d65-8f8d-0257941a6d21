package com.trinasolar.minio.service;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import com.trinasolar.minio.entity.FileResult;
import com.trinasolar.minio.vo.MinioItem;
import io.minio.StatObjectResponse;
import io.minio.messages.Bucket;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.util.Assert;

/**
 * @ClassName NasTemplate
 * @Description NAS存储服务实现
 * <AUTHOR>
 * @Date 2021/12/23 11:27 上午
 **/
@RequiredArgsConstructor
public class NasTemplate implements InitializingBean, FileTemplate {
    private final String clientPath;
    private final String nasUrl;

    @Override
    public void createBucket(String bucketName) {
        File file = new File(clientPath.concat("/").concat(bucketName));
        if (!file.exists()) {
            file.mkdir();
        }
    }

    @Override
    public List<Bucket> getAllBuckets() {
        //todo 获取clientPath下所有目录对象
        return null;
    }

    @Override
    public Optional<Bucket> getBucket(String bucketName) {
        //todo 目录对象的封装
        return Optional.empty();
    }

    @Override
    @SneakyThrows
    public void removeBucket(String bucketName) {
        deleteAllByPath(new File(clientPath.concat("/").concat(bucketName)));
    }

    @Override
    public List<MinioItem> getAllObjectsByPrefix(String bucketName, String prefix, boolean recursive) {
        //todo
        return null;
    }

    @Override
    public String getObjectURL(String bucketName, String objectName, Integer expires) {
        Assert.hasText(bucketName, "bucketName 为空");
        Assert.hasText(objectName, "objectName 为空");
        return nasUrl.concat("/").concat(bucketName).concat("/").concat(objectName);
    }

    @Override
    @SneakyThrows
    public InputStream getObject(String bucketName, String objectName) {
        Assert.hasText(bucketName, "bucketName 为空");
        Assert.hasText(objectName, "objectName 为空");
        File file = new File(clientPath.concat("/").concat(bucketName).concat("/").concat(objectName));
        return new FileInputStream(file);
    }

    @Override
    @SneakyThrows
    public FileResult putObject(String bucketName, String objectName, InputStream stream) {
        Assert.hasText(bucketName, "bucketName 为空");
        Assert.hasText(objectName, "objectName 为空");
        Assert.notNull(stream, "inputStream 为空");

        String filePath = clientPath.concat("/").concat(bucketName);
        String fileName = filePath.concat("/").concat(objectName);
        File path = new File(filePath);
        if (!path.exists()) {
            path.mkdir();
        }

        if (!(stream instanceof BufferedInputStream)) {
            stream = new BufferedInputStream(stream);
        }

        try (BufferedOutputStream outStream = new BufferedOutputStream(new FileOutputStream(fileName))) {
            int len = -1;
            byte[] b = new byte[1024];
            while ((len = stream.read(b)) != -1) {
                outStream.write(b, 0, len);
            }
            stream.close();
        }
        return null;
    }

    @Override
    public FileResult putObjectForce(String bucketName, String objectName, InputStream stream) throws Exception {
        Assert.hasText(bucketName, "bucketName 为空");
        Assert.hasText(objectName, "objectName 为空");
        Assert.notNull(stream, "inputStream 为空");

        String filePath = clientPath.concat("/").concat(bucketName);
        String fileName = filePath.concat("/").concat(objectName);
        File path = new File(filePath);
        if (!path.exists()) {
            path.mkdir();
        }

        if (!(stream instanceof BufferedInputStream)) {
            stream = new BufferedInputStream(stream);
        }

        try (BufferedOutputStream outStream = new BufferedOutputStream(new FileOutputStream(fileName))) {
            int len = -1;
            byte[] b = new byte[1024];
            while ((len = stream.read(b)) != -1) {
                outStream.write(b, 0, len);
            }
            stream.close();
        }
        return null;
    }

    @Override
    public FileResult putObject(String bucketName, String objectName, InputStream stream, long size, String contextType) {
        //todo
        this.putObject(bucketName, objectName, stream);
        return null;
    }

    @Override
    public StatObjectResponse getObjectInfo(String bucketName, String objectName) throws Exception {
        //todo 存储对象封装
        return null;
    }

    @Override
    public void removeObject(String bucketName, String objectName) throws Exception {
        String filePath = clientPath.concat("/").concat(bucketName);
        String fileName = filePath.concat("/").concat(objectName);
        File file = new File(fileName);
        if (file.exists()) {
            file.delete();
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Assert.hasText(clientPath, "client path 为空");
        Assert.hasText(nasUrl, "nas url 为空");
    }

    @Override
    public String getPresignedObjectUrl(String bucket, String object, Integer expireTime, TimeUnit timeUnit) throws Exception{
        return "";
    }

    /**
     * 删除某个目录下所有文件及文件夹
     *
     * @param rootFilePath 根目录
     * @return boolean
     */
    private void deleteAllByPath(File rootFilePath) throws IOException {
        File[] needToDeleteFiles = rootFilePath.listFiles();
        if (needToDeleteFiles == null) {
            return;
        }
        for (int i = 0; i < needToDeleteFiles.length; i++) {
            if (needToDeleteFiles[i].isDirectory()) {
                deleteAllByPath(needToDeleteFiles[i]);
            }
            Files.delete(needToDeleteFiles[i].toPath());
        }
    }
}
