---
title: Trina Security Starter 使用指南
description: Trina Security Starter 组件的快速开始、配置方法和使用说明
---

# Trina Security Starter 使用指南

## 1. 快速开始

### 1.1 引入依赖

在 `pom.xml` 中添加如下依赖：

```xml
<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-security-starter</artifactId>
    <version>4.0.0</version>
</dependency>
```

### 1.2 基础配置

在 `application.yml` 中添加基本配置：

```yaml
trina:
  iam:
    env-url: https://your-iam-server.com
    client-id: your-client-id
    client-secret: your-client-secret
    redirect-url: http://your-app.com/callback
    white-uris:
      - /api/public/**
      - /health
```

### 1.3 启用注解

在 Spring Boot 启动类或配置类上添加注解：

```java
import com.trinasolar.common.security.annotation.EnableSecurity;

@EnableSecurity
@SpringBootApplication
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

## 2. 核心功能

### 2.1 认证与Token机制

- 基于 OAuth2 的统一认证，支持 Token 校验与缓存
- 支持 IAM 平台集成，自动拉取用户信息与权限
- 支持白名单路径免认证

### 2.2 角色与权限注解

#### @HasRole 注解

```java
@RestController
@RequestMapping("/api/users")
public class UserController {
    // 需要admin角色
    @HasRole("admin")
    @GetMapping("/admin")
    public R<List<User>> getAdminUsers() { /* ... */ }

    // 需要admin或manager角色之一
    @HasRole({"admin", "manager"})
    @GetMapping("/manage")
    public R<List<User>> getManageUsers() { /* ... */ }

    // 需要同时拥有admin和manager角色
    @HasRole(value = {"admin", "manager"}, logical = HasRole.Logical.AND)
    @GetMapping("/super")
    public R<List<User>> getSuperUsers() { /* ... */ }
}
```

#### @HasPermission 注解

```java
@RestController
@RequestMapping("/api/users")
public class UserController {
    // 需要user:add权限
    @HasPermission("user:add")
    @PostMapping
    public R<User> addUser(@RequestBody User user) { /* ... */ }

    // 需要user:edit或user:add权限之一
    @HasPermission({"user:edit", "user:add"})
    @PutMapping("/{id}")
    public R<User> updateUser(@PathVariable Long id, @RequestBody User user) { /* ... */ }

    // 需要同时拥有user:delete和user:admin权限
    @HasPermission(value = {"user:delete", "user:admin"}, logical = HasPermission.Logical.AND)
    @DeleteMapping("/{id}")
    public R<Void> deleteUser(@PathVariable Long id) { /* ... */ }
}
```

### 2.3 列级数据权限

#### @ColPermission 注解

```java
@RestController
@RequestMapping("/api/data")
public class DataController {
    // 黑名单模式，自动过滤敏感字段
    @ColPermission(type = ColPermission.Type.BLACK, fields = {"salary", "ssn"})
    @GetMapping("/info")
    public R<UserInfo> getUserInfo() { /* ... */ }

    // 白名单模式，仅返回指定字段
    @ColPermission(type = ColPermission.Type.WHITE, fields = {"name", "email"})
    @GetMapping("/public")
    public R<UserInfo> getPublicInfo() { /* ... */ }
}
```

### 2.4 缓存与性能

- 权限与用户信息自动缓存（Caffeine），提升性能
- 支持自定义缓存参数（见 `IamCaffeineCacheProperties`）

## 3. 高级特性

### 3.1 白名单配置

- 通过 `trina.iam.white-uris` 配置无需认证的接口路径
- 支持通配符与多路径配置

### 3.2 自定义权限服务

- 实现 `PermissionService` 接口并注入为 Bean，可自定义权限获取逻辑

```java
@Service
public class CustomPermissionService implements PermissionService {
    // 实现自定义权限获取逻辑
}
```

### 3.3 多环境与扩展

- 支持多环境配置，灵活适配测试/生产 IAM 平台
- 支持自定义缓存、拦截器、切面等扩展

## 4. 常见问题

### 4.1 启动报"安全模块配置不完整"？
- 检查 `trina.iam.env-url`、`client-id`、`client-secret`、`redirect-url` 是否配置完整

### 4.2 权限注解不生效？
- 检查是否已启用 `@EnableSecurity` 注解
- 检查切面和拦截器是否被 Spring 扫描

### 4.3 Token 校验失败？
- 检查 IAM 服务可用性和配置项
- 检查 Token 是否过期或被篡改

### 4.4 如何自定义权限服务？
- 实现 `PermissionService` 接口并注入为 Bean

## 5. 最佳实践

- 仅在主类或配置类上添加一次 `@EnableSecurity` 注解
- 精确配置 `trina.iam.white-uris`，避免误放开敏感接口
- 合理设置缓存参数，提升性能
- 结合自定义 `PermissionService` 实现复杂权限逻辑
- 结合 CI/CD 自动校验权限注解覆盖率
- 生产环境务必配置 IAM 地址、clientId、clientSecret、redirectUrl

## 6. 参考资料

- [Trina Security 架构文档](../architecture/security-architecture.mdx)
- [Spring Security 官方文档](https://docs.spring.io/spring-security/)
- [Caffeine 缓存文档](https://github.com/ben-manes/caffeine)
- [Trina Common Core 使用指南](./common-core-usage-guide.mdx)