/**
 * CSS files with the .module.css suffix will be treated as CSS modules
 * and scoped locally.
 */

.heroBanner {
  background: #008BD6;
  padding: 6rem 0;
  text-align: center;
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
}



.heroContent {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
}

.heroLogo {
  margin-bottom: 2rem;
  animation: logoGlow 3s ease-in-out infinite;
}

.heroLogo img {
  height: 80px;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

@keyframes logoGlow {
  0%, 100% {
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
  }
  50% {
    filter: drop-shadow(0 4px 16px rgba(255, 255, 255, 0.3));
  }
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 800;
  color: white;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: slideInUp 1s ease-out;
}

.heroSubtitle {
  font-size: 1.4rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
  font-weight: 300;
  animation: slideInUp 1s ease-out 0.2s both;
}

.heroDescription {
  margin-bottom: 3rem;
  animation: slideInUp 1s ease-out 0.4s both;
}

.heroDescription p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 4rem;
  animation: slideInUp 1s ease-out 0.6s both;
}

.getStartedButton {
  background: linear-gradient(45deg, #FF6B35, #F7931E);
  border: none;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.getStartedButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
  color: white;
}

.learnMoreButton {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.learnMoreButton:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  color: white;
}

.stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  animation: slideInUp 1s ease-out 0.8s both;
}

.stat {
  text-align: center;
}

.statNumber {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.statLabel {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 1px;
}



/* Responsive Design */
@media screen and (max-width: 996px) {
  .heroBanner {
    padding: 4rem 0;
    min-height: 80vh;
  }
  
  .heroTitle {
    font-size: 2.5rem;
  }
  
  .heroSubtitle {
    font-size: 1.2rem;
  }
  
  .buttons {
    flex-direction: column;
    gap: 1rem;
  }
  
  .stats {
    flex-direction: column;
    gap: 2rem;
  }
  

}

@media screen and (max-width: 768px) {
  .heroBanner {
    padding: 3rem 0;
  }
  
  .heroTitle {
    font-size: 2rem;
  }
  
  .heroLogo img {
    height: 60px;
  }
  

}
