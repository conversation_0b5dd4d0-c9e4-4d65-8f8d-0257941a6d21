package com.trinasolar.mongodb.base;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * MongoDB基础服务接口
 * 定义通用的文档操作方法
 *
 * @param <T> 实体类型
 */
public interface BaseMongoService<T extends BaseMongoEntity> {

    /**
     * 保存单个文档
     *
     * @param entity 实体对象
     * @return 保存后的实体
     */
    T save(T entity);

    /**
     * 批量保存文档
     *
     * @param entities 实体集合
     * @return 保存后的实体集合
     */
    List<T> saveAll(List<T> entities);

    /**
     * 根据ID查找文档
     *
     * @param id 文档ID
     * @return 可选的实体对象
     */
    Optional<T> findById(String id);

    /**
     * 查询所有文档
     *
     * @return 文档集合
     */
    List<T> findAll();

    /**
     * 根据ID集合查询多个文档
     *
     * @param ids ID集合
     * @return 文档集合
     */
    List<T> findAllById(List<String> ids);

    /**
     * 检查指定ID的文档是否存在
     *
     * @param id 文档ID
     * @return 是否存在
     */
    boolean existsById(String id);

    /**
     * 统计文档总数
     *
     * @return 文档总数
     */
    long count();

    /**
     * 根据ID删除文档
     *
     * @param id 文档ID
     */
    void deleteById(String id);

    /**
     * 删除指定文档
     *
     * @param entity 实体对象
     */
    void delete(T entity);

    /**
     * 批量删除文档
     *
     * @param entities 实体集合
     */
    void deleteAll(List<T> entities);

    /**
     * 删除所有文档
     */
    void deleteAll();

    /**
     * 部分更新文档
     *
     * @param id 文档ID
     * @param updateFields 更新的字段和值
     * @return 更新后的文档
     */
    T partialUpdate(String id, Map<String, Object> updateFields);

    /**
     * 根据自定义查询条件查找文档
     *
     * @param query 查询条件
     * @return 文档列表
     */
    List<T> findByQuery(BaseMongoQuery<T> query);

    /**
     * 分页查询
     *
     * @param query 查询条件
     * @return 分页结果
     */
    PageResult<T> findPage(BaseMongoQuery<T> query);

    /**
     * 根据查询条件统计文档数量
     *
     * @param query 查询条件
     * @return 文档数量
     */
    long countByQuery(BaseMongoQuery<T> query);

    /**
     * 根据查询条件删除文档
     *
     * @param query 查询条件
     * @return 删除的文档数量
     */
    long deleteByQuery(BaseMongoQuery<T> query);

    /**
     * 逻辑删除文档
     *
     * @param id 文档ID
     * @return 是否删除成功
     */
    boolean softDelete(String id);

    /**
     * 批量逻辑删除文档
     *
     * @param ids 文档ID列表
     * @return 删除的文档数量
     */
    long softDeleteBatch(List<String> ids);

    /**
     * 根据查询条件逻辑删除文档
     *
     * @param query 查询条件
     * @return 删除的文档数量
     */
    long softDeleteByQuery(BaseMongoQuery<T> query);
} 