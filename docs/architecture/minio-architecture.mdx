---
title: MinIO 模块架构
description: MinIO 模块的架构设计与实现原理
---

# MinIO 模块架构

## 1. 整体架构

Trina MinIO 模块采用分层架构设计，主要包含以下几个层次：

```
+------------------+
|     API 层       |
|  MinioController |
+------------------+
         ↓
+------------------+
|    服务层        |
|   MinioService   |
+------------------+
         ↓
+------------------+
|    配置层        |
| MinioProperties  |
+------------------+
         ↓
+------------------+
|  MinIO 客户端    |
|   MinioClient    |
+------------------+
```

### 1.1 核心组件

1. **API 层**
   - `MinioController`: 提供 RESTful API 接口
   - `MinioFriendlyApiController`: 提供用户友好的 API 接口

2. **服务层**
   - `MinioService`: 核心业务逻辑接口
   - `MinioServiceImpl`: 服务实现类

3. **配置层**
   - `MinioProperties`: 配置属性类
   - `MinioFriendlyApiConfiguration`: 自动配置类

4. **客户端层**
   - `MinioClient`: MinIO 客户端封装

## 2. 核心功能模块

### 2.1 存储桶管理

```java
public interface MinioService {
    // 创建存储桶
    boolean createBucket(String bucket);
    
    // 检查存储桶是否存在
    boolean bucketExists(String bucket);
    
    // 列出所有存储桶
    List<Bucket> listBuckets();
    
    // 列出存储桶中的对象
    List<Item> listObjects(String bucket, String prefix, boolean recursive);
}
```

### 2.2 文件操作

```java
public interface MinioService {
    // 上传文件
    String uploadFile(MultipartFile file, String bucket, String objectName);
    
    // 下载文件
    void downloadFile(String bucket, String objectName, HttpServletResponse response);
    
    // 删除文件
    boolean deleteFile(String bucket, String objectName);
    
    // 获取文件信息
    StatObjectResponse getFileInfo(String bucket, String objectName);
}
```

### 2.3 临时访问

```java
public interface MinioService {
    // 生成临时访问 URL
    String getPresignedUrl(String bucket, String objectName, int expires);
    
    // 生成临时上传 URL
    String getPresignedPutUrl(String bucket, String objectName, int expires);
}
```

## 3. 配置管理

### 3.1 配置属性

```java
@ConfigurationProperties(prefix = "trina.minio")
public class MinioProperties {
    private String endpoint;            // MinIO 服务端点
    private String accessKey;           // 访问密钥
    private String secretKey;           // 密钥
    private String bucket;              // 默认存储桶
    private Integer connectTimeout;     // 连接超时时间
    private Integer writeTimeout;       // 写入超时时间
    private Integer readTimeout;        // 读取超时时间
}
```

配置示例：

```yaml
trina:
  minio:
    endpoint: ${MINIO_URL:http://localhost:9000}
    access-key: ${ACCESS_KEY:minioadmin}
    secret-key: ${SECRET_KEY:minioadmin}
    bucket: default-bucket
    connect-timeout: 5000
    write-timeout: 60000
    read-timeout: 30000
```

### 3.2 自动配置

```java
@Configuration
@EnableConfigurationProperties(MinioProperties.class)
public class MinioFriendlyApiConfiguration {
    @Bean
    public MinioClient minioClient(MinioProperties properties) {
        return MinioClient.builder()
            .endpoint(properties.getEndpoint())
            .credentials(properties.getAccessKey(), properties.getSecretKey())
            .build();
    }
    
    @Bean
    public MinioService minioService(MinioClient minioClient) {
        return new MinioServiceImpl(minioClient);
    }
}
```

## 4. 异常处理

### 4.1 异常层次结构

```java
public class MinioException extends RuntimeException {
    private int code;
    private String message;
    
    public MinioException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
}

public class BucketNotFoundException extends MinioException {
    public BucketNotFoundException(String bucket) {
        super(1001, "存储桶不存在: " + bucket);
    }
}

public class FileUploadException extends MinioException {
    public FileUploadException(String message) {
        super(1002, "文件上传失败: " + message);
    }
}
```

### 4.2 全局异常处理

```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler(MinioException.class)
    public Result<Void> handleMinioException(MinioException e) {
        return Result.error(e.getCode(), e.getMessage());
    }
}
```

## 5. 安全设计

### 5.1 访问控制

1. **认证机制**
   - 使用 AccessKey 和 SecretKey 进行身份认证
   - 支持临时访问凭证

2. **权限控制**
   - 基于存储桶的访问策略
   - 基于对象的访问控制

### 5.2 数据安全

1. **传输安全**
   - 支持 HTTPS 加密传输
   - 支持客户端加密

2. **存储安全**
   - 支持服务端加密
   - 支持数据完整性验证

## 6. 性能优化

### 6.1 连接池管理

```java
@Configuration
public class MinioConnectionPoolConfig {
    @Bean
    public PoolingHttpClientConnectionManager connectionManager() {
        PoolingHttpClientConnectionManager connectionManager = 
            new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(100);
        connectionManager.setDefaultMaxPerRoute(20);
        return connectionManager;
    }
}
```

### 6.2 缓存策略

1. **元数据缓存**
   - 缓存文件元数据信息
   - 定期刷新缓存

2. **URL 缓存**
   - 缓存临时访问 URL
   - 根据过期时间自动失效

## 7. 扩展设计

### 7.1 插件机制

```java
public interface MinioPlugin {
    void beforeUpload(MultipartFile file, String bucket, String objectName);
    void afterUpload(String url, String bucket, String objectName);
    void beforeDownload(String bucket, String objectName);
    void afterDownload(String bucket, String objectName);
}
```

### 7.2 事件机制

```java
public interface MinioEventListener {
    void onFileUploaded(String bucket, String objectName);
    void onFileDeleted(String bucket, String objectName);
    void onBucketCreated(String bucket);
    void onBucketDeleted(String bucket);
}
```

## 8. 最佳实践

### 8.1 文件命名规范

为了避免文件覆盖和提高查询效率，建议使用以下命名规则：

```
/{业务模块}/{年月日}/{随机UUID}_{原始文件名}
```

例如：

```
/user-avatar/20230615/a1b2c3d4-e5f6-7890-abcd-ef1234567890_profile.jpg
```

### 8.2 文件类型限制

通过白名单控制可上传的文件类型：

```java
public boolean isAllowedFileType(String fileName) {
    String extension = FilenameUtils.getExtension(fileName).toLowerCase();
    return allowedExtensions.contains(extension);
}
```

### 8.3 分片上传

对于大文件，建议使用分片上传：

```java
public String multipartUpload(String bucket, String objectName, String filePath) {
    try {
        minioClient.uploadObject(
            UploadObjectArgs.builder()
                .bucket(bucket)
                .object(objectName)
                .filename(filePath)
                .build());
        return minioClient.getPresignedObjectUrl(
            GetPresignedObjectUrlArgs.builder()
                .bucket(bucket)
                .object(objectName)
                .method(Method.GET)
                .build());
    } catch (Exception e) {
        throw new FileUploadException(e.getMessage());
    }
}
```